FROM maven:3.9.5-amazoncorretto-21 as package

WORKDIR application
ARG CODE_FILE=.
COPY ./settings-docker.xml /usr/share/maven/ref/settings.xml
COPY ${CODE_FILE} .
RUN mvn clean package -DskipTests --settings /usr/share/maven/ref/settings.xml

FROM amazoncorretto:21 as builder
WORKDIR application
ARG JAR_FILE=application/target/*.jar
COPY --from=package ${JAR_FILE} application.jar
RUN java -Djarmode=layertools -jar application.jar extract

FROM amazoncorretto:21

WORKDIR application
COPY --from=builder application/dependencies/ ./
COPY --from=builder application/spring-boot-loader/ ./
COPY --from=builder application/snapshot-dependencies/ ./
COPY --from=builder application/application/ ./
ENV JVM_OPTS="-Xmx256m -Xms256m" \
    TZ=Asia/Shanghai

RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone
RUN rm -rf /etc/yum.repos.d/*.repo #删除repo文件,或者自己备份
RUN curl -o /etc/yum.repos.d/CentOS-Base.repo https://mirrors.aliyun.com/repo/Centos-vault-8.5.2111.repo
RUN yum makecache #更新缓存
RUN yum install -y ttf-dejavu fontconfig

ENTRYPOINT ["sh", "-c","java ${JVM_OPTS} org.springframework.boot.loader.launch.JarLauncher"]

# 暴露容器的端口
EXPOSE 8080
