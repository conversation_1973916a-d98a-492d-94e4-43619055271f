# 销售单开单和取单业务说明文档

## 📋 概述

销售单系统支持开单和取单两种核心操作，通过统一的接口 `createSoldReceipt` 根据是否传入 `soldReceiptId` 来区分操作类型。

### 核心特性
- **开单**：创建新的销售单，支持挂单和直接完成
- **取单**：修改挂单中的销售单，可补齐款项完成交易
- **库存管理**：智能的冻结转销售机制
- **状态流转**：完整的销售单生命周期管理

## 🔄 业务流程

### 开单和取单流程图

```mermaid
flowchart TD
    A[开始] --> B{是否有销售单ID?}

    %% 开单流程
    B -->|无| C[开单流程]
    C --> C1[数据验证]
    C1 --> C2[获取实时金价]
    C2 --> C3[验证货品/赠品信息]
    C3 --> C4[创建旧料记录]
    C4 --> C5[计算销售单统计数据]
    C5 --> C6[创建销售单主记录]
    C6 --> C7[创建明细记录]
    C7 --> C8[创建支付明细]
    C8 --> C9{根据支付情况判断状态}
    C9 -->|已收金额=订单金额| C10[状态: 已完成]
    C9 -->|已收金额<订单金额| C11[状态: 挂单中]
    C10 --> C12[更新库存: 减少库存+增加销售数量]
    C11 --> C13[更新库存: 减少库存+增加冻结数量]
    C12 --> C14[记录操作日志]
    C13 --> C14
    C14 --> Z[结束]

    %% 取单流程
    B -->|有| D[取单流程]
    D --> D1[验证销售单状态为挂单中]
    D1 --> D2[验证订单金额≥已收金额]
    D2 --> D3[获取实时金价]
    D3 --> D4[更新货品明细]
    D4 --> D5[更新旧料明细]
    D5 --> D6[更新赠品明细]
    D6 --> D7[更新支付明细]
    D7 --> D8[重新计算统计数据]
    D8 --> D9{根据支付情况判断状态}
    D9 -->|已收金额=订单金额| D10[状态: 已完成]
    D9 -->|已收金额<订单金额| D11[状态: 挂单中]
    D10 --> D12{状态是否发生变化?}
    D11 --> D13[更新销售单明细记录]
    D12 -->|挂单→已完成| D14[执行冻结转销售]
    D12 -->|无变化| D13
    D14 --> D15[冻结数量转为销售数量]
    D15 --> D13
    D13 --> D16[记录操作日志]
    D16 --> Z

    %% 样式
    classDef processBox fill:#e1f5fe
    classDef decisionBox fill:#fff3e0
    classDef statusBox fill:#e8f5e8
    classDef stockBox fill:#fce4ec

    class C1,C2,C3,C4,C5,C6,C7,C8,D1,D2,D3,D4,D5,D6,D7,D8,D13,D14,D15,D16,C14 processBox
    class B,C9,D9,D12 decisionBox
    class C10,C11,D10,D11 statusBox
    class C12,C13 stockBox
```

### 开单流程
1. **数据验证** - 验证货品、赠品、支付信息
2. **价格获取** - 获取实时金价用于计算
3. **明细创建** - 创建货品、旧料、赠品、支付明细
4. **状态判断** - 根据已收金额与订单金额比较确定状态
5. **库存更新** - 根据状态更新库存（冻结/销售）
6. **日志记录** - 记录操作日志

### 取单流程
1. **状态验证** - 确认销售单为挂单中状态
2. **金额验证** - 确保新订单金额不小于已收金额
3. **明细更新** - 更新各类明细信息
4. **状态重算** - 重新计算销售单状态
5. **冻结转销售** - 如状态变为已完成，执行库存转换
6. **日志记录** - 记录取单操作

## 📊 状态管理

### 状态流转图

```mermaid
stateDiagram-v2
    [*] --> 挂单中 : 开单(已收<订单金额)
    [*] --> 已完成 : 开单(已收=订单金额)

    挂单中 --> 已完成 : 取单补齐款项
    挂单中 --> 已作废 : 作废操作

    已完成 --> 部分退货 : 部分商品退货
    已完成 --> 全部退货 : 全部商品退货

    部分退货 --> 全部退货 : 剩余商品全部退货

    已作废 --> [*]
    全部退货 --> [*]

    note right of 挂单中
        状态码: 0
        库存: 冻结数量
    end note

    note right of 已完成
        状态码: 1
        库存: 销售数量
    end note

    note right of 部分退货
        状态码: 2
        部分商品已退货
    end note

    note right of 全部退货
        状态码: 3
        所有商品已退货
    end note

    note right of 已作废
        状态码: -1
        销售单无效
    end note
```

### 状态说明

| 状态码 | 状态名称 | 说明 | 库存处理 |
|--------|----------|------|----------|
| -1 | 已作废 | 销售单无效 | 恢复库存 |
| 0 | 挂单中 | 已收金额 < 订单金额 | 冻结数量 |
| 1 | 已完成 | 已收金额 = 订单金额 | 销售数量 |
| 2 | 部分退货 | 部分商品已退货 | 部分恢复 |
| 3 | 全部退货 | 所有商品已退货 | 完全恢复 |

## 🏪 库存处理机制

### 库存处理流程图

```mermaid
flowchart TD
    A[库存操作] --> B{操作类型}

    %% 开单库存处理
    B -->|开单| C{销售单状态}
    C -->|挂单中| D[减少库存数量<br/>增加冻结数量]
    C -->|已完成| E[减少库存数量<br/>增加销售数量]

    %% 取单库存处理
    B -->|取单| F[统一操作冻结数量]
    F --> G[明细变更时<br/>只操作冻结数量]
    G --> H{状态是否变化?}
    H -->|挂单→已完成| I[执行冻结转销售]
    H -->|无变化| J[保持原状态]
    I --> K[冻结数量 → 销售数量]

    %% 退货库存处理
    B -->|退货| L[恢复库存数量<br/>减少销售数量]

    %% 样式
    classDef operationBox fill:#e3f2fd
    classDef statusBox fill:#f3e5f5
    classDef stockBox fill:#e8f5e8
    classDef conversionBox fill:#fff3e0

    class A,F,G,L operationBox
    class B,C,H statusBox
    class D,E,J,K stockBox
    class I conversionBox
```

### 开单库存处理
- **挂单状态**：`库存数量 ↓` + `冻结数量 ↑`
- **已完成状态**：`库存数量 ↓` + `销售数量 ↑`

### 取单库存处理详细流程

```mermaid
flowchart TD
    A[取单开始] --> B[获取现有货品明细]
    B --> C[比较请求明细与现有明细]
    C --> D{明细操作类型}

    %% 新增货品明细
    D -->|新增明细| E[新增货品明细]
    E --> E1[验证库存是否充足]
    E1 --> E2{库存是否足够?}
    E2 -->|否| E3[抛出库存不足异常]
    E2 -->|是| E4[创建新明细记录]
    E4 --> E5[库存变更: stock_num ↓<br/>frozen_num ↑]
    E5 --> M[收集库存变更]

    %% 更新货品明细数量
    D -->|更新明细| F[更新货品明细数量]
    F --> F1[计算数量变化 quantityChange]
    F1 --> F2{数量变化类型}
    F2 -->|增加数量| F3[验证库存是否充足]
    F3 --> F4{库存是否足够?}
    F4 -->|否| F5[抛出库存不足异常]
    F4 -->|是| F6[更新明细记录]
    F6 --> F7[库存变更: stock_num ↓<br/>frozen_num ↑<br/>变更量: +quantityChange]
    F7 --> M
    F2 -->|减少数量| F8[更新明细记录]
    F8 --> F9[库存变更: stock_num ↑<br/>frozen_num ↓<br/>变更量: -quantityChange]
    F9 --> M
    F2 -->|数量不变| F10[仅更新明细属性]
    F10 --> M

    %% 移除货品明细
    D -->|移除明细| G[移除货品明细]
    G --> G1[查询要删除的明细]
    G1 --> G2{销售单当前状态}
    G2 -->|挂单中| G3[库存变更: stock_num ↑<br/>frozen_num ↓]
    G2 -->|已完成| G4[库存变更: stock_num ↑<br/>sold_num ↓]
    G3 --> G5[删除明细记录]
    G4 --> G5
    G5 --> M

    %% 批量处理库存变更
    M --> N[批量执行库存变更]
    N --> O[记录操作日志]
    O --> P{销售单状态是否变化?}
    P -->|挂单→已完成| Q[执行冻结转销售]
    Q --> Q1[所有明细的frozen_num → sold_num]
    Q1 --> R[取单完成]
    P -->|状态无变化| R

    %% 样式
    classDef newBox fill:#e8f5e8
    classDef updateBox fill:#e3f2fd
    classDef deleteBox fill:#fce4ec
    classDef stockBox fill:#fff3e0
    classDef conversionBox fill:#f3e5f5

    class E,E1,E2,E4,E5 newBox
    class F,F1,F2,F6,F7,F8,F9,F10 updateBox
    class G,G1,G2,G3,G4,G5 deleteBox
    class M,N stockBox
    class Q,Q1 conversionBox
```

#### 1. 新增货品明细
- **库存验证**：检查 `stock_num >= 新增数量`
- **库存变更**：`stock_num ↓` + `frozen_num ↑`
- **异常处理**：库存不足时抛出异常，阻止操作

#### 2. 更新货品明细数量
- **数量增加**：
  - 验证：`stock_num >= 增加数量`
  - 变更：`stock_num ↓` + `frozen_num ↑` (变更量)
- **数量减少**：
  - 变更：`stock_num ↑` + `frozen_num ↓` (变更量)
- **数量不变**：仅更新明细属性，无库存变更

#### 3. 移除货品明细
- **挂单状态**：`stock_num ↑` + `frozen_num ↓`
- **已完成状态**：`stock_num ↑` + `sold_num ↓`
- **逆向操作**：根据当前状态恢复对应的库存类型

#### 4. 统一处理机制
- **明细变更**：统一操作冻结数量，避免重复处理
- **状态变更**：挂单→已完成时，执行 `冻结数量 → 销售数量`
- **核心方法**：`adjustStockForStatusChange()` 统一处理转换

### 库存字段说明
- `stock_num`：可用库存数量
- `frozen_num`：冻结数量（挂单占用）
- `sold_num`：销售数量（已售出）
- `num`：总数量（stock_num + frozen_num + sold_num + ...）

### 库存流转示例

假设某货品初始库存：`stock_num=100, frozen_num=0, sold_num=0`

| 操作场景 | 操作前状态 | 操作内容 | 库存变更 | 操作后状态 |
|----------|------------|----------|----------|------------|
| **新增明细** | stock:100, frozen:0 | 新增数量5 | stock:-5, frozen:+5 | stock:95, frozen:5 |
| **增加数量** | stock:95, frozen:5 | 数量5→8 | stock:-3, frozen:+3 | stock:92, frozen:8 |
| **减少数量** | stock:92, frozen:8 | 数量8→6 | stock:+2, frozen:-2 | stock:94, frozen:6 |
| **移除明细(挂单)** | stock:94, frozen:6 | 移除数量6 | stock:+6, frozen:-6 | stock:100, frozen:0 |
| **移除明细(已完成)** | stock:94, sold:6 | 移除数量6 | stock:+6, sold:-6 | stock:100, sold:0 |
| **冻结转销售** | stock:94, frozen:6 | 挂单→已完成 | frozen:-6, sold:+6 | stock:94, sold:6 |

### 关键业务规则

1. **库存验证规则**
   - 新增/增加数量时：必须 `stock_num >= 所需数量`
   - 减少数量时：无需验证（释放冻结库存）

2. **库存变更原则**
   - 取单明细变更：统一操作 `frozen_num`
   - 移除明细：根据销售单当前状态决定操作 `frozen_num` 或 `sold_num`
   - 状态变更：统一执行 `frozen_num → sold_num`

3. **异常处理**
   - 库存不足：立即抛出异常，阻止操作
   - 明细不存在：抛出业务异常
   - 状态不匹配：只能操作挂单中的销售单

## 🔧 核心方法

### SoldReceiptCreateServiceImpl

#### createSoldReceipt()
```java
// 统一入口，根据soldReceiptId判断操作类型
public Long createSoldReceipt(SoldReceiptCreateDTO dto)
```

#### createNewSoldReceipt()
```java
// 开单逻辑：创建新销售单
private Long createNewSoldReceipt(SoldReceiptCreateDTO dto)
```

#### updateSoldReceipt()
```java
// 取单逻辑：更新现有销售单
private Long updateSoldReceipt(SoldReceiptCreateDTO dto)
```

#### adjustStockForStatusChange()
```java
// 冻结转销售：挂单→已完成时调用
private void adjustStockForStatusChange(Long soldReceiptId)
```

### 核心代码逻辑

#### 新增货品明细
```java
// 验证库存
if (goods.getStockNum() < goodsItem.getNum()) {
    throw new BusinessException("货品 " + goods.getName() + " 库存不足");
}

// 创建库存变更
StockNumChangeBO stockChange = StockNumChangeBO.builder()
    .goodsId(goods.getId())
    .stockNum(-goodsItem.getNum())     // 减少库存
    .frozenNum(goodsItem.getNum())     // 增加冻结数量
    .comment("取单更新货品-销售冻结")
    .build();
```

#### 更新货品明细数量
```java
// 计算数量变化
int quantityChange = goodsItem.getNum() - existingDetail.getNum();

// 验证库存（仅在增加数量时）
if (quantityChange > 0 && goods.getStockNum() < quantityChange) {
    throw new BusinessException("货品库存不足，最大可出库数: " +
        (existingDetail.getNum() + goods.getStockNum()));
}

// 创建库存变更（统一操作冻结数量）
if (quantityChange != 0) {
    StockNumChangeBO stockChange = StockNumChangeBO.builder()
        .goodsId(goods.getId())
        .stockNum(-quantityChange)      // 库存变更
        .frozenNum(quantityChange)      // 冻结数量变更
        .comment("取单更新货品-销售冻结")
        .build();
}
```

#### 移除货品明细
```java
// 根据销售单状态决定库存操作
StockNumChangeBO.StockNumChangeBOBuilder builder = StockNumChangeBO.builder()
    .goodsId(goodsDetail.getGoodsId())
    .stockNum(goodsDetail.getNum());    // 增加库存

if (soldReceiptStatus != null && soldReceiptStatus == 1) {
    // 已完成状态：减少销售数量
    builder.soldNum(-goodsDetail.getNum()).comment("取单移除货品-恢复库存");
} else {
    // 挂单状态：减少冻结数量
    builder.frozenNum(-goodsDetail.getNum()).comment("取单移除货品-恢复库存");
}
```

#### 冻结转销售
```java
// 状态变更时统一处理
if (originalStatus == 0 && existingSoldReceipt.getStatus() == 1) {
    // 执行冻结转销售
    StockNumChangeBO stockChange = StockNumChangeBO.builder()
        .goodsId(goodsDetail.getGoodsId())
        .frozenNum(-goodsDetail.getNum())   // 减少冻结数量
        .soldNum(goodsDetail.getNum())      // 增加销售数量
        .comment("取单状态变更-冻结转销售")
        .build();
}
```

## ⚠️ 重要注意事项

### 库存操作原则
1. **取单明细变更**：统一操作冻结数量，不根据状态判断
2. **状态变更处理**：由 `adjustStockForStatusChange` 统一处理冻结转销售
3. **避免重复处理**：防止同时操作冻结和销售数量

### 数据一致性
- 使用悲观锁确保库存更新的原子性
- 事务保证数据的一致性
- 详细的操作日志便于问题追踪

### 业务规则
- 取单只能操作挂单中的销售单
- 新订单金额不能小于已收金额
- 状态变更时自动触发库存转换

## 🚀 API 接口

### 创建/取单接口
```http
POST /sold/receipt/create
Content-Type: application/json

{
  "soldReceiptId": null,        // null=开单, 有值=取单
  "merchantId": 1,              // 门店ID
  "memberId": 1,                // 会员ID（可选）
  "goodsList": [...],           // 货品明细
  "giftList": [...],            // 赠品明细
  "oldMaterialList": [...],     // 旧料明细
  "paymentList": [...]          // 支付明细
}
```

### 获取挂单详情
```http
GET /sold/receipt/pending?soldReceiptId=1
```

## 📈 性能优化

- 批量操作减少数据库访问
- 使用 Map 缓存减少重复查询
- 悲观锁保证并发安全
- 异步日志记录提升性能

---

*文档版本：v1.0 | 更新时间：2025-01-09*
