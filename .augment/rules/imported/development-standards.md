---
type: "always_apply"
---

# 项目开发规范与约定

## 一、基本约定

以下内容必须严格遵守!!! 

### 1.1 路由与参数
- 项目路由一律不使用路径参数，确保路由设计简洁统一。

### 1.2 数据库查询
- 优先使用 MyBatis-Flex 的 APT 方式生成查询代码：
  ```java
  // 1. 静态导入 TableDef
  import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
  import static com.xc.boot.modules.goods.model.entity.table.CategoryTableDef.CATEGORY;
  
  // 2. 在实体类上添加 @Table 注解
  @Table("goods")
  public class Goods {
      @Id
      private Long id;
      private String name;
      private BigDecimal price;
      private Integer stockNum;  // 数据库字段为 STOCK_NUM
      private Date createTime;   // 数据库字段为 CREATE_TIME
      // ... 其他字段
  }
  
  // 3. 构建查询，直接使用静态导入的 TableDef
  QueryWrapper query = QueryWrapper.create()
      .select(GOODS.ID, GOODS.NAME, GOODS.PRICE)
      .where(GOODS.STOCK_NUM.gt(0))
      .and(GOODS.CREATE_TIME.gt(LocalDateTime.now().minusDays(7)));
  ```
- APT 类生成说明：
  - APT 类在项目编译时自动生成
  - 如果找不到 APT 类，请执行以下步骤：
    1. 确保实体类已添加 `@Table` 注解
    2. 执行 `make package` 命令重新构建项目
    3. 检查 `target/generated-sources/annotations` 目录下是否生成了对应的 TableDef 类
  - 常见问题处理：
    - 如果执行 `make package` 后仍未生成，请检查：
      1. 实体类是否正确添加了 `@Table` 注解
      2. 实体类是否在正确的包路径下
      3. 项目的 `pom.xml` 中是否正确配置了 MyBatis-Flex 的 APT 依赖
    - 如果 IDE 中无法识别生成的类，请：
      1. 刷新 Maven 项目
      2. 重新导入项目
      3. 清除 IDE 缓存并重启
- TableDef 静态导入规范：
  - 导入路径格式：`com.xc.boot.modules.{模块名}.model.entity.table.{实体名}TableDef.{实体名}`
  - 示例：
    ```java
    // 商品模块
    import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
    import static com.xc.boot.modules.goods.model.entity.table.GoodsHasColumnsTableDef.GOODS_HAS_COLUMNS;
    
    // 订单模块
    import static com.xc.boot.modules.order.model.entity.table.OrderTableDef.ORDER;
    import static com.xc.boot.modules.order.model.entity.table.OrderItemTableDef.ORDER_ITEM;
    
    // 用户模块
    import static com.xc.boot.modules.user.model.entity.table.UserTableDef.USER;
    import static com.xc.boot.modules.user.model.entity.table.UserRoleTableDef.USER_ROLE;
    ```
  - 实际应用参考：ListFillService.java 中的静态导入示例：
    ```java
    // 系统模块
    import static com.xc.boot.system.model.entity.table.MerchantTableDef.MERCHANT;
    import static com.xc.boot.system.model.entity.table.PrintTagTableDef.PRINT_TAG;
    import static com.xc.boot.system.model.entity.table.SysRoleTableDef.SYS_ROLE;
    import static com.xc.boot.system.model.entity.table.SysUserMerchantTableDef.SYS_USER_MERCHANT;
    import static com.xc.boot.system.model.entity.table.SysUserRoleTableDef.SYS_USER_ROLE;
    import static com.xc.boot.system.model.entity.table.SysUserTableDef.SYS_USER;
    
    // 商品模块
    import static com.xc.boot.modules.goods.model.entity.table.GoodsHasColumnsTableDef.GOODS_HAS_COLUMNS;
    import static com.xc.boot.modules.goods.model.entity.table.GoodsHasImagesTableDef.GOODS_HAS_IMAGES;
    
    // 商户模块
    import static com.xc.boot.modules.merchant.model.entity.table.BrandTableDef.BRAND;
    import static com.xc.boot.modules.merchant.model.entity.table.CounterTableDef.COUNTER;
    import static com.xc.boot.modules.merchant.model.entity.table.GoodsColumnTableDef.GOODS_COLUMN;
    import static com.xc.boot.modules.merchant.model.entity.table.GoodsIncomeTemplateTableDef.GOODS_INCOME_TEMPLATE;
    import static com.xc.boot.modules.merchant.model.entity.table.JewelryTableDef.JEWELRY;
    import static com.xc.boot.modules.merchant.model.entity.table.QualityTableDef.QUALITY;
    import static com.xc.boot.modules.merchant.model.entity.table.StyleTableDef.STYLE;
    import static com.xc.boot.modules.merchant.model.entity.table.SubclassTableDef.SUBCLASS;
    import static com.xc.boot.modules.merchant.model.entity.table.SupplierTableDef.SUPPLIER;
    import static com.xc.boot.modules.merchant.model.entity.table.TechnologyTableDef.TECHNOLOGY;
    ```
  - ListFillService.java 中的查询示例：
    ```java
    // 1. 用户角色查询
    QueryWrapper.create()
        .leftJoin(SYS_ROLE).on(SYS_USER_ROLE.ROLE_ID.eq(SYS_ROLE.ID))
        .where(SYS_USER_ROLE.USER_ID.in(keys))
        .select(SYS_ROLE.NAME,
                SYS_USER_ROLE.USER_ID,
                SYS_ROLE.ID)
    
    // 2. 用户商店查询
    QueryWrapper.create()
        .leftJoin(MERCHANT).on(SYS_USER_MERCHANT.MERCHANT_ID.eq(MERCHANT.ID))
        .where(SYS_USER_MERCHANT.USER_ID.in(keys))
        .select(MERCHANT.NAME,
                SYS_USER_MERCHANT.USER_ID,
                MERCHANT.ID)
    
    // 3. 自定义字段查询
    QueryWrapper.create()
        .leftJoin(GOODS_COLUMN).on(GOODS_COLUMN.ID.eq(GOODS_HAS_COLUMNS.COLUMN_ID))
        .where(GOODS_HAS_COLUMNS.GOODS_ID.in(keys))
        .where(GOODS_COLUMN.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
        .select(GOODS_HAS_COLUMNS.COLUMN_SIGN.as("columnSign"),
                GOODS_HAS_COLUMNS.VALUE.as("value"),
                GOODS_COLUMN.TYPE.as("type"))
    ```
- APT 字段命名规范：
  - 实体类中的驼峰命名会自动转换为大写+下划线格式
  - 例如：
    - `stockNum` -> `STOCK_NUM`
    - `createTime` -> `CREATE_TIME`
    - `userId` -> `USER_ID`
  - 在 TableDef 中使用时，必须使用大写+下划线格式
  - 避免直接使用字符串常量作为字段名
- 仅在以下场景使用 QueryMethods 方式：
  - 动态表名查询
  - 复杂子查询
  - 自定义 SQL 片段
- 获取字段名时：
  - 优先使用 TableDef 中定义的大写字段
  - 其次使用实体类的 getter 方法
  - 最后才使用字符串常量

### 1.3 工具库
- 项目已引入 `Hutool` 工具库，优先使用 `Hutool` 提供的方法，避免重复封装。

### 1.4 类定义
- 定义新类前，需检查是否已存在符合需求的类，避免重复定义。

### 1.5 列表查询
- 列表查询接口在无明确排序字段时，默认按 `id` 降序排序。

### 1.6 文件状态
- 涉及文件字段时，保存文件 ID 到业务记录后，需调用 @CommonUtils.java 中的 `updateFileStatus` 方法更新文件使用状态。

### 1.7 操作日志
- 系统操作日志通过 @OpLogUtils.java 中的 `appendOpLog` 方法记录，参数说明: 
  - `comment`: 操作日志描述 (格式: 模块-操作) 。
  - `content`: 操作日志内容。
  - `extra`: 额外信息 (如修改前/后数据，可传 `null`) 。

### 1.8 import 规范
- 没有重名的前提下，必须使用 import 导入类，不允许使用包名直接调用 class。

## 二、业务约定

### 2.1 商户端功能
- 若数据表包含 `company_id` 字段，查询时需添加条件 `company_id = SecurityUtils.getCompanyId()`。
- 若数据表包含 `merchant_id` 字段，查询时需添加条件 `merchant_id in (SecurityUtils.getMerchantIds())`。
  - 注意: 若 `SecurityUtils.isMain()` 返回 `true`，表示当前用户为主账号，无需受门店范围约束。

### 2.2 表单校验
- 表单中的文本字段需添加长度校验，防止数据库越界错误。

## 三、目录结构
- 数据传输对象 (`dto`、`query`、`form`、`vo`、`bo`、`entity`) 均定义在 `model` 目录下，各自拥有独立子目录。

## 四、开发规范

### 4.2 异常处理
- 业务异常使用 @CommonUtils.java 中的 `abort` 方法抛出。

### 4.3 枚举定义
- 新定义的枚举需实现 @IBaseEnum.java 接口。

### 4.4 分页查询
- 涉及分页的 `Query` 对象需继承 @BasePageQuery.java。

### 4.5 QueryMethods 优化
- 若多次使用 `QueryMethods` 的方法，需静态导入: 
  ```java
  import static com.mybatisflex.core.query.QueryMethods.*;
  ```

### 4.6 数据库表限制
- @BaseDbTables.java 中的数据表位于基础库，禁止与业务库数据表进行 `join` 操作。

### 4.7 其他规范
- 价格字段应该统一使用 `BigDecimal` 类型处理

## 五、常用文件参考
- **工具类**:
  - 业务相关工具方法: @CommonUtils.java
  - 价格单位转换: @PriceUtil.java
  - 库存统一处理: @StockUtils.java
  - 系统操作日志: @OpLogUtils.java
  - 条码/单号生成: @SnUtils.java
  - 获取登录用户信息: @SecurityUtils.java
  - 列表关联数据填充: @ListFillUtil.java (使用参考: @UserServiceImpl.java，未定义方法需在 @ListFillService.java 中补充) 
- **基础类**:
  - 基础实体类: @BaseEntity.java
  - 基础分页请求对象: @BasePageQuery.java
  - 删除请求对象: @DeleteRequest.java
  - 状态更改请求对象: @SwitchRequest.java
  - 公共选项类型: @Option.java
- **业务开发参考**: 
  - Service 接口: @CompanyService.java
  - Service 实现类: @CompanyServiceImpl.java
  - Controller 实现: @CompanyController.java
  - Mapper 定义: @CompanyMapper.java
  - Entity 定义: @CompanyEntity.java

## 六、MySQL 批量更新语句实现参考
直接使用 `Db.updateBySql` 执行原生语句:

```sql
update users as u
    join (select 1 as id, 'inactive' as status
          union all
          select 2 as id, 'suspended' as status
          union all
          select 3 as id, 'active' as status
          union all
          select 4 as id, 'suspended' as status) as updates on u.id = updates.id
set u.status = updates.status
where u.id in (1, 2, 3, 4);
```