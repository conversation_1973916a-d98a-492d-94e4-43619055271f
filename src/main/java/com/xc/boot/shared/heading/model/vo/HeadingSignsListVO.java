package com.xc.boot.shared.heading.model.vo;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 表头标签列表视图对象
 */
@Data
@Schema(description = "表头标签列表视图对象")
public class HeadingSignsListVO {

    @Schema(description = "标签")
    private String sign;

    @Schema(description = "表头说明")
    private String title;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 