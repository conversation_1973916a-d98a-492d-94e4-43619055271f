package com.xc.boot.shared.heading.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表头标签分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "表头标签分页查询对象")
public class HeadingSignsPageQuery extends BasePageQuery {

    @Schema(description = "表头说明")
    private String title;

    @Schema(description = "标签")
    private String sign;

    @Schema(description = "是否需要自定义字段(0:否|1:是)")
    private Integer needCustom;
} 