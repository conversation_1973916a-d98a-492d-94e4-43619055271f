package com.xc.boot.shared.heading.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.shared.heading.model.query.HeadingSignsPageQuery;
import com.xc.boot.shared.heading.model.vo.HeadingSignsListVO;
import com.xc.boot.shared.heading.model.vo.HeadingSignsPageVO;
import com.xc.boot.system.model.entity.HeadingSignsEntity;

import java.util.List;

/**
 * 表头标签服务接口
 */
public interface TableHeadingService extends IService<HeadingSignsEntity> {
    
    /**
     * 获取表头标签分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<HeadingSignsPageVO> getHeadingSignsPage(HeadingSignsPageQuery queryParams);

    /**
     * 获取表头标签列表
     *
     * @return 表头标签列表
     */
    List<HeadingSignsListVO> getHeadingSignsList();

    /**
     * 保存表头标签
     *
     * @param entity 表头标签实体
     * @return 是否成功
     */
    boolean saveHeadingSigns(HeadingSignsEntity entity);

    /**
     * 更新表头标签
     *
     * @param entity 表头标签实体
     * @return 是否成功
     */
    boolean updateHeadingSigns(HeadingSignsEntity entity);

    /**
     * 删除表头标签
     *
     * @param id 表头标签ID
     * @return 是否成功
     */
    boolean deleteHeadingSigns(Long id);
} 