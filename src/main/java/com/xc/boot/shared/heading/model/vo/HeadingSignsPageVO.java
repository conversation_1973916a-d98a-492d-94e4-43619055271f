package com.xc.boot.shared.heading.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 表头标签分页视图对象
 */
@Data
@Schema(description = "表头标签分页视图对象")
public class HeadingSignsPageVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "表头说明")
    private String title;

    @Schema(description = "标签")
    private String sign;

    @Schema(description = "表头设置")
    private String columns;

    @Schema(description = "是否需要自定义字段(0:否|1:是)")
    private Integer needCustom;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;
} 