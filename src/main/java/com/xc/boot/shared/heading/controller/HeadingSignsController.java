package com.xc.boot.shared.heading.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.shared.heading.model.dto.HeadingSignsDeleteRequest;
import com.xc.boot.shared.heading.model.query.HeadingSignsPageQuery;
import com.xc.boot.shared.heading.model.vo.HeadingSignsListVO;
import com.xc.boot.shared.heading.model.vo.HeadingSignsPageVO;
import com.xc.boot.shared.heading.service.TableHeadingService;
import com.xc.boot.system.model.entity.HeadingSignsEntity;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 表头标签管理控制层
 */
@Tag(name = "系统-表头标签管理")
@RestController
@RequestMapping("/api/heading-signs")
@RequiredArgsConstructor
public class HeadingSignsController {

    private final TableHeadingService tableHeadingService;

    @Operation(summary = "表头标签分页列表")
    @GetMapping("/page")
    public PageResult<HeadingSignsPageVO> getHeadingSignsPage(HeadingSignsPageQuery queryParams) {
        Page<HeadingSignsPageVO> result = tableHeadingService.getHeadingSignsPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "表头标签列表")
    @GetMapping("/list")
    public Result<List<HeadingSignsListVO>> getHeadingSignsList() {
        List<HeadingSignsListVO> result = tableHeadingService.getHeadingSignsList();
        return Result.success(result);
    }

    @Operation(summary = "新增表头标签")
    @PostMapping
    public Result<Boolean> addHeadingSigns(@RequestBody @Valid HeadingSignsEntity entity) {
        boolean result = tableHeadingService.saveHeadingSigns(entity);
        return Result.success(result);
    }

    @Operation(summary = "编辑表头标签")
    @PutMapping
    public Result<Boolean> editHeadingSigns(@RequestBody @Valid HeadingSignsEntity entity) {
        boolean result = tableHeadingService.updateHeadingSigns(entity);
        return Result.success(result);
    }

    @Operation(summary = "删除表头标签")
    @DeleteMapping
    public Result<Boolean> deleteHeadingSigns(@RequestBody @Valid HeadingSignsDeleteRequest request) {
        boolean result = tableHeadingService.deleteHeadingSigns(request.getId());
        return Result.success(result);
    }
} 