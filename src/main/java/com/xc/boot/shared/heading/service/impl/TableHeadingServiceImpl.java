package com.xc.boot.shared.heading.service.impl;

import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.shared.heading.model.query.HeadingSignsPageQuery;
import com.xc.boot.shared.heading.model.vo.HeadingSignsListVO;
import com.xc.boot.shared.heading.model.vo.HeadingSignsPageVO;
import com.xc.boot.shared.heading.service.TableHeadingService;
import com.xc.boot.system.mapper.HeadingSignsMapper;
import com.xc.boot.system.mapper.HeadingUserCacheMapper;
import com.xc.boot.system.model.entity.HeadingSignsEntity;
import com.xc.boot.system.model.entity.HeadingUserCacheEntity;

import lombok.RequiredArgsConstructor;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 表头标签服务实现类
 */
@Service
@RequiredArgsConstructor
public class TableHeadingServiceImpl extends ServiceImpl<HeadingSignsMapper, HeadingSignsEntity> implements TableHeadingService {

    private final HeadingUserCacheMapper headingUserCacheMapper;

    @Override
    public Page<HeadingSignsPageVO> getHeadingSignsPage(HeadingSignsPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(HeadingSignsEntity::getId),
                        QueryMethods.column(HeadingSignsEntity::getTitle),
                        QueryMethods.column(HeadingSignsEntity::getSign),
                        QueryMethods.column(HeadingSignsEntity::getColumns),
                        QueryMethods.column(HeadingSignsEntity::getNeedCustom),
                        QueryMethods.column(HeadingSignsEntity::getCreatedAt),
                        QueryMethods.column(HeadingSignsEntity::getUpdatedAt)
                )
                .from(HeadingSignsEntity.class)
                // 表头说明模糊查询
                .where(HeadingSignsEntity::getTitle)
                .like(queryParams.getTitle(), StrUtil.isNotBlank(queryParams.getTitle()))
                // 标签模糊查询
                .and(HeadingSignsEntity::getSign)
                .like(queryParams.getSign(), StrUtil.isNotBlank(queryParams.getSign()))
                // 是否需要自定义字段精确匹配
                .and(HeadingSignsEntity::getNeedCustom)
                .eq(queryParams.getNeedCustom(), queryParams.getNeedCustom() != null);

        // 执行分页查询并直接映射到VO
        return mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(), queryWrapper, HeadingSignsPageVO.class);
    }

    @Override
    public List<HeadingSignsListVO> getHeadingSignsList() {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(HeadingSignsEntity::getSign),
                        QueryMethods.column(HeadingSignsEntity::getTitle),
                        QueryMethods.column(HeadingSignsEntity::getUpdatedAt)
                )
                .from(HeadingSignsEntity.class)
                .orderBy(HeadingSignsEntity::getUpdatedAt).desc();

        // 执行查询并直接映射到VO
        return mapper.selectListByQueryAs(queryWrapper, HeadingSignsListVO.class);
    }

    @Override
    public boolean saveHeadingSigns(HeadingSignsEntity entity) {
        return this.save(entity);
    }

    @Override
    public boolean updateHeadingSigns(HeadingSignsEntity entity) {
        deleteHeadingUserCacheByHeadingId(entity.getId());

        return this.updateById(entity);
    }

    @Override
    public boolean deleteHeadingSigns(Long id) {
        deleteHeadingUserCacheByHeadingId(id);

        return this.removeById(id);
    }

    /**
     * 根据表头ID异步删除缓存
     * @param headingId 表头ID
     */
    @Async
    private void deleteHeadingUserCacheByHeadingId(Long headingId){
        if(headingId == null){
            return;
        }
        HeadingSignsEntity headingSignsEntity = this.getById(headingId);
        if(headingSignsEntity == null){
            return;
        }

        headingUserCacheMapper.deleteByQuery(
            QueryWrapper.create()
            .from(HeadingUserCacheEntity.class)
            .where(HeadingUserCacheEntity::getSign).eq(headingSignsEntity.getSign())
        );
    }
} 