package com.xc.boot.shared.file.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Row;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.model.Option;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.shared.file.model.FileListQuery;
import com.xc.boot.shared.file.model.FileListVO;
import com.xc.boot.shared.file.model.FileExtensionVO;
import com.xc.boot.shared.file.model.FileStatisticsVO;
import com.xc.boot.shared.file.model.FileExportVO;
import com.xc.boot.shared.file.service.FileListService;
import com.xc.boot.system.mapper.FileMapper;
import com.xc.boot.system.model.entity.FileEntity;
import com.xc.boot.system.model.entity.CompanyEntity;
import com.xc.boot.system.model.entity.SysUserEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 文件列表查询服务实现类
 */
@Service
public class FileListServiceImpl extends ServiceImpl<FileMapper, FileEntity> implements FileListService {

    // 可预览的文件类型
    private static final Set<String> PREVIEWABLE_EXTENSIONS = new HashSet<>(Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "webp" // 图片
    ));

    @Override
    public Page<FileListVO> listFiles(FileListQuery query) {
        QueryWrapper queryWrapper = buildQueryWrapper(query);

        queryWrapper
                .leftJoin(CompanyEntity.class).on(q -> q.and(FileEntity::getCompanyId).eq(CompanyEntity::getId))
                .leftJoin(SysUserEntity.class).on(q -> q.and(FileEntity::getCreatedBy).eq(SysUserEntity::getId))
                .select(
                        QueryMethods.column(FileEntity::getId),
                        QueryMethods.column(FileEntity::getCompanyId),
                        QueryMethods.column(CompanyEntity::getName).as("companyName"),
                        QueryMethods.column(FileEntity::getName),
                        QueryMethods.column(FileEntity::getUrl),
                        QueryMethods.column(FileEntity::getSize),
                        QueryMethods.column(FileEntity::getDescription),
                        QueryMethods.column(FileEntity::getStatus),
                        QueryMethods.column(FileEntity::getCreatedAt),
                        QueryMethods.column(FileEntity::getCreatedBy),
                        QueryMethods.column(FileEntity::getUpdatedAt),
                        QueryMethods.column(SysUserEntity::getNickname).as("uploaderName"));

        // 添加排序条件
        queryWrapper.orderBy(FileEntity::getId, false);

        // 判断是否需要导出
        if (query.getExport() != null && query.getExport() == 1) {
            exportFiles(queryWrapper, query);
            return new Page<>();
        }

        // 执行分页查询
        Page<FileListVO> page = mapper.paginateAs(query.getPageNum(), query.getPageSize(), queryWrapper,
                FileListVO.class);

        // 处理结果集
        page.getRecords().forEach(this::processFileInfo);

        return page;
    }

    /**
     * 导出文件列表
     */
    private void exportFiles(QueryWrapper query, FileListQuery queryParams) {
        ExcelUtil.of(this.mapper, query, FileExportVO.class, "files", "文件列表")
                .getData((mapper, wrapper) -> {
                    List<FileListVO> voList = mapper.selectListByQueryAs(wrapper, FileListVO.class);
                    if (!voList.isEmpty()) {
                        voList.forEach(this::processFileInfo);
                        return voList.stream().map(vo -> {
                            FileExportVO exportVO = new FileExportVO();
                            exportVO.setCompanyName(vo.getCompanyName());
                            exportVO.setFullName(vo.getFullName());
                            exportVO.setExtension(vo.getExtension());
                            exportVO.setSizeStr(vo.getSizeStr());
                            exportVO.setUploaderName(vo.getUploaderName());
                            exportVO.setCreatedAtStr(vo.getCreatedAt() != null ? 
                                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(vo.getCreatedAt()) : "");
                            exportVO.setStatus(vo.getIsUsed());
                            exportVO.setDescription(vo.getDescription());
                            return exportVO;
                        }).collect(Collectors.toList());
                    }
                    return new ArrayList<>();
                })
                .doExport();
    }

    @Override
    public FileStatisticsVO getFileStatistics(FileListQuery query) {
        QueryWrapper queryWrapper = buildQueryWrapper(query);
        
        // 使用GROUP BY一次性查询所有统计信息
        Row stats = mapper.selectOneByQueryAs(
                queryWrapper
                        .select(
                                QueryMethods.count().as("total_count"),
                                QueryMethods.sum(FileEntity::getSize).as("total_size"),
                                QueryMethods.sum(
                                        QueryMethods.case_()
                                                .when(QueryMethods.column(FileEntity::getStatus).eq(0))
                                                .then(1).else_(0).end()
                                ).as("unused_count"),
                                QueryMethods.sum(
                                        QueryMethods.case_()
                                                .when(QueryMethods.column(FileEntity::getStatus).eq(0))
                                                .then(QueryMethods.column(FileEntity::getSize)).else_(0).end()
                                ).as("unused_size")
                        ),
                Row.class
        );

        // 构建统计结果
        FileStatisticsVO statistics = new FileStatisticsVO();
        statistics.setTotalCount(stats.getLong("total_count", 0L));
        statistics.setTotalSize(stats.getLong("total_size", 0L));
        statistics.setTotalSizeStr(FileUtil.readableFileSize(statistics.getTotalSize()));
        statistics.setUnusedCount(stats.getLong("unused_count", 0L));
        statistics.setUnusedSize(stats.getLong("unused_size", 0L));
        statistics.setUnusedSizeStr(FileUtil.readableFileSize(statistics.getUnusedSize()));

        return statistics;
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper buildQueryWrapper(FileListQuery query) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .from(FileEntity.class)
                // 文件名模糊查询
                .where(FileEntity::getName)
                .like(query.getName(), StringUtils.hasText(query.getName()))
                // 文件后缀查询（支持多选）
                .and(q -> {
                    if (StringUtils.hasText(query.getExtension())) {
                        String[] extensions = query.getExtension().split(",");
                        q.and(FileEntity::getExtension).in(Arrays.asList(extensions));
                    }
                })
                // 所属商家查询（支持多选）
                .and(q -> {
                    if (query.getCompanyId() != null) {
                        String[] companyIds = query.getCompanyId().toString().split(",");
                        q.and(FileEntity::getCompanyId).in(Arrays.asList(companyIds));
                    }
                })
                // 上传人员查询（支持多选）
                .and(q -> {
                    if (query.getCreatedBy() != null) {
                        String[] createdBys = query.getCreatedBy().toString().split(",");
                        q.and(FileEntity::getCreatedBy).in(Arrays.asList(createdBys));
                    }
                })
                // 文件大小范围查询
                .and(q -> {
                    if (query.getSize() != null) {
                        long minSize = 0;
                        long maxSize = Long.MAX_VALUE;
                        switch (query.getSize()) {
                            case 1 -> maxSize = 1024 * 1024; // 0-1M
                            case 2 -> {
                                minSize = 1024 * 1024;
                                maxSize = 10 * 1024 * 1024; // 1-10M
                            }
                            case 3 -> {
                                minSize = 10 * 1024 * 1024;
                                maxSize = 100 * 1024 * 1024; // 10-100M
                            }
                            case 4 -> minSize = 100 * 1024 * 1024; // 100M以上
                        }
                        q.and(FileEntity::getSize).ge(minSize)
                                .and(FileEntity::getSize).le(maxSize);
                    }
                })
                // 状态精确匹配
                .and(FileEntity::getStatus)
                .eq(query.getStatus(), query.getStatus() != null)
                // 按创建时间倒序排序
                .orderBy(FileEntity::getCreatedAt).desc();
        // 上传日期范围查询
        if(query.getCreatedAtRange() != null){
            queryWrapper.between(FileEntity::getCreatedAt, query.getCreatedAtRange()[0], query.getCreatedAtRange()[1]);
        }

        return queryWrapper;
    }

    @Override
    public List<Option<String>> getFileExtensions() {
        // 查询所有文件后缀
        List<FileExtensionVO> extensions = mapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(QueryMethods.distinct(FileEntity::getExtension))
                        .from(FileEntity.class)
                        .where(FileEntity::getExtension).isNotNull(),
                FileExtensionVO.class);

        // 转换为Option格式
        return extensions.stream()
                .map(vo -> new Option<>(vo.getExtension(), vo.getExtension()))
                .collect(Collectors.toList());
    }

    /**
     * 处理文件信息
     */
    private void processFileInfo(FileListVO file) {
        // 处理文件名称
        String fullName = file.getName();
        if (StrUtil.isNotBlank(fullName)) {
            file.setFullName(fullName);
            int lastDotIndex = fullName.lastIndexOf('.');
            if (lastDotIndex > 0) {
                file.setName(fullName.substring(0, lastDotIndex));
                file.setExtension(fullName.substring(lastDotIndex + 1).toLowerCase());
            }
        }

        // 处理文件大小
        if (file.getSize() != null) {
            file.setSizeStr(FileUtil.readableFileSize(file.getSize()));
        }

        // 处理是否使用
        file.setIsUsed(file.getStatus() != null && file.getStatus() == 1 ? "已使用" : "未使用");

        // 处理是否可预览
        file.setCanPreview(
                file.getExtension() != null && PREVIEWABLE_EXTENSIONS.contains(file.getExtension().toLowerCase()));
    }
}