package com.xc.boot.shared.file.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.xc.boot.common.constant.RedisConstants;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.shared.file.model.FileInfo;
import com.xc.boot.shared.file.service.FileService;
import com.xc.boot.shared.file.service.trait.PubUploadAliyun;
import com.xc.boot.system.mapper.FileMapper;
import com.xc.boot.system.model.entity.FileEntity;
import com.xc.boot.system.service.ConfigService;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 阿里云OSS配置属性
 */
@ConfigurationProperties(prefix = "oss.aliyun")
@lombok.Data
class AliyunOssProperties {
    /**
     * 服务Endpoint
     */
    private String endpoint;
    /**
     * 访问凭据
     */
    private String accessKeyId;
    /**
     * 凭据密钥
     */
    private String accessKeySecret;
    /**
     * 存储桶名称
     */
    private String bucketName;
    /**
     * 外部访问地址
     */
    private String externalUrl;
}

/**
 * Aliyun 对象存储服务类
 *
 * <AUTHOR>
 * @since 2.3.0
 */
@Component
@ConditionalOnProperty(value = "oss.type", havingValue = "aliyun")
@EnableConfigurationProperties(AliyunOssProperties.class)
@RequiredArgsConstructor
public class AliyunFileService implements FileService {

    private final AliyunOssProperties aliyunOssProperties;
    private final FileMapper fileMapper;

    private OSS aliyunOssClient;

    private final ConfigService configService;

    /**
     * 支持的文件后缀
     */
    private final Set<String> suffixes = Set.of(
            "jpg", "jpeg", "png", "gif", "bmp", "webp", "xlsx", "xls", "apk", "zip", "ipa"
            // ...
    );

    private static final Long MAX_FILE_SIZE = 10 * 1024 * 1024L;

    @PostConstruct
    public void init() {
        aliyunOssClient = new OSSClientBuilder().build(aliyunOssProperties.getEndpoint(), aliyunOssProperties.getAccessKeyId(), aliyunOssProperties.getAccessKeySecret());
    }

//    @Override
//    @SneakyThrows
//    public FileInfo uploadFile(MultipartFile file, String description) {
//        checkFileSize(file);
//        String suffix = FileUtil.getSuffix(file.getOriginalFilename());
//        String fileName = getUploadFileName(file.getOriginalFilename());
//
//        // try-with-resource 语法糖自动释放流
//        try (InputStream inputStream = file.getInputStream()) {
//            // 设置上传文件的元信息，例如Content-Type
//            ObjectMetadata metadata = new ObjectMetadata();
//            metadata.setContentType(file.getContentType());
//            // 创建PutObjectRequest对象，指定Bucket名称、对象名称和输入流
//            PutObjectRequest putObjectRequest = new PutObjectRequest(aliyunOssProperties.getBucketName(), fileName, inputStream, metadata);
//            // 上传文件
//            aliyunOssClient.putObject(putObjectRequest);
//        } catch (Exception e) {
//            throw new RuntimeException("文件上传失败");
//        }
//        // 获取文件访问路径
//        // String fileUrl = "https://" + bucketName + "." + endpoint + "/" + fileName;
//        String fileUrl = aliyunOssProperties.getExternalUrl() + "/" + fileName;
//
//        // 记录文件信息
//        FileEntity fileEntity = new FileEntity();
//        fileEntity.setCompanyId(SecurityUtils.getCompanyId());
//        fileEntity.setName(file.getOriginalFilename());
//        fileEntity.setExtension(suffix);
//        fileEntity.setUrl(fileUrl);
//        fileEntity.setSize(file.getSize());
//        fileEntity.setExtension(suffix);
//        fileEntity.setDescription(StrUtil.isNotBlank(description) ? description : "");
//        fileEntity.setStatus(0);
//        fileEntity.setCreatedBy(SecurityUtils.getUserId());
//        fileMapper.insertSelective(fileEntity);
//
//        FileInfo fileInfo = new FileInfo();
//        fileInfo.setId(fileEntity.getId());
//        fileInfo.setName(fileName);
//        fileInfo.setUrl(fileUrl);
//
//        return fileInfo;
//    }

    @Override
    public FileEntity uploadFileByStream(InputStream inputStream, String originName, String contentType, Long size, String description) {
        String suffix = FileUtil.getSuffix(originName);
        if (!suffixes.contains(suffix)) {
            throw new BusinessException("文件后缀不支持");
        }
//        String fileName = "gold_upload/" + DateUtil.format(LocalDateTime.now(), "yyyyMMdd") + "/" + originName;

        Long companyId = SecurityUtils.getCompanyId();
        // 请求公共上传接口
        String system = "gold";
        String merchantId = String.valueOf(companyId);
        String fileName = String.format(
                "%s/%s/%s/%s/%s",
                system, merchantId, DateUtil.format(LocalDateTime.now(), "yyyyMMdd"), suffix, originName);

        try {
            // 设置上传文件的元信息，例如Content-Type
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(contentType);
            // 创建PutObjectRequest对象，指定Bucket名称、对象名称和输入流
            PutObjectRequest putObjectRequest = new PutObjectRequest(aliyunOssProperties.getBucketName(), fileName, inputStream, metadata);
            // 上传文件
            aliyunOssClient.putObject(putObjectRequest);
        } catch (Exception e) {
            throw new RuntimeException("文件上传失败");
        }
        // 获取文件访问路径
        String fileUrl = aliyunOssProperties.getExternalUrl() + "/" + fileName;

        // 记录文件信息
        FileEntity fileEntity = new FileEntity();
        fileEntity.setCompanyId(companyId);
        fileEntity.setName(originName);
        fileEntity.setExtension(suffix);
        fileEntity.setUrl(fileUrl);
        fileEntity.setSize(size);
        fileEntity.setDescription(StrUtil.isNotBlank(description) ? description : "");
        fileEntity.setStatus(0);
        fileEntity.setCreatedBy(SecurityUtils.getUserId());
        fileMapper.insertSelective(fileEntity);
        return fileEntity;
    }

//    private String getUploadFileName(String originName) {
//        // 生成文件名(日期文件夹)
//        String suffix = FileUtil.getSuffix(originName);
//        // 获取文件名
//        String prefix = FileUtil.getPrefix(originName);
//        if (!suffixes.contains(suffix)) {
//            throw new BusinessException("文件后缀不支持");
//        }
//        // 文件名 + 日期yyyyMMdd + 6位随机数 + 后缀
//        String dateStr = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
//        // 6位随机数
//        String randomStr = String.format("%06d", new Random().nextInt(900000) + 100000);
//        String name = prefix + "_" + dateStr + randomStr + "." + suffix;
//        return  "gold_upload/" + DateUtil.format(LocalDateTime.now(), "yyyyMMdd") + "/" + name;
//    }
//
//    @Override
//    public boolean deleteFile(String filePath) {
//        Assert.notBlank(filePath, "删除文件路径不能为空");
//        String fileHost = "https://" + aliyunOssProperties.getBucketName() + "." + aliyunOssProperties.getEndpoint(); // 文件主机域名
//        String fileName = filePath.substring(fileHost.length() + 1); // +1 是/占一个字符，截断左闭右开
//        aliyunOssClient.deleteObject(aliyunOssProperties.getBucketName(), fileName);
//        return true;
//    }

    private void checkFileSize(MultipartFile file) {
        Assert.notNull(file, "文件不能为空");
        String contentType = file.getContentType();
        if (StringUtils.isBlank(contentType)) {
            throw new BusinessException("文件类型不能为空");
        }
        Object systemConfig = configService.getSystemConfig(RedisConstants.FILE_SIZE_LIMIT);
        if (systemConfig != null) {
            String fileSizeConfig = (String) systemConfig;
            List<JSONObject> list = JSONUtil.toList(fileSizeConfig, JSONObject.class);
            for (JSONObject object : list) {
                if (contentType.startsWith(object.getStr("type"))) {
                    Assert.isTrue(file.getSize() <= object.getLong("value"),
                            "文件大小不能超过" + object.getBigDecimal("value").divide(new BigDecimal(1024 * 1024), 2, RoundingMode.HALF_UP) + "M");
                    return;
                }
            }
        }
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BusinessException("文件大小不能超过10M");
        }
    }

    @Override
    @SneakyThrows
    public FileInfo uploadFile(MultipartFile file, String description) {
        checkFileSize(file);
        // 生成文件名(日期文件夹)
        String fileType = FileUtil.getSuffix(file.getOriginalFilename());
        // 获取文件名
        String originalFilename = FileUtil.getName(file.getOriginalFilename());

        if (!suffixes.contains(fileType)) {
            throw new BusinessException("文件后缀不支持");
        }

        Long companyId = SecurityUtils.getCompanyId();
        Long userId1 = SecurityUtils.getUserId();
        // 请求公共上传接口
        String system = "gold";
        String merchantId = String.valueOf(companyId);
        String groupId = "";
        String userId = String.valueOf(userId1);
        Map<String, String> result = PubUploadAliyun.publicUploadApi(
                system,
                merchantId,
                groupId,
                userId,
                fileType,
                file,
                "0", "0"
        );

        // 获取文件访问路径
        String fileName = result.get("object_path");
        String fileUrl = result.get("url") + "/" + fileName;

        // 记录文件信息
        FileEntity fileEntity = new FileEntity();
        fileEntity.setCompanyId(companyId);
        fileEntity.setName(originalFilename);
        fileEntity.setExtension(fileType);
        fileEntity.setUrl(fileUrl);
        fileEntity.setSize(file.getSize());
        fileEntity.setExtension(fileType);
        fileEntity.setDescription(StrUtil.isNotBlank(description) ? description : "");
        fileEntity.setStatus(0);
        fileEntity.setCreatedBy(userId1);
        fileMapper.insertSelective(fileEntity);

        FileInfo fileInfo = new FileInfo();
        fileInfo.setId(fileEntity.getId());
        fileInfo.setName(fileName);
        fileInfo.setUrl(fileUrl);

        return fileInfo;
    }

    @Override
    public boolean deleteFile(String filePath) {
        Assert.notBlank(filePath, "删除文件路径不能为空");
        // 文件主机域名
        String fileHost = "https://" + aliyunOssProperties.getBucketName() + "." + aliyunOssProperties.getEndpoint();

        // 1. 验证URL格式
        if (!filePath.startsWith(fileHost + "/")) {
            throw new IllegalArgumentException("非法文件路径格式");
        }
        // +1 是/占一个字符，截断左闭右开
        String fileName = filePath.substring(fileHost.length() + 1);
        return PubUploadAliyun.publicDeleteApi(fileName);
    }
}
