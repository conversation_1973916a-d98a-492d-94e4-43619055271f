package com.xc.boot.shared.file.model;

import com.xc.boot.common.base.BasePageQuery;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件列表查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件列表查询参数")
public class FileListQuery extends BasePageQuery {

    @Schema(description = "文件名称")
    private String name;

    @Schema(description = "文件后缀(多个用逗号分隔)")
    private String extension;

    @Schema(description = "所属商家(多个用逗号分隔)")
    private Long companyId;

    @Schema(description = "上传人员(多个用逗号分隔)")
    private Long createdBy;

    @Schema(description = "上传日期范围[开始日期,结束日期]")
    private String[] createdAtRange;

    @Schema(description = "文件大小(1:0-1M|2:1-10M|3:10-100M|4:100M以上)")
    private Integer size;

    @Schema(description = "状态(1:已使用 | 0:未使用)")
    private Integer status;
} 