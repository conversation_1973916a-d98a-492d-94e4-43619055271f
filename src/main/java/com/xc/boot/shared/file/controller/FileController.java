package com.xc.boot.shared.file.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.model.Option;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.shared.file.model.FileInfo;
import com.xc.boot.shared.file.model.FileListQuery;
import com.xc.boot.shared.file.model.FileListVO;
import com.xc.boot.shared.file.model.FileStatisticsVO;
import com.xc.boot.shared.file.service.FileListService;
import com.xc.boot.shared.file.service.FileService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件控制层
 *
 * <AUTHOR>
 * @since 2022/10/16
 */
@Tag(name = "系统-文件接口")
@RestController
@RequestMapping("/api/files")
@RequiredArgsConstructor
public class FileController {

    private final FileService fileService;
    private final FileListService fileListService;

    @PostMapping
    @Operation(summary = "文件上传")
    public Result<FileInfo> uploadFile(
            @Parameter(
                    name = "file",
                    description = "表单文件对象",
                    required = true,
                    in = ParameterIn.DEFAULT,
                    schema = @Schema(name = "file", format = "binary")
            )
            @RequestPart(value = "file") MultipartFile file,
            @Parameter( name = "description", description = "文件描述")
            @RequestParam(value = "description", required = false) String description
    ) {
        FileInfo fileInfo = fileService.uploadFile(file, description);
        return Result.success(fileInfo);
    }

    @PostMapping("/list")
    @Operation(summary = "文件列表")
    public PageResult<FileListVO> listFiles(@RequestBody FileListQuery query) {
        Page<FileListVO> page = fileListService.listFiles(query);
        return PageResult.success(page);
    }

    @GetMapping("/extensions")
    @Operation(summary = "获取文件后缀列表")
    public Result<List<Option<String>>> getFileExtensions() {
        List<Option<String>> extensions = fileListService.getFileExtensions();
        return Result.success(extensions);
    }

    @PostMapping("/statistics")
    @Operation(summary = "获取文件统计信息")
    public Result<FileStatisticsVO> getFileStatistics(@RequestBody FileListQuery query) {
        FileStatisticsVO statistics = fileListService.getFileStatistics(query);
        return Result.success(statistics);
    }

    // @DeleteMapping
    // @Operation(summary = "文件删除")
    // @SneakyThrows
    public Result<?> deleteFile(
            @Parameter(description = "文件路径") @RequestParam String filePath
    ) {
        boolean result = fileService.deleteFile(filePath);
        return Result.judge(result);
    }
}
