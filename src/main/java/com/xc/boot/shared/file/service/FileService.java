package com.xc.boot.shared.file.service;

import com.xc.boot.shared.file.model.FileInfo;
import com.xc.boot.system.model.entity.FileEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * 对象存储服务接口层
 *
 * <AUTHOR>
 * @since 2022/11/19
 */
public interface FileService {

    /**
     * 上传文件
     * @param file 表单文件对象
     * @return 文件信息
     */
    FileInfo uploadFile(MultipartFile file, String description);

    /**
     * 删除文件
     *
     * @param filePath 文件完整URL
     * @return 删除结果
     */
    boolean deleteFile(String filePath);

    /**
     * 系统内调用，上传文件
     */
    FileEntity uploadFileByStream(InputStream inputStream, String originName, String contentType, Long size, String description);
}
