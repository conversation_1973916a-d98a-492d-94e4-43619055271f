package com.xc.boot.shared.file.service;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.model.Option;
import com.xc.boot.shared.file.model.FileListQuery;
import com.xc.boot.shared.file.model.FileListVO;
import com.xc.boot.shared.file.model.FileStatisticsVO;

import java.util.List;

/**
 * 文件列表查询服务接口
 */
public interface FileListService {
    
    /**
     * 分页查询文件列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    Page<FileListVO> listFiles(FileListQuery query);

    /**
     * 获取所有文件后缀
     *
     * @return 文件后缀选项列表
     */
    List<Option<String>> getFileExtensions();

    /**
     * 获取文件统计信息
     *
     * @param query 查询参数
     * @return 统计信息
     */
    FileStatisticsVO getFileStatistics(FileListQuery query);
} 