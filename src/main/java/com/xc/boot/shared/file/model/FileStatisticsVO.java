package com.xc.boot.shared.file.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文件统计信息
 */
@Data
@Schema(description = "文件统计信息")
public class FileStatisticsVO {

    @Schema(description = "文件总数")
    private Long totalCount;

    @Schema(description = "文件总大小(字节)")
    private Long totalSize;

    @Schema(description = "文件总大小(格式化)")
    private String totalSizeStr;

    @Schema(description = "未使用文件数")
    private Long unusedCount;

    @Schema(description = "未使用文件总大小(字节)")
    private Long unusedSize;

    @Schema(description = "未使用文件总大小(格式化)")
    private String unusedSizeStr;
} 