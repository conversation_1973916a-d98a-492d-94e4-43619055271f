package com.xc.boot.shared.file.service.trait;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xc.boot.common.enums.EnvEnum;
import jakarta.annotation.PostConstruct;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

/**
 * OSS上传工具类
 * 提供文件上传到OSS的功能，包含HMAC-SHA256签名验证
 *
 * <AUTHOR>
 */
@Component  // 添加这个注解
public class PubUploadAliyun {

    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Value("${spring.profiles.active:default}")
    private String env;

    private static String staticEnv;

    @PostConstruct
    public void init() {
        staticEnv = this.env;
    }

    /**
     * 判断是否为开发环境
     * 这里需要根据实际项目配置来判断环境
     *
     * @return boolean
     */
    private static boolean isDevEnvironment() {
        // 检查classpath中是否存在application-dev.yml文件
//        return PubUploadAliyun.class.getClassLoader().getResource("application-dev.yml") != null;
        return !staticEnv.equals(EnvEnum.PROD.getValue());
    }

    /**
     * 返回HMAC-SHA256加密的key
     *
     * @return String
     */
    public static String getSha256Key() {
        return "4f70a0973ac84ca6";
    }

    /**
     * 返回公共上传接口地址
     *
     * @return String
     */
    public static String getPublicUploadUrl() {
        if (isDevEnvironment()) {
//            return "http://127.0.0.1:8088/oss/upload";
            return "https://uploadoss-dev.jaadee.net/oss/upload";
        }
        return "https://uploadoss.jaadee.net/oss/upload";
    }

    /**
     * 把map的key使用字典排序,然后拼接成字符串
     * 拼接方法: key:value\n
     * 去除字符串最后的\n
     *
     * @param map 参数映射
     * @return String
     */
    public static String toSortedString(Map<String, String> map) {
        // 验证输入是否为空或无效
        if (map == null || map.isEmpty()) {
            return "";
        }

        // 使用TreeMap自动按键名排序（升序）
        TreeMap<String, String> sortedMap = new TreeMap<>(map);

        StringBuilder result = new StringBuilder();
        String[] keys = sortedMap.keySet().toArray(new String[0]);

        // 遍历排序后的映射
        for (int i = 0; i < keys.length; i++) {
            String key = keys[i];
            String value = sortedMap.get(key);

            result.append(key).append(":").append(value);

            // 不在最后一行添加换行符
            if (i < keys.length - 1) {
                result.append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 生成HMAC-SHA256签名
     *
     * @param data 要签名的数据
     * @param key  签名密钥
     * @return 签名字符串
     */
    public static String generateHmac(String data, String key) {
        try {
            Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), HMAC_SHA256_ALGORITHM);
            mac.init(secretKeySpec);
            byte[] hmacBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));

            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hmacBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("生成HMAC签名失败", e);
        }
    }

    /**
     * 调用公共上传接口,上传到OSS
     *
     * @param system     系统标识
     * @param merchantId 商户ID
     * @param groupId    组ID
     * @param userId     用户ID
     * @param fileType   文件类型
     * @param file       上传的文件
     * @param record     是否记录上传日志到数据库(0:否, 1:是)
     * @param keepName   是否使用原文件名(0:否, 1:是)
     * @return 上传结果
     */
    public static Map<String, String> publicUploadApi(String system, String merchantId, String groupId,
                                                      String userId, String fileType, MultipartFile file,
                                                      String record, String keepName) {
        try {
            // 1. 生成签名
            Map<String, String> signParams = new HashMap<>();
            signParams.put("system", system);
            signParams.put("merchant_id", merchantId);
            signParams.put("user_id", userId);
            signParams.put("file_type", fileType);

            if (groupId != null && !groupId.trim().isEmpty()) {
                signParams.put("group_id", groupId);
            }

            String data = toSortedString(signParams);
            String signature = generateHmac(data, getSha256Key());

            // 2. 构建HTTP请求
            int timeout = 20;
            OkHttpClient client = new OkHttpClient.Builder()
                    // 连接超时
                    .connectTimeout(timeout, TimeUnit.SECONDS)
                    // 写入超时（上传时间）
                    .writeTimeout(timeout, TimeUnit.SECONDS)
                    // 读取超时（响应时间）
                    .readTimeout(timeout, TimeUnit.SECONDS)
                    .build();

            MultipartBody.Builder multipartBuilder = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file", file.getOriginalFilename(),
                            RequestBody.create(file.getBytes(), MediaType.parse("application/octet-stream")))
                    .addFormDataPart("system", system)
                    .addFormDataPart("merchant_id", merchantId)
                    .addFormDataPart("user_id", userId)
                    .addFormDataPart("file_type", fileType);

            if (groupId != null && !groupId.trim().isEmpty()) {
                multipartBuilder.addFormDataPart("group_id", groupId);
            }

            RequestBody requestBody = multipartBuilder.build();

            Request request = new Request.Builder()
                    .url(getPublicUploadUrl())
                    .post(requestBody)
                    .addHeader("Authorization", "HMAC-SHA256 " + signature)
                    .addHeader("Record", record)
                    .addHeader("X-Keep-Filename", keepName)
                    .build();

            // 3. 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (response.code() != 200) {
                    if (response.body() != null) {
                        throw new RuntimeException("上传失败:" + response.body().string());
                    }
                    throw new RuntimeException("上传失败: response.body()=null");
                }

                String responseBody = null;
                if (response.body() != null) {
                    responseBody = response.body().string();
                }
                JsonNode jsonNode = OBJECT_MAPPER.readTree(responseBody);

                if (!jsonNode.has("code") || jsonNode.get("code").asInt() != 0) {
                    String msg = jsonNode.has("msg") ? jsonNode.get("msg").asText() : "未知错误";
                    throw new RuntimeException(msg);
                }

                // 4. 返回包含url和object_path的Map对象
                JsonNode dataNode = jsonNode.get("data");
                Map<String, String> result = new HashMap<>();
                result.put("url", dataNode.get("url").asText());
                result.put("object_path", dataNode.get("object_path").asText());

                return result;
            }

        } catch (IOException e) {
            throw new RuntimeException("上传异常: " + e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException("系统异常: " + e.getMessage());
        }
    }

    /**
     * 调用公共删除接口,删除OSS文件
     *
     * @param fileName oss的文件路径
     * @return 上传结果
     */
    public static boolean publicDeleteApi(String fileName) {
        try {
            // 1. 验证文件路径是否以"gold"开头
            if (fileName == null || !fileName.startsWith("gold")) {
                throw new RuntimeException("文件路径异常：必须以gold开头");
            }

            // 2. 准备签名参数（按/分割并取前4部分）
            Map<String, String> signParams = new HashMap<>();
            String[] parts = fileName.split("/");
            if (parts.length < 4) {
                throw new RuntimeException("文件路径异常：必须包含至少4个部分");
            }

            String[] partsKey = {"system", "merchant_id", "user_id", "file_type"};
            // 添加分割后的前4个部分（如果存在）
            for (int i = 0; i < parts.length; i++) {
                signParams.put(partsKey[i], parts[i]);
            }

            String data = toSortedString(signParams);
            String signature = generateHmac(data, getSha256Key());

            // 3. 构建JSON请求体
            Map<String, String> requestBodyMap = new HashMap<>();
            requestBodyMap.put("object_id", fileName);

            String jsonBody = OBJECT_MAPPER.writeValueAsString(requestBodyMap);
            RequestBody body = RequestBody.create(
                    jsonBody,
                    MediaType.parse("application/json; charset=utf-8")
            );

            // 2. 构建HTTP请求
            OkHttpClient client = new OkHttpClient.Builder().build();

            Request request = new Request.Builder()
                    .url(getPublicUploadUrl())
                    .delete(body)
                    .addHeader("Authorization", "HMAC-SHA256 " + signature)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 5. 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (response.code() != 200) {
                    if (response.body() != null) {
                        throw new RuntimeException("删除失败: " + response.body().string());
                    }
                    throw new RuntimeException("上传失败: response.body()=null");
                }

                String responseBody = null;
                if (response.body() != null) {
                    responseBody = response.body().string();
                }
                JsonNode jsonNode = OBJECT_MAPPER.readTree(responseBody);

                if (!jsonNode.has("code") || jsonNode.get("code").asInt() != 0) {
                    String msg = jsonNode.has("msg") ? jsonNode.get("msg").asText() : "未知错误";
                    throw new RuntimeException(msg);
                }

                // 6. 返回删除结果
                return true;
            }

        } catch (IOException e) {
            throw new RuntimeException("删除异常: " + e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException("系统异常: " + e.getMessage());
        }
    }
}