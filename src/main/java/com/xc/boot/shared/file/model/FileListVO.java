package com.xc.boot.shared.file.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import cn.hutool.core.date.DateUtil;

/**
 * 文件列表展示对象
 */
@Data
@Schema(description = "文件列表展示对象")
public class FileListVO {

    @Schema(description = "文件ID")
    private Long id;

    @Schema(description = "所属商家ID")
    private Long companyId;

    @Schema(description = "所属商家")
    private String companyName;

    @Schema(description = "文件名称(不包含后缀)")
    private String name;

    @Schema(description = "文件名称(含后缀)")
    private String fullName;

    @Schema(description = "文件后缀")
    private String extension;

    @Schema(description = "文件URL")
    private String url;

    @Schema(description = "文件大小(字节)")
    private Long size;

    @Schema(description = "文件大小(格式化)")
    private String sizeStr;

    @Schema(description = "上传人员")
    private String uploaderName;

    @Schema(description = "上传人员ID")
    private Long createdBy;
    
    @Schema(description = "使用描述")
    private String description;

    @Schema(description = "状态(1:已使用 | 0:未使用)")
    private Integer status;

    @Schema(description = "是否使用")
    private String isUsed;

    @Schema(description = "是否可预览")
    private Boolean canPreview;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "创建时间(格式化)")
    private String createdAtStr;

    public String getCreatedAtStr() {
        return DateUtil.format(createdAt, "yyyy-MM-dd HH:mm:ss");
    }

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 