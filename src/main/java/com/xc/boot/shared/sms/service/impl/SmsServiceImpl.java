package com.xc.boot.shared.sms.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.config.property.SmsProperties;
import com.xc.boot.shared.sms.service.SmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @ClassName SmsServiceImpl
 * @Date: 2025/6/4 17:58
 * @Description: 描述
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class SmsServiceImpl implements SmsService {

    private final SmsProperties smsProperties;

    @Override
    public boolean sendSms(String mobile, String code) {
        // send code
        HashMap<String, Object> map = new HashMap<>();
        map.put("account", smsProperties.getAppKey());
        map.put("password", smsProperties.getAppSecret());
        map.put("msg", smsProperties.getTemplate());
        map.put("params", mobile + "," + code);
        String req = JSONUtil.toJsonStr(map);
        log.info("send sms code:{}", req);
        String rs = HttpUtil.post(smsProperties.getUrl(), req);
        JSONObject rsJson = JSONUtil.parseObj(rs);
        if (!rsJson.getStr("code").equals("0")) {
            throw new BusinessException(rsJson.getStr("errorMsg"));
        }
        return true;
    }
}
