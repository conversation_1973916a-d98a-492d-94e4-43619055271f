package com.xc.boot.shared.common.service.impl;

import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.model.Option;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.member.mapper.MemberMapper;
import com.xc.boot.modules.member.model.entity.MemberEntity;
import com.xc.boot.modules.merchant.mapper.*;
import com.xc.boot.modules.merchant.model.entity.*;
import com.xc.boot.modules.merchant.model.enums.CounterTypeEnum;
import com.xc.boot.shared.common.model.bo.OptionQueryBO;
import com.xc.boot.shared.common.model.query.OptionQuery;
import com.xc.boot.shared.common.service.OptionService;
import com.xc.boot.system.mapper.*;
import com.xc.boot.system.model.entity.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class OptionServiceImpl extends ServiceImpl<UserMapper, SysUserEntity> implements OptionService {

    /**
     * 统一数量限制
     */
    private static final int MAX_LIMIT = 200;

    private final UserMapper userMapper;
    private final CompanyMapper companyMapper;
    private final MerchantMapper merchantMapper;
    private final RoleMapper roleMapper;
    private final SubclassMapper subclassMapper;
    private final CounterMapper counterMapper;
    private final QualityMapper qualityMapper;
    private final StyleMapper styleMapper;
    private final BrandMapper brandMapper;
    private final JewelryMapper jewelryMapper;
    private final TechnologyMapper technologyMapper;
    private final SupplierMapper supplierMapper;
    private final GoodsIncomeTemplateMapper goodsIncomeTemplateMapper;
    private final HeadingSignsMapper headingSignsMapper;
    private final MemberMapper memberMapper;

    /**
     * 通用的选项查询方法
     *
     * @param bo 查询业务对象
     * @param <T> 实体类型
     * @return 选项列表
     */
    private <T> List<Option<Long>> getOptions(OptionQueryBO<T> bo) {
        // 构建基础查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(QueryMethods.column(bo.getIdGetter()), QueryMethods.column(bo.getNameGetter()))
                .from(bo.getEntityClass());
        if (bo.getTagGetter() != null) {
            queryWrapper.select(QueryMethods.column(bo.getTagGetter()));
        }
        if (bo.getParentIdGetter() != null) {
            queryWrapper.select(QueryMethods.column(bo.getParentIdGetter()));
        }
        // 应用额外的查询条件
        queryWrapper = bo.getAdditionalConditions().apply(queryWrapper, bo.getQuery());

        // 限制返回数量
        if (bo.getLimit() != null) {
            queryWrapper.limit(bo.getLimit());
        }else {
            queryWrapper.limit(MAX_LIMIT);
        }

        // 执行查询
        List<T> entities = bo.getMapper().selectListByQuery(queryWrapper);

        // 转换为Option对象
        List<Option<Long>> options = entities.stream()
                .map(entity -> {
                    Option<Long> option = new Option<>((Long) bo.getIdGetter().get(entity), (String) bo.getNameGetter().get(entity));
                    if (bo.getTagGetter() != null) {
                        Object tag = bo.getTagGetter().get(entity);
                        if (tag != null) {
                            option.setTag(tag.toString());
                        }
                    }
                    if (bo.getParentIdGetter() != null) {
                        Object parentId = bo.getParentIdGetter().get(entity);
                        if (parentId != null) {
                            option.setParentId(parentId.toString());
                        }
                    }
                    return option;
                })
                .collect(Collectors.toList());

        // 处理echo参数
        if (StringUtils.hasText(bo.getQuery().getEcho())) {
            List<Long> echoIds = Arrays.stream(bo.getQuery().getEcho().split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            // 获取echo中不在当前结果中的ID
            List<Long> missingIds = echoIds.stream()
                    .filter(id -> options.stream().noneMatch(opt -> opt.getValue().equals(id)))
                    .collect(Collectors.toList());

            if (!missingIds.isEmpty()) {
                // 查询缺失的ID对应的实体
                List<T> missingEntities = bo.getMapper().selectListByQuery(
                        QueryWrapper.create()
                                .select(QueryMethods.column(bo.getIdGetter()), QueryMethods.column(bo.getNameGetter()))
                                .from(bo.getEntityClass())
                                .where(bo.getIdGetter()).in(missingIds));

                // 将缺失的实体添加到结果中
                options.addAll(
                        missingEntities.stream()
                                .map(entity -> {
                                    Option<Long> option = new Option<>((Long) bo.getIdGetter().get(entity), (String) bo.getNameGetter().get(entity));
                                    if (bo.getTagGetter() != null) {
                                        Object tag = bo.getTagGetter().get(entity);
                                        if (tag != null) {
                                            option.setTag(tag.toString());
                                        }
                                    }
                                    if (bo.getParentIdGetter() != null) {
                                        Object parentId = bo.getParentIdGetter().get(entity);
                                        if (parentId != null) {
                                            option.setParentId(parentId.toString());
                                        }
                                    }
                                    return option;
                                })
                                .collect(Collectors.toList()));
            }
        }

        return options;
    }

    @Override
    public List<Option<Long>> users(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }

        return getOptions(OptionQueryBO.<SysUserEntity>builder()
                .query(query)
                .mapper(userMapper)
                .entityClass(SysUserEntity.class)
                .idGetter(SysUserEntity::getId)
                .nameGetter(SysUserEntity::getNickname)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(SysUserEntity::getCompanyId).eq(companyId, companyId != 1)
                            .and(SysUserEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0)
                            .and(SysUserEntity::getDeletedAt).isNull(q.getShowDeleted() == null || q.getShowDeleted() == 0);
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(SysUserEntity::getNickname).like(q.getKeyword());
                    }
                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> company(OptionQuery query) {
        return getOptions(OptionQueryBO.<CompanyEntity>builder()
                .query(query)
                .mapper(companyMapper)
                .entityClass(CompanyEntity.class)
                .idGetter(CompanyEntity::getId)
                .nameGetter(CompanyEntity::getName)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(CompanyEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0);
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(CompanyEntity::getName).like(q.getKeyword());
                    }
                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> merchant(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }

        return getOptions(OptionQueryBO.<MerchantEntity>builder()
                .query(query)
                .mapper(merchantMapper)
                .entityClass(MerchantEntity.class)
                .idGetter(MerchantEntity::getId)
                .nameGetter(MerchantEntity::getName)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(MerchantEntity::getCompanyId).eq(companyId)
                            .and(MerchantEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0);

                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(MerchantEntity::getName).like(q.getKeyword());
                    }

                    // 获取当前用户的门店权限
                    Set<Long> merchantIds = SecurityUtils.getMerchantIds();
                    if (!SecurityUtils.isMain() && merchantIds != null && !merchantIds.isEmpty()) {
                        wrapper.and(MerchantEntity::getId).in(merchantIds);
                    }
                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> role(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }
        return getOptions(OptionQueryBO.<SysRoleEntity>builder()
                .query(query)
                .mapper(roleMapper)
                .entityClass(SysRoleEntity.class)
                .idGetter(SysRoleEntity::getId)
                .nameGetter(SysRoleEntity::getName)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(SysRoleEntity::getCompanyId).eq(companyId)
                            .and(SysRoleEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0);
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(SysRoleEntity::getName).like(q.getKeyword());
                    }
                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> subclass(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }
        return getOptions(OptionQueryBO.<SubclassEntity>builder()
                .query(query)
                .mapper(subclassMapper)
                .entityClass(SubclassEntity.class)
                .idGetter(SubclassEntity::getId)
                .nameGetter(SubclassEntity::getName)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(SubclassEntity::getCompanyId).eq(companyId)
                            .and(SubclassEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0);
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(SubclassEntity::getName).like(q.getKeyword());
                    }
                    wrapper.orderBy(SubclassEntity::getSort, false);
                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> counter(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }

        // 是否主账号
        Boolean isMain = SecurityUtils.isMain();
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();

        return getOptions(OptionQueryBO.<CounterEntity>builder()
                .query(query)
                .mapper(counterMapper)
                .entityClass(CounterEntity.class)
                .idGetter(CounterEntity::getId)
                .nameGetter(CounterEntity::getName)
                .parentIdGetter(CounterEntity::getMerchantId)
                .additionalConditions((wrapper, q) -> {
                    // companyId和状态过滤
                    wrapper.where(CounterEntity::getCompanyId).eq(companyId)
                           .and(CounterEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0);

                    // 门店ID过滤（支持多选）
                    String merchantIdsStr = query.getMerchantIds();
                    if (StringUtils.hasText(merchantIdsStr)) {
                        List<Long> merchantIdList = Arrays.stream(merchantIdsStr.split(","))
                                .filter(StringUtils::hasText)
                                .map(Long::valueOf)
                                .collect(Collectors.toList());
                        if (!merchantIdList.isEmpty()) {
                            wrapper.and(CounterEntity::getMerchantId).in(merchantIdList);
                        }
                    }
                    // 过滤类型
                    if (StringUtils.hasText(query.getType())) {
                        wrapper.and(CounterEntity::getType).in(List.of(query.getType().split(",")));
                    }
                    // 关键字模糊查询
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(CounterEntity::getName).like(q.getKeyword());
                    }

                    // 非主账号时，限制可用门店范围
                    if (!isMain && merchantIds != null && !merchantIds.isEmpty()) {
                        wrapper.and(CounterEntity::getMerchantId).in(merchantIds);
                    }
                    wrapper.orderBy(CounterEntity::getSort, false);

                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> salesCounter(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }

        // 是否主账号
        Boolean isMain = SecurityUtils.isMain();
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();

        return getOptions(OptionQueryBO.<CounterEntity>builder()
                .query(query)
                .mapper(counterMapper)
                .entityClass(CounterEntity.class)
                .idGetter(CounterEntity::getId)
                .nameGetter(CounterEntity::getName)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(CounterEntity::getCompanyId).eq(companyId)
                            .and(CounterEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0)
                            .and(CounterEntity::getType).in(CounterTypeEnum.SALE.getValue(), CounterTypeEnum.WAREHOUSE.getValue()); // 销售类型的柜台
                    
                    // 门店ID过滤（支持多选）
                    String merchantIdsStr = query.getMerchantIds();
                    if (StringUtils.hasText(merchantIdsStr)) {
                        List<Long> merchantIdList = Arrays.stream(merchantIdsStr.split(","))
                                .filter(StringUtils::hasText)
                                .map(Long::valueOf)
                                .collect(Collectors.toList());
                        if (!merchantIdList.isEmpty()) {
                            wrapper.and(CounterEntity::getMerchantId).in(merchantIdList);
                        }
                    }

                    // 关键字模糊查询
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(CounterEntity::getName).like(q.getKeyword());
                    }

                    // 非主账号时，限制可用门店范围
                    if (!isMain && merchantIds != null && !merchantIds.isEmpty()) {
                        wrapper.and(CounterEntity::getMerchantId).in(merchantIds);
                    }
                    wrapper.orderBy(CounterEntity::getSort, false);
                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> quality(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }
        return getOptions(OptionQueryBO.<QualityEntity>builder()
                .query(query)
                .mapper(qualityMapper)
                .entityClass(QualityEntity.class)
                .idGetter(QualityEntity::getId)
                .nameGetter(QualityEntity::getName)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(QualityEntity::getCompanyId).eq(companyId)
                            .and(QualityEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0);
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(q.getCategoryIds())) {
                        wrapper.where(QualityEntity::getCategoryId).in(List.of(q.getCategoryIds().split(",")));
                    }
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(QualityEntity::getName).like(q.getKeyword());
                    }
                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> style(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }
        return getOptions(OptionQueryBO.<StyleEntity>builder()
                .query(query)
                .mapper(styleMapper)
                .entityClass(StyleEntity.class)
                .idGetter(StyleEntity::getId)
                .nameGetter(StyleEntity::getName)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(StyleEntity::getCompanyId).eq(companyId)
                            .and(StyleEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0);
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(StyleEntity::getName).like(q.getKeyword());
                    }
                    wrapper.orderBy(StyleEntity::getSort, false);
                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> brand(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }
        return getOptions(OptionQueryBO.<BrandEntity>builder()
                .query(query)
                .mapper(brandMapper)
                .entityClass(BrandEntity.class)
                .idGetter(BrandEntity::getId)
                .nameGetter(BrandEntity::getName)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(BrandEntity::getCompanyId).eq(companyId)
                            .and(BrandEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0);
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(BrandEntity::getName).like(q.getKeyword());
                    }
                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> jewelry(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }
        return getOptions(OptionQueryBO.<JewelryEntity>builder()
                .query(query)
                .mapper(jewelryMapper)
                .entityClass(JewelryEntity.class)
                .idGetter(JewelryEntity::getId)
                .nameGetter(JewelryEntity::getName)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(JewelryEntity::getCompanyId).eq(companyId)
                            .and(JewelryEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0);
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(JewelryEntity::getName).like(q.getKeyword());
                    }
                    wrapper.orderBy(JewelryEntity::getSort, false);
                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> technology(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }
        return getOptions(OptionQueryBO.<TechnologyEntity>builder()
                .query(query)
                .mapper(technologyMapper)
                .entityClass(TechnologyEntity.class)
                .idGetter(TechnologyEntity::getId)
                .nameGetter(TechnologyEntity::getName)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(TechnologyEntity::getCompanyId).eq(companyId)
                            .and(TechnologyEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0);
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(TechnologyEntity::getName).like(q.getKeyword());
                    }
                    wrapper.orderBy(TechnologyEntity::getSort, false);
                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> supplier(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }
        return getOptions(OptionQueryBO.<SupplierEntity>builder()
                .query(query)
                .mapper(supplierMapper)
                .entityClass(SupplierEntity.class)
                .idGetter(SupplierEntity::getId)
                .nameGetter(SupplierEntity::getName)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(SupplierEntity::getCompanyId).eq(companyId)
                            .and(SupplierEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0);
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(SupplierEntity::getName).like(q.getKeyword());
                    }
                    return wrapper;
                })
                .build());
    }

    @Override
    public List<Option<Long>> goodsIncomeTemplate(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }
        return getOptions(OptionQueryBO.<GoodsIncomeTemplateEntity>builder()
                .query(query)
                .mapper(goodsIncomeTemplateMapper)
                .entityClass(GoodsIncomeTemplateEntity.class)
                .idGetter(GoodsIncomeTemplateEntity::getId)
                .nameGetter(GoodsIncomeTemplateEntity::getName)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(GoodsIncomeTemplateEntity::getCompanyId).eq(companyId)
                            .and(GoodsIncomeTemplateEntity::getStatus).eq(1, q.getShowDisabled() == null || q.getShowDisabled() == 0);
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(GoodsIncomeTemplateEntity::getName).like(q.getKeyword());
                    }
                    return wrapper.orderBy(GoodsIncomeTemplateEntity::getDefaultFlag).desc();
                })
                .build());
    }

    @Override
    public List<Option<String>> sign(OptionQuery query) {
        List<HeadingSignsEntity> signsEntities = headingSignsMapper.selectListByQuery(QueryWrapper.create()
                .where(HeadingSignsEntity::getTitle).like(query.getKeyword(), org.apache.commons.lang3.StringUtils.isNotBlank(query.getKeyword())));
        return signsEntities.stream().map(signsEntity -> new Option<>(signsEntity.getSign(), signsEntity.getTitle())).toList();
    }

    @Override
    public List<Option<Long>> member(OptionQuery query) {
        // 获取当前登录用户的companyId
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return new ArrayList<>();
        }
        List<Option<Long>> options = getOptions(OptionQueryBO.<MemberEntity>builder()
                .query(query)
                .mapper(memberMapper)
                .entityClass(MemberEntity.class)
                .idGetter(MemberEntity::getId)
                .nameGetter(MemberEntity::getName)
                .tagGetter(MemberEntity::getMobile)
                .additionalConditions((wrapper, q) -> {
                    wrapper.where(MemberEntity::getCompanyId).eq(companyId);
                    if (StringUtils.hasText(q.getKeyword())) {
                        wrapper.and(MemberEntity::getName).like(q.getKeyword());
                    }
                    if (StringUtils.hasText(q.getMerchantIds())) {
                        wrapper.and(MemberEntity::getMerchantId).in(List.of(q.getMerchantIds().split(",")));
                    }
                    wrapper.orderBy(MemberEntity::getId, false);
                    return wrapper;
                })
                .limit(10000)
                .build());
        options.forEach(e -> e.setLabel(Optional.ofNullable(e.getLabel()).orElse("") + "(" + e.getTag() + ")"));
        return options;
    }
}