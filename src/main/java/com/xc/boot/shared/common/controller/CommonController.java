package com.xc.boot.shared.common.controller;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.result.Result;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateEntity;
import com.xc.boot.modules.merchant.model.vo.GoodsIncomeTemplateDetailVO;
import com.xc.boot.modules.merchant.service.GoodsIncomeTemplateService;
import com.xc.boot.system.model.entity.PrintTagTemplateEntity;
import com.xc.boot.system.service.PrintTagTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.xc.boot.modules.merchant.model.entity.table.GoodsIncomeTemplateTableDef.GOODS_INCOME_TEMPLATE;
import static com.xc.boot.system.model.entity.table.PrintTagTemplateTableDef.PRINT_TAG_TEMPLATE;

/**
 * 公共接口控制器
 */
@Tag(name = "公共接口")
@RestController
@RequestMapping("/api/common")
@RequiredArgsConstructor
public class CommonController {

    private final GoodsIncomeTemplateService goodsIncomeTemplateService;
    private final PrintTagTemplateService printTagTemplateService;

    @Operation(summary = "获取入库模板详情", description = "根据模板ID获取入库模板详情，可选择是否包含禁用的字段")
    @GetMapping("/template/details")
    public Result<List<GoodsIncomeTemplateDetailVO>> getTemplateDetails(
            @RequestParam Long id,
            @RequestParam(defaultValue = "false") Boolean includeDisabled) {
        GoodsIncomeTemplateEntity template = goodsIncomeTemplateService.getTemplateById(id);

        List<GoodsIncomeTemplateDetailVO> result = goodsIncomeTemplateService.getTemplateDetailsWithColumn(id, includeDisabled);
        return Result.success(result, Map.of("categoryId", template.getCategoryId(), "categoryName", template.getName()));
    }

    @Operation(summary = "获取某大类的默认入库模板详情（不返回货品条码、柜台和数量）")
    @GetMapping("/template/default")
    public Result<List<GoodsIncomeTemplateDetailVO>> getDefaultTemplateDetails(@RequestParam Integer categoryId) {
        // 查询默认模板
        List<GoodsIncomeTemplateEntity> list = goodsIncomeTemplateService.list(
                QueryWrapper.create()
                        .where(GOODS_INCOME_TEMPLATE.CATEGORY_ID.eq(categoryId))
                        .and(GOODS_INCOME_TEMPLATE.STATUS.eq(1))
                        .orderBy(GOODS_INCOME_TEMPLATE.DEFAULT_FLAG, false)
                        .orderBy(GOODS_INCOME_TEMPLATE.ID, false)
        );
        Assert.notEmpty(list, "当前大类下无生效中默认入库模板");
        GoodsIncomeTemplateEntity template = list.getFirst();

        if (template == null) {
            return Result.success(null);
        }

        // 获取模板详情
        List<GoodsIncomeTemplateDetailVO> result = goodsIncomeTemplateService.getTemplateDetailsWithColumn(template.getId());
        result = result.stream()
                .filter(detail ->
                        !detail.getSign().equals("goods_sn")
                        && !detail.getSign().equals("counter_id")
                        && !detail.getSign().equals("num"))
                .collect(Collectors.toList());
        return Result.success(result);
    }

    @Operation(summary = "获取打印标签模板列表")
    @GetMapping("/print/templates")
    public Result<List<PrintTagTemplateEntity>> getPrintTagTemplates(@RequestParam(defaultValue = "false") Boolean includeDisabled) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .from(PRINT_TAG_TEMPLATE)
                .where(PRINT_TAG_TEMPLATE.COMPANY_ID.eq(SecurityUtils.getCompanyId()));

        if (!includeDisabled) {
            queryWrapper.and(PRINT_TAG_TEMPLATE.STATUS.eq(1));
        }
        queryWrapper.orderBy(PRINT_TAG_TEMPLATE.ID, false);
        List<PrintTagTemplateEntity> templates = printTagTemplateService.list(queryWrapper);
        return Result.success(templates);
    }

    @Operation(summary = "获取打印标签模板详情")
    @GetMapping("/print/template/detail")
    public Result<PrintTagTemplateEntity> getPrintTagTemplateDetails(@RequestParam Long id) {
        PrintTagTemplateEntity template = printTagTemplateService.getById(id);
        return Result.success(template);
    }
} 