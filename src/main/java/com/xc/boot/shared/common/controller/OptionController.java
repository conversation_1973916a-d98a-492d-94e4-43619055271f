package com.xc.boot.shared.common.controller;

import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.system.model.enums.PdaEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xc.boot.common.model.Option;
import com.xc.boot.common.result.Result;
import com.xc.boot.shared.common.model.query.OptionQuery;
import com.xc.boot.shared.common.service.OptionService;

@Tag(name = "公共-选项接口")
@RestController
@RequestMapping("/api/common/option")
@RequiredArgsConstructor
public class OptionController {

    private final OptionService optionService;

    @Operation(summary = "用户列表")
    @GetMapping("/users")
    public Result<List<Option<Long>>> users(OptionQuery query) {
        return Result.success(optionService.users(query));
    }

    @Operation(summary = "商户列表")
    @GetMapping("/company")
    public Result<List<Option<Long>>> company(OptionQuery query) {
        return Result.success(optionService.company(query));
    }

    @Operation(summary = "门店列表")
    @GetMapping("/merchant")
    public Result<List<Option<Long>>> merchant(OptionQuery query) {
        return Result.success(optionService.merchant(query));
    }

    @Operation(summary = "角色列表")
    @GetMapping("/role")
    public Result<List<Option<Long>>> role(OptionQuery query) {
        return Result.success(optionService.role(query));
    }

    @Operation(summary = "大类列表")
    @GetMapping("/category")
    public Result<List<Option<Long>>> category(OptionQuery query) {
        return Result.success(CategoryEnum.toOptions(query));
    }

    @Operation(summary = "小类列表")
    @GetMapping("/subclass")
    public Result<List<Option<Long>>> subclass(OptionQuery query) {
        return Result.success(optionService.subclass(query));
    }

    @Operation(summary = "柜台列表")
    @GetMapping("/counter")
    public Result<List<Option<Long>>> counter(OptionQuery query) {
        return Result.success(optionService.counter(query));
    }

    @Operation(summary = "销售柜台列表")
    @GetMapping("/sales-counter")
    public Result<List<Option<Long>>> salesCounter(OptionQuery query) {
        return Result.success(optionService.salesCounter(query));
    }

    @Operation(summary = "成色列表")
    @GetMapping("/quality")
    public Result<List<Option<Long>>> quality(OptionQuery query) {
        return Result.success(optionService.quality(query));
    }

    @Operation(summary = "款式列表")
    @GetMapping("/style")
    public Result<List<Option<Long>>> style(OptionQuery query) {
        return Result.success(optionService.style(query));
    }

    @Operation(summary = "品牌列表")
    @GetMapping("/brand")
    public Result<List<Option<Long>>> brand(OptionQuery query) {
        return Result.success(optionService.brand(query));
    }

    @Operation(summary = "珠石列表")
    @GetMapping("/jewelry")
    public Result<List<Option<Long>>> jewelry(OptionQuery query) {
        return Result.success(optionService.jewelry(query));
    }

    @Operation(summary = "工艺列表")
    @GetMapping("/technology")
    public Result<List<Option<Long>>> technology(OptionQuery query) {
        return Result.success(optionService.technology(query));
    }

    @Operation(summary = "供应商列表")
    @GetMapping("/supplier")
    public Result<List<Option<Long>>> supplier(OptionQuery query) {
        return Result.success(optionService.supplier(query));
    }

    @Operation(summary = "入库模板列表")
    @GetMapping("/goods-income-template")
    public Result<List<Option<Long>>> goodsIncomeTemplate(OptionQuery query) {
        return Result.success(optionService.goodsIncomeTemplate(query));
    }

    @Operation(summary = "sign列表")
    @GetMapping("/sign")
    public Result<List<Option<String>>> sign(OptionQuery query) {
        return Result.success(optionService.sign(query));
    }

    @Operation(summary = "pda类型列表")
    @GetMapping("/pda")
    public Result<List<Option<String>>> pda(OptionQuery query) {
        return Result.success(PdaEnum.toOptions(query));
    }

    @Operation(summary = "会员列表")
    @GetMapping("/member")
    public Result<List<Option<Long>>> member(OptionQuery query) {
        return Result.success(optionService.member(query));
    }
}
