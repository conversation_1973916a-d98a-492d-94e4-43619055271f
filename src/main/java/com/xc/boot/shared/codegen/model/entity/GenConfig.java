package com.xc.boot.shared.codegen.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 代码生成基础配置
 *
 * <AUTHOR>
 * @since 2.10.0
 */
@Getter
@Setter
@Schema(description = "代码生成基础配置")
@Table(value = "gen_config")
public class GenConfig{
    @Schema(description = "主键ID")
    @Id(keyType = KeyType.Auto)
    private Long id;

    @Schema(description = "表名")
    @Column(value = "table_name")
    private String tableName;

    @Schema(description = "包名")
    @Column(value = "package_name")
    private String packageName;

    @Schema(description = "模块名")
    @Column(value = "module_name")
    private String moduleName;

    @Schema(description = "实体类名")
    @Column(value = "entity_name")
    private String entityName;

    @Schema(description = "业务名")
    @Column(value = "business_name")
    private String businessName;

    @Schema(description = "父菜单ID")
    @Column(value = "parent_menu_id")
    private Long parentMenuId;

    @Schema(description = "作者")
    @Column(value = "author")
    private String author;

    @Schema(description = "创建时间")
    @Column(value = "created_at",onInsertValue = "now()")
    private Date createdAt;

    @Schema(description = "更新时间")
    @Column(value = "updated_at",onInsertValue = "now()",onUpdateValue = "now()")
    private Date updatedAt;

}