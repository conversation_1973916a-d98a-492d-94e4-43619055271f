package com.xc.boot.shared.codegen.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.xc.boot.shared.codegen.enums.FormTypeEnum;
import com.xc.boot.shared.codegen.enums.QueryTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 字段生成配置实体
 *
 * <AUTHOR>
 * @since 2.10.0
 */
@Getter
@Setter
@Table(value = "gen_field_config")
public class GenFieldConfig {

    /**
     * 主键ID
     */
    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 关联的配置ID
     */
    @Column(value = "config_id")
    private Long configId;

    /**
     * 列名
     */
    @Column(value = "column_name")
    private String columnName;

    /**
     * 列类型
     */
    @Column(value = "column_type")
    private String columnType;

    /**
     * 字段长度
     */
    @Column(value = "max_length")
    private Long maxLength;

    /**
     * 字段名称
     */
    @Column(value = "field_name")
    private String fieldName;

    /**
     * 字段排序
     */
    @Column(value = "field_sort")
    private Integer fieldSort;

    /**
     * 字段类型
     */
    @Column(value = "field_type")
    private String fieldType;

    /**
     * 字段描述
     */
    @Column(value = "field_comment")
    private String fieldComment;

    /**
     * 表单类型
     */
    @Column(value = "form_type")
    private FormTypeEnum formType;

    /**
     * 查询方式
     */
    @Column(value = "query_type")
    private QueryTypeEnum queryType;

    /**
     * 是否在列表显示
     */
    @Column(value = "is_show_in_list")
    private Integer isShowInList;

    /**
     * 是否在表单显示
     */
    @Column(value = "is_show_in_form")
    private Integer isShowInForm;

    /**
     * 是否在查询条件显示
     */
    @Column(value = "is_show_in_query")
    private Integer isShowInQuery;

    /**
     * 是否必填
     */
    @Column(value = "is_required")
    private Integer isRequired;

    /**
     * TypeScript类型
     */
    @Column(ignore = true, value = "ts_type")
    @JsonIgnore
    private String tsType;

    /**
     * 字典类型
     */
    @Column(value = "dict_type")
    private String dictType;

    /**
     * 字段长度
     */
    @Column(value = "column_length")
    private String columnLength;

    /**
     * 创建时间
     */
    @Column(value = "created_at", onInsertValue = "now()")
    private Date createdAt;

    /**
     * 更新时间
     */
    @Column(value = "updated_at", onInsertValue = "now()", onUpdateValue = "now()")
    private Date updatedAt;
}
