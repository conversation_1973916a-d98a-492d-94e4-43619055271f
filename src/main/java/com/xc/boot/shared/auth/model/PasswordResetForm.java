package com.xc.boot.shared.auth.model;

import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.constant.SecurityConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 修改密码表单
 *
 * <AUTHOR>
 * @since 2024/8/13
 */
@Schema(description = "修改密码表单")
@Data
public class PasswordResetForm {

    @Schema(description = "账号")
    @NotBlank(message = "账号不能为空")
    private String username;

    @Schema(description = "验证码")
    @NotBlank(message = "验证码不能为空")
    private String code;

    @Schema(description = "新密码")
    @NotBlank(message = "新密码不能为空")
    @Pattern(regexp = SecurityConstants.PASSWORD_PATTERN, message = SecurityConstants.PASSWORD_TIPS)
    private String newPassword;

    @Schema(description = "确认新密码")
    @NotBlank(message = "确认新密码不能为空")
    @Pattern(regexp = SecurityConstants.PASSWORD_PATTERN, message = SecurityConstants.PASSWORD_TIPS)
    private String confirmPassword;

    private String sideCode;

}
