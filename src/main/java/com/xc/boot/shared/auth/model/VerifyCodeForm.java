package com.xc.boot.shared.auth.model;

import com.xc.boot.common.constant.SecurityConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName VerifyCodeForm
 * @Date: 2025/6/5 09:27
 * @Description: 发送验证码
 */
@Data
public class VerifyCodeForm {
    @Schema(description = "手机号")
    @Pattern(regexp = SecurityConstants.MOBILE_PATTERN, message = "手机号码格式不正确")
    @NotNull(message = "手机号不能为空")
    private String username;

    @Schema(description = "场景(1:登录|2:忘记密码|3:修改手机号)")
    @NotNull(message = "场景不能为空")
    private Integer type;
}
