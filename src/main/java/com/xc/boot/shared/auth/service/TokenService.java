package com.xc.boot.shared.auth.service;


import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.shared.auth.model.AuthTokenResponse;
import org.springframework.security.core.Authentication;

/**
 * 令牌接口
 *
 * <AUTHOR>
 * @since 2.16.0
 */
public interface TokenService {

    /**
     * 生成认证 Token
     *
     * @param authentication 用户认证信息
     * @return 认证 Token 响应
     */
    AuthTokenResponse generateToken(Authentication authentication);

    /**
     * 解析 Token 获取认证信息
     *
     * @param token JWT Token
     * @return 用户认证信息
     */
    Authentication parseToken(String token);


    /**
     * 校验 Token 是否有效
     *
     * @param token JWT Token
     * @return 是否有效
     */
    boolean validateToken(String token);


    /**
     *  刷新 Token
     * @param userId 刷新令牌
     * @return 认证 Token 响应
     */
    void refreshToken(String userId, SysUserDetails userDetails);

    /**
     * 获取用户token信息
     * @param userId
     * @return
     */
    SysUserDetails getTokenById(String userId, String sideCode);

    /**
     * 将 Token 删除
     */
    void blacklistToken(Long userId);

    /**
     * 登出单个jwtId
     */
    void logout(Long userId, String jwtId, String sideCode);

}
