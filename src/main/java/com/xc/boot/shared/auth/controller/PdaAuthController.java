package com.xc.boot.shared.auth.controller;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.annotation.Log;
import com.xc.boot.common.annotation.RepeatSubmit;
import com.xc.boot.common.annotation.methods.AnonymousPostMapping;
import com.xc.boot.common.enums.LogModuleEnum;
import com.xc.boot.common.enums.SideEnum;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.result.ResultCode;
import com.xc.boot.shared.auth.model.*;
import com.xc.boot.shared.auth.service.AuthService;
import com.xc.boot.system.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xc.boot.system.model.entity.table.SysUserTableDef.SYS_USER;


@Tag(name = "PDA-认证中心")
@RestController
@RequestMapping("/api/pda/auth")
@RequiredArgsConstructor
@Slf4j
public class PdaAuthController {

    private final AuthService authService;
    private final UserService userService;

    @Operation(summary = "PDA账号密码登录")
    @AnonymousPostMapping("/login")
    @Log(value = "登录", module = LogModuleEnum.LOGIN)
    public Result<AuthTokenResponse> login(@RequestBody @Validated LoginForm loginForm) {
        AuthTokenResponse authTokenResponse = authService.login(loginForm.getUsername(), loginForm.getPassword(), SideEnum.PDA.getValue());
        return Result.success(authTokenResponse);
    }

    @Operation(summary = "PDA注销")
    @GetMapping("/logout")
    public Result<?> logout() {
        authService.logout(SideEnum.PDA.getValue());
        return Result.success();
    }

    @Operation(summary = "PDA发送短信验证码")
    @AnonymousPostMapping(value = "/sendCode")
    @RepeatSubmit
    public Result<?> sendVerificationCode(@RequestBody @Validated VerifyCodeForm form) {
        boolean result = userService.sendVerificationCode(form.getUsername(), form.getType());
        return Result.judge(result);
    }

    @Operation(summary = "PDA验证码登录")
    @AnonymousPostMapping("/smsLogin")
    public Result<AuthTokenResponse> smsLogin(@RequestBody LoginForm loginForm) {
        Assert.notNull(loginForm.getCode(), "验证码不能为空");
        loginForm.setSideCode(SideEnum.PDA.getValue());
        AuthTokenResponse loginResult = authService.smsLogin(loginForm);
        return Result.success(loginResult);
    }
}
