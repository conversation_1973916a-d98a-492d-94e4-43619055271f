package com.xc.boot.shared.auth.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @ClassName LoginForm
 * @Date: 2025/6/3 10:02
 * @Description: 描述
 */
@Getter
@Setter
@Schema(description = "登录表单实体")
public class LoginForm {

    @Schema(description = "用户名", example = "admin")
    @NotBlank(message = "用户名不能为空")
    private String username;

    @Schema(description = "密码", example = "123456")
    private String password;

    @Schema(description = "验证码", example = "415123")
    private String code;

    private String sideCode;
}
