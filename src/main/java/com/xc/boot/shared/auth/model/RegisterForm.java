package com.xc.boot.shared.auth.model;

import com.xc.boot.common.constant.SecurityConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @ClassName RegisterForm
 * @Date: 2025/6/5 11:22
 * @Description: 注册表单
 */
@Data
public class RegisterForm {

    @Schema(description = "公司名")
    @NotBlank(message = "公司名不能为空")
    @Length(max = 200, message = "公司名长度不能超过200个字符")
    private String companyName;

    @Schema(description = "公司地址")
    @NotBlank(message = "公司地址不能为空")
    @Length(max = 200, message = "公司地址长度不能超过200个字符")
    private String companyAddress;

    @Schema(description = "姓名")
    @NotBlank(message = "姓名不能为空")
    @Length(max = 200, message = "姓名长度不能超过200个字符")
    private String nickname;

    @Schema(description = "手机号")
    @Pattern(regexp = SecurityConstants.MOBILE_PATTERN, message = "手机号码格式不正确")
    @NotBlank(message = "手机号不能为空")
    private String username;

}
