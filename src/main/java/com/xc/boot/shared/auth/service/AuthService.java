package com.xc.boot.shared.auth.service;

import com.xc.boot.shared.auth.model.*;

/**
 * 认证服务接口
 *
 * <AUTHOR>
 * @since 2.4.0
 */
public interface AuthService {

    /**
     * 登录
     *
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    AuthTokenResponse login(String username, String password, String sideCode);

    /**
     * 登出
     */
    void logout(String sideCode);

    /**
     * 获取验证码
     *
     * @return 验证码
     */
    CaptchaResponse getCaptcha();

    /**
     * 微信小程序登录
     *
     * @param code 微信登录code
     * @return 登录结果
     */
    AuthTokenResponse wechatLogin(String code);

    /**
     * 注册商家
     * @param registerForm
     * @return
     */
    boolean register(RegisterForm registerForm);


    boolean reSetPassword(PasswordResetForm resetForm);

    AuthTokenResponse smsLogin(LoginForm loginForm);
}
