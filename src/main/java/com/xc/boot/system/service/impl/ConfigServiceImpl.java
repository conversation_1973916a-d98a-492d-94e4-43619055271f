package com.xc.boot.system.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.constant.RedisConstants;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.system.converter.ConfigConverter;
import com.xc.boot.system.mapper.ConfigMapper;
import com.xc.boot.system.model.entity.SysConfigEntity;
import com.xc.boot.system.model.form.ConfigForm;
import com.xc.boot.system.model.query.ConfigPageQuery;
import com.xc.boot.system.model.vo.ConfigVO;
import com.xc.boot.system.service.ConfigService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统配置Service接口实现
 *
 * <AUTHOR>
 * @since 2024-07-29 11:17:26
 */
@Service
@RequiredArgsConstructor
public class ConfigServiceImpl extends ServiceImpl<ConfigMapper, SysConfigEntity> implements ConfigService {


    private final ConfigConverter configConverter;

    private final RedisTemplate<String, Object> redisTemplate;

    @SuppressWarnings("unused")
    private final PasswordEncoder passwordEncoder;


    /**
     * 系统启动完成后，加载系统配置到缓存
     */
    @PostConstruct
    public void init() {
        refreshCache();
    }

    /**
     * 分页查询系统配置
     *
     * @param configPageQuery 查询参数
     * @return 系统配置分页列表
     */
    @Override
    public Page<ConfigVO> page(ConfigPageQuery configPageQuery) {
        Page<SysConfigEntity> page = new Page<>(configPageQuery.getPageNum(), configPageQuery.getPageSize());
        String keywords = configPageQuery.getKeywords();
        QueryWrapper query = QueryWrapper.create()
                .and(q -> {
                    q.like(SysConfigEntity::getConfigKey, keywords)
                            .or(o -> {
                                o.like(SysConfigEntity::getConfigName, keywords);
                            });
                }, StringUtils.isNotBlank(keywords));
        Page<SysConfigEntity> pageList = this.page(page, query);
        return configConverter.toPageVo(pageList);
    }

    /**
     * 保存系统配置
     *
     * @param configForm 系统配置表单
     * @return 是否保存成功
     */
    @Override
    @Transactional
    public boolean save(ConfigForm configForm) {
        Assert.isTrue(
                super.count(
                        QueryWrapper.create().eq(SysConfigEntity::getConfigKey, configForm.getConfigKey())) == 0,
                "配置键已存在");
        SysConfigEntity config = configConverter.toEntity(configForm);
        boolean result = this.save(config);
        
        // 记录操作日志
        OpLogUtils.appendOpLog("系统配置-新增配置", "新增配置: " + config.getConfigName(), config);
        
        return result;
    }

    /**
     * 获取系统配置表单数据
     *
     * @param id 系统配置ID
     * @return 系统配置表单数据
     */
    @Override
    public ConfigForm getConfigFormData(Long id) {
        SysConfigEntity entity = this.getById(id);
        return configConverter.toForm(entity);
    }

    /**
     * 编辑系统配置
     *
     * @param id         系统配置ID
     * @param configForm 系统配置表单
     * @return 是否编辑成功
     */
    @Override
    @Transactional
    public boolean edit(Long id, ConfigForm configForm) {
        Assert.isTrue(
                super.count(QueryWrapper.create().eq(SysConfigEntity::getConfigKey, configForm.getConfigKey()).ne(SysConfigEntity::getId, id)) == 0,
                "配置键已存在");
        SysConfigEntity config = configConverter.toEntity(configForm);
        config.setUpdatedBy(SecurityUtils.getUserId());
        boolean result = this.updateById(config);
        
        // 记录操作日志
        OpLogUtils.appendOpLog("系统配置-编辑配置", "编辑配置: " + config.getConfigName(), 
            Map.of("修改前", config, "修改后", configForm));
        
        return result;
    }

    /**
     * 删除系统配置
     *
     * @param id 系统配置ID
     * @return 是否删除成功
     */
    @Override
    @Transactional
    public boolean delete(Long id) {
        if (id != null) {
            SysConfigEntity config = this.getById(id);
            boolean result = this.updateById(new SysConfigEntity().setId(id).setDeletedAt(new Date()));
            
            // 记录操作日志
            OpLogUtils.appendOpLog("系统配置-删除配置", "删除配置: " + config.getConfigName(), 
                Map.of("删除的配置", config));
            
            return result;
        }
        return false;
    }

    /**
     * 刷新系统配置缓存
     *
     * @return 是否刷新成功
     */
    @Override
    public boolean refreshCache() {
        redisTemplate.delete(RedisConstants.SYSTEM_CONFIG_KEY);
        List<SysConfigEntity> list = this.list();
        if (list != null) {
            Map<String, String> map = list.stream().collect(Collectors.toMap(SysConfigEntity::getConfigKey, SysConfigEntity::getConfigValue));
            redisTemplate.opsForHash().putAll(RedisConstants.SYSTEM_CONFIG_KEY, map);
            return true;
        }
        return false;
    }

    /**
     * 获取系统配置
     *
     * @param key 配置键
     * @return 配置值
     */
    @Override
    public Object getSystemConfig(String key) {
        if (StringUtils.isNotBlank(key)) {
            return redisTemplate.opsForHash().get(RedisConstants.SYSTEM_CONFIG_KEY, key);
        }
        return null;
    }

}
