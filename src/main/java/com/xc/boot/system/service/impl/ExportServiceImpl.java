package com.xc.boot.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.excel.model.ExportStatusEnum;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.system.mapper.ExportMapper;
import com.xc.boot.system.model.entity.SysExportEntity;
import com.xc.boot.system.model.query.ExportPageQuery;
import com.xc.boot.system.model.vo.ExportVo;
import com.xc.boot.system.service.ExportService;
import lombok.RequiredArgsConstructor;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.xc.boot.system.model.entity.table.SysExportTableDef.SYS_EXPORT;

/**
 * <AUTHOR>
 * @ClassName ExportServiceImpl
 * @Date: 2025/6/7 16:10
 * @Description: 任务中心
 */
@Service
@RequiredArgsConstructor
public class ExportServiceImpl extends ServiceImpl<ExportMapper, SysExportEntity> implements ExportService {
    private final AsyncTaskExecutor asyncTaskExecutor;
    private final ListFillService listFillService;

    @Override
    public void downloadCount(Long id) {
        asyncTaskExecutor.execute(() -> UpdateChain.of(SysExportEntity.class)
                .set(SYS_EXPORT.DOWNLOAD_COUNT, SYS_EXPORT.DOWNLOAD_COUNT.add(1))
                .where(SYS_EXPORT.ID.eq(id))
                .update());
    }

    @Override
    public Page<ExportVo> pageList(ExportPageQuery queryParams) {
        SysUserDetails userDetails = SecurityUtils.getUser().orElse(new SysUserDetails());
        QueryWrapper wrapper = QueryWrapper.create()
                .where(SYS_EXPORT.USER_ID.eq(queryParams.getUserId(), queryParams.getUserId() != null))
                // 非主账号固定只查询自己
                .where(SYS_EXPORT.USER_ID.eq(userDetails.getUserId(), !userDetails.getIsMain()))
                .where(SYS_EXPORT.COMPANY_ID.eq(userDetails.getCompanyId()))
                .where(SYS_EXPORT.SIGN.eq(queryParams.getType(), queryParams.getType() != null))
                .where(SYS_EXPORT.STATUS.eq(queryParams.getStatus(), queryParams.getStatus() != null));
        if (Objects.nonNull(queryParams.getTimeRange())) {
            wrapper.where(SYS_EXPORT.CREATED_AT.ge(queryParams.getTimeRange()[0]));
            wrapper.where(SYS_EXPORT.CREATED_AT.le(queryParams.getTimeRange()[1]));
        }
        wrapper.orderBy(SYS_EXPORT.ID.desc());
        // 导出
        if (queryParams.getExport().equals(1)) {
            export(wrapper, queryParams);
            return new Page<>();
        }

        Page<SysExportEntity> page = this.page(new Page<>(queryParams.getPageNum(), queryParams.getPageSize()), wrapper);
        Page<ExportVo> pageVo = new Page<>();
        BeanUtil.copyProperties(page, pageVo);
        List<ExportVo> vos = BeanUtil.copyToList(page.getRecords(), ExportVo.class);
        pageVo.setRecords(vos);

        if (!vos.isEmpty()) {
            Set<Long> userIds = vos.stream().map(ExportVo::getUserId).collect(Collectors.toSet());
            ListFillUtil.of(vos)
                    .build(listFillService::getUserNameByUserId, userIds,"userId", "creatorName")
                    .handle();
        }

        return pageVo;
    }

    @Override
    public boolean removeExport(Long id) {
        SysUserDetails userDetails = SecurityUtils.getUser().orElse(new SysUserDetails());
        SysExportEntity sysExportEntity = this.mapper.selectOneById(id);
        Assert.notNull(sysExportEntity, "导出记录不存在");
        Assert.isTrue(sysExportEntity.getCompanyId().equals(userDetails.getCompanyId()), "无权操作该导出记录");
        if (!userDetails.getIsMain()) {
            Assert.isTrue(sysExportEntity.getUserId().equals(SecurityUtils.getUserId()), "无权操作该导出记录");
        }
        Assert.isTrue(sysExportEntity.getStatus().equals(ExportStatusEnum.FAIL.getValue()), "只能删除导出失败任务");
        return this.mapper.deleteById(id) > 0;
    }

    private void export(QueryWrapper query, ExportPageQuery queryParams) {
        if (queryParams.getIds() != null && !queryParams.getIds().isEmpty()) {
            query.where(SYS_EXPORT.ID.in(queryParams.getIds()));
        }
        ExcelUtil.of(this.mapper, query, ExportVo.class, "export", "下载中心")
                .getData((mapper, queryWrapper) -> {
                    if (mapper instanceof ExportMapper exportMapper) {
                        List<SysExportEntity> entityList = exportMapper.selectListByQuery(queryWrapper);
                        List<ExportVo> vos = BeanUtil.copyToList(entityList, ExportVo.class);
                        if (!vos.isEmpty()) {
                            Set<Long> userIds = vos.stream().map(ExportVo::getUserId).collect(Collectors.toSet());
                            ListFillUtil.of(vos)
                                    .build(listFillService::getUserNameByUserId, userIds,"userId", "creatorName")
                                    .handle();
                        }
                        return vos;
                    }
                    return new ArrayList<>();
                }).doExport();
    }

}
