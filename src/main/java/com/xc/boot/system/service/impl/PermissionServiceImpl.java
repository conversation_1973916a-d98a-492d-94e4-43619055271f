package com.xc.boot.system.service.impl;

import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.constant.SystemConstants;
import com.xc.boot.common.model.Option;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.system.mapper.PermissionMapper;
import com.xc.boot.system.mapper.RolePermissionMapper;
import com.xc.boot.system.model.entity.SysPermissionEntity;
import com.xc.boot.system.model.form.PermissionForm;
import com.xc.boot.system.model.query.PermissionQuery;
import com.xc.boot.system.model.vo.PermissionVO;
import com.xc.boot.system.service.PermissionService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 权限服务实现类
 */
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, SysPermissionEntity> implements PermissionService {

    private final RolePermissionMapper rolePermissionMapper;

    @Override
    public List<PermissionVO> listPermissions(PermissionQuery queryParams) {
        QueryWrapper queryWrapper = query()
                .where(q -> {
                    q.where(SysPermissionEntity::getName)
                            .like(queryParams.getKeywords(), StrUtil.isNotBlank(queryParams.getKeywords()))
                            .or(SysPermissionEntity::getSign)
                            .like(queryParams.getKeywords(), StrUtil.isNotBlank(queryParams.getKeywords()));
                })
                .and(SysPermissionEntity::getScene).eq(queryParams.getScene(), queryParams.getScene() != null)
                .and(SysPermissionEntity::getType).eq(queryParams.getType(), queryParams.getType() != null)
                .orderBy(SysPermissionEntity::getSort).asc();

        List<SysPermissionEntity> permissions = mapper.selectListByQuery(queryWrapper);
        List<PermissionVO> permissionVOs = permissions.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        return buildTree(permissionVOs);
    }

    @Override
    public List<Option<Long>> listPermissionOptions() {
        List<PermissionVO> permissions = this.listPermissions(new PermissionQuery());

        return buildPermissionOptions(permissions);
    }

    private List<Option<Long>> buildPermissionOptions(List<PermissionVO> permissions) {
        List<Option<Long>> result = new ArrayList<>();

        for (PermissionVO permission : permissions) {
            Option<Long> option = new Option<>(permission.getId(), permission.getName());
            if (permission.getChildren() != null && !permission.getChildren().isEmpty()) {
                option.setChildren(buildPermissionOptions(permission.getChildren()));
            }
            result.add(option);
        }

        return result;
    }

    /**
     * 检查权限层级
     *
     * @param treePath 权限树路径
     * @throws RuntimeException 如果层级超过3级
     */
    private void checkPermissionLevel(String treePath) {
        if (treePath != null) {
            int level = treePath.split(",").length;
            if (level > 3) {
                throw new RuntimeException("权限层级不能超过3级");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePermission(Long id) {
        // 获取要删除的权限信息
        SysPermissionEntity permission = this.getById(id);
        if (permission == null) {
            throw new RuntimeException("权限不存在");
        }

        // 查找所有需要删除的权限ID（包括子权限）
        List<SysPermissionEntity> permissionsToDelete = this.list(
            QueryWrapper.create()
                .where(SysPermissionEntity::getTreePath).like(permission.getTreePath() + "," + id)
                .or(SysPermissionEntity::getId).eq(id)
        );

        if (permissionsToDelete.isEmpty()) {
            return false;
        }

        // 获取所有需要删除的权限 sign
        List<String> permissionSigns = permissionsToDelete.stream()
            .map(SysPermissionEntity::getSign)
            .collect(Collectors.toList());

        // 删除角色权限关联
        rolePermissionMapper.deleteByQuery(
            QueryWrapper.create()
                .from("sys_role_permission")
                .where(QueryMethods.column("permission_sign").in(permissionSigns))
        );

        // 删除权限记录
        boolean result = this.mapper.deleteByQuery(
            QueryWrapper.create()
                .from(SysPermissionEntity.class)
                .where(QueryMethods.column(SysPermissionEntity::getSign).in(permissionSigns))
        ) > 0;

        if (result) {
            // 记录操作日志
            OpLogUtils.appendOpLog("权限管理-删除权限", "删除权限及其子权限: " + permission.getName(), 
                Map.of("删除的权限", permissionsToDelete));
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePermission(PermissionForm permissionForm) {
        if (Objects.equals(permissionForm.getParentId(), permissionForm.getId())) {
            throw new RuntimeException("父级权限不能为当前权限");
        }

        // 检查父级权限是否为当前权限的子权限
        if(permissionForm.getParentId() != null){
            SysPermissionEntity entity = this.getOne(
                    this.query()
                        .where(SysPermissionEntity::getId).eq(permissionForm.getParentId())
                        .where(QueryMethods.raw("find_in_set(?, tree_path)", permissionForm.getId()))
                );

            Assert.isNull(entity, "父级权限不能为当前权限的子权限");
        }

        // 检查权限标识是否重复
        Long count = this.count(
            this.query()
                .where(SysPermissionEntity::getSign).eq(permissionForm.getSign())
                .where(SysPermissionEntity::getId).ne(permissionForm.getId())
        );

        Assert.isFalse(count > 0, "权限标识已存在");

        SysPermissionEntity permission;
        if (permissionForm.getId() != null) {
            permission = getById(permissionForm.getId());
            if (permission == null) {
                throw new RuntimeException("权限不存在");
            }
        } else {
            permission = new SysPermissionEntity();
        }

        BeanUtils.copyProperties(permissionForm, permission);

        if(permission.getParentId() == null){
            permission.setParentId(SystemConstants.ROOT_NODE_ID);
        }

        String treePath = generatePermissionTreePath(permissionForm.getParentId());
        // 检查权限层级
        checkPermissionLevel(treePath);
        permission.setTreePath(treePath);

        // 检查统一层级下是否存在相同的权限名称
        Long sameNameCount = this.count(
            this.query()
                .where(SysPermissionEntity::getName).eq(permissionForm.getName())
                .where(SysPermissionEntity::getParentId).eq(permission.getParentId())
                .where(SysPermissionEntity::getId).ne(permission.getId(), permission.getId() != null)
        );

        CommonUtils.abortIf(sameNameCount > 0, "同一层级下权限名称已存在");

        boolean result = this.saveOrUpdate(permission);
        if (result) {
            // 修改权限如果有子权限，则更新子权限的树路径
            updateChildrenTreePath(permission.getId(), treePath);
            
            // 记录操作日志
            if (permissionForm.getId() != null) {
                OpLogUtils.appendOpLog("权限管理-编辑权限", "编辑权限: " + permission.getName(), 
                    Map.of("修改前", permission, "修改后", permissionForm));
            } else {
                OpLogUtils.appendOpLog("权限管理-新增权限", "新增权限: " + permission.getName(), permissionForm);
            }
        }
        return result;
    }

    /**
     * 更新子权限树路径
     *
     * @param id       当前权限ID
     * @param treePath 当前权限树路径
     */
    private void updateChildrenTreePath(Long id, String treePath) {
        List<SysPermissionEntity> children = this.list(
                QueryWrapper.create().eq(SysPermissionEntity::getParentId, id));
        if (CollectionUtil.isNotEmpty(children)) {
            // 子权限的树路径等于父权限的树路径加上父权限ID
            String childTreePath = treePath + "," + id;
            this.update(
                    new SysPermissionEntity().setTreePath(childTreePath),
                    QueryWrapper.create()
                            .eq(SysPermissionEntity::getParentId, id)
            );
            for (SysPermissionEntity child : children) {
                // 递归更新子权限
                updateChildrenTreePath(child.getId(), childTreePath);
            }
        }
    }

    /**
     * 权限路径生成
     *
     * @param parentId 父ID
     * @return 父节点路径以英文逗号(, )分割，eg: 1,2,3
     */
    private String generatePermissionTreePath(Long parentId) {
        // 如果parentId为null或等于根节点ID，直接返回根节点ID
        if (parentId == null || SystemConstants.ROOT_NODE_ID.equals(parentId)) {
            return String.valueOf(SystemConstants.ROOT_NODE_ID);
        }
        
        // 获取父节点信息
        SysPermissionEntity parent = this.getById(parentId);
        return parent != null ? parent.getTreePath() + "," + parent.getId() : String.valueOf(SystemConstants.ROOT_NODE_ID);
    }

    /**
     * 将权限实体转换为视图对象
     */
    public PermissionVO convertToVO(SysPermissionEntity permission) {
        PermissionVO vo = new PermissionVO();
        BeanUtils.copyProperties(permission, vo);
        return vo;
    }

    /**
     * 构建树形结构
     */
    public List<PermissionVO> buildTree(List<PermissionVO> list) {
        // 使用Map存储所有节点，key为节点ID
        Map<Long, PermissionVO> nodeMap = list.stream()
                .collect(Collectors.toMap(PermissionVO::getId, node -> node, (a , b) -> a));

        // 找出最小的parentId作为最外层节点的判断标准
        Long minParentId = list.stream()
                .map(PermissionVO::getParentId)
                .filter(Objects::nonNull)
                .min(Long::compareTo)
                .orElse(SystemConstants.ROOT_NODE_ID);


        List<Long> childrenIds = new ArrayList<>();
        // 遍历所有节点，构建树形结构
        for (PermissionVO node : list) {
            // 获取父节点ID
            Long parentId = node.getParentId();
            if (parentId != null && !parentId.equals(minParentId)) {
                // 将当前节点添加到父节点的children列表中
                PermissionVO parent = nodeMap.get(parentId);
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(node);
                    childrenIds.add(node.getId());
                }
            }
        }

        // 返回最外层节点列表（parentId为最小parentId的节点）
        return list.stream()
                .filter(node -> !childrenIds.contains(node.getId()))
                .collect(Collectors.toList());
    }
}