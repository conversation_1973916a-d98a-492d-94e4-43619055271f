package com.xc.boot.system.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.system.model.dto.CompanyFormDTO;
import com.xc.boot.system.model.entity.CompanyEntity;
import com.xc.boot.system.model.entity.SysConfigEntity;
import com.xc.boot.system.model.query.CompanyPageQuery;
import com.xc.boot.system.model.vo.CompanyPageVO;

/**
 * 商家服务接口
 */
public interface CompanyService extends IService<CompanyEntity> {
    
    /**
     * 获取商家分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<CompanyPageVO> getCompanyPage(CompanyPageQuery queryParams);

    /**
     * 保存商家
     *
     * @param form 商家表单数据
     * @return 是否成功
     */
    boolean saveCompany(CompanyFormDTO form);

    /**
     * 修改商家状态
     *
     * @param id 商家ID
     * @param status 商家状态(1:启用；0:禁用)
     * @return 是否成功
     */
    boolean updateCompanyStatus(Long id, Integer status);

    /**
     * 初始化业务库
     *
     * @param company 商家实体
     * @return 是否成功
     */
    boolean initDb(CompanyEntity company);
}