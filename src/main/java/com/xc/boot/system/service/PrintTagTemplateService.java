package com.xc.boot.system.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.system.model.dto.PrintTagTemplateConfigDTO;
import com.xc.boot.system.model.dto.PrintTagTemplateFormDTO;
import com.xc.boot.system.model.entity.PrintTagTemplateEntity;
import com.xc.boot.system.model.query.PrintTagTemplatePageQuery;
import com.xc.boot.system.model.vo.PrintTagListVO;
import com.xc.boot.system.model.vo.PrintTagTemplatePageVO;

import java.util.List;

/**
 * 打印标签模板服务接口
 */
public interface PrintTagTemplateService extends IService<PrintTagTemplateEntity> {
    
    /**
     * 获取打印标签模板分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<PrintTagTemplatePageVO> getPrintTagTemplatePage(PrintTagTemplatePageQuery queryParams);

    /**
     * 保存打印标签模板
     *
     * @param form 表单数据
     * @return 是否成功
     */
    boolean savePrintTagTemplate(PrintTagTemplateFormDTO form);

    /**
     * 删除打印标签模板
     *
     * @param id 模板ID
     * @return 是否成功
     */
    boolean deletePrintTagTemplate(Long id);

    /**
     * 配置打印标签模板
     *
     * @param config 配置数据
     * @return 是否成功
     */
    boolean configPrintTagTemplate(PrintTagTemplateConfigDTO config);

    /**
     * 获取打印标签列表
     *
     * @return 标签列表
     */
    List<PrintTagListVO> getPrintTagList();
} 