package com.xc.boot.system.service;

import com.mybatisflex.core.service.IService;
import com.xc.boot.common.enums.SideEnum;
import com.xc.boot.common.model.Option;
import com.xc.boot.shared.codegen.model.entity.GenConfig;
import com.xc.boot.system.model.entity.SysMenuEntity;
import com.xc.boot.system.model.form.MenuForm;
import com.xc.boot.system.model.query.MenuQuery;
import com.xc.boot.system.model.vo.MenuVO;
import com.xc.boot.system.model.vo.RouteVO;

import java.util.List;

/**
 * 菜单业务接口
 *
 * <AUTHOR>
 * @since 2020/11/06
 */
public interface MenuService extends IService<SysMenuEntity> {

    /**
     * 获取菜单表格列表
     */
    List<MenuVO> listMenus(MenuQuery queryParams);

    /**
     * 获取菜单下拉列表
     *
     * @param onlyParent 是否只查询父级菜单
     */
    List<Option<Long>> listMenuOptions(boolean onlyParent);

    /**
     * 新增菜单
     *
     * @param menuForm 菜单表单对象
     */
    boolean saveMenu(MenuForm menuForm);

    /**
     * 获取路由列表
     */
    List<RouteVO> getCurrentUserRoutes(SideEnum sideEnum);

    /**
     * 修改菜单显示状态
     *
     * @param menuId  菜单ID
     * @param visible 是否显示(1-显示 0-隐藏)
     */
    boolean updateMenuVisible(Long menuId, Integer visible);

    /**
     * 获取菜单表单数据
     *
     * @param id 菜单ID
     */
    MenuForm getMenuForm(Long id);

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     */
    boolean deleteMenu(Long id);

    /**
     * 代码生成时添加菜单
     *
     * @param parentMenuId 父菜单ID
     * @param genConfig    实体名
     */
    void addMenuForCodegen(Long parentMenuId, GenConfig genConfig);

    /**
     * 获取菜单权限选项
     *
     * @return 菜单权限选项列表
     */
    List<Option<String>> listPermissionOptions();
}
