package com.xc.boot.system.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.system.model.entity.SysLogEntity;
import com.xc.boot.system.model.query.LogPageQuery;
import com.xc.boot.system.model.vo.LogPageVO;
import com.xc.boot.system.model.vo.VisitStatsVO;
import com.xc.boot.system.model.vo.VisitTrendVO;

import java.time.LocalDate;

/**
 * 系统日志 服务接口
 *
 * <AUTHOR>
 * @since 2.10.0
 */
public interface LogService extends IService<SysLogEntity> {

    /**
     * 获取日志分页列表
     */
    Page<LogPageVO> getLogPage(LogPageQuery queryParams);


    /**
     * 获取访问趋势
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     */
    VisitTrendVO getVisitTrend(LocalDate startDate, LocalDate endDate);

    /**
     * 获取访问统计
     */
    VisitStatsVO getVisitStats();

}
