package com.xc.boot.system.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.system.model.entity.SysExportEntity;
import com.xc.boot.system.model.query.ExportPageQuery;
import com.xc.boot.system.model.vo.ExportVo;
import jakarta.validation.constraints.NotNull;

public interface ExportService extends IService<SysExportEntity> {
    void downloadCount(Long id);

    Page<ExportVo> pageList(ExportPageQuery queryParams);

    boolean removeExport(@NotNull(message = "ID不能为空") Long id);
}
