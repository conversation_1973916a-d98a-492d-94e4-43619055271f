package com.xc.boot.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.system.mapper.LogMapper;
import com.xc.boot.system.model.bo.VisitCount;
import com.xc.boot.system.model.bo.VisitStatsBO;
import com.xc.boot.system.model.entity.SysLogEntity;
import com.xc.boot.system.model.entity.SysUserEntity;
import com.xc.boot.system.model.query.LogPageQuery;
import com.xc.boot.system.model.vo.LogPageVO;
import com.xc.boot.system.model.vo.VisitStatsVO;
import com.xc.boot.system.model.vo.VisitTrendVO;
import com.xc.boot.system.service.LogService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统日志 服务实现类
 *
 * <AUTHOR>
 * @since 2.10.0
 */
@Service
public class LogServiceImpl extends ServiceImpl<LogMapper, SysLogEntity>
        implements LogService {

    /**
     * 获取日志分页列表
     *
     * @param queryParams 查询参数
     * @return 日志分页列表
     */
    @Override
    public Page<LogPageVO> getLogPage(LogPageQuery queryParams) {
        QueryWrapper query = QueryWrapper.create().from(SysLogEntity.class)
                .leftJoin(SysUserEntity.class).on(q -> q.and(SysUserEntity::getId).eq(SysLogEntity::getCreatedBy))
                .select(
                        QueryMethods.column(SysLogEntity::getId),
                        QueryMethods.column(SysLogEntity::getModule),
                        QueryMethods.column(SysLogEntity::getContent),
                        QueryMethods.column(SysLogEntity::getRequestUri),
                        QueryMethods.column(SysLogEntity::getIp),
                        QueryMethods.concat("province", "city").as(LogPageVO::getRegion),
                        QueryMethods.column(SysLogEntity::getExecutionTime),
                        QueryMethods.concat("browser", "browser_version").as(LogPageVO::getBrowser),
                        QueryMethods.column(SysLogEntity::getOs),
                        QueryMethods.column(SysLogEntity::getCreatedAt),
                        QueryMethods.column(SysUserEntity::getNickname).as(LogPageVO::getOperator)
                )
                .and(q -> {
                    q.and(SysLogEntity::getContent).like(queryParams.getKeywords())
                            .or(SysLogEntity::getIp).like(queryParams.getKeywords())
                            .or(SysUserEntity::getNickname).like(queryParams.getKeywords());
                }, StrUtil.isNotBlank(queryParams.getKeywords()));


        if (queryParams.getCreateTime() != null) {
            String startDate = queryParams.getCreateTime().get(0);
            String endDate = queryParams.getCreateTime().get(1);

            if (StrUtil.isNotBlank(startDate) && StrUtil.isNotBlank(endDate)) {
                query.and(SysLogEntity::getCreatedAt).between(startDate, endDate);
            }

            if (StrUtil.isNotBlank(startDate)) {
                query.and(SysLogEntity::getCreatedAt).ge(startDate);
            }

            if (StrUtil.isNotBlank(endDate)) {
                query.and(SysLogEntity::getCreatedAt).le(endDate);
            }
        }

        return this.pageAs(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                query.orderBy(SysLogEntity::getCreatedAt).desc(),
                LogPageVO.class
        );
    }

    /**
     * 获取访问趋势
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return
     */
    @Override
    public VisitTrendVO getVisitTrend(LocalDate startDate, LocalDate endDate) {
        VisitTrendVO visitTrend = new VisitTrendVO();
        List<String> dates = new ArrayList<>();

        // 获取日期范围内的日期
        while (!startDate.isAfter(endDate)) {
            dates.add(startDate.toString());
            startDate = startDate.plusDays(1);
        }
        visitTrend.setDates(dates);

        // 获取访问量和访问 IP 数的统计数据
        QueryWrapper pvCountsQuery = QueryWrapper.create().from(SysLogEntity.class)
                .select(QueryMethods.count().as(VisitCount::getCount),QueryMethods.dateFormat(SysLogEntity::getCreatedAt,"%Y-%m-%d").as(VisitCount::getDate))
                .and(SysLogEntity::getCreatedAt).between(dates.get(0) + " 00:00:00",dates.get(dates.size() - 1) + " 23:59:59")
                .groupBy(QueryMethods.dateFormat(SysLogEntity::getCreatedAt,"%Y-%m-%d"));
        List<VisitCount> pvCounts = this.listAs(
                pvCountsQuery,
                VisitCount.class
        );

        QueryWrapper ipCountsQuery = QueryWrapper.create().from(SysLogEntity.class)
                .select(QueryMethods.count(QueryMethods.distinct(SysLogEntity::getIp)).as(VisitCount::getCount),QueryMethods.dateFormat(SysLogEntity::getCreatedAt,"%Y-%m-%d").as(VisitCount::getDate))
                .and(SysLogEntity::getCreatedAt).between(dates.get(0) + " 00:00:00",dates.get(dates.size() - 1) + " 23:59:59")
                .groupBy(QueryMethods.dateFormat(SysLogEntity::getCreatedAt,"%Y-%m-%d"));

        List<VisitCount> ipCounts = this.listAs(
                ipCountsQuery,
                VisitCount.class
        );

        // 将统计数据转换为 Map
        Map<String, Integer> pvMap = pvCounts.stream().collect(Collectors.toMap(VisitCount::getDate, VisitCount::getCount));
        Map<String, Integer> ipMap = ipCounts.stream().collect(Collectors.toMap(VisitCount::getDate, VisitCount::getCount));

        // 匹配日期和访问量/访问 IP 数
        List<Integer> pvList = new ArrayList<>();
        List<Integer> ipList = new ArrayList<>();

        for (String date : dates) {
            pvList.add(pvMap.getOrDefault(date, 0));
            ipList.add(ipMap.getOrDefault(date, 0));
        }

        visitTrend.setPvList(pvList);
        visitTrend.setIpList(ipList);

        return visitTrend;
    }

    /**
     * 访问量统计
     */
    @Override
    public VisitStatsVO getVisitStats() {
        VisitStatsVO result = new VisitStatsVO();

        // 访客数统计(UV)
        VisitStatsBO uvStats = this.mapper.getUvStats();
        if (uvStats != null) {
            result.setTodayUvCount(uvStats.getTodayCount());
            result.setTotalUvCount(uvStats.getTotalCount());
            result.setUvGrowthRate(uvStats.getGrowthRate());
        }

        // 浏览量统计(PV)
        VisitStatsBO pvStats = this.mapper.getPvStats();
        if (pvStats != null) {
            result.setTodayPvCount(pvStats.getTodayCount());
            result.setTotalPvCount(pvStats.getTotalCount());
            result.setPvGrowthRate(pvStats.getGrowthRate());
        }

        return result;
    }

}




