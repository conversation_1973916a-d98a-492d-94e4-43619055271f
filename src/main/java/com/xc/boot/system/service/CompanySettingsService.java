package com.xc.boot.system.service;

import com.mybatisflex.core.service.IService;
import com.xc.boot.system.model.entity.CompanySettingsEntity;
import com.xc.boot.system.model.vo.CompanySettingsVO;

/**
 * 商户设置服务接口
 */
public interface CompanySettingsService extends IService<CompanySettingsEntity> {
    
    /**
     * 获取商户设置
     *
     * @return 商户设置视图对象
     */
    CompanySettingsVO getSettings();

    /**
     * 保存商户设置
     *
     * @param settings 商户设置视图对象
     * @return 是否保存成功
     */
    boolean saveSettings(CompanySettingsVO settings);

    /**
     * 获取当前商户ID
     *
     * @return 商户ID
     */
    Long getCompanyId();
} 