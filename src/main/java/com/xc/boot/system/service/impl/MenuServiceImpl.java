package com.xc.boot.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;

import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Row;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.constant.SystemConstants;
import com.xc.boot.common.enums.SideEnum;
import com.xc.boot.common.enums.StatusEnum;
import com.xc.boot.common.model.Option;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.shared.codegen.model.entity.GenConfig;
import com.xc.boot.system.converter.MenuConverter;
import com.xc.boot.system.mapper.MenuMapper;
import com.xc.boot.system.mapper.PermissionMapper;
import com.xc.boot.system.mapper.RoleMapper;
import com.xc.boot.system.model.bo.RouteBO;
import com.xc.boot.system.model.entity.SysMenuEntity;
import com.xc.boot.system.model.entity.SysRoleEntity;
import com.xc.boot.system.model.entity.SysRolePermissionEntity;
import com.xc.boot.system.model.form.MenuForm;
import com.xc.boot.system.model.query.MenuQuery;
import com.xc.boot.system.model.vo.MenuVO;
import com.xc.boot.system.model.vo.RouteVO;
import com.xc.boot.system.service.MenuService;
import com.xc.boot.system.service.RoleMenuService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.mybatisflex.core.query.QueryMethods.column;

import java.util.*;
import java.util.stream.Collectors;

import com.xc.boot.system.enums.PermissionSceneEnum;
import com.xc.boot.system.enums.PermissionTypeEnum;
import com.xc.boot.system.model.entity.SysPermissionEntity;
import com.xc.boot.common.util.OpLogUtils;

/**
 * 菜单业务实现类
 *
 * <AUTHOR>
 * @since 2020/11/06
 */
@Service
@RequiredArgsConstructor
public class MenuServiceImpl extends ServiceImpl<MenuMapper, SysMenuEntity> implements MenuService {

    private final MenuConverter menuConverter;
    private final RoleMenuService roleMenuService;
    private final PermissionMapper permissionMapper;
    private final RoleMapper roleMapper;

    /**
     * 菜单列表
     *
     * @param queryParams {@link MenuQuery}
     */
    @Override
    public List<MenuVO> listMenus(MenuQuery queryParams) {
        List<SysMenuEntity> menus = this.list(QueryWrapper.create()
                .like(SysMenuEntity::getName, queryParams.getName(), StrUtil.isNotBlank(queryParams.getName()))
                .like(SysMenuEntity::getRoutePath, queryParams.getRoutePath(),
                        StrUtil.isNotBlank(queryParams.getRoutePath()))
                .eq(SysMenuEntity::getVisible, queryParams.getVisible(), queryParams.getVisible() != null)
                .orderBy(SysMenuEntity::getSort).asc());
        // 获取所有菜单ID
        Set<Long> menuIds = menus.stream()
                .map(SysMenuEntity::getId)
                .collect(Collectors.toSet());

        // 获取所有父级ID
        Set<Long> parentIds = menus.stream()
                .map(SysMenuEntity::getParentId)
                .collect(Collectors.toSet());

        // 获取根节点ID（递归的起点），即父节点ID中不包含在部门ID中的节点
        List<Long> rootIds = parentIds.stream()
                .filter(id -> !menuIds.contains(id))
                .toList();

        // 使用递归函数来构建菜单树
        return rootIds.stream()
                .flatMap(rootId -> buildMenuTree(rootId, menus).stream())
                .collect(Collectors.toList());
    }

    /**
     * 递归生成菜单列表
     *
     * @param parentId 父级ID
     * @param menuList 菜单列表
     * @return 菜单列表
     */
    private List<MenuVO> buildMenuTree(Long parentId, List<SysMenuEntity> menuList) {
        return CollectionUtil.emptyIfNull(menuList)
                .stream()
                .filter(menu -> menu.getParentId().equals(parentId))
                .map(entity -> {
                    MenuVO menuVO = menuConverter.toVo(entity);
                    List<MenuVO> children = buildMenuTree(entity.getId(), menuList);
                    menuVO.setChildren(children);
                    return menuVO;
                }).toList();
    }

    /**
     * 菜单下拉数据
     *
     * @param onlyParent 是否只查询父级菜单
     */
    @Override
    public List<Option<Long>> listMenuOptions(boolean onlyParent) {
        List<SysMenuEntity> menuList = this.list(
                QueryWrapper.create()
                        // .eq(SysMenuEntity::getVisible, true)
                        .orderBy(SysMenuEntity::getSort).asc());
        return buildMenuOptions(SystemConstants.ROOT_NODE_ID, menuList);
    }

    /**
     * 递归生成菜单下拉层级列表
     *
     * @param parentId 父级ID
     * @param menuList 菜单列表
     * @return 菜单下拉列表
     */
    private List<Option<Long>> buildMenuOptions(Long parentId, List<SysMenuEntity> menuList) {
        List<Option<Long>> menuOptions = new ArrayList<>();

        for (SysMenuEntity menu : menuList) {
            if (menu.getParentId().equals(parentId)) {
                Option<Long> option = new Option<>(menu.getId(), menu.getName());
                List<Option<Long>> subMenuOptions = buildMenuOptions(menu.getId(), menuList);
                if (!subMenuOptions.isEmpty()) {
                    option.setChildren(subMenuOptions);
                }
                menuOptions.add(option);
            }
        }

        return menuOptions;
    }

    /**
     * 获取菜单路由列表
     */
    @Override
    public List<RouteVO> getCurrentUserRoutes(SideEnum sideEnum) {
        boolean isRootUser = SecurityUtils.isRoot();
        boolean isMain = SecurityUtils.isMain();
        Long companyId = SecurityUtils.getCompanyId();

        List<RouteBO> menuList;
        // * 如果超级管理员，则获取所有超管菜单
        if (isRootUser) {
            menuList = this.listAs(
                    QueryWrapper.create().from(SysMenuEntity.class)
                            .in(SysMenuEntity::getScene, Arrays.asList(PermissionSceneEnum.PC_ADMIN.getValue(), PermissionSceneEnum.COMMON.getValue()))
                            .orderBy(SysMenuEntity::getSort).asc(),
                    RouteBO.class);

            return buildRoutes(SystemConstants.ROOT_NODE_ID, menuList);
        }

        // * 如果主账号，则获取所有商户菜单、pda、小程序
        if (isMain) {
            List<Integer> sceneList = getSceneList(sideEnum, companyId);
            menuList = this.listAs(
                    QueryWrapper.create().from(SysMenuEntity.class)
                            .in(SysMenuEntity::getScene, sceneList)
                            .orderBy(SysMenuEntity::getSort).asc(),
                    RouteBO.class);

            return buildRoutes(SystemConstants.ROOT_NODE_ID, menuList);
        }

        // * 根据权限返回菜单
        // 查询所有权限标识
        Set<String> roleCodes = SecurityUtils.getRoles();
        List<String> permissionSignsList = new ArrayList<>();
        if(!roleCodes.isEmpty()){
            QueryWrapper permissionSignQuery = QueryWrapper.create().from(SysRoleEntity.class)
                    .leftJoin(SysRolePermissionEntity.class).on(t1 -> {
                        t1.and(SysRoleEntity::getId).eq(SysRolePermissionEntity::getRoleId);
                    })
                    .leftJoin(SysPermissionEntity.class).on(t1 -> {
                        t1.and(SysRolePermissionEntity::getPermissionSign).eq(SysPermissionEntity::getSign);
                    })
                    .select(QueryMethods.column(SysPermissionEntity::getSign))
                    .where(SysPermissionEntity::getType).eq(PermissionTypeEnum.MENU.getValue())
                    .where(SysRoleEntity::getCode).in(roleCodes);
            List<Row> permissionSigns = roleMapper.selectListByQueryAs(permissionSignQuery, Row.class);

            permissionSignsList.addAll(permissionSigns.stream()
                    .map(row -> row.get("sign").toString())
                    .collect(Collectors.toList()));
        }

        // 获取菜单归属
        List<Integer> sceneList = getSceneList(sideEnum, companyId);
        QueryWrapper query = QueryWrapper.create().from(SysMenuEntity.class)
                .in(SysMenuEntity::getScene, sceneList)
                .where(q -> {
                    if (permissionSignsList.isEmpty()) {
                        q.where(SysMenuEntity::getPermissionSign).isNull()
                                .or(SysMenuEntity::getPermissionSign).eq("");
                    } else {
                        q.where(SysMenuEntity::getPermissionSign).in(permissionSignsList)
                                .or(SysMenuEntity::getPermissionSign).isNull()
                                .or(SysMenuEntity::getPermissionSign).eq("");
                    }
                })
                .orderBy(SysMenuEntity::getSort).asc();

        menuList = this.listAs(query, RouteBO.class);

        return buildRoutes(SystemConstants.ROOT_NODE_ID, menuList);
    }

    @NotNull
    private static List<Integer> getSceneList(SideEnum sideEnum, Long companyId) {
        List<Integer> sceneList = new ArrayList<>(List.of(PermissionSceneEnum.COMMON.getValue()));
        if (companyId.equals(1L) && sideEnum.equals(SideEnum.PC)) {
            sceneList.add(PermissionSceneEnum.PC_ADMIN.getValue());
        }else if (!companyId.equals(1L) && sideEnum.equals(SideEnum.PC)) {
            sceneList.add(PermissionSceneEnum.PC_MERCHANT.getValue());
        }else if (sideEnum.equals(SideEnum.MINI_APP)) {
            sceneList = List.of(PermissionSceneEnum.MINI_PROGRAM.getValue());
        }else if (sideEnum.equals(SideEnum.PDA)) {
            sceneList = List.of(PermissionSceneEnum.PDA.getValue());
        }
        return sceneList;
    }

    /**
     * 递归生成菜单路由层级列表
     *
     * @param parentId 父级ID
     * @param menuList 菜单列表
     * @return 路由层级列表
     */
    private List<RouteVO> buildRoutes(Long parentId, List<RouteBO> menuList) {
        List<RouteVO> routeList = new ArrayList<>();

        for (RouteBO menu : menuList) {
            if (menu.getParentId().equals(parentId)) {
                RouteVO routeVO = toRouteVo(menu);
                List<RouteVO> children = buildRoutes(menu.getId(), menuList);
                if (!children.isEmpty()) {
                    routeVO.setChildren(children);
                }
                routeList.add(routeVO);
            }
        }

        return routeList;
    }

    /**
     * 根据RouteBO创建RouteVO
     */
    private RouteVO toRouteVo(RouteBO routeBO) {
        RouteVO routeVO = new RouteVO();
        // 获取路由名称
        // String routeName = routeBO.getName();
        // if (StrUtil.isBlank(routeName)) {
        //     // 路由 name 需要驼峰，首字母大写
        //     routeName = StringUtils.capitalize(StrUtil.toCamelCase(routeBO.getRoutePath(), '-'));
        // }
        // 根据name路由跳转 this.$router.push({name:xxx})
        // routeVO.setName(routeName);
        routeVO.setName(routeBO.getSign());

        // 根据path路由跳转 this.$router.push({path:xxx})
        routeVO.setPath(routeBO.getRoutePath());
        routeVO.setRedirect(routeBO.getRedirect());
        routeVO.setComponent(routeBO.getComponent());

        RouteVO.Meta meta = new RouteVO.Meta();
        meta.setTitle(routeBO.getName());
        meta.setIcon(routeBO.getIcon());
        meta.setImage(routeBO.getImage());
        meta.setHighlight(routeBO.getHighlight());
        meta.setActive(routeBO.getHighlight());
        meta.setHidden(StatusEnum.DISABLE.getValue().equals(routeBO.getVisible() ? 1 : 0));
        routeVO.setMeta(meta);
        return routeVO;
    }

    /**
     * 验证菜单层级
     *
     * @param parentId 父级ID
     * @return 是否通过验证
     */
    private void validateMenuLevel(Long parentId) {
        if (SystemConstants.ROOT_NODE_ID.equals(parentId)) {
            return;
        }
        
        SysMenuEntity parent = this.getById(parentId);
        if (parent == null) {
            throw new RuntimeException("父级菜单不存在");
        }
        
        // 计算父级菜单的层级
        String[] parentTreePath = parent.getTreePath().split(",");
        int parentLevel = parentTreePath.length;
        
        if (parentLevel > 2) { // 父级已经是3级菜单
            throw new RuntimeException("菜单最多支持3级层级结构");
        }
    }

    /**
     * 验证同级同场景菜单名称唯一性
     *
     * @param menuForm 菜单表单
     */
    private void validateMenuNameUnique(MenuForm menuForm) {
        SysMenuEntity existMenu = this.getOne(
                QueryWrapper.create()
                        .select(column(SysMenuEntity::getId))
                        .where(SysMenuEntity::getParentId).eq(menuForm.getParentId())
                        .and(SysMenuEntity::getScene).eq(menuForm.getScene())
                        .and(SysMenuEntity::getName).eq(menuForm.getName())
                        .and(SysMenuEntity::getId).ne(menuForm.getId(), menuForm.getId() != null));

        if (existMenu != null) {
            throw new RuntimeException("同级菜单下已存在相同名称的菜单");
        }
    }

    /**
     * 验证路由路径唯一性
     *
     * @param menuForm 菜单表单
     * @param scene 场景
     */
    private void validateRoutePathUnique(MenuForm menuForm, Integer scene) {
        if (StrUtil.isBlank(menuForm.getRoutePath())) {
            return;
        }

        SysMenuEntity existMenu = this.getOne(
                QueryWrapper.create()
                        .select(column(SysMenuEntity::getId))
                        .where(SysMenuEntity::getRoutePath).eq(menuForm.getRoutePath())
                        .and(SysMenuEntity::getScene).eq(scene)
                        .and(SysMenuEntity::getId).ne(menuForm.getId(), menuForm.getId() != null));

        if (existMenu != null) {
            throw new RuntimeException("当前场景下菜单路径已存在");
        }
    }

    /**
     * 新增/修改菜单
     */
    @Override
    @Transactional
    @CacheEvict(cacheNames = "menu", key = "'routes'")
    public boolean saveMenu(MenuForm menuForm) {
        if (Objects.equals(menuForm.getParentId(), menuForm.getId())) {
            throw new RuntimeException("父级菜单不能为当前菜单");
        }

        // 检查父级菜单是否为当前菜单的子菜单
        if (menuForm.getParentId() != null) {
            SysMenuEntity entity = this.getOne(
                    this.query()
                            .where(SysMenuEntity::getId).eq(menuForm.getParentId())
                            .where(QueryMethods.raw("find_in_set(?, tree_path)", menuForm.getId())));

            Assert.isNull(entity, "父级菜单不能为当前菜单的子菜单");
        } else {
            // 如果父级菜单为空，则设置为根节点
            menuForm.setParentId(SystemConstants.ROOT_NODE_ID);
        }

        // 验证菜单层级
        validateMenuLevel(menuForm.getParentId());
        
        // 验证同级同场景菜单名称唯一性
        validateMenuNameUnique(menuForm);
        
        // 验证路由路径唯一性（同一场景下）
        validateRoutePathUnique(menuForm, menuForm.getScene());

        SysMenuEntity entity = menuConverter.toEntity(menuForm);
        String treePath = generateMenuTreePath(menuForm.getParentId());
        entity.setTreePath(treePath);

        // 处理清空权限的操作
        if(menuForm.getPermissionSign() == null){
            entity.setPermissionSign("");
        }

        boolean result = this.saveOrUpdate(entity);
        if (result) {
            // 编辑刷新角色权限缓存
            if (menuForm.getId() != null) {
                roleMenuService.refreshRolePermsCache();
            }
        }
        // 修改菜单如果有子菜单，则更新子菜单的树路径
        updateChildrenTreePath(entity.getId(), treePath);

        // 记录操作日志
        OpLogUtils.appendOpLog("菜单管理-新增菜单", "新增菜单: " + menuForm.getName(), menuForm);

        return result;
    }

    /**
     * 更新子菜单树路径
     *
     * @param id       当前菜单ID
     * @param treePath 当前菜单树路径
     */
    private void updateChildrenTreePath(Long id, String treePath) {
        List<SysMenuEntity> children = this.list(
                QueryWrapper.create().eq(SysMenuEntity::getParentId, id));
        if (CollectionUtil.isNotEmpty(children)) {
            // 子菜单的树路径等于父菜单的树路径加上父菜单ID
            String childTreePath = treePath + "," + id;
            this.update(
                    new SysMenuEntity().setTreePath(childTreePath),
                    QueryWrapper.create()
                            .eq(SysMenuEntity::getParentId, id));
            for (SysMenuEntity child : children) {
                // 递归更新子菜单
                updateChildrenTreePath(child.getId(), childTreePath);
            }
        }
    }

    /**
     * 部门路径生成
     *
     * @param parentId 父ID
     * @return 父节点路径以英文逗号(, )分割，eg: 1,2,3
     */
    private String generateMenuTreePath(Long parentId) {
        // 如果parentId为null或等于根节点ID，直接返回根节点ID
        if (parentId == null || SystemConstants.ROOT_NODE_ID.equals(parentId)) {
            return String.valueOf(SystemConstants.ROOT_NODE_ID);
        }
        
        // 获取父节点信息
        SysMenuEntity parent = this.getById(parentId);
        return parent != null ? parent.getTreePath() + "," + parent.getId() : String.valueOf(SystemConstants.ROOT_NODE_ID);
    }

    /**
     * 修改菜单显示状态
     *
     * @param menuId  菜单ID
     * @param visible 是否显示(1->显示；0->隐藏)
     * @return 是否修改成功
     */
    @Override
    @Transactional
    @CacheEvict(cacheNames = "menu", key = "'routes'")
    public boolean updateMenuVisible(Long menuId, Integer visible) {
        return this.update(
                new SysMenuEntity().setVisible(visible),
                QueryWrapper.create()
                        .eq(SysMenuEntity::getId, menuId));
    }

    /**
     * 获取菜单表单数据
     *
     * @param id 菜单ID
     * @return 菜单表单数据
     */
    @Override
    public MenuForm getMenuForm(Long id) {
        SysMenuEntity entity = this.getById(id);
        Assert.isTrue(entity != null, "菜单不存在");
        return menuConverter.toForm(entity);
    }

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * @return 是否删除成功
     */
    @Override
    @Transactional
    @CacheEvict(cacheNames = "menu", key = "'routes'")
    public boolean deleteMenu(Long id) {
        // 检查菜单是否存在
        SysMenuEntity menu = this.getById(id);
        Assert.isTrue(menu != null, "菜单不存在");

        boolean result = this.remove(
                QueryWrapper.create()
                        .eq(SysMenuEntity::getId, id)
                        .or(o -> {
                            o.and("CONCAT (',',tree_path,',') LIKE CONCAT('%,'," + id + ",',%')");
                        }));

        // 刷新角色权限缓存
        if (result) {
            roleMenuService.refreshRolePermsCache();
        }

        // 记录操作日志
        OpLogUtils.appendOpLog("菜单管理-删除菜单", "删除菜单: " + menu.getName(), 
            Map.of("删除的菜单", menu));

        return result;
    }

    /**
     * 代码生成时添加菜单
     *
     * @param parentMenuId 父菜单ID
     * @param genConfig    实体名称
     */
    @Override
    public void addMenuForCodegen(Long parentMenuId, GenConfig genConfig) {
        SysMenuEntity parentMenu = this.getById(parentMenuId);
        Assert.notNull(parentMenu, "上级菜单不存在");

        String entityName = genConfig.getEntityName();

        // 获取父级菜单子菜单最带的排序
        SysMenuEntity maxSortMenu = this.getOne(QueryWrapper.create().eq(SysMenuEntity::getParentId, parentMenuId)
                .orderBy(SysMenuEntity::getSort)
                .desc()
                .limit(1));
        int sort = 1;
        if (maxSortMenu != null) {
            sort = maxSortMenu.getSort() + 1;
        }

        SysMenuEntity menu = new SysMenuEntity();
        menu.setParentId(parentMenuId);
        menu.setName(genConfig.getBusinessName());
        menu.setRoutePath(StrUtil.toSymbolCase(entityName, '-'));
        menu.setComponent(genConfig.getModuleName() + "/" + StrUtil.toSymbolCase(entityName, '-') + "/index");
        menu.setSort(sort);
        menu.setVisible(1);
        boolean result = this.save(menu);

        if (result) {
            // 生成treePath
            String treePath = generateMenuTreePath(parentMenuId);
            menu.setTreePath(treePath);
            this.updateById(menu);
        }
    }

    @Override
    public List<Option<String>> listPermissionOptions() {
        // 查询菜单类型的权限
        List<SysPermissionEntity> permissions = permissionMapper.selectListByQuery(QueryWrapper.create()
                .select(
                        QueryMethods.column(SysPermissionEntity::getId),
                        QueryMethods.column(SysPermissionEntity::getParentId),
                        QueryMethods.column(SysPermissionEntity::getName),
                        QueryMethods.column(SysPermissionEntity::getSign))
                .where(SysPermissionEntity::getType).eq(PermissionTypeEnum.MENU.getValue())
                .orderBy(SysPermissionEntity::getSort).asc());

        // 构建树形结构
        return buildPermissionTree(permissions);
    }

    /**
     * 构建权限树
     */
    private List<Option<String>> buildPermissionTree(List<SysPermissionEntity> permissions) {
        List<Option<String>> options = new ArrayList<>();
        Map<Long, List<SysPermissionEntity>> parentMap = permissions.stream()
                .collect(Collectors.groupingBy(SysPermissionEntity::getParentId));

        // 获取根节点
        List<SysPermissionEntity> rootPermissions = parentMap.getOrDefault(0L, Collections.emptyList());

        // 递归构建树
        for (SysPermissionEntity permission : rootPermissions) {
            Option<String> option = new Option<>();
            option.setValue(permission.getSign());
            option.setLabel(permission.getName());
            option.setChildren(buildChildren(permission.getId(), parentMap));
            options.add(option);
        }

        return options;
    }

    /**
     * 递归构建子节点
     */
    private List<Option<String>> buildChildren(Long parentId, Map<Long, List<SysPermissionEntity>> parentMap) {
        List<SysPermissionEntity> children = parentMap.getOrDefault(parentId, Collections.emptyList());
        if (children.isEmpty()) {
            return null;
        }

        List<Option<String>> childOptions = new ArrayList<>();
        for (SysPermissionEntity child : children) {
            Option<String> option = new Option<>();
            option.setValue(child.getSign());
            option.setLabel(child.getName());
            option.setChildren(buildChildren(child.getId(), parentMap));
            childOptions.add(option);
        }

        return childOptions;
    }
}
