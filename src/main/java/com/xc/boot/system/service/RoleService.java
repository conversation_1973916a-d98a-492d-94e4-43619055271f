package com.xc.boot.system.service;


import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.common.model.Option;
import com.xc.boot.system.model.entity.SysPermissionEntity;
import com.xc.boot.system.model.entity.SysRoleEntity;
import com.xc.boot.system.model.form.RoleForm;
import com.xc.boot.system.model.query.RolePageQuery;
import com.xc.boot.system.model.vo.RolePageVO;
import com.xc.boot.system.model.vo.RolePermissionVo;

import java.util.List;

/**
 * 角色业务接口层
 *
 * <AUTHOR>
 * @since 2022/6/3
 */
public interface RoleService extends IService<SysRoleEntity> {

    /**
     * 角色分页列表
     *
     * @param queryParams
     * @return
     */
    Page<RolePageVO> getRolePage(RolePageQuery queryParams);


    /**
     * 角色下拉列表
     *
     * @return
     */
    List<Option<Long>> listRoleOptions();

    /**
     * 新增角色
     * @param roleForm
     * @return
     */
    boolean saveRole(RoleForm roleForm);


    /**
     * 批量删除角色
     *
     * @param ids 角色ID，多个使用英文逗号(,)分割
     * @return
     */
    boolean deleteRoles(String ids);


    /**
     * 修改角色
     * @param roleForm
     * @return
     */
    boolean updateRole(RoleForm roleForm);

    /**
     * 当前用户权限树
     * @return
     */
    RolePermissionVo getCurUserPermTree();

    /**
     * 当前用户权限列表
     * @return
     */
    List<SysPermissionEntity> getCurUserPermList();

}
