package com.xc.boot.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.logicdelete.LogicDeleteManager;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.system.mapper.PdaPackageMapper;
import com.xc.boot.system.model.entity.PdaPackageEntity;
import com.xc.boot.system.model.enums.PdaEnum;
import com.xc.boot.system.model.form.PdaPackageForm;
import com.xc.boot.system.model.query.PdaPackagePageQuery;
import com.xc.boot.system.model.vo.PdaPackageVo;
import com.xc.boot.system.service.PdaPackageService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.xc.boot.system.model.entity.table.PdaPackageTableDef.PDA_PACKAGE;

/**
 * PDA包管理服务实现类
 */
@Service
@RequiredArgsConstructor
public class PdaPackageServiceImpl extends ServiceImpl<PdaPackageMapper, PdaPackageEntity> implements PdaPackageService {

    @Override
    public Page<PdaPackageVo> page(PdaPackagePageQuery query) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(PdaPackageEntity::getDeletedAt).isNull()
                .where(PdaPackageEntity::getType).eq(query.getType(), query.getType() != null)
                .where(PdaPackageEntity::getVersion).eq(query.getVersion(), query.getVersion() != null)
                .orderBy(PdaPackageEntity::getId, false);
        Page<PdaPackageVo> page = this.pageAs(new Page<>(query.getPageNum(), query.getPageSize()), queryWrapper, PdaPackageVo.class);
        page.getRecords().forEach(e -> e.setTypeName(PdaEnum.getLabel(e.getType())));
        return page;
    }

    @Override
    public void create(PdaPackageForm form) {
        // 检查设备类型和版本号是否已存在
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(PdaPackageEntity::getType).eq(form.getType())
                .where(PdaPackageEntity::getVersion).eq(form.getVersion())
                .where(PdaPackageEntity::getDeletedAt).isNull();
        long count = this.count(queryWrapper);
        Assert.isTrue(count == 0, "该设备类型和版本号已存在");
        PdaPackageEntity entity = BeanUtil.copyProperties(form, PdaPackageEntity.class);
        String image = LogicDeleteManager.execWithoutLogicDelete(() -> this.mapper.selectOneByQueryAs(QueryWrapper.create()
                .where(PDA_PACKAGE.TYPE.eq(form.getType()))
                .where(PDA_PACKAGE.IMAGE.isNotNull())
                .where(PDA_PACKAGE.IMAGE.ne(""))
                .select(PDA_PACKAGE.IMAGE), String.class));
        if (StringUtils.isNotBlank(image)) {
            entity.setImage(image);
        }
        this.save(entity);
        CommonUtils.updateFileStatus(form.getFileId(), 1);
        OpLogUtils.appendOpLog("系统管理-新增pda包", String.format("""
                类型: %s
                版本号: %s""", PdaEnum.getLabel(form.getType()), form.getVersion()), entity);
    }

    @Override
    public void update(PdaPackageForm form) {
        PdaPackageEntity entity = this.getById(form.getId());
        Assert.notNull(entity, "PDA包不存在");
        // 检查设备类型和版本号是否已存在（排除当前记录）
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(PdaPackageEntity::getType).eq(form.getType())
                .where(PdaPackageEntity::getVersion).eq(form.getVersion())
                .where(PdaPackageEntity::getId).ne(form.getId())
                .where(PdaPackageEntity::getDeletedAt).isNull();
        long count = this.count(queryWrapper);
        Long oldId = entity.getFileId();
        Assert.isTrue(count == 0, "该设备类型和版本号已存在");
        BeanUtil.copyProperties(form, entity);
        this.updateById(entity);
        if (!oldId.equals(form.getFileId())) {
            CommonUtils.updateFileStatus(oldId, 0);
            CommonUtils.updateFileStatus(form.getFileId(), 1);
        }
        OpLogUtils.appendOpLog("系统管理-修改pda包", String.format("""
                类型: %s
                版本号: %s""", PdaEnum.getLabel(form.getType()), form.getVersion()), entity);
    }

    @Override
    public void delete(Long id) {
        PdaPackageEntity entity = this.getById(id);
        Assert.notNull(entity, "PDA包不存在");
        this.mapper.delete(entity);
        CommonUtils.updateFileStatus(entity.getFileId(), 0);
        OpLogUtils.appendOpLog("系统管理-删除pda包", String.format("""
                类型: %s
                版本号: %s""", PdaEnum.getLabel(entity.getType()), entity.getVersion()), null);
    }

    @Override
    public PdaPackageVo getLatestActivePackageByType(String type, String version) {
        // 查询所有type匹配、未删除、已生效的包，按version降序
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(PdaPackageEntity::getType).eq(type)
                .where(PdaPackageEntity::getDeletedAt).isNull()
                .where(PdaPackageEntity::getActiveAt).le(new java.util.Date());
        List<PdaPackageEntity> list = this.list(queryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        // 找到最新的version
        PdaPackageEntity latest = list.stream()
                .max((o1, o2) -> compareVersion(o1.getVersion(), o2.getVersion()))
                .orElse(null);
        if (latest == null) {
            return null;
        }
        // 如果数据库最新version大于传入version，返回最新包，否则返回null
        if (compareVersion(latest.getVersion(), version) > 0) {
            return BeanUtil.copyProperties(latest, PdaPackageVo.class);
        }
        return null;
    }

    /**
     * 版本号比较，返回1表示v1>v2，0表示相等，-1表示v1<v2
     */
    private static int compareVersion(String v1, String v2) {
        if (v1 == null && v2 == null) return 0;
        if (v1 == null) return -1;
        if (v2 == null) return 1;
        String[] arr1 = v1.split("\\.");
        String[] arr2 = v2.split("\\.");
        int len = Math.max(arr1.length, arr2.length);
        for (int i = 0; i < len; i++) {
            int n1 = i < arr1.length ? parseInt(arr1[i]) : 0;
            int n2 = i < arr2.length ? parseInt(arr2[i]) : 0;
            if (n1 != n2) {
                return Integer.compare(n1, n2);
            }
        }
        return 0;
    }

    private static int parseInt(String s) {
        try {
            return Integer.parseInt(s);
        } catch (Exception e) {
            return 0;
        }
    }
} 