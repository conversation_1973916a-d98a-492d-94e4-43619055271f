package com.xc.boot.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.system.mapper.PrintTagMapper;
import com.xc.boot.system.mapper.PrintTagTemplateMapper;
import com.xc.boot.system.model.dto.PrintTagTemplateConfigDTO;
import com.xc.boot.system.model.dto.PrintTagTemplateFormDTO;
import com.xc.boot.system.model.entity.PrintTagEntity;
import com.xc.boot.system.model.entity.PrintTagTemplateEntity;
import com.xc.boot.system.model.query.PrintTagTemplatePageQuery;
import com.xc.boot.system.model.vo.PrintTagListVO;
import com.xc.boot.system.model.vo.PrintTagTemplatePageVO;
import com.xc.boot.system.service.PrintTagTemplateService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.mybatisflex.core.query.QueryMethods.column;
import static com.xc.boot.system.model.entity.table.PrintTagTemplateTableDef.PRINT_TAG_TEMPLATE;

/**
 * 打印标签模板服务实现类
 */
@Service
@RequiredArgsConstructor
public class PrintTagTemplateServiceImpl extends ServiceImpl<PrintTagTemplateMapper, PrintTagTemplateEntity> implements PrintTagTemplateService {

    private final PrintTagTemplateMapper mapper;
    private final PrintTagMapper printTagMapper;
    private final ListFillService listFillService;

    @Override
    public Page<PrintTagTemplatePageVO> getPrintTagTemplatePage(PrintTagTemplatePageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(PRINT_TAG_TEMPLATE.COMPANY_ID.eq(SecurityUtils.getCompanyId()));
        
        if (StringUtils.isNotBlank(queryParams.getName())) {
            queryWrapper.and(PRINT_TAG_TEMPLATE.NAME.like(queryParams.getName()));
        }
        if (queryParams.getTagId() != null) {
            queryWrapper.and(PRINT_TAG_TEMPLATE.TAG_ID.eq(queryParams.getTagId()));
        }
        if (queryParams.getStatus() != null) {
            queryWrapper.and(PRINT_TAG_TEMPLATE.STATUS.eq(queryParams.getStatus()));
        }
        if (queryParams.getCreatedAt() != null && queryParams.getCreatedAt().size() == 2) {
            queryWrapper.and(PRINT_TAG_TEMPLATE.CREATED_AT.between(queryParams.getCreatedAt().get(0), queryParams.getCreatedAt().get(1)));
        }
        queryWrapper.orderBy(PRINT_TAG_TEMPLATE.CREATED_AT.desc());

        // 执行分页查询
        Page<PrintTagTemplatePageVO> page = mapper.paginateAs(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                queryWrapper,
                PrintTagTemplatePageVO.class
        );

        // 填充标签信息
        if (page != null && page.getRecords() != null) {
            Set<String> tagIds = new HashSet<>();
            page.getRecords().forEach(vo -> {
                if (vo.getTagId() != null) {
                    tagIds.add(vo.getTagId().toString());
                }
            });
            
            Map<String, Map<String, Object>> tagInfoMap = listFillService.getTagNameById(tagIds);
            page.getRecords().forEach(vo -> {
                if (vo.getTagId() != null) {
                    Map<String, Object> tagInfo = tagInfoMap.get(vo.getTagId().toString());
                    if (tagInfo != null) {
                        vo.setTagName((String) tagInfo.get("name"));
                        vo.setTagImage((String) tagInfo.get("image"));
                        vo.setTagType((Integer) tagInfo.get("type"));
                        vo.setTagWidth((Integer) tagInfo.get("width"));
                        vo.setTagHeight((Integer) tagInfo.get("height"));
                        vo.setTagPrintWidth((Integer) tagInfo.get("print_width"));
                        vo.setTagPrintHeight((Integer) tagInfo.get("print_height"));
                    }
                }
            });
        }

        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePrintTagTemplate(PrintTagTemplateFormDTO form) {
        // 获取当前公司ID
        Long companyId = SecurityUtils.getCompanyId();

        // 构建实体对象
        PrintTagTemplateEntity entity = new PrintTagTemplateEntity();
        BeanUtil.copyProperties(form, entity);
        entity.setCompanyId(companyId.intValue());

        // 处理图片状态
        if (entity.getImageId() != null) {
            CommonUtils.updateFileStatus(entity.getImageId().longValue(), 1);
        }

        // 保存或更新
        boolean result;
        if (entity.getId() == null) {
            // 新增时：如果存在tag_id，则需要填充tag的content到template的content
            if (entity.getTagId() != null) {
                PrintTagEntity printTag = printTagMapper.selectOneByQuery(
                    QueryWrapper.create().where(column(PrintTagEntity::getId).eq(entity.getTagId()))
                );
                if (printTag != null && StringUtils.isNotBlank(printTag.getContent())) {
                    entity.setContent(printTag.getContent());
                }
            }

            result = this.save(entity);
            if (result) {
                OpLogUtils.appendOpLog("打印标签模板-新增", "新增打印标签模板", null);
            }
        } else {
            // 检查是否存在
            PrintTagTemplateEntity oldEntity = this.getById(entity.getId());
            if (oldEntity == null || !oldEntity.getCompanyId().equals(companyId.intValue())) {
                CommonUtils.abort("模板不存在");
            }
            result = this.updateById(entity);
            if (result) {
                OpLogUtils.appendOpLog("打印标签模板-编辑", "编辑打印标签模板", null);
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePrintTagTemplate(Long id) {
        // 检查是否存在
        PrintTagTemplateEntity entity = this.getById(id);
        if (entity == null || !entity.getCompanyId().equals(SecurityUtils.getCompanyId().intValue())) {
            CommonUtils.abort("模板不存在");
        }
        // 更新图片状态
        if (entity.getImageId() != null) {
            CommonUtils.updateFileStatus(entity.getImageId().longValue(), 0);
        }
        // 删除
        boolean result = this.removeById(id);
        if (result) {
            OpLogUtils.appendOpLog("打印标签模板-删除", "删除打印标签模板", null);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean configPrintTagTemplate(PrintTagTemplateConfigDTO config) {
        // 检查是否存在
        PrintTagTemplateEntity entity = this.getById(config.getId());
        if (entity == null || !entity.getCompanyId().equals(SecurityUtils.getCompanyId().intValue())) {
            CommonUtils.abort("模板不存在");
        }

        // 更新图片状态
        if (entity.getImageId() != null) {
            CommonUtils.updateFileStatus(entity.getImageId().longValue(), 0);
        }
        if (config.getImageId() != null) {
            CommonUtils.updateFileStatus(config.getImageId().longValue(), 1);
        }

        // 更新配置
        entity.setImage(config.getImage());
        entity.setImageId(config.getImageId());
        entity.setContent(config.getContent());

        boolean result = this.updateById(entity);
        if (result) {
            OpLogUtils.appendOpLog("打印标签模板-配置", "配置打印标签模板", null);
        }
        return result;
    }

    @Override
    public List<PrintTagListVO> getPrintTagList() {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(
                column(PrintTagEntity::getId),
                column(PrintTagEntity::getName),
                column(PrintTagEntity::getImage),
                column(PrintTagEntity::getImageId),
                column(PrintTagEntity::getContent),
                column(PrintTagEntity::getType),
                column(PrintTagEntity::getWidth),
                column(PrintTagEntity::getHeight),
                column(PrintTagEntity::getPrintWidth),
                column(PrintTagEntity::getPrintHeight)
            )
            .where(PrintTagEntity::getEnabled).eq(1)
            .orderBy(column(PrintTagEntity::getId).desc());
        return printTagMapper.selectListByQueryAs(queryWrapper, PrintTagListVO.class);
    }
} 