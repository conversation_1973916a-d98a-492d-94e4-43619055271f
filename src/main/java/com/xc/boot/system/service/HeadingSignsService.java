package com.xc.boot.system.service;

import com.mybatisflex.core.service.IService;
import com.xc.boot.system.model.entity.HeadingSignsEntity;
import com.xc.boot.system.model.vo.TableHeading;

public interface HeadingSignsService extends IService<HeadingSignsEntity> {

    /**
     * 获取当前用户表头配置
     * @param sign
     * @return
     */
    TableHeading getTableHeading(String sign, Integer templateId);

    /**
     * 更新用户表头缓存
     * @param tableHeading
     * @return
     */
    boolean saveTableHeadingCache(TableHeading tableHeading);

}
