package com.xc.boot.system.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.system.mapper.UserNoticeMapper;
import com.xc.boot.system.model.entity.SysUserNoticeEntity;
import com.xc.boot.system.service.UserNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 用户公告状态服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-28 16:56
 */
@Service
@RequiredArgsConstructor
public class UserNoticeServiceImpl extends ServiceImpl<UserNoticeMapper, SysUserNoticeEntity> implements UserNoticeService {

    /**
     * 全部标记为已读
     *
     * @return 是否成功
     */
    @Override
    public boolean readAll() {
        Long userId = SecurityUtils.getUserId();
        return this.update(
                new SysUserNoticeEntity().setIsRead(1),
                QueryWrapper.create()
                        .eq(SysUserNoticeEntity::getUserId, userId)
                        .eq(SysUserNoticeEntity::getIsRead, 0)
        );
    }

}
