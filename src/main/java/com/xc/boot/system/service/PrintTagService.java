package com.xc.boot.system.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.system.model.entity.PrintTagEntity;
import com.xc.boot.system.model.query.PrintTagPageQuery;
import com.xc.boot.system.model.vo.PrintTagPageVO;
import com.xc.boot.system.model.dto.PrintTagConfigDTO;
import com.xc.boot.system.model.dto.PrintTagFormDTO;

/**
 * 打印标签服务接口
 */
public interface PrintTagService extends IService<PrintTagEntity> {

    /**
     * 获取打印标签分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<PrintTagPageVO> getPrintTagPage(PrintTagPageQuery queryParams);

    /**
     * 新增/编辑打印标签
     */
    boolean savePrintTag(PrintTagFormDTO form);

    /**
     * 删除打印标签
     */
    boolean deletePrintTag(Long id);

    /**
     * 配置打印标签
     *
     * @param form 打印标签配置表单数据
     * @return 是否成功
     */
    boolean configPrintTag(PrintTagConfigDTO form);
} 