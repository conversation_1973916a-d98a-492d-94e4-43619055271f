package com.xc.boot.system.service;

import com.mybatisflex.core.service.IService;
import com.xc.boot.common.model.Option;
import com.xc.boot.system.model.entity.SysPermissionEntity;
import com.xc.boot.system.model.form.PermissionForm;
import com.xc.boot.system.model.query.PermissionQuery;
import com.xc.boot.system.model.vo.PermissionVO;

import java.util.List;

/**
 * 权限服务接口
 */
public interface PermissionService extends IService<SysPermissionEntity> {

    /**
     * 获取权限列表
     *
     * @param queryParams 查询参数
     * @return 权限列表
     */
    List<PermissionVO> listPermissions(PermissionQuery queryParams);

    /**
     * 获取权限下拉选项
     *
     * @return 权限下拉选项
     */
    List<Option<Long>> listPermissionOptions();

    /**
     * 保存权限
     *
     * @param permissionForm 权限表单
     * @return 是否成功
     */
    boolean savePermission(PermissionForm permissionForm);

    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 是否成功
     */
    boolean deletePermission(Long id);

    /**
     * 转换为权限视图对象
     * @param permission
     * @return
     */
    PermissionVO convertToVO(SysPermissionEntity permission);

    /**
     * 构建权限树
     * @param list
     * @return
     */
    List<PermissionVO> buildTree(List<PermissionVO> list);
} 