package com.xc.boot.system.service;


import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.common.model.Option;
import com.xc.boot.system.model.dto.UserAuthInfo;
import com.xc.boot.system.model.dto.UserExportDTO;
import com.xc.boot.system.model.entity.SysUserEntity;
import com.xc.boot.system.model.form.PasswordChangeForm;
import com.xc.boot.system.model.form.UserForm;
import com.xc.boot.system.model.form.UserProfileForm;
import com.xc.boot.system.model.form.UserUpdateForm;
import com.xc.boot.system.model.query.UserPageQuery;
import com.xc.boot.system.model.vo.UserInfoVO;
import com.xc.boot.system.model.vo.UserPageVO;
import com.xc.boot.system.model.vo.UserProfileVO;

import java.util.List;

/**
 * 用户业务接口
 *
 * <AUTHOR>
 * @since 2022/1/14
 */
public interface UserService extends IService<SysUserEntity> {

    /**
     * 用户分页列表
     *
     */
    Page<UserPageVO> getUserPage(UserPageQuery queryParams);

    /**
     * 新增用户
     * @param form 用户表单对象
     */
    boolean saveUser(UserUpdateForm form);

    /**
     * 修改用户
     *
     * @param userForm 用户表单对象
     */
    boolean updateMe(UserForm userForm);


    /**
     * 删除用户
     *
     * @param idsStr 用户ID，多个以英文逗号(,)分割
     */
    boolean deleteUsers(String idsStr);


    /**
     * 根据用户名获取认证信息
     *
     * @param username 用户名
     */

    UserAuthInfo getUserAuthInfo(String username);


    /**
     * 获取导出用户列表
     *
     * @param queryParams 查询参数
     */
    @SuppressWarnings("unused")
    List<UserExportDTO> listExportUsers(UserPageQuery queryParams);


    /**
     * 获取登录用户信息
     *
     */
    UserInfoVO getCurrentUserInfo();

    /**
     * 获取个人中心用户信息
     *
     */
    @SuppressWarnings("unused")
    UserProfileVO getUserProfile(Long userId);

    /**
     * 修改个人中心用户信息
     *
     * @param formData 表单数据
     */
    @SuppressWarnings("unused")
    boolean updateUserProfile(UserProfileForm formData);

    /**
     * 修改用户密码
     *
     * @param userId 用户ID
     * @param data   修改密码表单数据
     */
    boolean changePassword(Long userId, PasswordChangeForm data);

    /**
     * 重置用户密码
     *
     */
    boolean resetPassword(PasswordChangeForm data);

    /**
     * 发送验证码
     *
     * @param contact 联系方式
     * @param type    联系方式类型
     */
    boolean sendVerificationCode(String contact, Integer type);

    /**
     * 修改当前用户手机号
     */
    boolean bindMobile(String username, String code, String sideCode);

    /**
     * 获取用户选项列表
     *
     */
    List<Option<String>> listUserOptions();

    /**
     * 根据 openid 获取用户认证信息
     *
     * @param username 用户名
     */

    UserAuthInfo getUserAuthInfoByOpenId(String username);

    /**
     * 修改用户信息
     */
    boolean update(UserUpdateForm updateForm);

    /**
     * 修改手机号-验证登录密码
     */
    boolean verifyPassword(String password);

    /**
     * 验证验证码
     */
    void verifyCode(String mobile, String code, Integer isLogin, String sideCode);

    /**
     * 登录密码错误处理
     * @param username 用户名
     * @param prefix 计数缓存前缀
     */
    void loginPassErrorHandle(String username, String prefix);

    /**
     * 根据用户ID获取用户名
     * @param userId 用户ID
     */
    String getUserName(Long userId);
}
