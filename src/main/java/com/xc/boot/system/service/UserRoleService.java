package com.xc.boot.system.service;


import com.mybatisflex.core.service.IService;
import com.xc.boot.system.model.entity.SysUserRoleEntity;

import java.util.List;
import java.util.Set;

public interface UserRoleService extends IService<SysUserRoleEntity> {

    /**
     * 保存用户角色
     *
     * @param userId
     * @param roleIds
     * @return
     */
    boolean saveUserRoles(Long userId, List<Long> roleIds);

    /**
     * 判断角色是否存在绑定的用户
     *
     * @param roleId 角色ID
     * @return true：已分配 false：未分配
     */
    boolean hasAssignedUsers(Long roleId);

    /**
     * 获取用户角色列表
     * @param userId
     * @return
     */
    Set<String> getUserRoles(Long userId);
}
