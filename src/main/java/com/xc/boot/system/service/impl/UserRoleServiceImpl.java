package com.xc.boot.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.system.mapper.UserRoleMapper;
import com.xc.boot.system.model.entity.SysRoleEntity;
import com.xc.boot.system.model.entity.SysUserEntity;
import com.xc.boot.system.model.entity.SysUserRoleEntity;
import com.xc.boot.system.service.UserRoleService;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, SysUserRoleEntity> implements UserRoleService {

    /**
     * 保存用户角色
     *
     * @param userId
     * @param roleIds
     * @return
     */
    @Override
    public boolean saveUserRoles(Long userId, List<Long> roleIds) {

        if (userId == null || CollectionUtil.isEmpty(roleIds)) {
            return false;
        }

        // 用户原角色ID集合
        List<Long> userRoleIds = this.list(
                        QueryWrapper.create()
                                .eq(SysUserRoleEntity::getUserId, userId))
                .stream()
                .map(SysUserRoleEntity::getRoleId)
                .collect(Collectors.toList());

        // 新增用户角色
        List<Long> saveRoleIds;
        if (CollectionUtil.isEmpty(userRoleIds)) {
            saveRoleIds = roleIds;
        } else {
            saveRoleIds = roleIds.stream()
                    .filter(roleId -> !userRoleIds.contains(roleId))
                    .collect(Collectors.toList());
        }

        List<SysUserRoleEntity> saveUserRoles = saveRoleIds
                .stream()
                .map(roleId -> new SysUserRoleEntity().setRoleId(roleId).setUserId(userId))
                .collect(Collectors.toList());
        this.saveBatch(saveUserRoles);

        // 删除用户角色
        if (CollectionUtil.isNotEmpty(userRoleIds)) {
            List<Long> removeRoleIds = userRoleIds.stream()
                    .filter(roleId -> !roleIds.contains(roleId))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(removeRoleIds)) {
                this.remove(
                        QueryWrapper.create()
                        .eq(SysUserRoleEntity::getUserId, userId)
                        .in(SysUserRoleEntity::getRoleId, removeRoleIds)
                );
            }
        }
        return true;

    }

    /**
     * 判断角色是否存在绑定的用户
     *
     * @param roleId 角色ID
     * @return true：已分配 false：未分配
     */
    @Override
    public boolean hasAssignedUsers(Long roleId) {
        long count = this.count(
                QueryWrapper.create().from(SysUserRoleEntity.class)
                        .leftJoin(SysRoleEntity.class).on(q->q.and(SysRoleEntity::getId).eq(SysUserRoleEntity::getRoleId))
                        .leftJoin(SysUserEntity.class).on(q->q.and(SysUserRoleEntity::getUserId).eq(SysUserEntity::getId))
                        .where(SysUserRoleEntity::getRoleId).eq(roleId)

        );
        return count > 0;
    }

    @Override
    public Set<String> getUserRoles(Long userId) {
        List<String> roles = this.mapper.selectListByQueryAs(QueryWrapper.create()
                .leftJoin(SysRoleEntity.class)
                .on(QueryMethods.column(SysUserRoleEntity::getRoleId).eq(QueryMethods.column(SysRoleEntity::getId)))
                .where(SysUserRoleEntity::getUserId).eq(userId)
                .where(SysRoleEntity::getStatus).eq(1)
                .where(SysRoleEntity::getCode).isNotNull()
                .select(QueryMethods.column(SysRoleEntity::getCode)), String.class);
        return new HashSet<>(roles);
    }
}
