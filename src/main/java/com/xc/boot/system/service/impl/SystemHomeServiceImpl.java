package com.xc.boot.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.date.LocalDateTimeUtil;

import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.merchant.mapper.GoldPriceMapper;
import com.xc.boot.modules.merchant.mapper.QualityMapper;
import com.xc.boot.modules.merchant.model.entity.GoldPriceEntity;
import com.xc.boot.modules.merchant.model.entity.QualityEntity;
import com.xc.boot.system.mapper.CompanyMapper;
import com.xc.boot.system.mapper.FileMapper;
import com.xc.boot.system.mapper.MenuMapper;
import com.xc.boot.system.mapper.MerchantMapper;
import com.xc.boot.system.mapper.NoticeMapper;
import com.xc.boot.system.model.entity.CompanyEntity;

import com.xc.boot.system.model.entity.SysMenuEntity;
import com.xc.boot.system.model.entity.SysNoticeEntity;
import com.xc.boot.system.model.entity.SysPermissionEntity;
import com.xc.boot.system.model.vo.HomeDataVO;
import com.xc.boot.system.service.RoleService;
import com.xc.boot.system.service.SystemHomeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
import static com.xc.boot.modules.merchant.model.entity.table.GoldPriceTableDef.GOLD_PRICE;
import static com.xc.boot.modules.merchant.model.entity.table.QualityTableDef.QUALITY;

import static com.xc.boot.system.model.entity.table.FileTableDef.FILE;
import static com.xc.boot.system.model.entity.table.MerchantTableDef.MERCHANT;
import static com.xc.boot.system.model.entity.table.SysMenuTableDef.SYS_MENU;
import static com.xc.boot.system.model.entity.table.SysNoticeTableDef.SYS_NOTICE;

/**
 * 系统首页服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemHomeServiceImpl implements SystemHomeService {

    private final GoldPriceMapper goldPriceMapper;
    private final QualityMapper qualityMapper;
    private final MenuMapper menuMapper;
    private final RoleService roleService;
    private final NoticeMapper noticeMapper;
    private final CompanyMapper companyMapper;
    private final MerchantMapper merchantMapper;
    private final GoodsMapper goodsMapper;
    private final FileMapper fileMapper;

    // 图片文件扩展名
    private static final Set<String> IMAGE_EXTENSIONS = Set.of("jpg", "jpeg", "png", "gif", "bmp", "webp");

    @Override
    public HomeDataVO getHomeData() {
        HomeDataVO homeData = new HomeDataVO();
        
        // 获取今日金价
        homeData.setGoldPrices(getGoldPrices());
        
        // 获取快捷入库菜单
        homeData.setQuickMenus(getQuickMenus());
        
        // 获取系统公告
        homeData.setSystemNotices(getSystemNotices());
        
        // 获取系统概况
        homeData.setSystemOverview(getSystemOverview());
        
        return homeData;
    }

    /**
     * 获取今日金价
     */
    private List<HomeDataVO.GoldPriceVO> getGoldPrices() {
        Long companyId = SecurityUtils.getCompanyId();

        // 查询生效中的金价，按大类分组，每个大类只取一个
        List<GoldPriceEntity> goldPrices = goldPriceMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GOLD_PRICE.COMPANY_ID.eq(companyId))
                .where(GOLD_PRICE.STATUS.eq(1)) // 生效中状态
                .orderBy(GOLD_PRICE.CATEGORY_ID, true)
                .orderBy(GOLD_PRICE.UPDATED_AT, false)
        );

        if (CollectionUtil.isEmpty(goldPrices)) {
            return new ArrayList<>();
        }

        // 按大类分组，每个大类只取第一个（最新的）
        List<GoldPriceEntity> distinctByCategory = goldPrices.stream()
                .collect(Collectors.toMap(
                        GoldPriceEntity::getCategoryId,
                        price -> price,
                        (existing, replacement) -> existing
                ))
                .values()
                .stream()
                .limit(4) // 最多展示4个
                .toList();

        // 获取成色信息
        Set<Long> qualityIds = distinctByCategory.stream()
            .map(GoldPriceEntity::getQualityId)
            .collect(Collectors.toSet());

        Map<Long, QualityEntity> qualityMap = qualityMapper.selectListByQuery(
            QueryWrapper.create().where(QUALITY.ID.in(qualityIds))
        ).stream().collect(Collectors.toMap(QualityEntity::getId, q -> q));

        return distinctByCategory.stream().map(goldPrice -> {
            HomeDataVO.GoldPriceVO vo = new HomeDataVO.GoldPriceVO();

            // 大类名称
            CategoryEnum category = CategoryEnum.getByValue(goldPrice.getCategoryId());
            vo.setCategoryName(category != null ? category.getLabel() : "未知");

            // 成色名称
            QualityEntity quality = qualityMap.get(goldPrice.getQualityId());
            vo.setQualityName(quality != null ? quality.getName() : "未知");

            // 销售价和回收价（从分转换为元）
            vo.setSalePrice(goldPrice.getSalePrice().divide(BigDecimal.valueOf(100)));
            vo.setRecyclePrice(goldPrice.getRecyclePrice().divide(BigDecimal.valueOf(100)));

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取快捷入库菜单
     */
    private List<HomeDataVO.QuickMenuVO> getQuickMenus() {
        // 指定的菜单顺序
        List<String> menuSigns = Arrays.asList(
            "purchaseWarehouse", "purchaseReturn", "salesBilling", "recycleBilling",
            "businessGoods", "oldMaterialsGoods", "businessGift", "purchaseWarehouseReceipts",
            "salesOrder", "recycleForm"
        );
        
        // 查询菜单信息
        List<SysMenuEntity> menus = menuMapper.selectListByQuery(
            QueryWrapper.create()
                .where(SYS_MENU.SIGN.in(menuSigns))
        );
        
        Map<String, SysMenuEntity> menuMap = menus.stream()
            .collect(Collectors.toMap(SysMenuEntity::getSign, m -> m));
        
        // 获取用户权限列表
        List<SysPermissionEntity> userPermissions = roleService.getCurUserPermList();
        Set<String> userPermissionSigns = userPermissions.stream()
            .map(SysPermissionEntity::getSign)
            .collect(Collectors.toSet());
        
        // 按指定顺序返回菜单
        return menuSigns.stream()
            .map(menuMap::get)
            .filter(menu -> menu != null)
            .map(menu -> {
                HomeDataVO.QuickMenuVO vo = new HomeDataVO.QuickMenuVO();
                vo.setRoutePath(menu.getRoutePath());
                vo.setSign(menu.getSign());
                vo.setName(menu.getName());
                vo.setComponent(menu.getComponent());
                vo.setIcon(menu.getIcon());
                vo.setImage(menu.getImage());
                vo.setPermissionSign(menu.getPermissionSign());
                
                // 检查权限
                boolean hasPermission = StrUtil.isBlank(menu.getPermissionSign()) || 
                    userPermissionSigns.contains(menu.getPermissionSign());
                vo.setHasPermission(hasPermission);
                
                return vo;
            })
            .collect(Collectors.toList());
    }

    /**
     * 获取系统公告
     */
    private List<HomeDataVO.SystemNoticeVO> getSystemNotices() {
        // 查询前6条公告，按创建时间倒序
        List<SysNoticeEntity> notices = noticeMapper.selectListByQuery(
            QueryWrapper.create()
                .where(SYS_NOTICE.PUBLISH_STATUS.eq(1)) // 已发布状态
                .orderBy(SYS_NOTICE.CREATED_AT, false)
                .limit(6)
        );
        
        return notices.stream().map(notice -> {
            HomeDataVO.SystemNoticeVO vo = new HomeDataVO.SystemNoticeVO();
            vo.setId(notice.getId());
            vo.setTitle(notice.getTitle());

            // 如果没有发布时间，则取创建时间
            if (notice.getPublishTime() != null) {
                vo.setPublishTime(notice.getPublishTime());
            } else {
                vo.setPublishTime(LocalDateTimeUtil.of(notice.getCreatedAt()));
            }

            // 处理概述：截取前30个字符并去除markdown符号
            String summary = CommonUtils.removeMarkdownAndGenerateSummary(notice.getContent());
            vo.setSummary(summary);

            return vo;
        }).collect(Collectors.toList());
    }



    /**
     * 获取系统概况
     */
    private HomeDataVO.SystemOverviewVO getSystemOverview() {
        Long companyId = SecurityUtils.getCompanyId();
        HomeDataVO.SystemOverviewVO vo = new HomeDataVO.SystemOverviewVO();
        
        // 获取商户信息
        CompanyEntity company = companyMapper.selectOneById(companyId);
        if (company != null) {
            vo.setExpirationDate(company.getExpirationDate());
            vo.setMaxMerchantCount(company.getMaxNumber());
        }

        // 门店数量
        Long merchantCount = merchantMapper.selectCountByQuery(
            QueryWrapper.create().where(MERCHANT.COMPANY_ID.eq(companyId))
        );
        vo.setMerchantCount(merchantCount.intValue());
        
        // 条码数量（根据goods_sn去重统计）
        Object barcodeCountObj = goodsMapper.selectObjectByQuery(
            QueryWrapper.create()
                .select(QueryMethods.count(QueryMethods.distinct(GOODS.GOODS_SN)).as("barcodeCount"))
                .where(GOODS.COMPANY_ID.eq(companyId))
                .where(GOODS.STOCK_NUM.gt(0).or(GOODS.FROZEN_NUM.gt(0)))
        );
        Long goodsSnCount = barcodeCountObj != null ? Long.valueOf(barcodeCountObj.toString()) : 0L;
        vo.setGoodsSnCount(goodsSnCount + "/" + (company == null || company.getMaxSn() == null ? "0" : company.getMaxSn()));
        
        // 存储大小（图片文件）
        Object storageSizeObj = fileMapper.selectObjectByQuery(
            QueryWrapper.create()
                .select(QueryMethods.sum(FILE.SIZE).as("totalSize"))
                .where(FILE.COMPANY_ID.eq(companyId))
                .and(FILE.EXTENSION.in(IMAGE_EXTENSIONS))
                .and(FILE.STATUS.eq(1))
        );
        Long storageSize = storageSizeObj != null ? Long.valueOf(storageSizeObj.toString()) : 0L;
        Long finalStorageSize = storageSize != null ? storageSize : 0L;
        vo.setStorageSize(finalStorageSize);

        return vo;
    }
}
