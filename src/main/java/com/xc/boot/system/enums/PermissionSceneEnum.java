package com.xc.boot.system.enums;

import com.xc.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 权限归属枚举
 */
@Getter
public enum PermissionSceneEnum implements IBaseEnum<Integer> {
    COMMON(0, "公共"),
    PC_MERCHANT(1, "PC商家"),
    PC_ADMIN(2, "PC超管"),
    MINI_PROGRAM(3, "小程序"),
    PDA(4, "PDA");

    private final Integer value;
    private final String label;

    PermissionSceneEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
} 