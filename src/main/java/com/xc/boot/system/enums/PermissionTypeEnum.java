package com.xc.boot.system.enums;

import com.xc.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 权限类型枚举
 */
@Getter
public enum PermissionTypeEnum implements IBaseEnum<Integer> {
    
    MENU(1, "菜单权限"),
    OPERATION(2, "操作权限");

    private final Integer value;
    private final String label;

    PermissionTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
} 