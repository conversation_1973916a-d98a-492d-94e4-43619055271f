package com.xc.boot.system.model.bo;

import lombok.Data;

/**
 * 路由
 */
@Data
public class RouteBO {

    private Long id;

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单别名
     */
    private String sign;

    /**
     * 路由路径（Vue Router 中定义的 URL 路径）
     */
    private String routePath;

    /**
     * 组件路径(vue页面完整路径，省略.vue后缀)
     */
    private String component;

    /**
     * 显示状态(true:显示;false:隐藏)
     */
    private Boolean visible;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 展示图标
     */
    private String image;

    /**
     * 跳转路径
     */
    private String redirect;

    /**
     * 菜单高亮
     */
    private String highlight;
}