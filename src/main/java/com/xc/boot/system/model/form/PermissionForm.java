package com.xc.boot.system.model.form;

import org.hibernate.validator.constraints.Range;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 权限表单对象
 */
@Data
@Schema(description = "权限表单对象")
public class PermissionForm {

    @Schema(description = "权限ID")
    private Long id;

    @Schema(description = "父级权限ID")
    private Long parentId;

    @NotBlank(message = "权限名称不能为空")
    @Schema(description = "权限名称")
    private String name;

    @NotBlank(message = "权限标识不能为空")
    @Schema(description = "权限标识")
    private String sign;

    @NotNull(message = "权限归属不能为空")
    @Schema(description = "权限归属")
    @Range(min = 0, max = 4, message = "权限归属不正确")
    private Integer scene;

    @NotNull(message = "权限类型不能为空")
    @Schema(description = "权限类型")
    @Range(min = 1, max = 2, message = "权限类型不正确")
    private Integer type;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "API列表(逗号分隔)")
    private String apiList;
} 