package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import com.xc.boot.system.enums.PermissionSceneEnum;
import com.xc.boot.system.enums.PermissionTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 系统权限实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table("sys_permission")
public class SysPermissionEntity extends BaseEntity {

    /**
     * 父级权限
     */
    @Column
    private Long parentId;

    /**
     * 权限名称
     */
    @Column
    private String name;

    /**
     * 权限标识
     */
    @Column
    private String sign;

    /**
     * 权限归属
     * @see PermissionSceneEnum
     */
    @Column
    private Integer scene;

    /**
     * 权限类型
     * @see PermissionTypeEnum
     */
    @Column
    private Integer type;

    /**
     * 排序
     */
    @Column
    private Integer sort;

    /**
     * API列表(逗号分隔)
     */
    @Column
    private String apiList;

    /**
     * 树路径
     */
    @Column
    private String treePath;
} 