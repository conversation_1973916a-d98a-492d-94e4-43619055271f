package com.xc.boot.system.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 通知公告表单对象
 *
 * <AUTHOR>
 * @since 2024-08-27 10:31
 */
@Getter
@Setter
@Schema(description = "通知公告表单对象")
public class NoticeForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "通知ID")
    private Long id;

    @Schema(description = "通知标题")
    @NotBlank(message = "通知标题不能为空")
    @Size(max=50, message="通知标题长度不能超过50个字符")
    private String title;

    @Schema(description = "通知内容")
    @NotBlank(message = "通知内容不能为空")
    @Size(max=65535, message="通知内容长度不能超过65535个字符")
    private String content;

    @Schema(description = "是否强制弹窗")
    private Integer forcePopup;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "发布状态(0-未发布 1已发布)")
    private Integer publishStatus;
}
