package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import com.xc.boot.common.listener.UpdatedByListenerFlag;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 部门实体
 *
 * <AUTHOR>
 * @since 2024/06/23
 */

@Getter
@Setter
@Accessors(chain = true)
@Table(value = "sys_dept")
public class SysDeptEntity implements CreatedByListenerFlag, UpdatedByListenerFlag {
    /**
     * 主键ID
     */
    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 部门名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 部门编码
     */
    @Column(value = "code")
    private String code;

    /**
     * 父节点id
     */
    @Column(value = "parent_id")
    private Long parentId;

    /**
     * 父节点id路径
     */
    @Column(value = "tree_path")
    private String treePath;

    /**
     * 显示顺序
     */
    @Column(value = "sort")
    private Integer sort;

    /**
     * 状态(1-正常 0-禁用)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 创建人ID
     */
    @Column(value = "created_by")
    private Long createdBy;

    /**
     * 更新人ID
     */
    @Column(value = "updated_by")
    private Long updatedBy;

    /**
     * 逻辑删除
     */
    @Column(value = "deleted_at", isLogicDelete = true)
    private Date deletedAt;

    /**
     * 创建时间
     */
    @Column(value = "created_at", onInsertValue = "now()")
    private Date createdAt;

    /**
     * 更新时间
     */
    @Column(value = "updated_at", onInsertValue = "now()", onUpdateValue = "now()")
    private Date updatedAt;
}