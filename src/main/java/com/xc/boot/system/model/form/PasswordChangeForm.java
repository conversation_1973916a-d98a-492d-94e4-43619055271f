package com.xc.boot.system.model.form;

import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.constant.SecurityConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 修改密码表单
 *
 * <AUTHOR>
 * @since 2024/8/13
 */
@Schema(description = "修改密码表单")
@Data
public class PasswordChangeForm {

    @Schema(description = "用户ID")
    @NotNull(message = "用户ID不能为空", groups = {Create.class})
    private Long userId;

    @Schema(description = "原密码")
    @NotBlank(message = "原密码不能为空", groups = {Update.class})
    private String oldPassword;

    @Schema(description = "新密码")
    @NotBlank(message = "新密码不能为空", groups = {Update.class, Create.class})
    private String newPassword;

    @Schema(description = "确认新密码")
    @NotBlank(message = "确认新密码不能为空", groups = {Update.class, Create.class})
    private String confirmPassword;

}
