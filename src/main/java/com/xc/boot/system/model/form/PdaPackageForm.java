package com.xc.boot.system.model.form;

import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

/**
 * PDA包管理表单
 */
@Data
@Schema(description = "PDA包管理表单")
public class PdaPackageForm {
    @Schema(description = "主键ID")
    @NotNull(message = "ID不能为空", groups = {Update.class})
    private Long id;

    @Schema(description = "包名称")
    @NotBlank(message = "包名称不能为空", groups = {Create.class, Update.class})
    @Length(max = 200, message = "包名称不能超过200个字符", groups = {Create.class, Update.class})
    private String name;

    @Schema(description = "设备类型")
    @NotNull(message = "设备类型不能为空", groups = {Create.class, Update.class})
    private String type;

    @Schema(description = "版本号")
    @NotBlank(message = "版本号不能为空", groups = {Create.class, Update.class})
    @Length(max = 200, message = "版本号不能超过200个字符", groups = {Create.class, Update.class})
    @Pattern(regexp = "^\\d+(\\.\\d+){2}$", message = "版本号格式需要为0.0.0格式", groups = {Create.class, Update.class})
    private String version;

    @Schema(description = "设备图片")
    @Length(max = 500, message = "设备图片不能超过500个字符", groups = {Create.class, Update.class})
    private String image;

    @Schema(description = "下载地址")
    @NotBlank(message = "下载地址不能为空", groups = {Create.class, Update.class})
    @Length(max = 500, message = "下载地址长度不能超过500", groups = {Create.class, Update.class})
    private String url;

    @Schema(description = "文件ID")
    @NotNull(message = "文件ID不能为空", groups = {Create.class, Update.class})
    private Long fileId;

    @Schema(description = "备注")
    @Length(max = 200, message = "备注长度不能超过200", groups = {Create.class, Update.class})
    private String remark;

    @Schema(description = "生效时间")
    @NotNull(message = "生效时间不能为空", groups = {Create.class, Update.class})
    private Date activeAt;
} 