package com.xc.boot.system.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 商家表单请求DTO
 */
@Data
@Schema(description = "商家表单请求")
public class CompanyFormDTO {

    @Schema(description = "商家ID")
    private Long id;

    @NotBlank(message = "商家名称不能为空")
    @Size(max = 50, message = "商家名称长度不能超过50个字符")
    @Schema(description = "商家名称")
    private String name;

    @NotBlank(message = "商家账号不能为空")
    @Size(max = 20, message = "商家账号长度不能超过20个字符")
    @Schema(description = "商家账号")
    private String phone;

    @NotBlank(message = "联系人不能为空")
    @Size(max = 20, message = "联系人长度不能超过20个字符")
    @Schema(description = "联系人")
    private String contact;

    @NotBlank(message = "商家地址不能为空")
    @Size(max = 200, message = "商家地址长度不能超过200个字符")
    @Schema(description = "商家地址")
    private String address;

    @NotNull(message = "是否允许多门店不能为空")
    @Schema(description = "是否允许多门店")
    private Boolean isMultiple;

    @Schema(description = "允许门店数")
    private Integer maxNumber;

    @Schema(description = "最大条码数")
    @Min(value = 0, message = "最大条码数不能小于0")
    private Integer maxSn = 0;

    @Schema(description = "套餐费用(元)")
    @Min(value = 0, message = "套餐费用不能小于0")
    private BigDecimal packageFee = BigDecimal.ZERO;

    @NotNull(message = "过期时间不能为空")
    @Schema(description = "过期时间")
    private String expirationDate;

    @Size(max = 200, message = "备注长度不能超过200个字符")
    @Schema(description = "备注")
    private String remark;
} 