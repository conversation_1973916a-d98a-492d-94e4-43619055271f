package com.xc.boot.system.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * 菜单表单对象
 *
 * <AUTHOR>
 * @since 2024/06/23
 */
@Schema(description = "菜单表单对象")
@Data
public class MenuForm {

    @Schema(description = "菜单ID")
    private Long id;

    @Schema(description = "父菜单ID")
    private Long parentId;

    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "菜单别名")
    private String sign;

    @Schema(description = "路由路径")
    private String routePath;

    @Schema(description = "视图组件")
    private String component;

    @Schema(description = "菜单归属(0:公共;1:超管;2:商户;3:小程序;4:PDA)")
    @Range(max = 4, min = 0, message = "菜单归属不正确")
    private Integer scene;

    @Schema(description = "显示状态(1:显示;0:隐藏)")
    @Range(max = 1, min = 0, message = "显示状态不正确")
    private Integer visible;

    @Schema(description = "排序(数字越小排名越靠前)")
    private Integer sort;

    @Schema(description = "菜单图标")
    private String icon;

    @Schema(description = "展示图标")
    private String image;

    @Schema(description = "跳转路径")
    private String redirect;

    @Schema(description = "菜单高亮")
    private String highlight;

    @Schema(description = "权限标识")
    private String permissionSign;
}
