package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import com.xc.boot.common.listener.UpdatedByListenerFlag;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 角色实体
 *
 * <AUTHOR>
 * @since 2024/6/23
 */
@Getter
@Setter
@Table(value = "sys_role")
public class SysRoleEntity implements CreatedByListenerFlag, UpdatedByListenerFlag {
    /**
     * 主键ID
     */
    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 公司id
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 角色名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 角色编码
     */
    @Column(value = "code")
    private String code;

    /**
     * 说明
     */
    @Column(value = "description")
    private String description;

    /**
     * 角色状态(1-正常 0-停用)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 创建人ID
     */
    @Column(value = "created_by")
    private Long createdBy;

    /**
     * 更新人ID
     */
    @Column(value = "updated_by")
    private Long updatedBy;

    /**
     * 逻辑删除
     */
    @Column(value = "deleted_at", isLogicDelete = true)
    private Date deletedAt;

    /**
     * 创建时间
     */
    @Column(value = "created_at", onInsertValue = "now()")
    private Date createdAt;

    /**
     * 更新时间
     */
    @Column(value = "updated_at", onInsertValue = "now()", onUpdateValue = "now()")
    private Date updatedAt;
}