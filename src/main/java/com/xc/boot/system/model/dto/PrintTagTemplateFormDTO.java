package com.xc.boot.system.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 打印标签模板表单对象
 */
@Data
@Schema(description = "打印标签模板表单对象")
public class PrintTagTemplateFormDTO {

    @Schema(description = "模板ID")
    private Long id;

    @Schema(description = "标签ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "标签不能为空")
    private Integer tagId;

    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "模板名称不能为空")
    @Size(max = 50, message = "模板名称长度不能超过50个字符")
    private String name;

    @Schema(description = "模板图片")
    private String image;

    @Schema(description = "图片ID")
    private Integer imageId;

    @Schema(description = "状态(1:启用;0:禁用)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "备注")
    @Size(max = 200, message = "备注长度不能超过200个字符")
    private String remark;
} 