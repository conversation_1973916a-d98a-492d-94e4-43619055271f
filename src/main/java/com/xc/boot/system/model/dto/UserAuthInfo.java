package com.xc.boot.system.model.dto;

import com.mybatisflex.annotation.RelationManyToMany;
import com.xc.boot.system.model.entity.SysRoleEntity;
import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * 用户认证信息
 *
 * <AUTHOR>
 * @since 2022/10/22
 */
@Data
public class UserAuthInfo {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商家ID
     */
    private Long companyId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 性别
     */
    private Integer gender;

    private String sideCode;

    /**
     * 是否主账号
     */
    private Integer mainFlag;

    private Integer secret;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 头像链接
     */
    private String avatar;

    /**
     * 头像ID
     */
    private Long avatarId;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 状态（1:启用；0:禁用）
     */
    private Integer status;

    /**
     * 用户所属的角色集合
     */
    @RelationManyToMany(
            selfField = "userId",
            targetTable = "sys_role",
            targetField = "id",
            joinTable = "sys_user_role",
            joinSelfColumn = "user_id",
            joinTargetColumn = "role_id"
    )
    private Set<SysRoleEntity> roles;

}
