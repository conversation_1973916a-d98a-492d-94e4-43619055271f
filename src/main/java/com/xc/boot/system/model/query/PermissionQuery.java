package com.xc.boot.system.model.query;

import com.xc.boot.system.enums.PermissionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 权限查询对象
 */
@Data
@Schema(description = "权限查询对象")
public class PermissionQuery {

    @Schema(description = "关键字(权限名称/标识)")
    private String keywords;

    @Schema(description = "权限归属")
    private Integer scene;

    @Schema(description = "权限类型")
    private Integer type;
} 