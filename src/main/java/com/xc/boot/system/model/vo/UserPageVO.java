package com.xc.boot.system.model.vo;

import com.xc.boot.common.util.excel.model.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 用户分页视图对象
 *
 * <AUTHOR>
 * @since 2022/1/15 9:41
 */
@Schema(description ="用户分页对象")
@Data
@Accessors(chain = true)
public class UserPageVO {
    @Schema(description="用户ID")
    private Long id;

    @Schema(description="用户头像地址")
    @Excel(type = 2)
    private String avatar;

    @Schema(description="用户头像文件id")
    private Integer avatarId;

    @Schema(description="姓名")
    private String nickname;

    @Schema(description="是否主账号(0:否|1:是)")
    private Boolean mainFlag;

    @Schema(description="手机号")
    private String username;

    @Schema(description="用户性别(0:未知;1:男;2:女)")
    @Excel(type = 3, key = {"0", "1", "2"}, value = {"未知", "男", "女"})
    private Integer gender;

    @Schema(description="用户状态(1:启用;0:禁用)")
    @Excel(type = 3, key = {"0", "1"}, value = {"禁用", "启用"})
    private Integer status;

    @Schema(description="用户机密(1:启用;0:禁用)")
    @Excel(type = 3, key = {"0", "1"}, value = {"否", "是"})
    private Integer secret;

    @Schema(description="用户角色列表")
    @Excel(type = 4, subFieldName = "name")
    private List<UserRoleVo> roles;

    @Schema(description="用户门店列表")
    @Excel(type = 4, subFieldName = "name")
    private List<UserMerchantVo> merchants;

    @Schema(description="创建时间")
    private Date createdAt;

    @Schema(description="更新时间")
    private Date updatedAt;

}
