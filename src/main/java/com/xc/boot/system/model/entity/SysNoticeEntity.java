package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import com.xc.boot.common.listener.UpdatedByListenerFlag;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 通知公告实体对象
 *
 * <AUTHOR>
 * @since 2024-08-27 10:31
 */
@Getter
@Setter
@Table(value = "sys_notice")
public class SysNoticeEntity implements CreatedByListenerFlag, UpdatedByListenerFlag {
    /**
     * 主键ID
     */
    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 通知标题
     */
    @Column(value = "title")
    private String title;

    /**
     * 通知内容
     */
    @Column(value = "content")
    private String content;

    /**
     * 发布人
     */
    @Column(value = "publisher_id")
    private Long publisherId;

    /**
     * 发布状态（0: 未发布, 1: 已发布, -1: 已撤回）
     */
    @Column(value = "publish_status")
    private Integer publishStatus;

    /**
     * 发布时间
     */
    @Column(value = "publish_time")
    private LocalDateTime publishTime;

    /**
     * 是否强制弹窗
     */
    @Column(value = "force_popup")
    private Integer forcePopup;

    /**
     * 排序
     */
    @Column(value = "sort")
    private Integer sort;

    /**
     * 创建人ID
     */
    @Column(value = "created_by")
    private Long createdBy;

    /**
     * 更新人ID
     */
    @Column(value = "updated_by")
    private Long updatedBy;

    /**
     * 逻辑删除
     */
    @Column(value = "deleted_at", isLogicDelete = true)
    private Date deletedAt;

    /**
     * 创建时间
     */
    @Column(value = "created_at", onInsertValue = "now()")
    private Date createdAt;

    /**
     * 更新时间
     */
    @Column(value = "updated_at", onInsertValue = "now()", onUpdateValue = "now()")
    private Date updatedAt;
}
