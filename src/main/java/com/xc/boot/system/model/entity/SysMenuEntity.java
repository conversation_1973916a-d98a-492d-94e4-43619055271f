package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 菜单实体
 *
 * <AUTHOR>
 * @since 2023/3/6
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "sys_menu")
public class SysMenuEntity {
    @Schema(description = "主键ID")
    @Id(keyType = KeyType.Auto)
    private Long id;

    @Schema(description = "父菜单ID")
    @Column(value = "parent_id")
    private Long parentId;

    @Schema(description = "父节点ID路径")
    @Column(value = "tree_path")
    private String treePath;

    @Schema(description = "菜单名称")
    @Column(value = "name")
    private String name;

    @Schema(description = "菜单别名")
    @Column(value = "sign")
    private String sign;

    @Schema(description = "路由路径")
    @Column(value = "route_path")
    private String routePath;

    @Schema(description = "视图组件")
    @Column(value = "component")
    private String component;

    @Schema(description = "显示状态(1:显示;0:隐藏)")
    @Column(value = "visible")
    private Integer visible;

    @Schema(description = "排序")
    @Column(value = "sort")
    private Integer sort;

    @Schema(description = "菜单图标")
    @Column(value = "icon")
    private String icon;

    @Schema(description = "展示图标")
    @Column(value = "image")
    private String image;

    @Schema(description = "跳转路径")
    @Column(value = "redirect")
    private String redirect;

    @Schema(description = "菜单高亮")
    @Column(value = "highlight")
    private String highlight;

    @Schema(description = "权限标识")
    @Column(value = "permission_sign")
    private String permissionSign;

    @Schema(description = "菜单归属(0:公共;1:超管;2:商户;3:小程序;4:PDA)")
    @Column(value = "scene")
    private Integer scene;

    @Schema(description = "创建时间")
    @Column(value = "created_at", onInsertValue = "now()")
    private Date createdAt;

    @Schema(description = "更新时间")
    @Column(value = "updated_at", onInsertValue = "now()", onUpdateValue = "now()")
    private Date updatedAt;
}