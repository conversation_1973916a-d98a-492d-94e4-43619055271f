package com.xc.boot.system.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 图片尺寸控制类型枚举
 *
 * <AUTHOR>
 * @since 2024/3/28
 */
@Getter
public enum ImageSizeCtrlEnum implements IBaseEnum<String> {

    /**
     * 控制宽高
     */
    FULL("full", "控制宽高"),

    /**
     * 控制高
     */
    HEIGHT("height", "控制高"),

    /**
     * 控制宽
     */
    WIDTH("width", "控制宽");

    private final String value;
    private final String label;

    ImageSizeCtrlEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
} 