package com.xc.boot.system.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 商家状态更新请求DTO
 */
@Data
@Schema(description = "商家状态更新请求")
public class CompanyStatusDTO {

    @NotNull(message = "商家ID不能为空")
    @Schema(description = "商家ID")
    private Long id;

    @NotNull(message = "商家状态不能为空")
    @Schema(description = "商家状态(1:启用；0:禁用)")
    private Integer status;
} 