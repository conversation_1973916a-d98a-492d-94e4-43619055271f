package com.xc.boot.system.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 打印标签模板分页返回对象
 */
@Data
@Schema(description = "打印标签模板分页返回对象")
public class PrintTagTemplatePageVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "模板名称")
    private String name;

    @Schema(description = "模板图片")
    private String image;

    @Schema(description = "模板图片ID")
    private Integer imageId;

    @Schema(description = "模板内容")
    private String content;

    @Schema(description = "关联标签ID")
    private Integer tagId;

    @Schema(description = "关联标签名称")
    private String tagName;

    @Schema(description = "标签图片")
    private String tagImage;

    @Schema(description = "标签类型")
    private Integer tagType;

    @Schema(description = "标签宽度")
    private Integer tagWidth;

    @Schema(description = "标签打印宽度")
    private Integer tagPrintWidth;

    @Schema(description = "标签高度")
    private Integer tagHeight;

    @Schema(description = "标签打印高度")
    private Integer tagPrintHeight;

    @Schema(description = "状态(0-禁用 1-启用)")
    private Integer status;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;
} 