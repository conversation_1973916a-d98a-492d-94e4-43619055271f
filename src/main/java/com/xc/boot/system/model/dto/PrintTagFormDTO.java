package com.xc.boot.system.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 打印标签表单对象
 */
@Data
@Schema(description = "打印标签表单对象")
public class PrintTagFormDTO {
    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "标签名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "标准RFID标签")
    @NotBlank(message = "标签名称不能为空")
    private String name;

    @Schema(description = "标签类型(1:普通标签|2:RFID标签)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "标签类型不能为空")
    private Integer type;

    @Schema(description = "芯片类型", example = "UHF-915MHz")
    private String chipType;

    @Schema(description = "识别距离(最小)", example = "0.5")
    private String minDistance;

    @Schema(description = "识别距离(最大)", example = "5.0")
    private String maxDistance;

    @Schema(description = "最小信号强度", example = "10")
    private Integer minPower;

    @Schema(description = "最大信号强度", example = "30")
    private Integer maxPower;

    @Schema(description = "宽(mm)", example = "50")
    private Integer width;

    @Schema(description = "高(mm)", example = "30")
    private Integer height;

    @Schema(description = "打印宽度(mm)", example = "45")
    private Integer printWidth;

    @Schema(description = "打印高度(mm)", example = "25")
    private Integer printHeight;

    @Schema(description = "标签图片", example = "https://example.com/images/tag.png")
    private String image;

    @Schema(description = "标签图片ID", example = "1001")
    private Integer imageId;

    @Schema(description = "模板图", example = "https://example.com/images/template.png")
    private String cover;

    @Schema(description = "模板图片ID", example = "1002")
    private Integer coverId;

    @Schema(description = "其他参数(JSON格式)", example = "")
    private String args;

    @Schema(description = "显示状态(1:显示;0:隐藏)", example = "1")
    private Integer enabled;

    @Schema(description = "备注", example = "适用于仓库管理")
    private String remark;

    @Schema(description = "模板内容", example = "{\"text\":\"仓库标签\",\"barcode\":\"123456789\"}")
    private String content;
} 