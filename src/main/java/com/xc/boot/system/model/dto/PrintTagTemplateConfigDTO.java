package com.xc.boot.system.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 打印标签模板配置对象
 */
@Data
@Schema(description = "打印标签模板配置对象")
public class PrintTagTemplateConfigDTO {

    @Schema(description = "模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模板ID不能为空")
    private Long id;

    @Schema(description = "模板图片")
    private String image;

    @Schema(description = "图片ID")
    private Integer imageId;

    @Schema(description = "内容")
    private String content;
} 