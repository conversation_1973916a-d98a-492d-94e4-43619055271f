package com.xc.boot.system.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 打印标签分页列表对象
 */
@Data
@Schema(description = "打印标签分页列表对象")
public class PrintTagPageVO {

    @Schema(description = "标签ID")
    private Long id;

    @Schema(description = "标签名称")
    private String name;

    @Schema(description = "标签类型(1:普通标签|2:RFID标签)")
    private Integer type;

    @Schema(description = "芯片类型")
    private String chipType;

    @Schema(description = "识别距离(最小)")
    private String minDistance;

    @Schema(description = "识别距离(最大)")
    private String maxDistance;

    @Schema(description = "最小信号强度")
    private Integer minPower;

    @Schema(description = "最大信号强度")
    private Integer maxPower;

    @Schema(description = "宽")
    private Integer width;

    @Schema(description = "高")
    private Integer height;

    @Schema(description = "打印宽度")
    private Integer printWidth;

    @Schema(description = "打印高度")
    private Integer printHeight;

    @Schema(description = "标签图片")
    private String image;

    @Schema(description = "标签图片ID")
    private Integer imageId;

    @Schema(description = "模板图")
    private String cover;

    @Schema(description = "模板图片ID")
    private Integer coverId;

    @Schema(description = "其他参数")
    private String args;

    @Schema(description = "显示状态(1:显示;0:隐藏)")
    private Integer enabled;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "模板内容")
    private String content;
} 