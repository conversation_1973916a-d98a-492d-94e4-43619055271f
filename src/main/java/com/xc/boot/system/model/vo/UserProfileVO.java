package com.xc.boot.system.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 个人中心用户信息
 *
 * <AUTHOR>
 * @since 2024/8/13
 */
@Schema(description = "个人中心用户信息")
@Data
public class UserProfileVO {
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long id;

    /**
     * 账户名
     */
    @Schema(description = "用户名")
    private String username;


    /**
     * 昵称
     */
    @Schema(description = "用户昵称")
    private String nickname;

    /**
     * 头像URL
     */
    @Schema(description = "头像URL")
    private String avatar;

    /**
     * 性别(1->男；2->女)
     */
    @Schema(description = "性别")
    private Integer gender;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 状态: 1->启用;0->禁用
     */
    @Schema(description = "状态: 1->启用;0->禁用")
    private Integer status;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 角色名称，多个使用英文逗号(,)分割
     */
    @Schema(description = "角色名称")
    private String roleNames;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createdAt;

}
