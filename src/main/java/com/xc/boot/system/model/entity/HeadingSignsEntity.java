package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date: 2025/6/4 13:34
 * @Description: 动态表头基础字段配置实体类
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "heading_signs")
public class HeadingSignsEntity extends BaseEntity {
    @Schema(description = "表头说明")
    @Column(value = "title")
    private String title;

    @Schema(description = "标签")
    @Column(value = "sign")
    private String sign;

    @Schema(description = "表头设置")
    @Column(value = "columns")
    private String columns;

    @Schema(description = "是否需要自定义字段(0:否|1:是)")
    @Column(value = "need_custom")
    private String needCustom;

}
