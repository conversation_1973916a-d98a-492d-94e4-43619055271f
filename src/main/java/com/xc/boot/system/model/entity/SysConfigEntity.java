package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import com.xc.boot.common.listener.UpdatedByListenerFlag;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 系统配置 实体
 *
 * <AUTHOR>
 * @since 2024-07-29 11:17:26
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "sys_config")
public class SysConfigEntity implements CreatedByListenerFlag, UpdatedByListenerFlag {
    /**
     * 主键ID
     */
    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 配置名称
     */
    @Column(value = "config_name")
    private String configName;

    /**
     * 配置键
     */
    @Column(value = "config_key")
    private String configKey;

    /**
     * 配置值
     */
    @Column(value = "config_value")
    private String configValue;

    /**
     * 描述、备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 更新人ID
     */
    @Column(value = "updated_by")
    private Long updatedBy;

    /**
     * 创建人ID
     */
    @Column(value = "created_by")
    private Long createdBy;

    /**
     * 逻辑删除
     */
    @Column(value = "deleted_at", isLogicDelete = true)
    private Date deletedAt;

    /**
     * 创建时间
     */
    @Column(value = "created_at", onInsertValue = "now()")
    private Date createdAt;

    /**
     * 更新时间
     */
    @Column(value = "updated_at", onInsertValue = "now()", onUpdateValue = "now()")
    private Date updatedAt;


}
