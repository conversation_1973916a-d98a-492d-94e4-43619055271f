package com.xc.boot.system.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 角色分页查询对象
 *
 * <AUTHOR>
 * @since 2022/6/3
 */
@Schema(description = "角色分页查询对象")
@Getter
@Setter
public class RolePageQuery extends BasePageQuery {

    @Schema(description="角色名称")
    private String name;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "时间区间")
    private Date[] timeRange;
}
