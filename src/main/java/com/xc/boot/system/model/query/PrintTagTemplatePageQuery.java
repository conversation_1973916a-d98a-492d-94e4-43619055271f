package com.xc.boot.system.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 打印标签模板分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "打印标签模板分页查询对象")
public class PrintTagTemplatePageQuery extends BasePageQuery {

    @Schema(description = "模板名称")
    private String name;

    @Schema(description = "标签类型")
    private Integer type;

    @Schema(description = "关联标签ID")
    private Integer tagId;

    @Schema(description = "状态(0-禁用 1-启用)")
    private Integer status;

    @Schema(description = "创建时间")
    private List<String> createdAt;
} 