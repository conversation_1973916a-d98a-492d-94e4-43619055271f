package com.xc.boot.system.model.form;

import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.constant.SecurityConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 用户列表表单对象
 */
@Schema(description = "用户列表表单对象")
@Data
public class UserListForm {

    @Schema(description="用户ID")
    @NotNull(message = "用户ID不能为空",  groups = Update.class)
    private Long id;

    @Schema(description="昵称")
    @NotBlank(message = "昵称不能为空", groups = {Update.class, Create.class})
    private String nickname;

    @Schema(description="手机号码")
    @Pattern(regexp = SecurityConstants.MOBILE_PATTERN, message = "手机号码格式不正确", groups = {Create.class})
    private String username;

    @Schema(description="性别")
    @NotNull(message = "性别不能为空",  groups = {Update.class})
    private Integer gender;

    @Schema(description="用户头像")
    private String avatar;

    @Schema(description="用户头像id")
    private Long avatarId;
}
