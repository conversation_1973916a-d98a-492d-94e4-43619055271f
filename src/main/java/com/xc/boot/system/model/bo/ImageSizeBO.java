package com.xc.boot.system.model.bo;

import cn.hutool.json.JSONUtil;
import com.xc.boot.system.model.enums.ImageSizeCtrlEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 图片尺寸业务对象
 *
 * <AUTHOR>
 * @since 2024/3/28
 */
@Data
@Schema(description = "图片尺寸业务对象")
public class ImageSizeBO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 控制类型
     * 
     * - full: 完整尺寸
     * - width: 按宽度缩放
     * - height: 按高度缩放
     */
    @Schema(description = "控制类型(full:控制宽高;height:控制高;width:控制宽)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "控制类型不能为空")
    private ImageSizeCtrlEnum ctrl;

    /**
     * 宽度(像素)
     */
    @Schema(description = "宽度(像素)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "宽度不能为空")
    @Min(value = 1, message = "宽度不能小于1")
    @Max(value = 10000, message = "宽度不能大于10000")
    private Integer width;

    /**
     * 高度(像素)
     */
    @Schema(description = "高度(像素)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "高度不能为空")
    @Min(value = 1, message = "高度不能小于1")
    @Max(value = 10000, message = "高度不能大于10000")
    private Integer height;

    /**
     * 从JSON字符串转换为ImageSizeBO对象
     *
     * @param json JSON字符串
     * @return ImageSizeBO对象，如果转换失败则返回null
     */
    public static ImageSizeBO fromJson(String json) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            return JSONUtil.toBean(json, ImageSizeBO.class);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }
} 