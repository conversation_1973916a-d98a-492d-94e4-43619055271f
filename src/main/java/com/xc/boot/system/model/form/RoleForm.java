package com.xc.boot.system.model.form;

import com.xc.boot.common.annotation.validGroup.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

// import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Schema(description = "角色表单对象")
@Data
public class RoleForm {

    @Schema(description="角色ID")
    @NotNull(message = "角色ID不能为空", groups = {Update.class})
    private Long id;

    @Schema(description="角色名称")
    @NotBlank(message = "角色名称不能为空")
    private String name;

    @Schema(description="说明")
    private String description;

    @Schema(description="角色状态(1-正常；0-停用)")
    @Range(max = 1, min = 0, message = "角色状态不正确")
    @NotNull(message = "角色状态不能为空")
    private Integer status;

    @Schema(description="权限列表")
    private List<String> permissions;
}
