package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date: 2025/6/4 13:34
 * @Description: 动态表头字段配置-用户缓存实体类
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "heading_user_cache")
public class HeadingUserCacheEntity extends BaseEntity {

    @Schema(description = "表头说明")
    @Column(value = "user_id")
    private Long userId;

    @Schema(description = "公司ID")
    @Column(value = "company_id")
    private Long companyId;

    @Schema(description = "标签")
    @Column(value = "sign")
    private String sign;

    @Schema(description = "表头设置")
    @Column(value = "columns")
    private String columns;
}
