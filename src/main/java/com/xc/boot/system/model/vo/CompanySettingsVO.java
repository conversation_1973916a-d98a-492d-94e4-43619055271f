package com.xc.boot.system.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.xc.boot.system.model.bo.ImageSizeBO;
import com.xc.boot.system.model.entity.CompanySettingsEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 商户设置视图对象
 */
@Data
@Accessors(chain = true)
@Schema(description = "商户设置视图对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CompanySettingsVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "商户ID")
    private Integer companyId;

    @Schema(description = "图片打印尺寸")
    private ImageSizeBO imagePrintSize;

    @Schema(description = "图片导出尺寸")
    private ImageSizeBO imageExportSize;

    @Schema(description = "货品入库审核是否开启")
    private Boolean incomeAuditEnabled;

    @Schema(description = "货品调拨审核是否开启")
    private Boolean transferAuditEnabled;

    @Schema(description = "采购退审核是否开启")
    private Boolean returnAuditEnabled;

    @Schema(description = "赠品入库审核是否开启")
    private Boolean giftIncomeAuditEnabled;

    @Schema(description = "赠品调拨审核是否开启")
    private Boolean giftTransferAuditEnabled;

    @Schema(description = "零库存盘点是否开启")
    private Boolean zeroStockTakeEnabled;

    @Schema(description = "旧料出库审核是否开启")
    private Boolean oldMaterialOutcomeAuditEnabled;

    /**
     * 从实体转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    public static CompanySettingsVO fromEntity(CompanySettingsEntity entity) {
        if (entity == null) {
            return null;
        }

        CompanySettingsVO vo = new CompanySettingsVO();
        vo.setCompanyId(entity.getCompanyId());
        vo.setIncomeAuditEnabled(entity.getIncomeAuditEnabled());
        vo.setTransferAuditEnabled(entity.getTransferAuditEnabled());
        vo.setReturnAuditEnabled(entity.getReturnAuditEnabled());
        vo.setGiftIncomeAuditEnabled(entity.getGiftIncomeAuditEnabled());
        vo.setGiftTransferAuditEnabled(entity.getGiftTransferAuditEnabled());
        vo.setZeroStockTakeEnabled(entity.getZeroStockTakeEnabled());
        vo.setOldMaterialOutcomeAuditEnabled(entity.getOldMaterialOutcomeAuditEnabled());

        // 转换图片尺寸
        if (entity.getImagePrintSize() != null) {
            vo.setImagePrintSize(ImageSizeBO.fromJson(entity.getImagePrintSize()));
        }
        if (entity.getImageExportSize() != null) {
            vo.setImageExportSize(ImageSizeBO.fromJson(entity.getImageExportSize()));
        }

        return vo;
    }
} 