package com.xc.boot.system.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 阅读通知公告VO
 *
 * <AUTHOR>
 * @since 2024-9-8 01:25:06
 */
@Data
public class NoticeDetailVO {

    @Schema(description = "通知ID")
    private Long id;

    @Schema(description = "通知标题")
    private String title;

    @Schema(description = "通知内容")
    private String content;

    @Schema(description = "发布人")
    private String publisherName;

    @Schema(description = "发布状态(0-未发布 1已发布)")
    private Integer publishStatus;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "是否强制弹窗")
    private Integer forcePopup;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建人ID")
    private Long creatorId;
}
