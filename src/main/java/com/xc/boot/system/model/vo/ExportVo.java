package com.xc.boot.system.model.vo;

import com.xc.boot.common.util.excel.model.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Schema(description ="下载中心对象")
@Data
public class ExportVo {
    @Schema(description="ID")
    private Long id;

    @Schema(description="用户ID")
    private Long userId;

    @Schema(description="商家ID")
    private Long companyId;

    @Schema(description="文件名称")
    private String name;

    @Schema(description="创建人")
    private String creatorName;

    @Schema(description="创建时间")
    private Date createdAt;

    @Schema(description="下载次数")
    private Integer downloadCount;

    @Schema(description="状态")
    @Excel(type = 3, key = {"2", "1", "-1"}, value = {"进行中", "导出成功", "导出失败"})
    private String status;

    @Schema(description="下载链接")
    private String url;
}
