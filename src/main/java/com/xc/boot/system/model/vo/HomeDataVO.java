package com.xc.boot.system.model.vo;

import cn.hutool.core.io.FileUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;



/**
 * 后台首页数据VO
 */
@Data
@Schema(description = "后台首页数据")
public class HomeDataVO {

    @Schema(description = "今日金价")
    private List<GoldPriceVO> goldPrices;

    @Schema(description = "快捷入库")
    private List<QuickMenuVO> quickMenus;

    @Schema(description = "系统公告")
    private List<SystemNoticeVO> systemNotices;

    @Schema(description = "系统概况")
    private SystemOverviewVO systemOverview;

    /**
     * 金价信息
     */
    @Data
    @Schema(description = "金价信息")
    public static class GoldPriceVO {
        @Schema(description = "大类名称")
        private String categoryName;

        @Schema(description = "成色名称")
        private String qualityName;

        @Schema(description = "销售价(元/克)")
        private BigDecimal salePrice;

        @Schema(description = "回收价(元/克)")
        private BigDecimal recyclePrice;
    }

    /**
     * 快捷菜单信息
     */
    @Data
    @Schema(description = "快捷菜单信息")
    public static class QuickMenuVO {
        @Schema(description = "路由路径")
        private String routePath;

        @Schema(description = "菜单标识")
        private String sign;

        @Schema(description = "菜单名称")
        private String name;

        @Schema(description = "组件路径")
        private String component;

        @Schema(description = "菜单图标")
        private String icon;

        @Schema(description = "展示图标")
        private String image;

        @Schema(description = "权限标识")
        private String permissionSign;

        @Schema(description = "是否有权限")
        private Boolean hasPermission;
    }

    /**
     * 系统公告信息
     */
    @Data
    @Schema(description = "系统公告信息")
    public static class SystemNoticeVO {
        @Schema(description = "公告ID")
        private Long id;

        @Schema(description = "公告标题")
        private String title;

        @Schema(description = "发布时间")
        @JsonFormat(pattern = "yyyy/MM/dd HH:mm")
        private LocalDateTime publishTime;

        @Schema(description = "概述")
        private String summary;

        @Schema(description = "是否为新公告(发布时间在2天内)")
        public Boolean getIsNew() {
            if (publishTime == null) {
                return false;
            }
            return publishTime.isAfter(LocalDateTime.now().minusDays(2));
        }
    }

    /**
     * 系统概况信息
     */
    @Data
    @Schema(description = "系统概况信息")
    public static class SystemOverviewVO {
        @Schema(description = "到期时间")
        @JsonFormat(pattern = "yyyy/MM/dd")
        private LocalDateTime expirationDate;

        @Schema(description = "门店数量")
        private Integer merchantCount;

        @Schema(description = "最大门店数量")
        private Integer maxMerchantCount;

        @Schema(description = "条码数量")
        private String goodsSnCount;

        @Schema(description = "存储大小(字节)")
        private Long storageSize;

        @Schema(description = "存储大小(格式化)")
        public String getStorageSizeFormatted() {
            return storageSize != null ? FileUtil.readableFileSize(storageSize) : "0 B";
        }
    }
}
