package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 验证码实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "verify_code")
@Data
public class VerifyCodeEntity extends BaseEntity {
    @Schema(description = "验证场景(1:登录|2:注册|3:修改密码)")
    @Column(value = "type")
    private Integer type;

    @Schema(description = "手机号")
    @Column(value = "mobile")
    private String mobile;

    @Schema(description = "ip地址")
    @Column(value = "ip_address")
    private String ip_address;

    @Schema(description = "验证码")
    @Column(value = "code")
    private String code;


}
