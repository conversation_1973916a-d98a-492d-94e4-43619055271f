package com.xc.boot.system.model.form;

import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.constant.SecurityConstants;
import com.xc.boot.system.model.vo.UserMerchantVo;
import com.xc.boot.system.model.vo.UserRoleVo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName UserUpdateForm
 * @Date: 2025/6/4 09:24
 * @Description: 用户修改表单对象
 */
@Data
@Schema(description="用户修改表单对象")
public class UserUpdateForm {

    @Schema(description="用户ID")
    @NotNull(message = "用户ID不能为空", groups = {Update.class})
    private Long id;

    @Schema(description="用户头像地址")
    private String avatar;

    @Schema(description="用户头像文件id")
    private Long avatarId;

    @Schema(description="姓名")
    @NotNull(message = "姓名不能为空")
    private String nickname;

    @Schema(description="密码")
    @NotBlank(message = "密码不能为空", groups = {Create.class})
    private String password;

    @Schema(description="手机号")
    @Pattern(regexp = SecurityConstants.MOBILE_PATTERN, message = "手机号码格式不正确")
    @NotNull(message = "手机号不能为空")
    private String username;

    @Schema(description="用户性别(0:未知;1:男;2:女)")
    @NotNull(message = "用户性别不能为空")
    private Integer gender;

    @Schema(description="用户状态(1:启用;0:禁用)")
    @NotNull(message = "用户状态不能为空")
    private Integer status;

    @Schema(description="用户机密(1:启用;0:禁用)")
    @NotNull(message = "用户机密查看不能为空")
    private Integer secret;

    @Schema(description="用户角色列表(只需要id)")
    @NotEmpty(message = "用户角色列表不能为空")
    private List<Long> roles;

    @Schema(description="用户门店列表(只需要id)")
    @NotEmpty(message = "用户门店列表不能为空")
    private List<Long> merchants;
}
