package com.xc.boot.system.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @ClassName TableHeadingColumsVo
 * @Date: 2025/6/4 13:49
 * @Description: columns
 */
@Data
@Accessors(chain = true)
@Schema(description = "columns定义")
public class HeadingColumns {
    @Schema(description = "标题")
    private String label;
    @Schema(description = "字段")
    private String prop;
    @Schema(description = "是否显示")
    private Boolean show = true;
    @Schema(description = "是否图片")
    private Boolean isImg = false;

    @Schema(description = "额外信息")
    private Object extra;
}
