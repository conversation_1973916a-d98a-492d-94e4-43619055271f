package com.xc.boot.system.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 弹窗公告VO
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Data
@Schema(description = "弹窗公告VO")
public class PopupNoticeVO {

    @Schema(description = "通知ID")
    private Long id;

    @Schema(description = "通知标题")
    private String title;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;
}
