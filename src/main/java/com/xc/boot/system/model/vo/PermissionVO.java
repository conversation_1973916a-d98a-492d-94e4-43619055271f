package com.xc.boot.system.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.system.enums.PermissionSceneEnum;
import com.xc.boot.system.enums.PermissionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 权限视图对象
 */
@Data
@Schema(description = "权限视图对象")
public class PermissionVO {

    @Schema(description = "权限ID")
    private Long id;

    @Schema(description = "父级权限ID")
    private Long parentId;

    @Schema(description = "权限名称")
    private String name;

    @Schema(description = "权限标识")
    private String sign;

    @Schema(description = "权限归属")
    private Integer scene;

    @Schema(description = "权限归属文本")
    private String sceneText;

    public String getSceneText() {
        return IBaseEnum.getLabelByValue(this.scene, PermissionSceneEnum.class);
    }

    @Schema(description = "权限类型")
    private Integer type;

    @Schema(description = "权限类型文本")
    private String typeText;

    public String getTypeText() {
        return IBaseEnum.getLabelByValue(this.type, PermissionTypeEnum.class);
    }

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "API列表(逗号分隔)")
    private String apiList;

    @Schema(description = "子权限")
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private List<PermissionVO> children;
} 