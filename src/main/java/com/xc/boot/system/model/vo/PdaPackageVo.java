package com.xc.boot.system.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * PDA包管理VO
 */
@Data
@Schema(description = "PDA包管理VO")
@Accessors(chain = true)
public class PdaPackageVo {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "包名称")
    private String name;

    @Schema(description = "设备类型")
    private String type;

    @Schema(description = "设备类型名称")
    private String typeName;

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "设备图片")
    private String image;

    @Schema(description = "下载地址")
    private String url;

    @Schema(description = "文件ID")
    private Long fileId;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;

    @Schema(description = "生效时间")
    private Date activeAt;
} 