package com.xc.boot.system.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Schema(description ="用户列表角色对象")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class UserRoleVo {
    @Schema(description="用户ID")
    private Long userId;

    @Schema(description="角色ID")
    private Long id;

    @Schema(description="角色名称")
    private String name;
}
