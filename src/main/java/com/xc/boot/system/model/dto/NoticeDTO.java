package com.xc.boot.system.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 通知传送对象
 *
 * <AUTHOR>
 * @since 2024-9-2 14:32:58
 */
@Data
public class NoticeDTO {

    @Schema(description = "通知ID")
    private Long id;

    @Schema(description = "通知类型")
    private Integer type;

    @Schema(description = "通知标题")
    @Size(max = 100, message = "通知标题长度不能超过100个字符")
    private String title;

    @Schema(description = "通知时间")
    private LocalDateTime publishTime;

}
