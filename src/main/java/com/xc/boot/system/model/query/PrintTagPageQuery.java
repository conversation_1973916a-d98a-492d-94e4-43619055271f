package com.xc.boot.system.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 打印标签分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "打印标签分页查询对象")
public class PrintTagPageQuery extends BasePageQuery {

    @Schema(description = "标签名称")
    private String name;

    @Schema(description = "标签类型(1:普通标签|2:RFID标签)")
    private Integer type;

    @Schema(description = "芯片类型")
    private String chipType;

    @Schema(description = "显示状态(1:显示;0:隐藏)")
    private Integer enabled;
} 