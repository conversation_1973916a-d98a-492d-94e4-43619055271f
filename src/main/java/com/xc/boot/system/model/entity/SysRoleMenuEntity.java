package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;


/**
 * 角色和菜单关联表
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "sys_role_menu")
public class SysRoleMenuEntity {

    @Schema(description = "角色ID")
    @Column(value = "role_id")
    private Long roleId;

    @Schema(description = "菜单ID")
    @Column(value = "menu_id")
    private Long menuId;
}