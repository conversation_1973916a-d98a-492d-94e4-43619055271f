package com.xc.boot.system.model.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TableHeadingVo
 * @Date: 2025/6/4 13:48
 * @Description: 动态表头配置
 */
@Data
@Accessors(chain = true)
public class TableHeading {
    @NotBlank(message = "sign不能为空")
    private String sign;

    @NotEmpty(message = "columns不能为空")
    private List<HeadingColumns> columns;
}
