package com.xc.boot.system.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 通知公告视图对象
 *
 * <AUTHOR>
 * @since 2024-08-27 10:31
 */
@Getter
@Setter
@Schema(description = "通知公告视图对象")
public class NoticePageVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "通知ID")
    private Long id;

    @Schema(description = "通知标题")
    private String title;

    @Schema(description = "通知状态")
    private Integer publishStatus;

    @Schema(description = "发布人姓名")
    private String publisherName;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "是否已读")
    private Integer isRead;

    @Schema(description = "是否强制弹窗")
    private Integer forcePopup;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "创建人ID")
    private Long creatorId;
}
