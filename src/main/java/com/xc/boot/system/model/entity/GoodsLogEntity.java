package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 货品日志实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_logs")
public class GoodsLogEntity extends BaseEntity {
    
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 门店ID
     */
    @Column(value = "merchant_id")
    private Long merchantId;

    /**
     * 货品ID
     */
    @Column(value = "goods_id")
    private Long goodsId;

    /**
     * 货品SN
     */
    @Column(value = "goods_sn")
    private String goodsSn;

    /**
     * 操作人ID
     */
    @Column(value = "user_id")
    private Long userId;

    /**
     * 操作说明(模块-操作)
     */
    @Column(value = "comment")
    private String comment;

    /**
     * 日志内容
     */
    @Column(value = "content")
    private String content;

    /**
     * 补充信息
     */
    @Column(value = "extra")
    private String extra;

    /**
     * 请求方式
     */
    @Column(value = "request_method")
    private String requestMethod;

    /**
     * 请求地址
     */
    @Column(value = "request_uri")
    private String requestUri;

    /**
     * 请求参数
     */
    @Column(value = "request_params")
    private String requestParams;

    /**
     * IP地址
     */
    @Column(value = "ip")
    private String ip;

    /**
     * 省
     */
    @Column(value = "province")
    private String province;

    /**
     * 市
     */
    @Column(value = "city")
    private String city;
} 