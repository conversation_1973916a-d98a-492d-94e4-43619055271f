package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;


/**
 * 角色和权限关联表
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "sys_role_permission")
public class SysRolePermissionEntity {

    @Schema(description = "角色ID")
    @Column(value = "role_id")
    private Long roleId;

    @Schema(description = "权限标识")
    @Column(value = "permission_sign")
    private String permissionSign;
}