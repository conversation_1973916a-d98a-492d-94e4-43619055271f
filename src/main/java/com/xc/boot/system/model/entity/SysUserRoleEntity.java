package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;


/**
 * 用户和角色关联表
 *
 * <AUTHOR>
 * @since 2022/12/17
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "sys_user_role")
public class SysUserRoleEntity {

    @Schema(description = "用户ID")
    @Column(value = "user_id")
    private Long userId;

    @Schema(description = "角色ID")
    @Column(value = "role_id")
    private Long roleId;

}