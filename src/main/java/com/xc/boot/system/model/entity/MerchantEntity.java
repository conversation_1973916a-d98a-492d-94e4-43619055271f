package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门店实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "merchant")
@Schema(description = "门店实体")
public class MerchantEntity extends BaseEntity {
    /**
     * 商户ID
     */
    @Schema(description = "商户ID")
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 门店名称
     */
    @Schema(description = "门店名称")
    @Column(value = "name")
    private String name;

    /**
     * 门店地址
     */
    @Schema(description = "门店地址")
    @Column(value = "address")
    private String address;

    /**
     * 门店联系电话
     */
    @Schema(description = "门店联系电话")
    @Column(value = "phone")
    private String phone;

    /**
     * 联系人
     */
    @Schema(description = "联系人")
    @Column(value = "contact")
    private String contact;

    /**
     * 联系人电话
     */
    @Schema(description = "联系人电话")
    @Column(value = "contact_phone")
    private String contactPhone;

    /**
     * 状态(0:禁用|1:启用)
     */
    @Schema(description = "状态(0:禁用|1:启用)")
    @Column(value = "status")
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(value = "remark")
    private String remark;
} 