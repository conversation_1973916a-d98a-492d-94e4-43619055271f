package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 打印标签实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "print_tags")
public class PrintTagEntity extends BaseEntity {
    /**
     * 标签名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 标签类型(1:普通标签|2:RFID标签)
     */
    @Column(value = "type")
    private Integer type;

    /**
     * 芯片类型
     */
    @Column(value = "chip_type")
    private String chipType;

    /**
     * 识别距离(最小)
     */
    @Column(value = "min_distance")
    private String minDistance;

    /**
     * 识别距离(最大)
     */
    @Column(value = "max_distance")
    private String maxDistance;

    /**
     * 最小信号强度
     */
    @Column(value = "min_power")
    private Integer minPower;

    /**
     * 最大信号强度
     */
    @Column(value = "max_power")
    private Integer maxPower;

    /**
     * 宽
     */
    @Column(value = "width")
    private Integer width;

    /**
     * 高
     */
    @Column(value = "height")
    private Integer height;

    /**
     * 打印宽度
     */
    @Column(value = "print_width")
    private Integer printWidth;

    /**
     * 打印高度
     */
    @Column(value = "print_height")
    private Integer printHeight;

    /**
     * 标签图片
     */
    @Column(value = "image")
    private String image;

    /**
     * 标签图片ID
     */
    @Column(value = "image_id")
    private Integer imageId;

    /**
     * 模板图
     */
    @Column(value = "cover")
    private String cover;

    /**
     * 模板图片ID
     */
    @Column(value = "cover_id")
    private Integer coverId;

    /**
     * 其他参数
     */
    @Column(value = "args")
    private String args;

    /**
     * 显示状态
     */
    @Column(value = "enabled")
    private Integer enabled;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 模板内容
     */
    @Column(value = "content")
    private String content;
} 