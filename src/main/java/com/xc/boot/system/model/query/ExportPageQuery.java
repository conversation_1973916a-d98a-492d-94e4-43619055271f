package com.xc.boot.system.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description ="下载中心查询对象")
public class ExportPageQuery extends BasePageQuery {
    @Schema(description = "文件类型")
    private String type;
    @Schema(description = "任务状态")
    private String status;
    @Schema(description = "导出人(主账号才有该选项)")
    private Long userId;
    @Schema(description = "创建时间")
    private Date[] timeRange;

    @Schema(description = "导出标识")
    private Integer export = 0;
}
