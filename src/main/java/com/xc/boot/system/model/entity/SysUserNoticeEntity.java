package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户通知公告实体对象
 *
 * <AUTHOR>
 * @since 2024-08-28 16:56
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "sys_user_notice")
public class SysUserNoticeEntity {
    @Schema(description = "主键ID")
    @Id(keyType = KeyType.Auto)
    private Long id;

    @Schema(description = "公共通知id")
    @Column(value = "notice_id")
    private Long noticeId;

    @Schema(description = "用户id")
    @Column(value = "user_id")
    private Long userId;

    @Schema(description = "读取状态，0未读，1已读")
    @Column(value = "is_read")
    private Integer isRead;

    @Schema(description = "用户阅读时间")
    @Column(value = "read_time")
    private LocalDateTime readTime;

    @Schema(description = "逻辑删除")
    @Column(value = "deleted_at", isLogicDelete = true)
    private Date deletedAt;

    @Schema(description = "创建时间")
    @Column(value = "created_at", onInsertValue = "now()")
    private Date createdAt;

    @Schema(description = "更新时间")
    @Column(value = "updated_at", onInsertValue = "now()", onUpdateValue = "now()")
    private Date updatedAt;

}
