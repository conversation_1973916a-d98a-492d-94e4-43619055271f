package com.xc.boot.system.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "打印标签列表VO")
public class PrintTagListVO {
    @Schema(description = "标签ID")
    private Long id;

    @Schema(description = "标签名称")
    private String name;

    @Schema(description = "标签图片")
    private String image;

    @Schema(description = "标签图片ID")
    private Integer imageId;

    @Schema(description = "标签内容")
    private String content;

    @Schema(description = "标签类型")
    private Integer type;

    @Schema(description = "标签宽度")
    private Integer width;

    @Schema(description = "标签高度")
    private Integer height;

    @Schema(description = "打印宽度")
    private Integer printWidth;

    @Schema(description = "打印高度")
    private Integer printHeight;
} 