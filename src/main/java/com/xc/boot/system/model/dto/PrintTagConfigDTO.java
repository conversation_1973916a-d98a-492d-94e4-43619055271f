package com.xc.boot.system.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 打印标签配置表单对象
 */
@Data
@Schema(description = "打印标签配置表单对象")
public class PrintTagConfigDTO {

    @Schema(description = "标签ID", example = "1")
    @NotNull(message = "标签ID不能为空")
    private Long id;

    @Schema(description = "标签图片", example = "https://example.com/images/tag.png")
    private String image;

    @Schema(description = "标签图片ID", example = "1001")
    @NotNull(message = "标签图片ID不能为空")
    private Integer imageId;

    @Schema(description = "模板内容", example = "{\"text\":\"仓库标签\",\"barcode\":\"123456789\"}")
    @NotNull(message = "模板内容不能为空")
    private String content;
} 