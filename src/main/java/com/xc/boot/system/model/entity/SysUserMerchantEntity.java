package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;


/**
 * 用户和门店关联表
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "sys_user_merchant")
public class SysUserMerchantEntity {

    @Schema(description = "用户ID")
    @Column(value = "user_id")
    private Long userId;

    @Schema(description = "角色ID")
    @Column(value = "merchant_id")
    private Long merchantId;

}