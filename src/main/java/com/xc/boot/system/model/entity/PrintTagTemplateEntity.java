package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 打印标签模板实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "print_tag_templates")
public class PrintTagTemplateEntity extends BaseEntity {

    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 关联标签ID
     */
    @Column(value = "tag_id")
    private Integer tagId;

    /**
     * 模板名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 模板图片
     */
    @Column(value = "image")
    private String image;

    /**
     * 模板图片ID
     */
    @Column(value = "image_id")
    private Integer imageId;

    /**
     * 状态(0-禁用 1-启用)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 模板内容
     */
    @Column(value = "content")
    private String content;
} 