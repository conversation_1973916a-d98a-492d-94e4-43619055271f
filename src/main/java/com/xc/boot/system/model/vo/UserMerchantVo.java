package com.xc.boot.system.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description ="用户列表门店对象")
@Data
@Accessors(chain = true)
public class UserMerchantVo {
    @Schema(description="用户ID")
    private Long userId;

    @Schema(description="门店ID")
    private Long id;

    @Schema(description="门店名称")
    private String name;
}
