package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName File
 * @Date: 2025/6/3 11:52
 * @Description: 文件实体类
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "file")
public class FileEntity  extends BaseEntity implements CreatedByListenerFlag {

    /**
     * 商家id
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 文件名（不含后缀）
     */
    @Column(value = "name")
    private String name;

    /**
     * 文件后缀
     */
     @Column(value = "extension")
     private String extension;

    /**
     * 访问链接
     */
    @Column(value = "url")
    private String url;

    /**
     * 大小 字节
     */
    @Column(value = "size")
    private Long size;

    /**
     * 使用描述
     */
    @Column(value = "description")
    private String description;

    /**
     * 状态 1:已使用 | 0:未使用
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 引用计数
     */
    @Column(value = "reference_count")
    private Integer referenceCount;

    /**
     * 上传人员
     */
    @Column(value = "created_by")
    private Long createdBy;

    /**
     * 删除时间
     */
    @Column(value = "deleted_at")
    private Date deletedAt;
}
