package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.enums.LogModuleEnum;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 系统日志 实体类
 *
 * <AUTHOR>
 * @since 2.10.0
 */
@Getter
@Setter
@Table(value = "sys_log")
public class SysLogEntity implements CreatedByListenerFlag {

    /**
     * 主键
     */
    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 日志模块
     */
    @Column(value = "module")
    private LogModuleEnum module;

    /**
     * 请求方式
     */
    @Column(value = "request_method")
    private String requestMethod;

    /**
     * 请求参数
     */
    @Column(value = "request_params")
    private String requestParams;

    /**
     * 响应参数
     */
    @Column(value = "response_content")
    private String responseContent;

    /**
     * 日志内容
     */
    @Column(value = "content")
    private String content;

    /**
     * 请求路径
     */
    @Column(value = "request_uri")
    private String requestUri;

    /**
     * 方法名称
     */
    @Column(value = "method")
    private String method;

    /**
     * IP 地址
     */
    @Column(value = "ip")
    private String ip;

    /**
     * 省份
     */
    @Column(value = "province")
    private String province;

    /**
     * 城市
     */
    @Column(value = "city")
    private String city;

    /**
     * 浏览器
     */
    @Column(value = "browser")
    private String browser;

    /**
     * 浏览器版本
     */
    @Column(value = "browser_version")
    private String browserVersion;

    /**
     * 终端系统
     */
    @Column(value = "os")
    private String os;

    /**
     * 执行时间(毫秒)
     */
    @Column(value = "execution_time")
    private Long executionTime;

    /**
     * 创建时间
     */
    @Column(value = "created_at", onInsertValue = "now()")
    private Date createdAt;

    /**
     * 创建人ID
     */
    @Column(value = "created_by")
    private Long createdBy;

    /**
     * 逻辑删除
     */
    @Column(value = "deleted_at", isLogicDelete = true)
    private Date deletedAt;

}