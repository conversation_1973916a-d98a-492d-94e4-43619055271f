package com.xc.boot.system.controller;

import com.xc.boot.common.enums.SideEnum;
import com.xc.boot.common.result.Result;
import com.xc.boot.system.model.vo.RouteVO;
import com.xc.boot.system.service.MenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "PDA-菜单接口")
@RestController
@RequestMapping("/api/pda/menus")
@RequiredArgsConstructor
@Slf4j
public class PdaMenuController {
    private final MenuService menuService;

    @Operation(summary = "菜单路由列表")
    @GetMapping("/routes")
    public Result<List<RouteVO>> listRoutes() {
        List<RouteVO> routeList = menuService.getCurrentUserRoutes(SideEnum.PDA);
        return Result.success(routeList);
    }
}
