package com.xc.boot.system.controller;

import com.xc.boot.common.enums.SideEnum;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.enums.LogModuleEnum;
import com.xc.boot.common.annotation.RepeatSubmit;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.base.SwitchRequest;
import com.xc.boot.system.model.form.MenuForm;
import com.xc.boot.system.model.query.MenuQuery;
import com.xc.boot.system.model.vo.MenuVO;
import com.xc.boot.common.model.Option;
import com.xc.boot.system.model.vo.RouteVO;
import com.xc.boot.common.annotation.Log;
import com.xc.boot.system.service.MenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜单控制层
 *
 * <AUTHOR>
 * @since 2020/11/06
 */
@Tag(name = "超管-菜单接口")
@RestController
@RequestMapping("/api/menus")
@RequiredArgsConstructor
@Slf4j
public class MenuController {

    private final MenuService menuService;

    @Operation(summary = "菜单列表")
    @GetMapping
    @Log( value = "菜单列表",module = LogModuleEnum.MENU)
    public Result<List<MenuVO>> listMenus(MenuQuery queryParams) {
        List<MenuVO> menuList = menuService.listMenus(queryParams);
        return Result.success(menuList);
    }

    @Operation(summary = "菜单下拉列表")
    @GetMapping("/options")
    public Result<List<Option<Long>>> listMenuOptions(
          @Parameter(description = "是否只查询父级菜单")
          @RequestParam(required = false, defaultValue = "false") boolean onlyParent
    ) {
        List<Option<Long>> menus = menuService.listMenuOptions(onlyParent);
        return Result.success(menus);
    }

    @Operation(summary = "菜单权限选项")
    @GetMapping("/permission-options")
    public Result<List<Option<String>>> listPermissionOptions() {
        List<Option<String>> options = menuService.listPermissionOptions();
        return Result.success(options);
    }

    @Operation(summary = "菜单路由列表")
    @GetMapping("/routes")
    public Result<List<RouteVO>> listRoutes() {
        List<RouteVO> routeList = menuService.getCurrentUserRoutes(SideEnum.PC);
        return Result.success(routeList);
    }

    // @Operation(summary = "菜单表单数据")
    // @GetMapping("/{id}/form")
    // public Result<MenuForm> getMenuForm(
    //         @Parameter(description = "菜单ID") @PathVariable Long id
    // ) {
    //     MenuForm menu = menuService.getMenuForm(id);
    //     return Result.success(menu);
    // }

    @Operation(summary = "新增菜单")
    @PostMapping
    @RepeatSubmit
    public Result<?> addMenu(@RequestBody MenuForm menuForm) {
        boolean result = menuService.saveMenu(menuForm);
        return Result.judge(result);
    }

    @Operation(summary = "修改菜单")
    @PutMapping
    public Result<?> updateMenu(
            @RequestBody MenuForm menuForm
    ) {
        boolean result = menuService.saveMenu(menuForm);
        return Result.judge(result);
    }

    @Operation(summary = "删除菜单")
    @PostMapping("/delete")
    public Result<?> deleteMenu(@RequestBody DeleteRequest request) {
        boolean result = menuService.deleteMenu(request.getId());
        return Result.judge(result);
    }

    @Operation(summary = "修改菜单显示状态")
    @PutMapping("/visible")
    public Result<?> updateMenuVisible(@RequestBody SwitchRequest request) {
        boolean result = menuService.updateMenuVisible(request.getId(), request.getVisible());
        return Result.judge(result);
    }

}

