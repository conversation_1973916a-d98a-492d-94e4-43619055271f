package com.xc.boot.system.controller;

import com.xc.boot.common.result.Result;
import com.xc.boot.system.model.vo.CompanySettingsVO;
import com.xc.boot.system.service.CompanySettingsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 商户设置控制层
 */
@Tag(name = "系统配置-商户设置")
@RestController
@RequestMapping("/api/company/settings")
@RequiredArgsConstructor
public class CompanySettingsController {

    private final CompanySettingsService companySettingsService;

    @Operation(summary = "获取商户设置")
    @GetMapping
    public Result<CompanySettingsVO> getSettings() {
        CompanySettingsVO settings = companySettingsService.getSettings();
        return Result.success(settings);
    }

    @Operation(summary = "保存商户设置")
    @PostMapping
    public Result<Boolean> saveSettings(@RequestBody @Valid CompanySettingsVO settings) {
        boolean result = companySettingsService.saveSettings(settings);
        return Result.success(result);
    }
} 