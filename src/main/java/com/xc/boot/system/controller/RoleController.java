package com.xc.boot.system.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.annotation.RepeatSubmit;
import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.model.Option;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.system.model.form.RoleForm;
import com.xc.boot.system.model.query.RolePageQuery;
import com.xc.boot.system.model.vo.RolePageVO;
import com.xc.boot.system.model.vo.RolePermissionVo;
import com.xc.boot.system.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 角色控制层
 *
 * <AUTHOR>
 * @since 2022/10/16
 */
@Tag(name = "超管-角色接口")
@RestController
@RequestMapping("/api/roles")
@RequiredArgsConstructor
public class RoleController {

    private final RoleService roleService;

    @Operation(summary = "角色分页列表")
    @PostMapping("/page")
    public PageResult<RolePageVO> getRolePage(@RequestBody RolePageQuery queryParams) {
        Page<RolePageVO> result = roleService.getRolePage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "当前用户权限列表")
    @GetMapping("/perTree")
    public Result<?> getRolePage() {
        RolePermissionVo result = roleService.getCurUserPermTree();
        return Result.success(result);
    }

    @Operation(summary = "角色下拉列表")
    @GetMapping("/options")
    public Result<List<Option<Long>>> listRoleOptions() {
        List<Option<Long>> list = roleService.listRoleOptions();
        return Result.success(list);
    }

    @Operation(summary = "新增角色")
    @PostMapping
    @RepeatSubmit
    public Result<?> addRole(@Validated(Create.class) @RequestBody RoleForm roleForm) {
        boolean result = roleService.saveRole(roleForm);
        return Result.judge(result);
    }

    @Operation(summary = "修改角色")
    @PutMapping(value = "/update")
    public Result<?> updateRole(@Validated(Update.class) @RequestBody RoleForm roleForm) {
        boolean result = roleService.updateRole(roleForm);
        return Result.judge(result);
    }

    @Operation(summary = "删除角色")
    @GetMapping("/delete")
    public Result<?> deleteRoles(@Parameter(description = "删除角色，多个以英文逗号(,)拼接") @RequestParam String ids) {
        boolean result = roleService.deleteRoles(ids);
        return Result.judge(result);
    }

}
