package com.xc.boot.system.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.system.model.query.PrintTagPageQuery;
import com.xc.boot.system.model.vo.PrintTagPageVO;
import com.xc.boot.system.service.PrintTagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.xc.boot.system.model.dto.PrintTagFormDTO;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.Result;
import jakarta.validation.Valid;
import com.xc.boot.system.model.dto.PrintTagConfigDTO;

/**
 * 打印标签管理
 */
@Tag(name = "系统-打印标签管理")
@RestController
@RequestMapping("/api/print-tag")
@RequiredArgsConstructor
public class PrintTagController {

    private final PrintTagService printTagService;

    @Operation(summary = "打印标签分页列表")
    @GetMapping("/page")
    public PageResult<PrintTagPageVO> getPrintTagPage(PrintTagPageQuery queryParams) {
        Page<PrintTagPageVO> result = printTagService.getPrintTagPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增打印标签")
    @PostMapping
    public Result<Boolean> addPrintTag(@RequestBody @Valid PrintTagFormDTO form) {
        boolean result = printTagService.savePrintTag(form);
        return Result.success(result);
    }

    @Operation(summary = "编辑打印标签")
    @PutMapping
    public Result<Boolean> editPrintTag(@RequestBody @Valid PrintTagFormDTO form) {
        boolean result = printTagService.savePrintTag(form);
        return Result.success(result);
    }

    @Operation(summary = "删除打印标签")
    @DeleteMapping
    public Result<Boolean> deletePrintTag(@RequestBody @Valid DeleteRequest request) {
        boolean result = printTagService.deletePrintTag(request.getId());
        return Result.success(result);
    }

    @Operation(summary = "配置打印标签")
    @PostMapping("/config")
    public Result<Boolean> configPrintTag(@RequestBody @Valid PrintTagConfigDTO form) {
        boolean result = printTagService.configPrintTag(form);
        return Result.success(result);
    }
} 