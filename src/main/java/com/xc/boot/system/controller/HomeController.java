package com.xc.boot.system.controller;

import com.xc.boot.system.model.vo.HomeDataVO;
import com.xc.boot.system.service.SystemHomeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import com.xc.boot.common.result.Result;

/**
 * 系统首页
 */
@Tag(name = "系统-首页")
@RestController
@RequestMapping("/api/home")
@RequiredArgsConstructor
public class HomeController {

    private final SystemHomeService systemHomeService;

    @Operation(summary = "首页数据接口")
    @GetMapping
    public Result<HomeDataVO> getHomeData() {
        HomeDataVO homeData = systemHomeService.getHomeData();
        return Result.success(homeData);
    }
}
