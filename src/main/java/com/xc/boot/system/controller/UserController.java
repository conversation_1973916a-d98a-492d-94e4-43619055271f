package com.xc.boot.system.controller;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.annotation.RepeatSubmit;
import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.enums.SideEnum;
import com.xc.boot.common.model.Option;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.shared.auth.model.LoginForm;
import com.xc.boot.system.model.form.PasswordChangeForm;
import com.xc.boot.system.model.form.UserForm;
import com.xc.boot.system.model.form.UserUpdateForm;
import com.xc.boot.shared.auth.model.VerifyCodeForm;
import com.xc.boot.system.model.query.UserPageQuery;
import com.xc.boot.system.model.vo.UserInfoVO;
import com.xc.boot.system.model.vo.UserPageVO;
import com.xc.boot.system.service.CompanyService;
import com.xc.boot.system.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xc.boot.system.model.entity.table.CompanyTableDef.COMPANY;
import static com.xc.boot.system.model.entity.table.SysUserTableDef.SYS_USER;


/**
 * 用户控制层
 *
 * <AUTHOR>
 * @since 2022/10/16
 */
@Tag(name = "超管-用户接口")
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;
    private final CompanyService companyService;

    @Operation(summary = "用户分页列表")
    @PostMapping("/page")
    public PageResult<UserPageVO> getUserPage(@RequestBody @Valid UserPageQuery queryParams) {
        Page<UserPageVO> result = userService.getUserPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "编辑用户信息")
    @PostMapping("/update")
    public Result<?> update(@RequestBody @Validated(Update.class) UserUpdateForm updateForm) {
        boolean result = userService.update(updateForm);
        return Result.judge(result);
    }

    @Operation(summary = "新增用户")
    @PostMapping
    @RepeatSubmit
    public Result<?> saveUser(@RequestBody @Validated(Create.class) UserUpdateForm form) {
        boolean result = userService.saveUser(form);
        return Result.judge(result);
    }

    @Operation(summary = "修改账号基础信息")
    @PostMapping(value = "/updateMe")
    public Result<Void> updateMe(@RequestBody @Validated(Update.class) UserForm userForm) {
        boolean result = userService.updateMe(userForm);
        return Result.judge(result);
    }

    @Operation(summary = "删除用户")
    @GetMapping("/delete")
    public Result<Void> deleteUsers(@Parameter(description = "用户ID，多个以英文逗号(,)分割") @RequestParam String ids) {
        boolean result = userService.deleteUsers(ids);
        return Result.judge(result);
    }

    @Operation(summary = "获取当前登录用户信息")
    @GetMapping("/me")
    public Result<UserInfoVO> getCurrentUserInfo() {
        UserInfoVO userInfoVO = userService.getCurrentUserInfo();
        return Result.success(userInfoVO);
    }

    @Operation(summary = "重置用户密码")
    @PutMapping(value = "/password/reset")
    public Result<?> resetPassword(@RequestBody @Validated(Create.class) PasswordChangeForm data) {
        boolean result = userService.resetPassword(data);
        return Result.judge(result);
    }

    @Operation(summary = "修改个人密码")
    @PutMapping(value = "/password")
    public Result<?> changePassword(@RequestBody @Validated(Update.class) PasswordChangeForm data) {
        Long currUserId = SecurityUtils.getUserId();
        boolean result = userService.changePassword(currUserId, data);
        return Result.judge(result);
    }

    @Operation(summary = "发送短信验证码(修改手机号-需要登录)")
    @PostMapping(value = "/sendCode")
    @RepeatSubmit
    public Result<?> sendVerificationCode(@RequestBody @Validated VerifyCodeForm form) {
        long exist = userService.count(QueryWrapper.create()
                .where(SYS_USER.USERNAME.eq(form.getUsername())));
        exist += companyService.count(QueryWrapper.create()
                .where(COMPANY.PHONE.eq(form.getUsername())));
        Assert.isTrue(exist == 0, "该手机号已被使用！");
        boolean result = userService.sendVerificationCode(form.getUsername(), form.getType());
        return Result.judge(result);
    }

    @Operation(summary = "用户下拉选项列表")
    @GetMapping("/options")
    public Result<List<Option<String>>> listUserOptions() {
        List<Option<String>> list = userService.listUserOptions();
        return Result.success(list);
    }

    @Operation(summary = "修改手机号-验证登录密码(参数仅需传密码)")
    @PostMapping("/verify/password")
    public Result<?> verifyPassword(@RequestBody LoginForm loginForm) {
        Assert.notNull(loginForm.getPassword(), "密码不能为空");
        boolean result = userService.verifyPassword(loginForm.getPassword());
        return Result.judge(result);
    }

    @Operation(summary = "修改手机号-验证验证码(参数传用户名、验证码)")
    @PostMapping("/verify/code")
    public Result<?> verifyCode(@RequestBody @Validated LoginForm loginForm) {
        Assert.notNull(loginForm.getCode(), "验证码不能为空");
        boolean result = userService.bindMobile(loginForm.getUsername(), loginForm.getCode(), SideEnum.PC.getValue());
        return Result.judge(result);
    }

}
