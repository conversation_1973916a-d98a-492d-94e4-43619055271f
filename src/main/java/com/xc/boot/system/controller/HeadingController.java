package com.xc.boot.system.controller;

import com.xc.boot.common.result.Result;
import com.xc.boot.system.model.vo.TableHeading;
import com.xc.boot.system.service.HeadingSignsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 表头管理控制层
 */
@Tag(name = "系统-动态表头管理")
@RestController
@RequestMapping("/api/heading")
@RequiredArgsConstructor
public class HeadingController {
    private final HeadingSignsService headingSignsService;

    @Operation(summary = "获取表头")
    @GetMapping("/detail")
    public Result<TableHeading> getTableHeading(@RequestParam String sign, @RequestParam(required = false) Integer templateId) {
        TableHeading tableHeading = headingSignsService.getTableHeading(sign, templateId);
        return Result.success(tableHeading);
    }

    @Operation(summary = "保存表头")
    @PostMapping("/save")
    public Result<Void> saveTableHeading(@RequestBody @Validated TableHeading tableHeading) {
        boolean result = headingSignsService.saveTableHeadingCache(tableHeading);
        return Result.judge(result);
    }
}
