package com.xc.boot.system.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.system.model.dto.CompanyFormDTO;
import com.xc.boot.system.model.dto.CompanyStatusDTO;
import com.xc.boot.system.model.query.CompanyPageQuery;
import com.xc.boot.system.model.vo.CompanyPageVO;
import com.xc.boot.system.service.CompanyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 商家管理控制层
 */
@Tag(name = "超管-商家管理")
@RestController
@RequestMapping("/api/company")
@RequiredArgsConstructor
public class CompanyController {

    private final CompanyService companyService;

    @Operation(summary = "商家分页列表")
    @PostMapping("/page")
    public PageResult<CompanyPageVO> getCompanyPage(@RequestBody CompanyPageQuery queryParams) {
        Page<CompanyPageVO> result = companyService.getCompanyPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增商家")
    @PostMapping
    public Result<Boolean> addCompany(@RequestBody @Valid CompanyFormDTO form) {
        boolean result = companyService.saveCompany(form);
        return Result.success(result);
    }

    @Operation(summary = "编辑商家")
    @PutMapping
    public Result<Boolean> editCompany(@RequestBody @Valid CompanyFormDTO form) {
        boolean result = companyService.saveCompany(form);
        return Result.success(result);
    }

    @Operation(summary = "修改商家状态")
    @PutMapping("/status")
    public Result<Boolean> updateCompanyStatus(@RequestBody @Valid CompanyStatusDTO dto) {
        boolean result = companyService.updateCompanyStatus(dto.getId(), dto.getStatus());
        return Result.success(result);
    }
}
