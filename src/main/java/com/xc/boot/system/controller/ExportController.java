package com.xc.boot.system.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.Result;
import com.xc.boot.system.model.query.ExportPageQuery;
import com.xc.boot.system.model.vo.ExportVo;
import com.xc.boot.system.service.ExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @ClassName ExportController
 * @Date: 2025/6/7 16:12
 * @Description: 描述
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "系统-下载中心")
@RequestMapping("/api/export")
public class ExportController {
    private final ExportService exportService;

    @Operation(summary = "下载次数增加")
    @GetMapping("/count")
    public Result<?> downloadCount(@RequestParam @NotNull @Valid Long id){
        exportService.downloadCount(id);
        return Result.judge(true);
    }

    @Operation(summary = "查询导出列表")
    @PostMapping("/page")
    public Result<Page<ExportVo>> page(@RequestBody ExportPageQuery queryParams){
        Page<ExportVo> page = exportService.pageList(queryParams);
        return Result.success(page);
    }

    @Operation(summary = "删除")
    @DeleteMapping
    public Result<?> delete(@RequestBody @Validated DeleteRequest request){
        boolean result = exportService.removeExport(request.getId());
        return Result.judge(result);
    }
}
