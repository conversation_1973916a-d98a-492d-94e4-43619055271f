package com.xc.boot.system.controller;

import com.xc.boot.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @date 2025/1/8
 * @description
 */

@RestController
@RequiredArgsConstructor
@Tag(name = "系统配置")
@RequestMapping("")
public class SystemController {

    @Value("${spring.profiles.active}")
    private String env;
    @Operation(summary = "健康检查")
    @GetMapping("/health")
    public Result<?> healthCheck() {
        return Result.success(env);
    }

}
