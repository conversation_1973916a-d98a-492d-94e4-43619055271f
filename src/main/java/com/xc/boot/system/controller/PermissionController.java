package com.xc.boot.system.controller;

import com.xc.boot.common.result.Result;
import com.xc.boot.common.enums.LogModuleEnum;
import com.xc.boot.common.annotation.RepeatSubmit;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.system.model.form.PermissionForm;
import com.xc.boot.system.model.query.PermissionQuery;
import com.xc.boot.system.model.vo.PermissionVO;
import com.xc.boot.common.model.Option;
import com.xc.boot.common.annotation.Log;
import com.xc.boot.system.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限控制层
 */
@Tag(name = "超管-权限接口")
@RestController
@RequestMapping("/api/permissions")
@RequiredArgsConstructor
@Slf4j
public class PermissionController {

    private final PermissionService permissionService;

    @Operation(summary = "权限列表")
    @GetMapping
    @Log(value = "权限列表", module = LogModuleEnum.SETTING)
    public Result<List<PermissionVO>> listPermissions(PermissionQuery queryParams) {
        List<PermissionVO> permissionList = permissionService.listPermissions(queryParams);
        return Result.success(permissionList);
    }

    @Operation(summary = "权限下拉选项")
    @GetMapping("/options")
    public Result<List<Option<Long>>> listPermissionOptions() {
        List<Option<Long>> options = permissionService.listPermissionOptions();
        return Result.success(options);
    }

    @Operation(summary = "新增权限")
    @PostMapping
    @RepeatSubmit
    public Result<?> addPermission(@RequestBody PermissionForm permissionForm) {
        boolean result = permissionService.savePermission(permissionForm);
        return Result.judge(result);
    }

    @Operation(summary = "修改权限")
    @PutMapping
    public Result<?> updatePermission(@RequestBody PermissionForm permissionForm) {
        boolean result = permissionService.savePermission(permissionForm);
        return Result.judge(result);
    }

    @Operation(summary = "删除权限")
    @PostMapping("/delete")
    public Result<?> deletePermission(@RequestBody DeleteRequest request) {
        boolean result = permissionService.deletePermission(request.getId());
        return Result.judge(result);
    }
} 