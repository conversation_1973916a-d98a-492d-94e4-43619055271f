package com.xc.boot.system.mapper;

import com.xc.boot.system.model.bo.VisitStatsBO;
import com.xc.boot.system.model.entity.SysLogEntity;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;


/**
 * 系统日志数据访问层
 *
 * <AUTHOR>
 * @since 2.10.0
 */
@Mapper
public interface LogMapper extends BaseMapper<SysLogEntity> {

    /**
     * 获取浏览量(PV)统计
     */
    VisitStatsBO getPvStats();

    /**
     * 获取访问IP统计
     */
    VisitStatsBO getUvStats();
}




