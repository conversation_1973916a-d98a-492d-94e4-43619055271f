package com.xc.boot.system.mapper;

import com.mybatisflex.core.BaseMapper;
import com.xc.boot.system.model.bo.RouteBO;
import com.xc.boot.system.model.entity.SysMenuEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * 菜单访问层
 *
 * <AUTHOR>
 * @since 2022/1/24
 */

@Mapper
public interface MenuMapper extends BaseMapper<SysMenuEntity> {

    /**
     * 获取菜单路由列表
     */
    List<RouteBO> listRoutes(Set<String> roles);

}
