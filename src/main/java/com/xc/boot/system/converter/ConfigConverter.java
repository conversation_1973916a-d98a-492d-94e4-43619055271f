package com.xc.boot.system.converter;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.system.model.entity.SysConfigEntity;
import com.xc.boot.system.model.vo.ConfigVO;
import com.xc.boot.system.model.form.ConfigForm;
import org.mapstruct.Mapper;

/**
 * 系统配置对象转换器
 *
 * <AUTHOR>
 * @since 2024-7-29 11:42:49
 */
@Mapper(componentModel = "spring")
public interface ConfigConverter {

    Page<ConfigVO> toPageVo(Page<SysConfigEntity> page);

    SysConfigEntity toEntity(ConfigForm configForm);

    ConfigForm toForm(SysConfigEntity entity);
}
