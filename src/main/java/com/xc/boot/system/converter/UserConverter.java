package com.xc.boot.system.converter;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.system.model.entity.SysUserEntity;
import com.xc.boot.system.model.vo.UserInfoVO;
import com.xc.boot.system.model.vo.UserPageVO;
import com.xc.boot.system.model.vo.UserProfileVO;
import com.xc.boot.system.model.bo.UserBO;
import com.xc.boot.system.model.form.UserForm;
import com.xc.boot.system.model.dto.UserImportDTO;
import com.xc.boot.system.model.form.UserProfileForm;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

/**
 * 用户对象转换器
 *
 * <AUTHOR>
 * @since 2022/6/8
 */
@Mapper(componentModel = "spring")
public interface UserConverter {

    UserPageVO toPageVo(UserBO bo);

    Page<UserPageVO> toPageVo(Page<UserBO> bo);

    UserForm toForm(SysUserEntity entity);

    @InheritInverseConfiguration(name = "toForm")
    SysUserEntity toEntity(UserForm entity);

    UserInfoVO toUserInfoVo(SysUserDetails entity);

    SysUserEntity toEntity(UserImportDTO vo);


    UserProfileVO toProfileVO(UserBO bo);

    SysUserEntity toEntity(UserProfileForm formData);
}
