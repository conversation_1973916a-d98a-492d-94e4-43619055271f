package com.xc.boot.system.converter;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.system.model.entity.SysRoleEntity;
import com.xc.boot.system.model.vo.RolePageVO;
import com.xc.boot.common.model.Option;
import com.xc.boot.system.model.form.RoleForm;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * 角色对象转换器
 *
 * <AUTHOR>
 * @since 2022/5/29
 */
@Mapper(componentModel = "spring")
public interface RoleConverter {

    Page<RolePageVO> toPageVo(Page<SysRoleEntity> page);

    @Mappings({
            @Mapping(target = "value", source = "id"),
            @Mapping(target = "label", source = "name")
    })
    Option<Long> entity2Option(SysRoleEntity role);

    List<Option<Long>> entities2Options(List<SysRoleEntity> roles);

    SysRoleEntity toEntity(RoleForm roleForm);

    RoleForm toForm(SysRoleEntity entity);
}