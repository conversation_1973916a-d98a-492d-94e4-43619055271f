package com.xc.boot.system.converter;

import com.xc.boot.system.model.entity.SysMenuEntity;
import com.xc.boot.system.model.vo.MenuVO;
import com.xc.boot.system.model.form.MenuForm;
import org.mapstruct.Mapper;

/**
 * 菜单对象转换器
 *
 * <AUTHOR>
 * @since 2024/5/26
 */
@Mapper(componentModel = "spring")
public interface MenuConverter {

    MenuVO toVo(SysMenuEntity entity);

    MenuForm toForm(SysMenuEntity entity);

    SysMenuEntity toEntity(MenuForm menuForm);

}