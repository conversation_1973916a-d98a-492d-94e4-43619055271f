package com.xc.boot.system.converter;

import com.xc.boot.system.model.entity.SysDeptEntity;
import com.xc.boot.system.model.vo.DeptVO;
import com.xc.boot.system.model.form.DeptForm;
import org.mapstruct.Mapper;

/**
 * 部门对象转换器
 *
 * <AUTHOR>
 * @since 2022/7/29
 */
@Mapper(componentModel = "spring")
public interface DeptConverter {

    DeptForm toForm(SysDeptEntity entity);
    
    DeptVO toVo(SysDeptEntity entity);

    SysDeptEntity toEntity(DeptForm deptForm);

}