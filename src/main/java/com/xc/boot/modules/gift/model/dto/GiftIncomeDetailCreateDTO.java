package com.xc.boot.modules.gift.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;
import com.xc.boot.common.base.FileItemDTO;

@Data
@Schema(description = "赠品入库明细创建")
public class GiftIncomeDetailCreateDTO {
    @Schema(description = "赠品编号")
    @NotBlank(message = "赠品编号不能为空")
    private String giftSn;

    @Schema(description = "赠品名称")
    @NotBlank(message = "赠品名称不能为空")
    private String giftName;

    @Schema(description = "数量")
    @NotNull(message = "数量不能为空")
    @Positive(message = "数量必须大于0")
    private Integer num;

    @Schema(description = "重量(g)")
    @NotNull(message = "重量不能为空")
    private BigDecimal weight;

    @Schema(description = "成本单价(元)")
    @NotNull(message = "成本单价不能为空")
    private BigDecimal costPrice;

    @Schema(description = "标签单价(元)")
    @NotNull(message = "标签单价不能为空")
    private BigDecimal tagPrice;

    @Schema(description = "赠品图片")
    private List<FileItemDTO> image;
} 