package com.xc.boot.modules.gift.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.xc.boot.common.base.FileItemDTO;
import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "赠品列表分页VO")
public class GiftPageVO {
    @Schema(description = "赠品ID")
    private Long id;

    @Schema(description = "所属门店ID")
    private Integer merchantId;

    @Schema(description = "门店名称")
    private String merchantName;

    @Schema(description = "供应商ID")
    private Integer supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "赠品编号")
    private String giftSn;

    @Schema(description = "赠品名称")
    private String name;

    @Schema(description = "原始数量")
    private Integer num;

    @Schema(description = "库存数量")
    private Integer stockNum;

    @Schema(description = "售出数量")
    private Integer soldNum;

    @Schema(description = "调拨中数量")
    private Integer transferNum;

    @Schema(description = "冻结数量")
    private Integer frozenNum;

    @Schema(description = "重量(g)")
    private BigDecimal weight;

    @Schema(description = "成本单价(元)")
    private BigDecimal costPrice;

    @Schema(description = "成本总价(元)")
    private BigDecimal totalCostPrice;

    @Schema(description = "标签单价(元)")
    private BigDecimal tagPrice;

    @Schema(description = "标签总价(元)")
    private BigDecimal totalTagPrice;

    @Schema(description = "赠品图片")
    private List<FileItemDTO> images;
} 