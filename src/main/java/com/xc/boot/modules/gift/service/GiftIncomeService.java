package com.xc.boot.modules.gift.service;

import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.gift.model.entity.GiftIncomeEntity;
import com.xc.boot.modules.gift.model.dto.GiftIncomeCreateDTO;
import com.xc.boot.modules.gift.model.dto.GiftIncomeUpdateDTO;
import com.xc.boot.modules.gift.model.dto.GiftIncomeDetailUpdateDTO;
import com.xc.boot.modules.gift.model.vo.GiftSnCheckVO;
import com.xc.boot.modules.gift.model.vo.GiftIncomeInfoVO;
import com.xc.boot.modules.gift.model.query.GiftIncomePageQuery;
import com.xc.boot.modules.gift.model.query.GiftIncomeDetailPageQuery;
import com.xc.boot.modules.gift.model.vo.GiftIncomePageVO;
import com.xc.boot.modules.gift.model.vo.GiftIncomeDetailPageVO;
import com.mybatisflex.core.paginate.Page;

/**
 * 赠品入库服务接口
 */
public interface GiftIncomeService extends IService<GiftIncomeEntity> {
    /**
     * 创建赠品入库单
     */
    Integer create(GiftIncomeCreateDTO dto);

    /**
     * 检查赠品条码
     */
    GiftSnCheckVO checkGiftSn(String giftSn);

    /**
     * 赠品入库单分页列表
     */
    Page<GiftIncomePageVO> getIncomePage(GiftIncomePageQuery query);

    /**
     * 编辑赠品入库单
     */
    boolean update(GiftIncomeUpdateDTO dto);

    /**
     * 删除赠品入库单
     */
    boolean delete(Long id);

    /**
     * 获取赠品入库单信息
     */
    GiftIncomeInfoVO getIncomeInfo(Long id);

    /**
     * 赠品入库单明细分页列表
     */
    Page<GiftIncomeDetailPageVO> getIncomeDetailPage(GiftIncomeDetailPageQuery query);

    /**
     * 编辑赠品入库单明细
     */
    boolean updateIncomeDetail(Long id, GiftIncomeDetailUpdateDTO dto);

    /**
     * 删除赠品入库单明细
     */
    boolean deleteIncomeDetail(Long id);
} 