package com.xc.boot.modules.gift.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
 * 赠品实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "gift")
public class GiftEntity extends BaseEntity {
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 所属门店
     */
    @Column(value = "merchant_id")
    private Integer merchantId;

    /**
     * 供应商ID
     */
    @Column(value = "supplier_id")
    private Integer supplierId;

    /**
     * 赠品编号
     */
    @Column(value = "gift_sn")
    private String giftSn;

    /**
     * 赠品名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 原始数量
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 库存数量
     */
    @Column(value = "stock_num")
    private Integer stockNum;

    /**
     * 售出数量
     */
    @Column(value = "sold_num")
    private Integer soldNum;

    /**
     * 调拨中数量
     */
    @Column(value = "transfer_num")
    private Integer transferNum;

    /**
     * 冻结数量
     */
    @Column(value = "frozen_num")
    private Integer frozenNum;

    /**
     * 重量(g)
     */
    @Column(value = "weight")
    private BigDecimal weight;

    /**
     * 成本单价(分)
     */
    @Column(value = "cost_price")
    private Integer costPrice;

    /**
     * 标签单价(分)
     */
    @Column(value = "tag_price")
    private Integer tagPrice;
}
