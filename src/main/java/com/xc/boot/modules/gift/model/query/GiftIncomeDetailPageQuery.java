package com.xc.boot.modules.gift.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.xc.boot.common.base.BasePageQuery;
import java.util.List;

@Data
@Schema(description = "赠品入库单明细分页查询")
public class GiftIncomeDetailPageQuery extends BasePageQuery {
    @Schema(description = "入库单ID")
    private Long id;

    @Schema(description = "明细ID列表")
    private List<Long> ids;

    @Schema(description = "导出标识(1:导出)")
    private Integer export;

    @Schema(description = "打印标识(1:打印)")
    private Integer print;
} 