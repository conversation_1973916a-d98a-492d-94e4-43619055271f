package com.xc.boot.modules.gift.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 赠品库存变化记录
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "gift_num_logs")
public class GiftNumLogsEntity extends BaseEntity {
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 赠品ID
     */
    @Column(value = "gift_id")
    private Long giftId;

    /**
     * 变更描述
     */
    @Column(value = "comment")
    private String comment;

    /**
     * 原始数量(变化量)
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 库存数量(变化量)
     */
    @Column(value = "stock_num")
    private Integer stockNum;

    /**
     * 售出数量(变化量)
     */
    @Column(value = "sold_num")
    private Integer soldNum;

    /**
     * 调拨数量(变化量)
     */
    @Column(value = "transfer_num")
    private Integer transferNum;

    /**
     * 冻结数量(变化量)
     */
    @Column(value = "frozen_num")
    private Integer frozenNum;

    /**
     * 操作人
     */
    @Column(value = "created_by")
    private Long createdBy;
} 