package com.xc.boot.modules.gift.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.xc.boot.common.base.BasePageQuery;
import java.math.BigDecimal;

@Data
@Schema(description = "赠品列表分页查询")
public class GiftPageQuery extends BasePageQuery {
    @Schema(description = "赠品编号")
    private String giftSn;

    @Schema(description = "赠品名称")
    private String name;

    @Schema(description = "所属门店ID(多个用逗号分隔)")
    private String merchantIds;

    @Schema(description = "供应商ID(多个用逗号分隔)")
    private String supplierIds;

    @Schema(description = "成本单价起始值(元)")
    private BigDecimal costPriceStart;

    @Schema(description = "成本单价结束值(元)")
    private BigDecimal costPriceEnd;

    @Schema(description = "标签单价起始值(元)")
    private BigDecimal tagPriceStart;

    @Schema(description = "标签单价结束值(元)")
    private BigDecimal tagPriceEnd;
} 