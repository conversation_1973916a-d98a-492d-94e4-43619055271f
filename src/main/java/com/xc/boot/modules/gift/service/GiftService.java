package com.xc.boot.modules.gift.service;

import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.gift.model.entity.GiftEntity;
import com.xc.boot.modules.gift.model.query.GiftPageQuery;
import com.xc.boot.modules.gift.model.vo.GiftPageVO;
import com.xc.boot.modules.gift.model.dto.GiftUpdateDTO;
import com.mybatisflex.core.paginate.Page;

/**
 * 赠品服务接口
 */
public interface GiftService extends IService<GiftEntity> {
    /**
     * 赠品列表分页查询
     */
    Page<GiftPageVO> getGiftPage(GiftPageQuery query);
    
    /**
     * 编辑赠品
     */
    boolean updateGift(GiftUpdateDTO dto);
} 