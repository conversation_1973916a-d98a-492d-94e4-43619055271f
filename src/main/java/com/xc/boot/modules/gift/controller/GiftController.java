package com.xc.boot.modules.gift.controller;

import com.xc.boot.modules.gift.service.GiftService;
import com.xc.boot.modules.gift.model.query.GiftPageQuery;
import com.xc.boot.modules.gift.model.vo.GiftPageVO;
import com.xc.boot.modules.gift.model.dto.GiftUpdateDTO;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.mybatisflex.core.paginate.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;

/**
 * 赠品控制层
 */
@Tag(name = "赠品-赠品管理")
@RestController
@RequestMapping("/api/gift")
@RequiredArgsConstructor
public class GiftController {
    private final GiftService giftService;

    @Operation(summary = "赠品列表分页查询")
    @PostMapping("/page")
    public PageResult<GiftPageVO> getGiftPage(@RequestBody GiftPageQuery query) {
        Page<GiftPageVO> page = giftService.getGiftPage(query);
        return PageResult.success(page);
    }

    @Operation(summary = "编辑赠品")
    @PostMapping("/edit")
    public Result<Boolean> editGift(@RequestBody @Valid GiftUpdateDTO dto) {
        boolean result = giftService.updateGift(dto);
        return Result.success(result);
    }
} 