package com.xc.boot.modules.gift.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "调拨单明细视图对象")
public class GiftTransferDetailVO {

    @Schema(description = "调拨单明细ID")
    private Long id;

    @Schema(description = "赠品ID")
    private Long giftId;

    @Schema(description = "赠品编号")
    private String giftSn;

    @Schema(description = "赠品名称")
    private String name;

    @Schema(description = "所属门店")
    private String merchantName;

    @Schema(description = "赠品图片")
    private List<String> images;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "最大可出库数量")
    private Integer maxNum;

    @Schema(description = "单重(g)")
    private BigDecimal weight;

    @Schema(description = "总重量(g)")
    private BigDecimal totalWeight;

    @Schema(description = "成本单价")
    private BigDecimal costPrice;

    @Schema(description = "成本总价")
    private BigDecimal totalCostPrice;

    @Schema(description = "标签单价")
    private BigDecimal tagPrice;

    @Schema(description = "标签总价")
    private BigDecimal totalTagPrice;

    @Schema(description = "备注")
    private String remark;
}
