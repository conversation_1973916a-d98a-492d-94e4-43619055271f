package com.xc.boot.modules.gift.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 赠品调拨单实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "gift_transfer")
public class GiftTransferEntity extends BaseEntity {
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 调拨单号
     */
    @Column(value = "transfer_sn")
    private String transferSn;

    /**
     * 调出门店
     */
    @Column(value = "merchant_outcome")
    private Integer merchantOutcome;

    /**
     * 调入门店
     */
    @Column(value = "merchant_income")
    private Integer merchantIncome;

    /**
     * 状态 (0-待审核 1-调拨中 2-已完成)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 总数量
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 总成本价(分)
     */
    @Column(value = "total_cost_price")
    private Integer totalCostPrice;

    /**
     * 总标签价(分)
     */
    @Column(value = "total_tag_price")
    private Integer totalTagPrice;

    /**
     * 调拨单备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 审核人
     */
    @Column(value = "audit_by")
    private Integer auditBy;

    /**
     * 审核时间
     */
    @Column(value = "audit_at")
    private Date auditAt;

    /**
     * 收货人
     */
    @Column(value = "receipt_by")
    private Integer receiptBy;

    /**
     * 收货时间
     */
    @Column(value = "receipt_at")
    private Date receiptAt;

    /**
     * 收货备注
     */
    @Column(value = "receipt_remark")
    private String receiptRemark;

    /**
     * 创建人
     */
    @Column(value = "created_by")
    private Integer createdBy;

    /**
     * 删除时间
     */
    @Column(value = "deleted_at")
    private Date deletedAt;
} 