package com.xc.boot.modules.gift.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 赠品入库明细实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "gift_income_detail")
public class GiftIncomeDetailEntity extends BaseEntity {
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 所属门店
     */
    @Column(value = "merchant_id")
    private Integer merchantId;

    /**
     * 供应商ID
     */
    @Column(value = "supplier_id")
    private Integer supplierId;

    /**
     * 入库单ID
     */
    @Column(value = "receive_id")
    private Integer receiveId;

    /**
     * 入库单号
     */
    @Column(value = "income_code")
    private String incomeCode;

    /**
     * 赠品编号
     */
    @Column(value = "gift_sn")
    private String giftSn;

    /**
     * 赠品ID
     */
    @Column(value = "gift_id")
    private Integer giftId;

    /**
     * 赠品名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 数量
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 重量(g)
     */
    @Column(value = "weight")
    private BigDecimal weight;

    /**
     * 成本单价(分)
     */
    @Column(value = "cost_price")
    private Integer costPrice;

    /**
     * 标签单价(分)
     */
    @Column(value = "tag_price")
    private Integer tagPrice;

    /**
     * 状态(0:待审核,1:已审核)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 审核人ID
     */
    @Column(value = "audit_by")
    private Integer auditBy;

    /**
     * 审核时间
     */
    @Column(value = "audit_at")
    private Date auditAt;
}
