package com.xc.boot.modules.gift.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 赠品调拨单明细实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "gift_transfer_detail")
public class GiftTransferDetailEntity extends BaseEntity {
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 调拨单ID
     */
    @Column(value = "transfer_id")
    private Integer transferId;

    /**
     * 赠品ID
     */
    @Column(value = "gift_id")
    private Integer giftId;

    /**
     * 调拨后的赠品ID
     */
    @Column(value = "target_gift_id")
    private Integer targetGiftId;

    /**
     * 调拨数量
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 删除时间
     */
    @Column(value = "deleted_at")
    private Date deletedAt;
} 