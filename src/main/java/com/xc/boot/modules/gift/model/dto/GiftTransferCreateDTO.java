package com.xc.boot.modules.gift.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "赠品调拨单创建DTO")
public class GiftTransferCreateDTO {

    @NotNull(message = "调出门店不能为空")
    @Schema(description = "调出门店ID")
    private Integer merchantOutcome;

    @NotNull(message = "调入门店不能为空")
    @Schema(description = "调入门店ID")
    private Integer merchantIncome;

    @Schema(description = "备注")
    private String remark;

    @Valid
    @NotEmpty(message = "调拨明细不能为空")
    @Schema(description = "调拨明细")
    private List<Detail> details;

    @Data
    @Schema(description = "调拨明细")
    public static class Detail {
        @NotNull(message = "赠品ID不能为空")
        @Schema(description = "赠品ID")
        private Long id;

        @NotNull(message = "数量不能为空")
        @Schema(description = "调拨数量")
        private Integer num;
    }
} 