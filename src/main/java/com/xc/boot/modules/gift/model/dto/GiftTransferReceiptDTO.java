package com.xc.boot.modules.gift.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "赠品调拨单收货DTO")
public class GiftTransferReceiptDTO {

    @NotNull(message = "ID不能为空")
    @Schema(description = "调拨单ID")
    private Long id;

    @Schema(description = "收货备注")
    private String receiptRemark;
} 