package com.xc.boot.modules.gift.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 赠品图片关联表
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "gift_has_images")
public class GiftHasImagesEntity extends BaseEntity {
    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 赠品ID
     */
    @Column(value = "gift_id")
    private Integer giftId;

    /**
     * 图片ID
     */
    @Column(value = "image_id")
    private Integer imageId;

    /**
     * 图片URL
     */
    @Column(value = "url")
    private String url;

    /**
     * 排序号
     */
    @Column(value = "sort")
    private Integer sort;
}
