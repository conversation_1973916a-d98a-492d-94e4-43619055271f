package com.xc.boot.modules.gift.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "赠品调拨单分页查询")
public class GiftTransferPageQuery extends BasePageQuery {

    @Schema(description = "调拨单号")
    private String transferSn;

    @Schema(description = "调出门店ID, 逗号分割")
    private String merchantOutcomeIds;

    @Schema(description = "调入门店ID, 逗号分割")
    private String merchantIncomeIds;

    @Schema(description = "状态 (0-待审核 1-调拨中 2-已完成)")
    private Integer status;

    @Schema(description = "创建人ID, 逗号分割")
    private String createdByIds;

    @Schema(description = "审核人ID, 逗号分割")
    private String auditByIds;

    @Schema(description = "收货人ID, 逗号分割")
    private String receiptByIds;

    @Schema(description = "创建时间范围")
    private List<String> createdAt;

    @Schema(description = "审核时间范围")
    private List<String> auditAt;

    @Schema(description = "收货时间范围")
    private List<String> receiptAt;
} 