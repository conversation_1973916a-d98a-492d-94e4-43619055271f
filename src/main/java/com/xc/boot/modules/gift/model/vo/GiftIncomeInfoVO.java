package com.xc.boot.modules.gift.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "赠品入库单信息VO")
public class GiftIncomeInfoVO {
    @Schema(description = "入库单号")
    private String incomeCode;

    @Schema(description = "所属门店ID")
    private Integer merchantId;
    @Schema(description = "门店名称")
    private String storeName;

    @Schema(description = "供应商ID")
    private Integer supplierId;
    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "状态")
    private Integer status;
    @Schema(description = "状态")
    private String statusLabel;

    @Schema(description = "总数量")
    private Integer num;

    @Schema(description = "总重量(g)")
    private BigDecimal totalWeight;

    @Schema(description = "总成本价(元)")
    private BigDecimal totalCostPrice;

    @Schema(description = "总标签价(元)")
    private BigDecimal totalTagPrice;

    @Schema(description = "创建人ID")
    private Integer createdBy;
    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "审核人ID")
    private Integer auditBy;
    @Schema(description = "审核人")
    private String auditor;

    @Schema(description = "审核时间")
    private Date auditAt;

    @Schema(description = "备注")
    private String remark;
} 