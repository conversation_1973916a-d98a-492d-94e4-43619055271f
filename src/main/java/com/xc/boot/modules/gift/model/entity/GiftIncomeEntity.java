package com.xc.boot.modules.gift.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 赠品入库单实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "gift_income")
public class GiftIncomeEntity extends BaseEntity {
    /**
     * 入库单号
     */
    @Column(value = "income_code")
    private String incomeCode;

    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 所属门店ID
     */
    @Column(value = "merchant_id")
    private Integer merchantId;

    /**
     * 供应商ID
     */
    @Column(value = "supplier_id")
    private Integer supplierId;

    /**
     * 入库数量
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 总重量(g)
     */
    @Column(value = "total_weight")
    private BigDecimal totalWeight;

    /**
     * 总成本价(分)
     */
    @Column(value = "total_cost_price")
    private Long totalCostPrice;

    /**
     * 总标签价(分)
     */
    @Column(value = "total_tag_price")
    private Long totalTagPrice;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 状态(0:待审核,1:已审核)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 审核人ID
     */
    @Column(value = "audit_by")
    private Integer auditBy;

    /**
     * 审核时间
     */
    @Column(value = "audit_at")
    private Date auditAt;

    /**
     * 创建人ID
     */
    @Column(value = "created_by")
    private Integer createdBy;
}
