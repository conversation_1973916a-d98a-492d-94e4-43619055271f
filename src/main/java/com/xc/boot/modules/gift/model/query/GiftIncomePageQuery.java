package com.xc.boot.modules.gift.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.xc.boot.common.base.BasePageQuery;
import java.util.List;

@Data
@Schema(description = "赠品入库单分页查询")
public class GiftIncomePageQuery extends BasePageQuery {
    @Schema(description = "入库单号")
    private String incomeCode;

    @Schema(description = "所属门店ID(多个用逗号分隔)")
    private String merchantIds;

    @Schema(description = "供应商ID(多个用逗号分隔)")
    private String supplierIds;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "创建时间范围，格式：yyyy-MM-dd HH:mm:ss")
    private List<String> createdAtRange;

    @Schema(description = "审核时间范围，格式：yyyy-MM-dd HH:mm:ss")
    private List<String> auditAtRange;

    @Schema(description = "创建人ID(多个用逗号分隔)")
    private String createdByIds;

    @Schema(description = "审核人ID(多个用逗号分隔)")
    private String auditByIds;
} 