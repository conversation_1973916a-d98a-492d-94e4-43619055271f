package com.xc.boot.modules.gift.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "编辑赠品入库单")
public class GiftIncomeUpdateDTO {
    @Schema(description = "入库单ID")
    @NotNull(message = "入库单ID不能为空")
    private Integer id;

    @Schema(description = "备注")
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;
} 