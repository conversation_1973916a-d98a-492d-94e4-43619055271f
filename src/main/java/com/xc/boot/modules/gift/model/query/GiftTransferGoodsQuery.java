package com.xc.boot.modules.gift.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "调拨赠品查询")
public class GiftTransferGoodsQuery {

    @NotNull(message = "调出门店不能为空")
    @Schema(description = "调出门店ID")
    private Integer merchantOutcome;

    @Schema(description = "赠品编号")
    private String giftSn;
} 