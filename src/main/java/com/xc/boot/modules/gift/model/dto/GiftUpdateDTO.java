package com.xc.boot.modules.gift.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.xc.boot.common.base.FileItemDTO;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "赠品编辑DTO")
public class GiftUpdateDTO {
    
    @Schema(description = "赠品ID", required = true)
    @NotNull(message = "赠品ID不能为空")
    private Long id;
    
    @Schema(description = "赠品名称")
    private String name;
    
    @Schema(description = "赠品图片")
    private List<FileItemDTO> images;
    
    @Schema(description = "重量(g)")
    @DecimalMin(value = "0", message = "重量不能小于0")
    private BigDecimal weight;
    
    @Schema(description = "成本单价(元)")
    @DecimalMin(value = "0", message = "成本单价不能小于0")
    private BigDecimal costPrice;
    
    @Schema(description = "标签单价(元)")
    @DecimalMin(value = "0", message = "标签单价不能小于0")
    private BigDecimal tagPrice;
} 