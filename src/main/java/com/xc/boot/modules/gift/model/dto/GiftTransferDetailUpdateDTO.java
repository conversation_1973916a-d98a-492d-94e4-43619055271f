package com.xc.boot.modules.gift.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "赠品调拨单明细更新DTO")
public class GiftTransferDetailUpdateDTO {

    @NotNull(message = "明细ID不能为空")
    @Schema(description = "明细ID")
    private Long id;

    @NotNull(message = "数量不能为空")
    @Schema(description = "调拨数量")
    private Integer num;
} 