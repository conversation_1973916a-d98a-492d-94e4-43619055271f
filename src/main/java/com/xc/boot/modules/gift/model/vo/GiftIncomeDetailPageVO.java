package com.xc.boot.modules.gift.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.xc.boot.common.base.FileItemDTO;
import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "赠品入库单明细分页VO")
public class GiftIncomeDetailPageVO {
    @Schema(description = "明细ID")
    private Long id;

    @Schema(description = "入库单ID")
    private Long receiveId;

    @Schema(description = "入库单号")
    private String incomeCode;

    @Schema(description = "赠品编号")
    private String giftSn;

    @Schema(description = "赠品ID")
    private Integer giftId;

    @Schema(description = "赠品名称")
    private String name;

    @Schema(description = "供应商ID")
    private Integer supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "单重(g)")
    private BigDecimal weight;

    @Schema(description = "总重量(g)")
    private BigDecimal totalWeight;

    @Schema(description = "成本单价(元)")
    private BigDecimal costPrice;

    @Schema(description = "成本总价(元)")
    private BigDecimal totalCostPrice;

    @Schema(description = "标签单价(元)")
    private BigDecimal tagPrice;

    @Schema(description = "标签总价(元)")
    private BigDecimal totalTagPrice;

    @Schema(description = "状态")
    private Object status;

    @Schema(description = "审核人ID")
    private Integer auditBy;

    @Schema(description = "审核时间")
    private String auditAt;

    @Schema(description = "赠品图片")
    private List<FileItemDTO> image;
} 