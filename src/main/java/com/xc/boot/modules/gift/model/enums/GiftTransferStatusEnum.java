package com.xc.boot.modules.gift.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 赠品调拨单状态枚举
 */
@Getter
@AllArgsConstructor
public enum GiftTransferStatusEnum implements IBaseEnum<Integer> {
    /**
     * 待审核
     */
    PENDING(0, "待审核"),
    /**
     * 调拨中
     */
    TRANSFERRING(1, "调拨中"),
    /**
     * 已完成
     */
    COMPLETED(2, "已完成");

    private final Integer value;
    private final String label;
} 