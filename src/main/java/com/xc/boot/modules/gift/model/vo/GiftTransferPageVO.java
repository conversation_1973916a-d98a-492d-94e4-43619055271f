package com.xc.boot.modules.gift.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "赠品调拨单分页视图对象")
public class GiftTransferPageVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "调拨单号")
    private String transferSn;

    @Schema(description = "调出门店ID")
    private Integer merchantOutcome;

    @Schema(description = "调出门店")
    private String merchantOutcomeName;

    @Schema(description = "调入门店ID")
    private Integer merchantIncome;

    @Schema(description = "调入门店")
    private String merchantIncomeName;

    @Schema(description = "调拨数量")
    private Integer num;

    @Schema(description = "总成本价(元)")
    private BigDecimal totalCostPrice;

    @Schema(description = "总标签价(元)")
    private BigDecimal totalTagPrice;

    @Schema(description = "状态")
    private Object status;

    @Schema(description = "状态中文")
    private String statusLabel;

    @Schema(description = "创建人ID")
    private Integer createdBy;

    @Schema(description = "创建人")
    private String createdByName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @Schema(description = "审核人ID")
    private Integer auditBy;

    @Schema(description = "审核人")
    private String auditByName;

    @Schema(description = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditAt;

    @Schema(description = "收货人ID")
    private Integer receiptBy;

    @Schema(description = "收货人")
    private String receiptByName;

    @Schema(description = "收货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiptAt;

    @Schema(description = "收货备注")
    private String receiptRemark;

    @Schema(description = "备注")
    private String remark;
} 