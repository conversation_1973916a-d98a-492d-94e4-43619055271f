package com.xc.boot.modules.gift.model.vo;

import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.modules.gift.model.entity.GiftEntity;
import com.xc.boot.modules.gift.model.entity.GiftHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "赠品条码检查返回VO")
public class GiftSnCheckVO {
    @Schema(description = "赠品基础信息")
    private GiftInfo gift;

    @Schema(description = "赠品图片信息")
    private List<GiftHasImagesEntity> images;

    /**
     * 赠品信息VO（价格字段为元单位）
     */
    @Data
    @Schema(description = "赠品信息（价格为元单位）")
    public static class GiftInfo {
        @Schema(description = "赠品ID")
        private Long id;

        @Schema(description = "商户ID")
        private Integer companyId;

        @Schema(description = "所属门店")
        private Integer merchantId;

        @Schema(description = "供应商ID")
        private Integer supplierId;

        @Schema(description = "赠品编号")
        private String giftSn;

        @Schema(description = "赠品名称")
        private String name;

        @Schema(description = "原始数量")
        private Integer num;

        @Schema(description = "库存数量")
        private Integer stockNum;

        @Schema(description = "售出数量")
        private Integer soldNum;

        @Schema(description = "调拨中数量")
        private Integer transferNum;

        @Schema(description = "冻结数量")
        private Integer frozenNum;

        @Schema(description = "重量(g)")
        private BigDecimal weight;

        @Schema(description = "成本单价（元）")
        private BigDecimal costPrice;

        @Schema(description = "标签单价（元）")
        private BigDecimal tagPrice;

        /**
         * 从GiftEntity转换
         */
        public static GiftInfo fromEntity(GiftEntity entity) {
            if (entity == null) {
                return null;
            }

            GiftInfo info = new GiftInfo();
            info.setId(entity.getId());
            info.setCompanyId(entity.getCompanyId());
            info.setMerchantId(entity.getMerchantId());
            info.setSupplierId(entity.getSupplierId());
            info.setGiftSn(entity.getGiftSn());
            info.setName(entity.getName());
            info.setNum(entity.getNum());
            info.setStockNum(entity.getStockNum());
            info.setSoldNum(entity.getSoldNum());
            info.setTransferNum(entity.getTransferNum());
            info.setFrozenNum(entity.getFrozenNum());
            info.setWeight(entity.getWeight());

            // 价格字段转换为元
            info.setCostPrice(entity.getCostPrice() != null ? PriceUtil.fen2yuan(entity.getCostPrice()) : null);
            info.setTagPrice(entity.getTagPrice() != null ? PriceUtil.fen2yuan(entity.getTagPrice()) : null);

            return info;
        }
    }
}