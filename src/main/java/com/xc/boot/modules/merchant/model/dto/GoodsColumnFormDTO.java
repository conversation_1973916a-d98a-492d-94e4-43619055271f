package com.xc.boot.modules.merchant.model.dto;

import com.xc.boot.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品字段表单对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商品字段表单对象")
public class GoodsColumnFormDTO extends BaseEntity {

    @Schema(description = "字段名称")
    @NotBlank(message = "字段名称不能为空")
    private String name;

    @Schema(description = "机密级别(1-常规 2-敏感 3-机密)")
    @NotNull(message = "机密级别不能为空")
    private Integer secretLevel;

    @Schema(description = "字段属性(1-文本 2-数字 3-多行文本 4-日期 5-下拉列表 6-图片)")
    @NotNull(message = "字段属性不能为空")
    private Integer type;

    @Schema(description = "是否可多选")
    private Integer isMultiple;

    @Schema(description = "下拉选项(最多20个选项,json)")
    private String options;

    @Schema(description = "小数位数")
    private Integer numberPrecision;
} 