package com.xc.boot.modules.merchant.service.impl;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.merchant.model.dto.MerchantFormDTO;
import com.xc.boot.modules.merchant.model.query.MerchantQuery;
import com.xc.boot.modules.merchant.model.vo.MerchantVO;
import com.xc.boot.modules.merchant.service.MerchantService;
import com.xc.boot.modules.merchant.service.CounterService;
import com.xc.boot.system.mapper.MerchantMapper;
import com.xc.boot.system.model.entity.MerchantEntity;
import com.xc.boot.system.mapper.CompanyMapper;
import com.xc.boot.system.model.entity.CompanyEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class MerchantServiceImpl extends ServiceImpl<MerchantMapper, MerchantEntity> implements MerchantService {

    private final CounterService counterService;
    private final CompanyMapper companyMapper;

    @Override
    public Page<MerchantVO> getMerchantPage(MerchantQuery query) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(MerchantEntity::getId),
                        QueryMethods.column(MerchantEntity::getCompanyId),
                        QueryMethods.column(MerchantEntity::getName),
                        QueryMethods.column(MerchantEntity::getAddress),
                        QueryMethods.column(MerchantEntity::getPhone),
                        QueryMethods.column(MerchantEntity::getContact),
                        QueryMethods.column(MerchantEntity::getContactPhone),
                        QueryMethods.column(MerchantEntity::getStatus),
                        QueryMethods.column(MerchantEntity::getRemark),
                        QueryMethods.column(MerchantEntity::getCreatedAt),
                        QueryMethods.column(MerchantEntity::getUpdatedAt)
                )
                .from(MerchantEntity.class)
                .orderBy(MerchantEntity::getCreatedAt).desc()
                .eq(MerchantEntity::getCompanyId, SecurityUtils.getCompanyId())
                // 门店名称模糊查询
                .where(MerchantEntity::getName)
                .like(query.getName(), StringUtils.hasText(query.getName()))
                // 门店地址模糊查询
                .and(MerchantEntity::getAddress)
                .like(query.getAddress(), StringUtils.hasText(query.getAddress()))
                // 门店电话模糊查询
                .and(MerchantEntity::getPhone)
                .like(query.getPhone(), StringUtils.hasText(query.getPhone()))
                // 联系人模糊查询
                .and(MerchantEntity::getContact)
                .like(query.getContact(), StringUtils.hasText(query.getContact()))
                // 联系人电话模糊查询
                .and(MerchantEntity::getContactPhone)
                .like(query.getContactPhone(), StringUtils.hasText(query.getContactPhone()))
                // 状态精确匹配
                .and(MerchantEntity::getStatus)
                .eq(query.getStatus(), query.getStatus() != null);

        // 添加排序条件
        queryWrapper.orderBy(MerchantEntity::getId, false);

        // 获取当前用户的门店权限
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        if (!SecurityUtils.isMain() && merchantIds != null && !merchantIds.isEmpty()) {
            queryWrapper.and(MerchantEntity::getId).in(merchantIds);
        }

        // 执行分页查询并直接映射到VO
        return mapper.paginateAs(query.getPageNum(), query.getPageSize(), queryWrapper, MerchantVO.class);
    }

    @Override
    public boolean saveMerchant(MerchantFormDTO form) {
        // 获取当前商户ID
        Long companyId = SecurityUtils.getCompanyId();
        Assert.notNull(companyId, "商户ID不能为空");

        // 获取商家信息
        CompanyEntity company = companyMapper.selectOneById(companyId);
        Assert.notNull(company, "商家不存在");

        // 检查门店数量限制
        if (!company.getIsMultiple()) {
            // 如果不允许多门店，检查是否已有门店
            long merchantCount = this.count(
                    QueryWrapper.create()
                            .from(MerchantEntity.class)
                            .where(MerchantEntity::getCompanyId)
                            .eq(companyId));
            Assert.isTrue(merchantCount == 0, "该商家不允许多门店");
        } else {
            // 如果允许多门店，检查是否达到最大门店数
            long merchantCount = this.count(
                    QueryWrapper.create()
                            .from(MerchantEntity.class)
                            .where(MerchantEntity::getCompanyId)
                            .eq(companyId));
            Assert.isTrue(merchantCount < company.getMaxNumber(), "已达到最大门店数量限制");
        }

        // 检查门店名称是否已存在
        long count = this.count(
                QueryWrapper.create()
                        .from(MerchantEntity.class)
                        .where(MerchantEntity::getName)
                        .eq(form.getName())
                        .and(MerchantEntity::getCompanyId)
                        .eq(companyId));
        Assert.isTrue(count == 0, "门店名称已存在");

        // 创建门店实体
        MerchantEntity merchant = new MerchantEntity();
        merchant.setCompanyId(companyId.intValue());
        merchant.setName(form.getName());
        merchant.setAddress(form.getAddress());
        merchant.setPhone(form.getPhone());
        merchant.setContact(form.getContact());
        merchant.setContactPhone(form.getContactPhone());
        merchant.setStatus(form.getStatus() != null ? form.getStatus() : 0); // 默认禁用
        merchant.setRemark(form.getRemark());

        // 记录操作日志
        OpLogUtils.appendOpLog("门店管理-新增门店", "新增门店: " + merchant.getName(), merchant);

        // 保存门店信息
        boolean result = this.save(merchant);
        if (result) {
            // * 创建默认柜台
            counterService.createDefaultCounters(merchant.getId().intValue());
        }
        return result;
    }

    @Override
    public boolean updateMerchant(MerchantFormDTO form) {
        // 获取当前商户ID
        Long companyId = SecurityUtils.getCompanyId();
        Assert.notNull(companyId, "商户ID不能为空");

        // 检查门店是否存在
        MerchantEntity merchant = this.getById(form.getId());
        Assert.notNull(merchant, "门店不存在");
        Assert.isTrue(merchant.getCompanyId().equals(companyId.intValue()), "无权操作该门店");

        // 检查门店名称是否已存在（排除自身）
        long count = this.count(
                QueryWrapper.create()
                        .from(MerchantEntity.class)
                        .where(MerchantEntity::getName)
                        .eq(form.getName())
                        .and(MerchantEntity::getCompanyId)
                        .eq(companyId)
                        .and(MerchantEntity::getId)
                        .ne(form.getId()));
        Assert.isTrue(count == 0, "门店名称已存在");

        // 记录操作日志
        OpLogUtils.appendOpLog("门店管理-编辑门店", "编辑门店: " + merchant.getName(), 
            Map.of("修改前", merchant, "修改后", form));

        // 更新门店信息
        merchant.setName(form.getName());
        merchant.setAddress(form.getAddress());
        merchant.setPhone(form.getPhone());
        merchant.setContact(form.getContact());
        merchant.setContactPhone(form.getContactPhone());
        merchant.setStatus(form.getStatus());
        merchant.setRemark(form.getRemark());

        // 保存更新
        return this.updateById(merchant);
    }
} 