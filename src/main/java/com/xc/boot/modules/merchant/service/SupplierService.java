package com.xc.boot.modules.merchant.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.merchant.model.entity.SupplierEntity;
import com.xc.boot.modules.merchant.model.query.SupplierPageQuery;
import com.xc.boot.modules.merchant.model.vo.SupplierPageVO;

/**
 * 供应商服务接口
 */
public interface SupplierService extends IService<SupplierEntity> {

    /**
     * 获取供应商分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<SupplierPageVO> getSupplierPage(SupplierPageQuery queryParams);

    /**
     * 保存供应商
     *
     * @param entity 供应商实体
     * @return 是否成功
     */
    boolean saveSupplier(SupplierEntity entity);

    /**
     * 修改供应商状态
     *
     * @param id     供应商ID
     * @param status 供应商状态(1:启用；0:禁用)
     * @return 是否成功
     */
    boolean updateSupplierStatus(Long id, Integer status);

    /**
     * 删除供应商
     *
     * @param id 供应商ID
     * @return 是否成功
     */
    boolean deleteSupplier(Long id);
} 