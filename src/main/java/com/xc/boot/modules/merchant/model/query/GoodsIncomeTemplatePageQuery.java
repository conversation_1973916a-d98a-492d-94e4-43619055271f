package com.xc.boot.modules.merchant.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 入库模板分页查询对象
 */
@Schema(description = "入库模板分页查询对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsIncomeTemplatePageQuery extends BasePageQuery {

    @Schema(description = "模板名称")
    private String name;

    @Schema(description = "所属大类")
    private Integer categoryId;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "是否默认")
    private Integer defaultFlag;
} 