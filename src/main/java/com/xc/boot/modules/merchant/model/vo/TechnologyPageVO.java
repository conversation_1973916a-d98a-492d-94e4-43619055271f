package com.xc.boot.modules.merchant.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 工艺分页视图对象
 */
@Data
@Schema(description = "工艺分页视图对象")
public class TechnologyPageVO {

    @Schema(description = "工艺ID")
    private Long id;

    @Schema(description = "商户ID")
    private Integer companyId;

    @Schema(description = "工艺名称")
    private String name;

    @Schema(description = "说明")
    private String remark;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "状态(0:禁用;1:启用)")
    private Integer status;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;
} 