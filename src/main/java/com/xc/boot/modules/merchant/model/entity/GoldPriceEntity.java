package com.xc.boot.modules.merchant.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import com.xc.boot.common.listener.UpdatedByListenerFlag;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName GoldConfigEntity
 * @Date: 2025/6/9 17:04
 * @Description: 金价设置实体
 */
@Getter
@Setter
@Builder
@Accessors(chain = true)
@Table(value = "gold_price")
@AllArgsConstructor
@NoArgsConstructor
public class GoldPriceEntity extends BaseEntity implements CreatedByListenerFlag, UpdatedByListenerFlag {
    /**
     * 商家id
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 大类id
     */
    @Column(value = "category_id")
    private Long categoryId;

    /**
     * 成色id
     */
    @Column(value = "quality_id")
    private Long qualityId;

    /**
     * 销售价(分/ 克)
     */
    @Column(value = "sale_price")
    private BigDecimal salePrice;

    /**
     * 回收价(分/ 克)
     */
    @Column(value = "recycle_price")
    private BigDecimal recyclePrice;

    /**
     * 生效时间
     */
    @Column(value = "active_time")
    private Date activeTime;

    /**
     * 过期时间
     */
    @Column(value = "expire_time")
    private Date expireTime;

    /**
     * 状态(-1:已失效|0:待生效|1:生效中)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 调价原因
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 创建人
     */
    @Column(value = "created_by")
    private Long createdBy;

    /**
     * 更新人
     */
    @Column(value = "updated_by")
    private Long updatedBy;
}