package com.xc.boot.modules.merchant.service.impl;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.merchant.mapper.BrandMapper;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.income.mapper.GoodsIncomeDetailMapper;
import com.xc.boot.modules.merchant.model.dto.BrandFormDTO;
import com.xc.boot.modules.merchant.model.entity.BrandEntity;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.income.model.entity.GoodsIncomeDetailEntity;
import com.xc.boot.modules.merchant.model.enums.BrandStatusEnum;
import com.xc.boot.modules.merchant.model.query.BrandPageQuery;
import com.xc.boot.modules.merchant.model.vo.BrandPageVO;
import com.xc.boot.modules.merchant.service.BrandService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 品牌服务实现类
 */
@Service
@RequiredArgsConstructor
public class BrandServiceImpl extends ServiceImpl<BrandMapper, BrandEntity> implements BrandService {

    private final GoodsMapper goodsMapper;
    private final GoodsIncomeDetailMapper goodsIncomeDetailMapper;

    @Override
    public Page<BrandPageVO> getBrandPage(BrandPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(BrandEntity::getId),
                        QueryMethods.column(BrandEntity::getCompanyId),
                        QueryMethods.column(BrandEntity::getName),
                        QueryMethods.column(BrandEntity::getCompanyName),
                        QueryMethods.column(BrandEntity::getUrl),
                        QueryMethods.column(BrandEntity::getAddress),
                        QueryMethods.column(BrandEntity::getStatus),
                        QueryMethods.column(BrandEntity::getRemark),
                        QueryMethods.column(BrandEntity::getCreatedAt),
                        QueryMethods.column(BrandEntity::getUpdatedAt)
                )
                .from(BrandEntity.class)
                // 品牌名称模糊查询
                .where(BrandEntity::getName)
                .like(queryParams.getName(), StringUtils.hasText(queryParams.getName()))
                // 公司名称模糊查询
                .and(BrandEntity::getCompanyName)
                .like(queryParams.getCompanyName(), StringUtils.hasText(queryParams.getCompanyName()))
                // 状态精确匹配
                .and(BrandEntity::getStatus)
                .eq(queryParams.getStatus(), queryParams.getStatus() != null);

        // 添加排序条件
        queryWrapper.orderBy(BrandEntity::getId, false);

        // 执行分页查询并直接映射到VO
        return mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(), queryWrapper,
                BrandPageVO.class);
    }

    @Override
    public boolean saveBrand(BrandFormDTO form) {
        Long brandId = form.getId();
        Long companyId = SecurityUtils.getCompanyId();
        Assert.notNull(companyId, "未获取到商户信息");

        // 编辑品牌时，判断品牌是否存在
        BrandEntity oldBrand = null;
        if (brandId != null) {
            oldBrand = this.getById(brandId);
            Assert.isTrue(oldBrand != null, "品牌不存在");
            Assert.isTrue(oldBrand.getCompanyId().equals(companyId.intValue()), "无权操作该品牌");
        }

        // 检查品牌名称是否已存在
        long count = this.count(
                QueryWrapper.create()
                        .from(BrandEntity.class)
                        .ne(BrandEntity::getId, brandId, brandId != null)
                        .and(BrandEntity::getCompanyId).eq(companyId)
                        .and(BrandEntity::getName).eq(form.getName()));
        Assert.isTrue(count == 0, "品牌名称已存在");

        // 创建品牌实体
        BrandEntity brand = new BrandEntity();
        brand.setName(form.getName());
        brand.setCompanyId(companyId.intValue());
        brand.setCompanyName(form.getCompanyName());
        brand.setUrl(form.getUrl());
        brand.setAddress(form.getAddress());
        brand.setRemark(form.getRemark());
        brand.setStatus(form.getStatus());

        // 如果是编辑，设置ID
        if (oldBrand != null) {
            brand.setId(oldBrand.getId());
        }

        // 记录操作日志
        if (brandId == null) {
            OpLogUtils.appendOpLog("品牌管理-新增品牌", "新增品牌: " + brand.getName(), brand);
        } else {
            OpLogUtils.appendOpLog("品牌管理-编辑品牌", "编辑品牌: " + brand.getName(), 
                Map.of("修改前", oldBrand, "修改后", brand));
        }

        // 保存品牌信息
        return this.saveOrUpdate(brand);
    }

    @Override
    public boolean updateBrandStatus(Long id, Integer status) {
        BrandEntity brand = this.getById(id);
        Assert.isTrue(brand != null, "品牌不存在");
        Assert.isTrue(brand.getCompanyId().equals(SecurityUtils.getCompanyId().intValue()), "无权操作该品牌");

        // 记录操作日志
        OpLogUtils.appendOpLog("品牌管理-修改品牌状态", 
            String.format("修改品牌[%s]状态: %d -> %d", brand.getName(), brand.getStatus(), status),
            Map.of("品牌ID", id, "原状态", brand.getStatus(), "新状态", status));

        brand.setStatus(status);
        return this.updateById(brand);
    }

    @Override
    public boolean deleteBrand(Long id) {
        BrandEntity brand = this.getById(id);
        Assert.isTrue(brand != null, "品牌不存在");
        Assert.isTrue(brand.getCompanyId().equals(SecurityUtils.getCompanyId().intValue()), "无权操作该品牌");

        // 检查是否有关联的商品数据
        // 1. 检查商品表中是否使用了该品牌
        Long goodsCount = goodsMapper.selectCountByQuery(
            QueryWrapper.create()
                .from(GoodsEntity.class)
                .where(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsEntity::getBrandId).eq(id)
        );

        CommonUtils.abortIf(goodsCount > 0, "该品牌已被商品使用，不允许删除");

        // 2. 检查入库单中是否使用了该品牌
        Long incomeCount = goodsIncomeDetailMapper.selectCountByQuery(
            QueryWrapper.create()
                .from(GoodsIncomeDetailEntity.class)
                .where(GoodsIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsIncomeDetailEntity::getBrandId).eq(id)
        );

        CommonUtils.abortIf(incomeCount > 0, "该品牌已被入库单使用，不允许删除");

        // 记录操作日志
        OpLogUtils.appendOpLog("品牌管理-删除品牌", "删除品牌: " + brand.getName(), brand);

        return this.removeById(id);
    }
} 