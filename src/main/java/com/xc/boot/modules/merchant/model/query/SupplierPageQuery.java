package com.xc.boot.modules.merchant.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商分页查询对象")
public class SupplierPageQuery extends BasePageQuery {

    @Schema(description = "供应商编码")
    private String sign;

    @Schema(description = "供应商名称")
    private String name;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "联系人电话")
    private String contactPhone;

    @Schema(description = "状态(0:禁用;1:启用)")
    private Integer status;
} 