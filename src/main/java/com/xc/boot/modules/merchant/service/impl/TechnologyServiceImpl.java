package com.xc.boot.modules.merchant.service.impl;

import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.income.mapper.GoodsIncomeDetailMapper;
import com.xc.boot.modules.income.model.entity.GoodsIncomeDetailEntity;
import com.xc.boot.modules.merchant.mapper.TechnologyMapper;
import com.xc.boot.modules.merchant.model.entity.TechnologyEntity;
import com.xc.boot.modules.merchant.model.query.TechnologyPageQuery;
import com.xc.boot.modules.merchant.model.vo.TechnologyPageVO;
import com.xc.boot.modules.merchant.service.TechnologyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 工艺服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TechnologyServiceImpl extends ServiceImpl<TechnologyMapper, TechnologyEntity> implements TechnologyService {

    private final GoodsMapper goodsMapper;
    private final GoodsIncomeDetailMapper goodsIncomeDetailMapper;

    @Override
    public Page<TechnologyPageVO> getTechnologyPage(TechnologyPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(TechnologyEntity::getId),
                        QueryMethods.column(TechnologyEntity::getCompanyId),
                        QueryMethods.column(TechnologyEntity::getName),
                        QueryMethods.column(TechnologyEntity::getRemark),
                        QueryMethods.column(TechnologyEntity::getSort),
                        QueryMethods.column(TechnologyEntity::getStatus),
                        QueryMethods.column(TechnologyEntity::getCreatedAt),
                        QueryMethods.column(TechnologyEntity::getUpdatedAt)
                )
                .from(TechnologyEntity.class)
                // 工艺名称模糊查询
                .where(TechnologyEntity::getName)
                .like(queryParams.getName(), StrUtil.isNotBlank(queryParams.getName()))
                // 状态精确匹配
                .and(TechnologyEntity::getStatus)
                .eq(queryParams.getStatus(), queryParams.getStatus() != null)
                // 商户ID精确匹配
                .and(TechnologyEntity::getCompanyId)
                .eq(SecurityUtils.getCompanyId());

        // 添加排序条件
        queryWrapper.orderBy(TechnologyEntity::getId, false);

        // 执行分页查询并直接映射到VO
        return mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(), queryWrapper,
                TechnologyPageVO.class);
    }

    @Override
    public boolean saveTechnology(TechnologyEntity entity) {
        // 设置商户ID
        entity.setCompanyId(SecurityUtils.getCompanyId().intValue());

        // 检查同一商家下工艺名称是否重复
        long count = this.count(
                QueryWrapper.create()
                        .from(TechnologyEntity.class)
                        .where(TechnologyEntity::getCompanyId)
                        .eq(entity.getCompanyId())
                        .and(TechnologyEntity::getName)
                        .eq(entity.getName())
                        .and(TechnologyEntity::getId)
                        .ne(entity.getId(), entity.getId() != null));
        if (count > 0) {
            throw new RuntimeException("工艺名称不能重复");
        }

        // 记录操作日志
        if (entity.getId() == null) {
            OpLogUtils.appendOpLog("工艺管理-新增工艺", "新增工艺: " + entity.getName(), entity);
        } else {
            TechnologyEntity oldEntity = this.getById(entity.getId());
            OpLogUtils.appendOpLog("工艺管理-编辑工艺", "编辑工艺: " + entity.getName(), 
                Map.of("修改前", oldEntity, "修改后", entity));
        }

        return this.saveOrUpdate(entity);
    }

    @Override
    public boolean updateTechnologyStatus(Long id, Integer status) {
        TechnologyEntity entity = this.getById(id);
        if (entity != null) {
            // 记录操作日志
            OpLogUtils.appendOpLog("工艺管理-修改工艺状态", 
                String.format("修改工艺[%s]状态: %d -> %d", entity.getName(), entity.getStatus(), status),
                Map.of("工艺ID", id, "原状态", entity.getStatus(), "新状态", status));
            
            entity.setStatus(status);
            return this.updateById(entity);
        }
        return false;
    }

    @Override
    public boolean deleteTechnology(Long id) {
        // 查询工艺是否存在
        TechnologyEntity entity = this.getById(id);
        if (entity != null) {
            // 验证是否为当前商户的工艺
            if (!entity.getCompanyId().equals(SecurityUtils.getCompanyId().intValue())) {
                return false;
            }

            // 检查是否有关联的货品数据
            // 1. 检查商品表中是否使用了该工艺
            Long goodsCount = goodsMapper.selectCountByQuery(
                QueryWrapper.create()
                    .from(GoodsEntity.class)
                    .where(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(GoodsEntity::getTechnologyId).eq(id)
            );

            CommonUtils.abortIf(goodsCount > 0, "该工艺已被商品使用，不允许删除");

            // 2. 检查入库单中是否使用了该工艺
            Long incomeCount = goodsIncomeDetailMapper.selectCountByQuery(
                QueryWrapper.create()
                    .from(GoodsIncomeDetailEntity.class)
                    .where(GoodsIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(GoodsIncomeDetailEntity::getTechnologyId).eq(id)
            );

            CommonUtils.abortIf(incomeCount > 0, "该工艺已被入库单使用，不允许删除");

            // 记录操作日志
            OpLogUtils.appendOpLog("工艺管理-删除工艺", "删除工艺: " + entity.getName(), entity);

            return this.removeById(id);
        }
        return false;
    }
} 