package com.xc.boot.modules.merchant.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.common.enums.StatusEnum;
import com.xc.boot.modules.merchant.model.enums.CounterTypeEnum;

/**
 * 柜台分页视图对象
 */
@Data
@Schema(description = "柜台分页视图对象")
public class CounterPageVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "商户ID")
    private Integer companyId;

    @Schema(description = "门店ID")
    private Integer merchantId;

    @Schema(description = "门店名称")
    private String merchantName;

    @Schema(description = "柜台名称")
    private String name;

    @Schema(description = "柜台类型(1-销售 2-仓库 3-旧料)")
    private Integer type;

    @Schema(description = "柜台类型描述")
    private String typeDesc;

    // getter
    public String getTypeDesc() {
        return IBaseEnum.getLabelByValue(this.type, CounterTypeEnum.class);
    }

    @Schema(description = "状态(0-禁用 1-启用)")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDesc;

    // getter
    public String getStatusDesc() {
        return IBaseEnum.getLabelByValue(this.status, StatusEnum.class);
    }

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;
} 