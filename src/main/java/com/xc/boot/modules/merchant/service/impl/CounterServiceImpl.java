package com.xc.boot.modules.merchant.service.impl;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.baseColum.JoinColumEnum;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.DataCheckUtil;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.income.mapper.GoodsIncomeDetailMapper;
import com.xc.boot.modules.income.model.entity.GoodsIncomeDetailEntity;
import com.xc.boot.modules.merchant.mapper.CounterMapper;
import com.xc.boot.modules.merchant.model.dto.CounterFormDTO;
import com.xc.boot.modules.merchant.model.entity.CounterEntity;
import com.xc.boot.modules.merchant.model.enums.CounterTypeEnum;
import com.xc.boot.modules.merchant.model.query.CounterPageQuery;
import com.xc.boot.modules.merchant.model.vo.CounterPageVO;
import com.xc.boot.modules.merchant.service.CounterService;
import com.xc.boot.system.mapper.MerchantMapper;
import com.xc.boot.system.model.entity.MerchantEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mybatisflex.core.query.QueryMethods.*;

/**
 * 柜台服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CounterServiceImpl extends ServiceImpl<CounterMapper, CounterEntity> implements CounterService {

    private final MerchantMapper merchantMapper;
    private final GoodsMapper goodsMapper;
    private final GoodsIncomeDetailMapper goodsIncomeDetailMapper;

    @Override
    public Page<CounterPageVO> getCounterPage(CounterPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        column(CounterEntity::getId),
                        column(CounterEntity::getCompanyId),
                        column(CounterEntity::getMerchantId),
                        column(CounterEntity::getName),
                        column(CounterEntity::getType),
                        column(CounterEntity::getStatus),
                        column(CounterEntity::getSort),
                        column(CounterEntity::getRemark),
                        column(CounterEntity::getCreatedAt),
                        column(CounterEntity::getUpdatedAt)
                )
                .from(CounterEntity.class);

        // 商户ID条件
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId != null) {
            queryWrapper.and(CounterEntity::getCompanyId).eq(companyId.intValue());
        }

        // 门店ID条件
        if (!SecurityUtils.isMain()) {
            queryWrapper.and(CounterEntity::getMerchantId).in(SecurityUtils.getMerchantIds());
        }

        // 门店ID精确匹配
        if (queryParams.getMerchantId() != null) {
            queryWrapper.and(CounterEntity::getMerchantId).eq(queryParams.getMerchantId());
        }

        // 柜台名称模糊查询
        if (StringUtils.hasText(queryParams.getName())) {
            queryWrapper.and(CounterEntity::getName).like("%" + queryParams.getName() + "%");
        }

        // 柜台类型精确匹配
        if (queryParams.getType() != null) {
            queryWrapper.and(CounterEntity::getType).eq(queryParams.getType());
        }

        // 状态精确匹配
        if (queryParams.getStatus() != null) {
            queryWrapper.and(CounterEntity::getStatus).eq(queryParams.getStatus());
        }

        // 添加排序条件
        queryWrapper.orderBy(CounterEntity::getId, false);

        // 执行分页查询
        Page<CounterPageVO> page = mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(), queryWrapper, CounterPageVO.class);

        // 获取所有商户ID
        List<Long> merchantIds = page.getRecords().stream()
                .map(CounterPageVO::getMerchantId)
                .map(Long::valueOf)
                .collect(Collectors.toList());

        if (!merchantIds.isEmpty()) {
            // 查询商户信息
            List<MerchantEntity> merchants = merchantMapper.selectListByQuery(
                    QueryWrapper.create()
                            .from(MerchantEntity.class)
                            .where(MerchantEntity::getId).in(merchantIds)
            );

            // 构建商户ID到商户名称的映射
            Map<Long, String> merchantNameMap = merchants.stream()
                    .collect(Collectors.toMap(
                            merchant -> merchant.getId().longValue(),
                            MerchantEntity::getName
                    ));

            // 设置商户名称
            page.getRecords().forEach(vo -> 
                vo.setMerchantName(merchantNameMap.get(vo.getMerchantId().longValue()))
            );
        }

        return page;
    }

    @Override
    public boolean saveCounter(CounterFormDTO form) {
        Long counterId = form.getId();
        CounterEntity counter = null;

        // 编辑时，判断柜台是否存在
        if (counterId != null) {
            counter = this.getById(counterId);
            Assert.isTrue(counter != null, "柜台不存在");

            // 旧料类型的柜台不允许编辑
            Assert.isTrue(counter.getType() != CounterTypeEnum.OLD_MATERIAL.getValue(), "旧料类型的柜台不允许编辑");
        }

        // 检查同一门店下柜台名称是否重复
        long count = this.count(
                QueryWrapper.create()
                        .from(CounterEntity.class)
                        .where(CounterEntity::getMerchantId).eq(form.getMerchantId())
                        .and(CounterEntity::getName).eq(form.getName())
                        .and(CounterEntity::getId).ne(counterId, counterId != null));
        Assert.isTrue(count == 0, "同一门店下柜台名称不能重复");

        // 只能新增销售和仓库类型的柜台
        if (counterId == null) {
            Assert.isTrue(
                    form.getType() == CounterTypeEnum.SALE.getValue() || form.getType() == CounterTypeEnum.WAREHOUSE.getValue(),
                    "只能新增销售和仓库类型的柜台");
        }

        // 创建柜台实体
        if (counter == null) {
            counter = new CounterEntity();
            counter.setCompanyId(SecurityUtils.getCompanyId().intValue());
        }

        counter.setMerchantId(form.getMerchantId());
        counter.setName(form.getName());
        counter.setType(form.getType());
        counter.setSort(form.getSort());
        counter.setRemark(form.getRemark());
        counter.setStatus(form.getStatus());

        // 记录操作日志
        if (counterId == null) {
            OpLogUtils.appendOpLog("柜台管理-新增柜台", "新增柜台: " + counter.getName(), counter);
        } else {
            CounterEntity oldCounter = this.getById(counterId);
            OpLogUtils.appendOpLog("柜台管理-编辑柜台", "编辑柜台: " + counter.getName(), 
                Map.of("修改前", oldCounter, "修改后", counter));
        }

        return this.saveOrUpdate(counter);
    }

    @Override
    public boolean deleteCounter(Long id) {
        CounterEntity counter = this.getById(id);
        Assert.isTrue(counter != null, "柜台不存在");

        // 旧料类型的柜台不允许删除
        Assert.isTrue(counter.getType() != CounterTypeEnum.OLD_MATERIAL.getValue(), "旧料类型的柜台不允许删除");

        // 检查是否有关联的业务数据
        // 1. 检查商品表中是否使用了该柜台
        Long goodsCount = goodsMapper.selectCountByQuery(
            QueryWrapper.create()
                .where(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsEntity::getCounterId).eq(id)
        );

        CommonUtils.abortIf(goodsCount > 0, "该柜台已被商品使用，不允许删除");

        // 2. 检查入库单中是否使用了该柜台
        Long incomeCount = goodsIncomeDetailMapper.selectCountByQuery(
            QueryWrapper.create()
                .where(GoodsIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsIncomeDetailEntity::getCounterId).eq(id)
        );

        CommonUtils.abortIf(incomeCount > 0, "该柜台已被入库单使用，不允许删除");

        // 记录操作日志
        OpLogUtils.appendOpLog("柜台管理-删除柜台", "删除柜台: " + counter.getName(), counter);
        Assert.isTrue(DataCheckUtil.checkJoinFieldDelete(id, JoinColumEnum.COUNTER_ID), "柜台已被使用，请先解除关联关系");
        return this.removeById(id);
    }

    @Override
    public boolean updateCounterStatus(Long id, Integer status) {
        CounterEntity counter = this.getById(id);
        Assert.isTrue(counter != null, "柜台不存在");

        // 记录操作日志
        OpLogUtils.appendOpLog("柜台管理-修改柜台状态", 
            String.format("修改柜台[%s]状态: %d -> %d", counter.getName(), counter.getStatus(), status),
            Map.of("柜台ID", id, "原状态", counter.getStatus(), "新状态", status));

        counter.setStatus(status);
        return this.updateById(counter);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createDefaultCounters(Integer merchantId) {
        // 检查柜台数量
        long count = this.count(
                QueryWrapper.create()
                        .from(CounterEntity.class)
                        .where(CounterEntity::getMerchantId).eq(merchantId)
        );

        // 如果已有柜台，直接返回
        if (count > 0) {
            return true;
        }

        // 查询门店
        MerchantEntity merchant = merchantMapper.selectOneByQuery(
                QueryWrapper.create()
                        .from(MerchantEntity.class)
                        .where(MerchantEntity::getId).eq(merchantId)
        );
        Assert.isTrue(merchant != null, "门店不存在");

        // 创建默认柜台列表
        List<CounterEntity> counters = new ArrayList<>();

        // 创建销售柜台
        CounterEntity saleCounter = new CounterEntity()
                .setCompanyId(merchant.getCompanyId())
                .setMerchantId(merchantId)
                .setName("销售柜")
                .setType(CounterTypeEnum.SALE.getValue())
                .setStatus(1)
                .setSort(1)
                .setRemark("默认销售柜台");
        counters.add(saleCounter);

        // 创建旧料柜台
        CounterEntity oldMaterialCounter = new CounterEntity()
                .setCompanyId(merchant.getCompanyId())
                .setMerchantId(merchantId)
                .setName("旧料柜")
                .setType(CounterTypeEnum.OLD_MATERIAL.getValue())
                .setStatus(1)
                .setSort(2)
                .setRemark("默认旧料柜台");
        counters.add(oldMaterialCounter);

        // 批量插入柜台
        boolean result = this.saveBatch(counters);

        // 记录操作日志
        if (result) {
            OpLogUtils.appendOpLog(
                    "柜台管理-创建默认柜台",
                    "为门店[" + merchantId + "]创建默认柜台",
                    counters
            );
        }

        return result;
    }
} 