package com.xc.boot.modules.merchant.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 珠石实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "jewelry")
public class JewelryEntity extends BaseEntity {
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 珠石名称
     */
    @NotBlank(message = "珠石名称不能为空")
    @Size(min = 2, max = 20, message = "珠石名称长度应为2到20个字符")
    @Schema(description = "珠石名称")
    @Column(value = "name")
    private String name;

    /**
     * 说明
     */
    @Schema(description = "说明")
    @Size(max = 200, message = "说明长度不能超过200个字符")
    @Column(value = "remark")
    private String remark;

    /**
     * 排序
     */
    @Schema(description = "排序")
    @Column(value = "sort")
    private Integer sort;

    /**
     * 状态(0:禁用;1:启用)
     */
    @Schema(description = "状态(0:禁用;1:启用)")
    @Column(value = "status")
    private Integer status;
} 