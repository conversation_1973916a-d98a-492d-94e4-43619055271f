package com.xc.boot.modules.merchant.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 入库模板明细实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_income_template_details")
@Schema(description = "入库模板明细实体")
public class GoodsIncomeTemplateDetailEntity extends BaseEntity {
    
    /**
     * 商户ID
     */
    @Schema(description = "商户ID")
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    @Column(value = "template_id")
    private Integer templateId;

    /**
     * 字段标识
     */
    @Schema(description = "字段标识")
    @Column(value = "sign")
    private String sign;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @Column(value = "enabled")
    private Integer enabled;

    /**
     * 是否启用(是否可编辑)
     */
    @Schema(description = "是否启用(是否可编辑)")
    @Column(value = "editable_enabled")
    private Integer editableEnabled;

    /**
     * 是否必填
     */
    @Schema(description = "是否必填")
    @Column(value = "required_flag")
    private Integer requiredFlag;

    /**
     * 是否必填(是否可编辑)
     */
    @Schema(description = "是否必填(是否可编辑)")
    @Column(value = "editable_required")
    private Integer editableRequired;

    /**
     * 默认值
     */
    @Schema(description = "默认值")
    @Column(value = "default_value")
    private String defaultValue;

    /**
     * 用于存储图片类型的ID
     */
    @Schema(description = "用于存储图片类型的ID")
    @Column(value = "image_id")
    private Long imageId;

    /**
     * 默认值(是否可编辑)
     */
    @Schema(description = "默认值(是否可编辑)")
    @Column(value = "editable_default_value")
    private Integer editableDefaultValue;
} 