package com.xc.boot.modules.merchant.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.merchant.model.dto.StyleFormDTO;
import com.xc.boot.modules.merchant.model.query.StylePageQuery;
import com.xc.boot.modules.merchant.model.vo.StylePageVO;
import com.xc.boot.modules.merchant.service.StyleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 款式管理控制层
 */
@Tag(name = "系统配置-款式管理")
@RestController
@RequestMapping("/api/styles")
@RequiredArgsConstructor
public class StyleController {

    private final StyleService styleService;

    @Operation(summary = "款式分页列表")
    @GetMapping("/page")
    public PageResult<StylePageVO> getStylePage(StylePageQuery queryParams) {
        Page<StylePageVO> result = styleService.getStylePage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增款式")
    @PostMapping
    public Result<Boolean> addStyle(@RequestBody @Valid StyleFormDTO form) {
        boolean result = styleService.saveStyle(form);
        return Result.success(result);
    }

    @Operation(summary = "编辑款式")
    @PutMapping
    public Result<Boolean> editStyle(@RequestBody @Valid StyleFormDTO form) {
        boolean result = styleService.saveStyle(form);
        return Result.success(result);
    }

    @Operation(summary = "删除款式")
    @DeleteMapping
    public Result<?> deleteStyle(@RequestBody @Valid DeleteRequest dto) {
        boolean result = styleService.deleteStyle(dto.getId());
        return Result.judge(result);
    }
}