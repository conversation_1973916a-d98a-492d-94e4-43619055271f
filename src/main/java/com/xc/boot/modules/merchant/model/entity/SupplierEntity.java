package com.xc.boot.modules.merchant.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 供应商实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "supplier")
public class SupplierEntity extends BaseEntity {
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商名称不能为空")
    @Size(max = 50, message = "供应商名称长度不能超过50个字符")
    @Schema(description = "供应商名称")
    @Column(value = "name")
    private String name;

    /**
     * 供应商编码
     */
    @NotBlank(message = "供应商编码不能为空")
    @Size(max = 50, message = "供应商编码长度不能超过50个字符")
    @Schema(description = "供应商编码")
    @Column(value = "sign")
    private String sign;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空")
    @Size(max = 20, message = "联系人长度不能超过20个字符")
    @Schema(description = "联系人")
    @Column(value = "contact")
    private String contact;

    /**
     * 联系人电话
     */
    @NotBlank(message = "联系人电话不能为空")
    @Size(max = 20, message = "联系人电话长度不能超过20个字符")
    @Schema(description = "联系人电话")
    @Column(value = "contact_phone")
    private String contactPhone;

    /**
     * 联系地址
     */
    @Schema(description = "联系地址")
    @Size(max = 200, message = "联系地址长度不能超过200个字符")
    @Column(value = "address")
    private String address;

    /**
     * 状态(0:禁用;1:启用)
     */
    @Schema(description = "状态(0:禁用;1:启用)")
    @Column(value = "status")
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 200, message = "备注长度不能超过200个字符")
    @Column(value = "remark")
    private String remark;
} 