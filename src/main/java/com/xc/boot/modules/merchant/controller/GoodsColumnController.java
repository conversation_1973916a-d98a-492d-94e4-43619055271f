package com.xc.boot.modules.merchant.controller;

import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.merchant.model.dto.GoodsColumnFormDTO;
import com.xc.boot.modules.merchant.model.query.GoodsColumnQuery;
import com.xc.boot.modules.merchant.model.vo.GoodsColumnVO;
import com.xc.boot.modules.merchant.service.GoodsColumnService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品字段管理控制层
 */
@Tag(name = "系统配置-货品字段管理")
@RestController
@RequestMapping("/api/goods-column")
@RequiredArgsConstructor
public class GoodsColumnController {

    private final GoodsColumnService goodsColumnService;

    @Operation(summary = "商品字段列表")
    @PostMapping("/list")
    public Result<List<GoodsColumnVO>> getGoodsColumnList(@RequestBody GoodsColumnQuery query) {
        List<GoodsColumnVO> list = goodsColumnService.getGoodsColumnList(query);
        return Result.success(list);
    }

    @Operation(summary = "新增商品字段")
    @PostMapping
    public Result<Boolean> addGoodsColumn(@RequestBody @Valid GoodsColumnFormDTO form) {
        boolean result = goodsColumnService.saveGoodsColumn(form);
        return Result.success(result);
    }

    @Operation(summary = "编辑商品字段")
    @PutMapping
    public Result<Boolean> editGoodsColumn(@RequestBody @Valid GoodsColumnFormDTO form) {
        boolean result = goodsColumnService.saveGoodsColumn(form);
        return Result.success(result);
    }

    @Operation(summary = "删除商品字段")
    @DeleteMapping
    public Result<Boolean> deleteGoodsColumn(@RequestBody @Valid DeleteRequest request) {
        boolean result = goodsColumnService.deleteGoodsColumn(request.getId());
        return Result.success(result);
    }
} 