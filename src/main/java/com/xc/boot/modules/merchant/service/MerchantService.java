package com.xc.boot.modules.merchant.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.merchant.model.dto.MerchantFormDTO;
import com.xc.boot.modules.merchant.model.query.MerchantQuery;
import com.xc.boot.modules.merchant.model.vo.MerchantVO;
import com.xc.boot.system.model.entity.MerchantEntity;

public interface MerchantService extends IService<MerchantEntity> {
    /**
     * 获取门店分页列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    Page<MerchantVO> getMerchantPage(MerchantQuery query);

    /**
     * 保存门店信息
     *
     * @param form 门店表单对象
     * @return 是否成功
     */
    boolean saveMerchant(MerchantFormDTO form);

    /**
     * 更新门店信息
     *
     * @param form 门店表单对象
     * @return 是否成功
     */
    boolean updateMerchant(MerchantFormDTO form);
}
