package com.xc.boot.modules.merchant.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.common.model.Option;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 柜台类型枚举
 */
@Getter
public enum CounterTypeEnum implements IBaseEnum<Integer> {
    /**
     * 销售柜台
     */
    SALE(1, "销售"),

    /**
     * 仓库柜台
     */
    WAREHOUSE(2, "仓库"),

    /**
     * 旧料柜台
     */
    OLD_MATERIAL(3, "旧料");

    /**
     * 类型值
     */
    private final Integer value;

    /**
     * 类型名称
     */
    private final String label;

    CounterTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    /**
     * 获取所有选项
     */
    public static List<Option<Integer>> getOptions() {
        return Arrays.stream(values())
                .map(type -> new Option<>(type.getValue(), type.getLabel()))
                .collect(Collectors.toList());
    }

    /**
     * 根据值获取枚举
     */
    public static CounterTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(type -> type.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
} 