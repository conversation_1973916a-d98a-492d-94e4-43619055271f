package com.xc.boot.modules.merchant.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.modules.merchant.model.dto.GoodsIncomeTemplateFormDTO;
import com.xc.boot.modules.merchant.model.dto.GoodsIncomeTemplateDetailSaveDTO;
import com.xc.boot.modules.merchant.model.dto.GoodsIncomeTemplateDetailQueryDTO;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateDetailEntity;
import com.xc.boot.modules.merchant.model.query.GoodsIncomeTemplatePageQuery;
import com.xc.boot.modules.merchant.model.vo.GoodsIncomeTemplateDetailWithColumnVO;
import com.xc.boot.modules.merchant.model.vo.GoodsIncomeTemplatePageVO;
import com.xc.boot.modules.merchant.service.GoodsIncomeTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 入库模板管理控制层
 */
@Tag(name = "系统配置-入库模板管理")
@RestController
@RequestMapping("/api/goods/income-template")
@RequiredArgsConstructor
public class GoodsIncomeTemplateController {

    private final GoodsIncomeTemplateService goodsIncomeTemplateService;

    @Operation(summary = "入库模板分页列表")
    @PostMapping("/page")
    public PageResult<GoodsIncomeTemplatePageVO> getTemplatePage(@RequestBody GoodsIncomeTemplatePageQuery queryParams) {
        Page<GoodsIncomeTemplatePageVO> result = goodsIncomeTemplateService.getTemplatePage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增入库模板")
    @PostMapping
    public Result<Boolean> addTemplate(@RequestBody @Valid GoodsIncomeTemplateFormDTO form) {
        boolean result = goodsIncomeTemplateService.saveTemplate(form);
        return Result.success(result);
    }

    @Operation(summary = "编辑入库模板")
    @PutMapping
    public Result<Boolean> editTemplate(@RequestBody @Valid GoodsIncomeTemplateFormDTO form) {
        boolean result = goodsIncomeTemplateService.saveTemplate(form);
        return Result.success(result);
    }

    @Operation(summary = "删除入库模板")
    @DeleteMapping
    public Result<Boolean> deleteTemplate(@RequestBody @Valid DeleteRequest request) {
        boolean result = goodsIncomeTemplateService.deleteTemplate(request.getId());
        return Result.success(result);
    }

    @Operation(summary = "获取入库模板明细")
    @PostMapping("/details")
    public Result<List<GoodsIncomeTemplateDetailWithColumnVO>> getTemplateDetails(@RequestBody @Valid GoodsIncomeTemplateDetailQueryDTO query) {
        List<GoodsIncomeTemplateDetailWithColumnVO> result = goodsIncomeTemplateService.getTemplateDetails(query.getId(), query.getName());
        return Result.success(result);
    }

    @Operation(summary = "保存入库模板明细")
    @PostMapping("/details/save")
    public Result<Boolean> saveTemplateDetails(@RequestBody @Valid GoodsIncomeTemplateDetailSaveDTO dto) {
        // 校验柜台和数量字段
        if (dto.getDetails() != null) {
            boolean hasCounter = false;
            boolean hasQuantity = false;
            for (GoodsIncomeTemplateDetailEntity detail : dto.getDetails()) {
                if ("counter_id".equals(detail.getSign())) {
                    hasCounter = true;
                    // 柜台必须启用或设置默认值
                    if (detail.getEnabled() != 1 && (detail.getDefaultValue() == null || detail.getDefaultValue().trim().isEmpty())) {
                        CommonUtils.abort("柜台字段必须启用或设置默认值");
                    }
                }
                if ("num".equals(detail.getSign())) {
                    hasQuantity = true;
                    // 数量必须启用或设置默认值
                    if (detail.getEnabled() != 1 && (detail.getDefaultValue() == null || detail.getDefaultValue().trim().isEmpty())) {
                        CommonUtils.abort("数量字段必须启用或设置默认值");
                    }
                }
            }
            // 确保柜台和数量字段都存在
            if (!hasCounter) {
                CommonUtils.abort("缺少柜台字段");
            }
            if (!hasQuantity) {
                CommonUtils.abort("缺少数量字段");
            }
        }
        boolean result = goodsIncomeTemplateService.saveTemplateDetails(dto.getTemplateId(), dto.getDetails());
        return Result.success(result);
    }
} 