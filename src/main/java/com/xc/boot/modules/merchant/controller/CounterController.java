package com.xc.boot.modules.merchant.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.merchant.model.dto.CounterFormDTO;
import com.xc.boot.modules.merchant.model.dto.CounterStatusDTO;
import com.xc.boot.modules.merchant.model.query.CounterPageQuery;
import com.xc.boot.modules.merchant.model.vo.CounterPageVO;
import com.xc.boot.modules.merchant.service.CounterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 柜台管理控制层
 */
@Tag(name = "系统配置-柜台管理")
@RestController
@RequestMapping("/api/counter")
@RequiredArgsConstructor
public class CounterController {

    private final CounterService counterService;

    @Operation(summary = "柜台分页列表")
    @GetMapping("/page")
    public PageResult<CounterPageVO> getCounterPage(CounterPageQuery queryParams) {
        Page<CounterPageVO> result = counterService.getCounterPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增柜台")
    @PostMapping
    public Result<Boolean> addCounter(@RequestBody @Valid CounterFormDTO form) {
        boolean result = counterService.saveCounter(form);
        return Result.success(result);
    }

    @Operation(summary = "编辑柜台")
    @PutMapping
    public Result<Boolean> editCounter(@RequestBody @Valid CounterFormDTO form) {
        boolean result = counterService.saveCounter(form);
        return Result.success(result);
    }

    @Operation(summary = "删除柜台")
    @DeleteMapping
    public Result<?> deleteCounter(@RequestBody @Valid DeleteRequest request) {
        boolean result = counterService.deleteCounter(request.getId());
        return Result.judge(result);
    }

    @Operation(summary = "修改柜台状态")
    @PutMapping("/status")
    public Result<Boolean> updateCounterStatus(@RequestBody @Valid CounterStatusDTO dto) {
        boolean result = counterService.updateCounterStatus(dto.getId(), dto.getStatus());
        return Result.success(result);
    }
} 