package com.xc.boot.modules.merchant.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.config.interceptor.model.BaseDbTables;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsHasColumnsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsHasColumnsEntity;
import com.xc.boot.modules.income.mapper.GoodsIncomeHasColumnsMapper;
import com.xc.boot.modules.income.model.entity.GoodsIncomeHasColumnsEntity;
import com.xc.boot.modules.merchant.mapper.GoodsColumnMapper;
import com.xc.boot.modules.merchant.mapper.GoodsIncomeTemplateDetailMapper;
import com.xc.boot.modules.merchant.mapper.GoodsIncomeTemplateMapper;
import com.xc.boot.modules.merchant.model.dto.GoodsColumnFormDTO;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateDetailEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateEntity;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnCategoryEnum;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnSecretLevelEnum;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnTypeEnum;
import com.xc.boot.modules.merchant.model.query.GoodsColumnQuery;
import com.xc.boot.modules.merchant.model.vo.GoodsColumnVO;
import com.xc.boot.modules.merchant.service.GoodsColumnService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品字段服务实现类
 */
@Service
@RequiredArgsConstructor
public class GoodsColumnServiceImpl extends ServiceImpl<GoodsColumnMapper, GoodsColumnEntity>
        implements GoodsColumnService {

    private final GoodsHasColumnsMapper goodsHasColumnsMapper;
    private final GoodsIncomeTemplateDetailMapper goodsIncomeTemplateDetailMapper;
    private final GoodsIncomeTemplateMapper goodsIncomeTemplateMapper;
    private final GoodsIncomeHasColumnsMapper goodsIncomeHasColumnsMapper;

    @Value("classpath:default_templates.json")
    private Resource defaultTemplateData;

    @Override
    public List<GoodsColumnVO> getGoodsColumnList(GoodsColumnQuery query) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(GoodsColumnEntity::getId),
                        QueryMethods.column(GoodsColumnEntity::getCompanyId),
                        QueryMethods.column(GoodsColumnEntity::getName),
                        QueryMethods.column(GoodsColumnEntity::getSign),
                        QueryMethods.column(GoodsColumnEntity::getSecretLevel),
                        QueryMethods.column(GoodsColumnEntity::getCategory),
                        QueryMethods.column(GoodsColumnEntity::getType),
                        QueryMethods.column(GoodsColumnEntity::getIsMultiple),
                        QueryMethods.column(GoodsColumnEntity::getOptions),
                        QueryMethods.column(GoodsColumnEntity::getNumberPrecision),
                        QueryMethods.column(GoodsColumnEntity::getCreatedAt),
                        QueryMethods.column(GoodsColumnEntity::getUpdatedAt))
                .from(GoodsColumnEntity.class)
                // 商户ID条件
                .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                // 字段名称模糊查询
                .and(GoodsColumnEntity::getName).like(query.getName(), StringUtils.hasText(query.getName()));

        // 执行查询
        List<GoodsColumnEntity> list = this.list(queryWrapper);

        // 如果列表为空，则初始化默认字段
        if (list.isEmpty()) {
            this.initDefaultColumns();
            list = this.list(queryWrapper);
        }

        // 转换为VO
        return list.stream().map(entity -> {
            GoodsColumnVO vo = new GoodsColumnVO();
            BeanUtils.copyProperties(entity, vo);

            // 设置枚举文本
            vo.setSecretLevelLabel(
                    IBaseEnum.getLabelByValue(entity.getSecretLevel(), GoodsColumnSecretLevelEnum.class));
            vo.setCategoryLabel(IBaseEnum.getLabelByValue(entity.getCategory(), GoodsColumnCategoryEnum.class));
            vo.setTypeLabel(IBaseEnum.getLabelByValue(entity.getType(), GoodsColumnTypeEnum.class));

            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveGoodsColumn(GoodsColumnFormDTO form) {
        Long id = form.getId();
        GoodsColumnEntity entity = new GoodsColumnEntity();
        BeanUtils.copyProperties(form, entity);

        // 设置商户ID
        entity.setCompanyId(SecurityUtils.getCompanyId().intValue());

        // 检查字段名称是否重复
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(QueryMethods.column(GoodsColumnEntity::getId))
                .from(GoodsColumnEntity.class)
                .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsColumnEntity::getName).eq(entity.getName());

        // 如果是编辑，排除当前记录
        if (id != null) {
            queryWrapper.and(GoodsColumnEntity::getId).ne(id);
        }

        // 检查是否存在同名字段
        Long count = this.count(queryWrapper);
        CommonUtils.abortIf(count > 0, "字段名称已存在");

        if (id != null) {
            // 编辑
            GoodsColumnEntity oldEntity = this.getById(id);
            CommonUtils.abortIf(oldEntity == null, "商品字段不存在");

            // 编辑时不允许修改 category 和 sign
            entity.setCategory(oldEntity.getCategory());
            entity.setSign(oldEntity.getSign());

            boolean result = this.updateById(entity);
            if (result) {
                OpLogUtils.appendOpLog("商品字段-编辑", "编辑商品字段", entity);
            }
            return result;
        } else {
            // 新增
            // 设置类目为自定义
            entity.setCategory(GoodsColumnCategoryEnum.CUSTOM.getValue());
            // 生成UUID作为sign
            entity.setSign(UUID.randomUUID().toString());

            boolean result = this.save(entity);
            if (result) {
                OpLogUtils.appendOpLog("商品字段-新增", "新增商品字段", entity);
            }
            return result;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteGoodsColumn(Long id) {
        // 检查是否存在
        GoodsColumnEntity entity = this.getById(id);
        CommonUtils.abortIf(entity == null, "商品字段不存在");

        if (isColumnInUse(id)) {
            CommonUtils.abort("该字段已被使用，不允许删除");
        }

        // 删除模板明细关联数据
        goodsIncomeTemplateDetailMapper.deleteByQuery(QueryWrapper.create()
                .from(GoodsIncomeTemplateDetailEntity.class)
                .where(GoodsIncomeTemplateDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsIncomeTemplateDetailEntity::getSign).eq(entity.getSign()));

        // 删除
        boolean result = this.removeById(id);
        if (result) {
            OpLogUtils.appendOpLog("商品字段-删除", "删除商品字段", entity);
        }

        return result;
    }

    /**
     * 检查字段是否被使用
     * 
     * @param columnId 字段ID
     * @return 是否被使用
     */
    private boolean isColumnInUse(Long columnId) {
        // 获取字段信息
        GoodsColumnEntity column = this.getById(columnId);
        if (column == null) {
            return false;
        }

        // 检查商品表中是否使用了该字段
        Long goodsCount = goodsHasColumnsMapper.selectCountByQuery(
                QueryWrapper.create()
                        .from(GoodsHasColumnsEntity.class)
                        .where(GoodsHasColumnsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .and(GoodsHasColumnsEntity::getColumnId).eq(columnId));

        if (goodsCount > 0) {
            return true;
        }

        // 检查入库单中是否使用了该字段
        Long incomeCount = goodsIncomeHasColumnsMapper.selectCountByQuery(
                QueryWrapper.create()
                        .from(GoodsIncomeHasColumnsEntity.class)
                        .where(GoodsIncomeHasColumnsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .and(GoodsIncomeHasColumnsEntity::getColumnId).eq(columnId));

        return incomeCount > 0;
    }

    /**
     * 初始化默认字段
     */
    public void initDefaultColumns() {
        // 检查是否存在默认字段
        Long count = this.count(
                QueryWrapper.create()
                        .from(GoodsColumnEntity.class)
                        .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .where(GoodsColumnEntity::getCategory).eq(GoodsColumnCategoryEnum.COMMON.getValue()));

        if (count > 0) {
            return;
        }

        // 插入默认字段
        List<GoodsColumnEntity> columns = BaseDbTables.BASE_GOODS_COLUMNS.stream()
                .map(column -> {
                    GoodsColumnEntity entity = new GoodsColumnEntity();
                    entity.setCompanyId(SecurityUtils.getCompanyId().intValue());
                    entity.setName(column.get("name").toString());
                    entity.setSign(column.get("sign").toString());
                    entity.setSecretLevel(Integer.parseInt(column.get("secret_level").toString()));
                    entity.setType(Integer.parseInt(column.get("type").toString()));
                    entity.setCategory(GoodsColumnCategoryEnum.COMMON.getValue());
                    entity.setNumberPrecision(Integer.parseInt(column.get("number_precision").toString()));
                    return entity;
                })
                .collect(Collectors.toList());

        // 批量插入
        boolean result = this.saveBatch(columns);
        if (result) {
            OpLogUtils.appendOpLog("商品字段-初始化", "初始化商品默认字段", columns);
        }

        // 初始化入库模板
        initGoodsIncomeTemplate();
    }

    @Override
    public List<GoodsColumnEntity> getGoodsColumnListByCompanyId(Long companyId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .from(GoodsColumnEntity.class)
                .where(GoodsColumnEntity::getCompanyId).eq(companyId);

        List<GoodsColumnEntity> list = this.list(queryWrapper);
        // 如果列表为空，则初始化默认字段
        if (list.isEmpty()) {
            this.initDefaultColumns();
            list = this.list(queryWrapper);
        }
        return list;
    }

    /**
     * 初始化入库模板
     */
    private void initGoodsIncomeTemplate(){
        // 获取当前商户ID
        Integer companyId = SecurityUtils.getCompanyId().intValue();

        // 检查是否已存在入库模板，如果存在则跳过初始化
        long existingTemplateCount = goodsIncomeTemplateMapper.selectCountByQuery(
                QueryWrapper.create()
                        .where(GoodsIncomeTemplateEntity::getCompanyId).eq(companyId)
        );
        boolean hasExistingTemplates = existingTemplateCount > 0;

        if (hasExistingTemplates) {
            // 已存在入库模板，跳过初始化
            return;
        }

        // select d.template_id,
        //        t.name         as template_name,
        //        t.category_id  as template_category_id,
        //        t.default_flag as template_default_flag,
        //        d.sign,
        //        d.enabled,
        //        d.editable_enabled,
        //        d.required_flag,
        //        d.editable_required,
        //        d.default_value,
        //        d.editable_default_value
        // from goods_income_template_details d
        //          left join goods_income_templates t on d.template_id = t.id
        // where sign not like '%-%'
        //   and t.id is not null
        //   and t.name not like '%测试%';

        try {
            // 读取 resources/default_templates.json
            ClassPathResource resource = new ClassPathResource("default_templates.json");
            String jsonContent = new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);

            // 解析JSON数据
            JSONArray jsonArray = JSONUtil.parseArray(jsonContent);

            // 按template_id分组数据
            Map<Integer, List<JSONObject>> templateGroups = new HashMap<>();
            Map<Integer, JSONObject> templateInfos = new HashMap<>();

            for (Object item : jsonArray) {
                JSONObject jsonItem = (JSONObject) item;
                Integer templateId = jsonItem.getInt("template_id");

                // 存储模板基本信息
                if (!templateInfos.containsKey(templateId)) {
                    JSONObject templateInfo = new JSONObject();
                    templateInfo.set("template_name", jsonItem.getStr("template_name"));
                    templateInfo.set("template_category_id", jsonItem.getInt("template_category_id"));
                    templateInfo.set("template_default_flag", jsonItem.getInt("template_default_flag"));
                    templateInfos.put(templateId, templateInfo);
                }

                // 分组模板详情
                templateGroups.computeIfAbsent(templateId, k -> new ArrayList<>()).add(jsonItem);
            }



            // 清空模板表和模板详情表
            goodsIncomeTemplateMapper.deleteByQuery(QueryWrapper.create()
                    .where(GoodsIncomeTemplateEntity::getCompanyId).eq(companyId));
            goodsIncomeTemplateDetailMapper.deleteByQuery(QueryWrapper.create()
                    .where(GoodsIncomeTemplateDetailEntity::getCompanyId).eq(companyId));

            // 处理每个模板
            for (Map.Entry<Integer, JSONObject> entry : templateInfos.entrySet()) {
                Integer templateId = entry.getKey();
                JSONObject templateInfo = entry.getValue();
                List<JSONObject> details = templateGroups.get(templateId);

                // 创建新模板
                GoodsIncomeTemplateEntity template = new GoodsIncomeTemplateEntity();
                template.setCompanyId(companyId);
                template.setName(templateInfo.getStr("template_name"));
                template.setCategoryId(templateInfo.getInt("template_category_id"));
                template.setDefaultFlag(templateInfo.getInt("template_default_flag"));
                template.setStatus(1); // 默认启用
                template.setRemark("系统初始化默认模板");

                // 保存模板
                goodsIncomeTemplateMapper.insertSelective(template);

                // 创建模板详情
                List<GoodsIncomeTemplateDetailEntity> detailEntities = new ArrayList<>();
                for (JSONObject detail : details) {
                    GoodsIncomeTemplateDetailEntity detailEntity = new GoodsIncomeTemplateDetailEntity();
                    detailEntity.setCompanyId(companyId);
                    detailEntity.setTemplateId(template.getId().intValue());
                    detailEntity.setSign(detail.getStr("sign"));
                    detailEntity.setEnabled(detail.getInt("enabled"));
                    detailEntity.setEditableEnabled(detail.getInt("editable_enabled"));
                    detailEntity.setRequiredFlag(detail.getInt("required_flag"));
                    detailEntity.setEditableRequired(detail.getInt("editable_required"));
                    detailEntity.setDefaultValue(detail.getStr("default_value"));
                    detailEntity.setEditableDefaultValue(detail.getInt("editable_default_value"));
                    detailEntity.setImageId(0L); // 默认图片ID为0

                    detailEntities.add(detailEntity);
                }

                // 批量插入模板详情
                if (!detailEntities.isEmpty()) {
                    goodsIncomeTemplateDetailMapper.insertBatchSelective(detailEntities);
                }

                OpLogUtils.appendOpLog("商品字段-初始化入库模板", "初始化入库模板: " + template.getName(), template);
            }

        } catch (IOException e) {
            throw new RuntimeException("读取默认模板文件失败", e);
        } catch (Exception e) {
            throw new RuntimeException("初始化入库模板失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String resetGoodsColumns() {
        Long companyId = SecurityUtils.getCompanyId();

        // 1. 按id顺序查询出已有的货品字段，排除category=1的字段（通用字段）
        List<GoodsColumnEntity> existingCustomColumns = this.list(
                QueryWrapper.create()
                        .from(GoodsColumnEntity.class)
                        .where(GoodsColumnEntity::getCompanyId).eq(companyId.intValue())
                        .where(GoodsColumnEntity::getCategory).ne(GoodsColumnCategoryEnum.COMMON.getValue())
                        .orderBy(QueryMethods.column(GoodsColumnEntity::getId), true)
        );

        // 2. 重新组织货品字段：系统内定义的货品字段 + 原有的过滤之后的字段
        List<GoodsColumnEntity> newColumns = new ArrayList<>();

        // 添加系统内定义的货品字段（通用字段）
        List<GoodsColumnEntity> systemColumns = BaseDbTables.BASE_GOODS_COLUMNS.stream()
                .map(column -> {
                    GoodsColumnEntity entity = new GoodsColumnEntity();
                    entity.setCompanyId(companyId.intValue());
                    entity.setName(column.get("name").toString());
                    entity.setSign(column.get("sign").toString());
                    entity.setSecretLevel(Integer.parseInt(column.get("secret_level").toString()));
                    entity.setType(Integer.parseInt(column.get("type").toString()));
                    entity.setCategory(GoodsColumnCategoryEnum.COMMON.getValue());
                    entity.setNumberPrecision(Integer.parseInt(column.get("number_precision").toString()));
                    return entity;
                })
                .collect(Collectors.toList());

        newColumns.addAll(systemColumns);

        // 添加原有的自定义字段（重置ID，保持其他属性）
        List<GoodsColumnEntity> resetCustomColumns = existingCustomColumns.stream()
                .map(column -> {
                    GoodsColumnEntity entity = new GoodsColumnEntity();
                    BeanUtils.copyProperties(column, entity);
                    entity.setId(null); // 重置ID，让数据库自动生成新ID
                    entity.setCreatedAt(null); // 重置创建时间
                    entity.setUpdatedAt(null); // 重置更新时间
                    return entity;
                })
                .collect(Collectors.toList());

        newColumns.addAll(resetCustomColumns);

        // 3. 清空表后重新插入
        // 删除当前商户的所有货品字段
        this.remove(QueryWrapper.create()
                .from(GoodsColumnEntity.class)
                .where(GoodsColumnEntity::getCompanyId).eq(companyId.intValue()));

        // 批量插入重新组织的字段
        boolean result = this.saveBatch(newColumns);

        if (result) {
            return String.format("货品字段重置成功！系统字段：%d个，自定义字段：%d个，总计：%d个",
                systemColumns.size(), resetCustomColumns.size(), newColumns.size());
        } else {
            return "货品字段重置失败";
        }
    }
}