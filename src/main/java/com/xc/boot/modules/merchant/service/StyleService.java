package com.xc.boot.modules.merchant.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.merchant.model.dto.StyleFormDTO;
import com.xc.boot.modules.merchant.model.entity.StyleEntity;
import com.xc.boot.modules.merchant.model.query.StylePageQuery;
import com.xc.boot.modules.merchant.model.vo.StylePageVO;

/**
 * 款式服务接口
 */
public interface StyleService extends IService<StyleEntity> {

    /**
     * 获取款式分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<StylePageVO> getStylePage(StylePageQuery queryParams);

    /**
     * 保存款式
     *
     * @param form 款式表单数据
     * @return 是否成功
     */
    boolean saveStyle(StyleFormDTO form);

    /**
     * 删除款式
     *
     * @param id 款式ID
     * @return 是否成功
     */
    boolean deleteStyle(Long id);
}