package com.xc.boot.modules.merchant.model.vo;

import com.xc.boot.common.util.PriceUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName GoldPriceVo
 * @Date: 2025/6/9 17:11
 * @Description: 描述
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "金价视图")
public class GoldPriceVo {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "商户ID")
    private Long companyId;

    @Schema(description = "金价")
    private BigDecimal goldPrice;

    @Schema(description = "白金价格")
    private BigDecimal platinumPrice;

    @Schema(description = "白银价格")
    private BigDecimal silverPrice;

    @Schema(description = "金价")
    private BigDecimal goldPriceChange;

    @Schema(description = "白金价格")
    private BigDecimal platinumPriceChange;

    @Schema(description = "白银价格")
    private BigDecimal silverPriceChange;

    @Schema(description = "创建时间")
    private Date createdAt;

    public BigDecimal getGoldPrice() {
        return PriceUtil.formatTwoDecimal(goldPrice);
    }

    public BigDecimal getPlatinumPrice() {
        return PriceUtil.formatTwoDecimal(platinumPrice);
    }

    public BigDecimal getSilverPrice() {
        return PriceUtil.formatTwoDecimal(silverPrice);
    }

    public BigDecimal getGoldPriceChange() {
        return PriceUtil.formatTwoDecimal(goldPriceChange);
    }

    public BigDecimal getPlatinumPriceChange() {
        return PriceUtil.formatTwoDecimal(platinumPriceChange);
    }

    public BigDecimal getSilverPriceChange() {
        return PriceUtil.formatTwoDecimal(silverPriceChange);
    }
}
