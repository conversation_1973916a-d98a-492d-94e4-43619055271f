package com.xc.boot.modules.merchant.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 入库模板分页返回对象
 */
@Schema(description = "入库模板分页返回对象")
@Data
public class GoodsIncomeTemplatePageVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "模板名称")
    private String name;

    @Schema(description = "所属大类")
    private Integer categoryId;

    @Schema(description = "所属大类文本")
    private String categoryName;

    @Schema(description = "是否默认")
    private Integer defaultFlag;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "说明")
    private String remark;

    @Schema(description = "创建时间")
    private Date createdAt;
} 