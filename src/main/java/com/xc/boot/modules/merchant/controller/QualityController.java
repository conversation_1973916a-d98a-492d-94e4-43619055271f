package com.xc.boot.modules.merchant.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.base.SwitchRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.merchant.model.dto.QualityFormDTO;
import com.xc.boot.modules.merchant.model.query.QualityPageQuery;
import com.xc.boot.modules.merchant.model.vo.QualityPageVO;
import com.xc.boot.modules.merchant.service.QualityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 成色管理控制层
 */
@Tag(name = "系统配置-成色管理")
@RestController
@RequestMapping("/api/qualities")
@RequiredArgsConstructor
public class QualityController {

    private final QualityService qualityService;

    @Operation(summary = "成色分页列表")
    @GetMapping("/page")
    public PageResult<QualityPageVO> getQualityPage(QualityPageQuery queryParams) {
        Page<QualityPageVO> result = qualityService.getQualityPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增成色")
    @PostMapping
    public Result<Boolean> addQuality(@RequestBody @Valid QualityFormDTO form) {
        boolean result = qualityService.saveQuality(form);
        return Result.success(result);
    }

    @Operation(summary = "编辑成色")
    @PutMapping
    public Result<Boolean> editQuality(@RequestBody @Valid QualityFormDTO form) {
        boolean result = qualityService.saveQuality(form);
        return Result.success(result);
    }

    @Operation(summary = "删除成色")
    @DeleteMapping
    public Result<?> deleteQuality(@RequestBody @Valid DeleteRequest dto) {
        boolean result = qualityService.deleteQuality(dto.getId());
        return Result.judge(result);
    }
}