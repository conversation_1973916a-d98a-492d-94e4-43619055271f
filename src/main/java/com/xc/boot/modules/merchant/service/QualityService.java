package com.xc.boot.modules.merchant.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.merchant.model.dto.QualityFormDTO;
import com.xc.boot.modules.merchant.model.entity.QualityEntity;
import com.xc.boot.modules.merchant.model.query.QualityPageQuery;
import com.xc.boot.modules.merchant.model.vo.QualityPageVO;

/**
 * 成色服务接口
 */
public interface QualityService extends IService<QualityEntity> {

    /**
     * 获取成色分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<QualityPageVO> getQualityPage(QualityPageQuery queryParams);

    /**
     * 保存成色
     *
     * @param form 成色表单数据
     * @return 是否成功
     */
    boolean saveQuality(QualityFormDTO form);


    /**
     * 删除成色
     *
     * @param id 成色ID
     * @return 是否成功
     */
    boolean deleteQuality(Long id);

    /**
     * 初始化默认成色
     * 为新商户创建默认的成色数据
     */
    void initDefaultQualities();
}