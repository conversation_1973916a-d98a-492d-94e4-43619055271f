package com.xc.boot.modules.merchant.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 入库模板实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_income_templates")
public class GoodsIncomeTemplateEntity extends BaseEntity {
    
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 模板名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 所属大类
     */
    @Column(value = "category_id")
    private Integer categoryId;

    /**
     * 是否默认
     */
    @Column(value = "default_flag")
    private Integer defaultFlag;

    /**
     * 状态
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 说明
     */
    @Column(value = "remark")
    private String remark;
} 