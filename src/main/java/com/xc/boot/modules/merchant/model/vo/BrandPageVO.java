package com.xc.boot.modules.merchant.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.modules.merchant.model.enums.BrandStatusEnum;

/**
 * 品牌分页视图对象
 */
@Data
@Schema(description = "品牌分页视图对象")
public class BrandPageVO {

    @Schema(description = "品牌ID")
    private Long id;

    @Schema(description = "商户ID")
    private Integer companyId;

    @Schema(description = "品牌名称")
    private String name;

    @Schema(description = "品牌所属的公司名称")
    private String companyName;

    @Schema(description = "网址")
    private String url;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "状态(0:禁用|1:启用)")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDesc;

    // getter
    public String getStatusDesc() {
        return IBaseEnum.getLabelByValue(this.status, BrandStatusEnum.class);
    }

    @Schema(description = "说明")
    private String remark;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;
}