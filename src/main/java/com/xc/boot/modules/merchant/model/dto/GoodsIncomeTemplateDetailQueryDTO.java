package com.xc.boot.modules.merchant.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 入库模板明细查询对象
 */
@Data
@Schema(description = "入库模板明细查询对象")
public class GoodsIncomeTemplateDetailQueryDTO {

    @Schema(description = "模板ID")
    @NotNull(message = "模板ID不能为空")
    private Long id;

    @Schema(description = "模板名称")
    private String name;
} 