package com.xc.boot.modules.merchant.model.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 成色分页展示VO
 */
@Getter
@Setter
@Accessors(chain = true)
public class QualityPageVO {

    /**
     * 成色ID
     */
    private Long id;

    /**
     * 所属大类
     */
    private Integer categoryId;

    /**
     * 成色名称
     */
    private String name;

    /**
     * 含量
     */
    private String content;

    /**
     * 状态(0:禁用|1:启用)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createdAt;

    /**
     * 修改时间
     */
    private String updatedAt;
}