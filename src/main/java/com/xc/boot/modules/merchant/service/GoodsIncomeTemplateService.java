package com.xc.boot.modules.merchant.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.merchant.model.dto.GoodsIncomeTemplateFormDTO;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateDetailEntity;
import com.xc.boot.modules.merchant.model.query.GoodsIncomeTemplatePageQuery;
import com.xc.boot.modules.merchant.model.vo.GoodsIncomeTemplatePageVO;
import com.xc.boot.modules.merchant.model.vo.GoodsIncomeTemplateDetailVO;
import com.xc.boot.modules.merchant.model.vo.GoodsIncomeTemplateDetailWithColumnVO;

import java.util.List;

/**
 * 入库模板服务接口
 */
public interface GoodsIncomeTemplateService extends IService<GoodsIncomeTemplateEntity> {

    /**
     * 获取入库模板分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<GoodsIncomeTemplatePageVO> getTemplatePage(GoodsIncomeTemplatePageQuery queryParams);

    /**
     * 保存入库模板
     *
     * @param form 表单数据
     * @return 是否成功
     */
    boolean saveTemplate(GoodsIncomeTemplateFormDTO form);

    /**
     * 删除入库模板
     *
     * @param id 模板ID
     * @return 是否成功
     */
    boolean deleteTemplate(Long id);

    /**
     * 获取入库模板明细
     *
     * @param templateId 模板ID
     * @param name 模板名称
     * @return 模板明细列表
     */
    List<GoodsIncomeTemplateDetailWithColumnVO> getTemplateDetails(Long templateId, String name);

    /**
     * 保存入库模板明细
     *
     * @param templateId 模板ID
     * @param details 模板明细列表
     * @return 是否成功
     */
    boolean saveTemplateDetails(Long templateId, List<GoodsIncomeTemplateDetailEntity> details);

    /**
     * 获取入库模板详情（包含字段信息）
     *
     * @param id 入库模板ID
     * @return 入库模板详情列表（包含字段信息）
     */
    List<GoodsIncomeTemplateDetailVO> getTemplateDetailsWithColumn(Long id);

    /**
     * 获取入库模板详情（包含字段信息）
     *
     * @param id 入库模板ID
     * @param includeDisabled 是否包含禁用的字段
     * @return 入库模板详情列表（包含字段信息）
     */
    List<GoodsIncomeTemplateDetailVO> getTemplateDetailsWithColumn(Long id, Boolean includeDisabled);

    /**
     * 根据ID获取入库模板实体
     *
     * @param id 模板ID
     * @return 入库模板实体
     */
    GoodsIncomeTemplateEntity getTemplateById(Long id);
} 