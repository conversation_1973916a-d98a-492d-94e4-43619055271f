package com.xc.boot.modules.merchant.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.base.SwitchRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.merchant.model.dto.BrandFormDTO;
import com.xc.boot.modules.merchant.model.query.BrandPageQuery;
import com.xc.boot.modules.merchant.model.vo.BrandPageVO;
import com.xc.boot.modules.merchant.service.BrandService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 品牌管理控制层
 */
@Tag(name = "系统配置-品牌管理")
@RestController
@RequestMapping("/api/brands")
@RequiredArgsConstructor
public class BrandController {

    private final BrandService brandService;

    @Operation(summary = "品牌分页列表")
    @GetMapping("/page")
    public PageResult<BrandPageVO> getBrandPage(BrandPageQuery queryParams) {
        Page<BrandPageVO> result = brandService.getBrandPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增品牌")
    @PostMapping
    public Result<Boolean> addBrand(@RequestBody @Valid BrandFormDTO form) {
        boolean result = brandService.saveBrand(form);
        return Result.success(result);
    }

    @Operation(summary = "编辑品牌")
    @PutMapping
    public Result<Boolean> editBrand(@RequestBody @Valid BrandFormDTO form) {
        boolean result = brandService.saveBrand(form);
        return Result.success(result);
    }

    @Operation(summary = "修改品牌状态")
    @PutMapping("/status")
    public Result<Boolean> updateBrandStatus(@RequestBody @Valid SwitchRequest dto) {
        boolean result = brandService.updateBrandStatus(dto.getId(), dto.getStatus());
        return Result.success(result);
    }

    @Operation(summary = "删除品牌")
    @DeleteMapping
    public Result<?> deleteBrand(@RequestBody @Valid DeleteRequest dto) {
        boolean result = brandService.deleteBrand(dto.getId());
        return Result.judge(result);
    }
} 