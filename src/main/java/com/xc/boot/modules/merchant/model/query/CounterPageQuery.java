package com.xc.boot.modules.merchant.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 柜台分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "柜台分页查询对象")
public class CounterPageQuery extends BasePageQuery {

    @Schema(description = "门店")
    private Integer merchantId;

    @Schema(description = "柜台名称")
    private String name;

    @Schema(description = "柜台类型(1-销售 2-仓库 3-旧料)")
    private Integer type;

    @Schema(description = "状态(0-禁用 1-启用)")
    private Integer status;
} 