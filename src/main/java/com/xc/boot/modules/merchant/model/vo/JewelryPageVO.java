package com.xc.boot.modules.merchant.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 珠石分页视图对象
 */
@Data
@Schema(description = "珠石分页视图对象")
public class JewelryPageVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "商户ID")
    private Integer companyId;

    @Schema(description = "珠石名称")
    private String name;

    @Schema(description = "说明")
    private String remark;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "状态(0:禁用;1:启用)")
    private Integer status;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;
} 