package com.xc.boot.modules.merchant.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.merchant.model.dto.CounterFormDTO;
import com.xc.boot.modules.merchant.model.entity.CounterEntity;
import com.xc.boot.modules.merchant.model.query.CounterPageQuery;
import com.xc.boot.modules.merchant.model.vo.CounterPageVO;

/**
 * 柜台服务接口
 */
public interface CounterService extends IService<CounterEntity> {
    
    /**
     * 获取柜台分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<CounterPageVO> getCounterPage(CounterPageQuery queryParams);

    /**
     * 保存柜台
     *
     * @param form 柜台表单数据
     * @return 是否成功
     */
    boolean saveCounter(CounterFormDTO form);

    /**
     * 删除柜台
     *
     * @param id 柜台ID
     * @return 是否成功
     */
    boolean deleteCounter(Long id);

    /**
     * 修改柜台状态
     *
     * @param id 柜台ID
     * @param status 柜台状态(1:启用；0:禁用)
     * @return 是否成功
     */
    boolean updateCounterStatus(Long id, Integer status);

    /**
     * 创建默认柜台
     *
     * @param merchantId 门店ID
     * @return 是否成功
     */
    boolean createDefaultCounters(Integer merchantId);
} 