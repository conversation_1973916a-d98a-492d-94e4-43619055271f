package com.xc.boot.modules.merchant.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.merchant.model.entity.JewelryEntity;
import com.xc.boot.modules.merchant.model.query.JewelryPageQuery;
import com.xc.boot.modules.merchant.model.vo.JewelryPageVO;

/**
 * 珠石服务接口
 */
public interface JewelryService extends IService<JewelryEntity> {

    /**
     * 获取珠石分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<JewelryPageVO> getJewelryPage(JewelryPageQuery queryParams);

    /**
     * 保存珠石
     *
     * @param entity 珠石实体
     * @return 是否成功
     */
    boolean saveJewelry(JewelryEntity entity);

    /**
     * 修改珠石状态
     *
     * @param id     珠石ID
     * @param status 珠石状态(1:启用；0:禁用)
     * @return 是否成功
     */
    boolean updateJewelryStatus(Long id, Integer status);

    /**
     * 删除珠石
     *
     * @param id 珠石ID
     * @return 是否成功
     */
    boolean deleteJewelry(Long id);
} 