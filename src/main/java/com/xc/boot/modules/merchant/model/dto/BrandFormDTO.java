package com.xc.boot.modules.merchant.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * 品牌表单对象
 */
@Data
@Schema(description = "品牌表单对象")
public class BrandFormDTO {

    @Schema(description = "品牌ID")
    private Long id;

    @Schema(description = "品牌名称")
    @NotBlank(message = "品牌名称不能为空")
    @Size(min = 2, max = 50, message = "品牌名称长度应为2到50个字符")
    private String name;

    @Schema(description = "品牌所属的公司名称")
    @Size(max = 50, message = "公司名称长度不能超过50个字符")
    private String companyName;

    @Schema(description = "网址")
    @Pattern(regexp = "^(http://|https://)?([\\da-z.-]+)\\.([a-z.]{2,6})([/\\w .-]*)*/?$", message = "网址格式不正确", groups = {NotEmpty.class})
    @Size(max = 200, message = "网址长度不能超过200个字符")
    private String url;

    @Schema(description = "地址")
    @Size(max = 200, message = "地址长度不能超过200个字符")
    private String address;

    @Schema(description = "状态(0-禁用 1-启用)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "说明")
    @Size(max = 200, message = "说明长度不能超过200个字符")
    private String remark;
} 