package com.xc.boot.modules.merchant.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "金价分页查询对象")
public class GoldPricePageQuery extends BasePageQuery {
    @Schema(description = "大类id")
    private String categoryIds;
    @Schema(description = "成色id")
    private Long qualityId;
    @Schema(description = "更新时间范围")
    private Date[] timeRange;
    @Schema(description = "导出状态")
    private Integer export = 0;
}
