package com.xc.boot.modules.merchant.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.merchant.model.dto.BrandFormDTO;
import com.xc.boot.modules.merchant.model.entity.BrandEntity;
import com.xc.boot.modules.merchant.model.query.BrandPageQuery;
import com.xc.boot.modules.merchant.model.vo.BrandPageVO;

/**
 * 品牌服务接口
 */
public interface BrandService extends IService<BrandEntity> {
    
    /**
     * 获取品牌分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<BrandPageVO> getBrandPage(BrandPageQuery queryParams);

    /**
     * 保存品牌
     *
     * @param form 品牌表单数据
     * @return 是否成功
     */
    boolean saveBrand(BrandFormDTO form);

    /**
     * 修改品牌状态
     *
     * @param id 品牌ID
     * @param status 品牌状态(1:启用；0:禁用)
     * @return 是否成功
     */
    boolean updateBrandStatus(Long id, Integer status);

    /**
     * 删除品牌
     *
     * @param id 品牌ID
     * @return 是否成功
     */
    boolean deleteBrand(Long id);
} 