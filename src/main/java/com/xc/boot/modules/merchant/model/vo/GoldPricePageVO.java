package com.xc.boot.modules.merchant.model.vo;

import com.xc.boot.common.util.excel.model.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Schema(description = "金价设置视图对象")
@Data
public class GoldPricePageVO {
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "商家id")
    private Long companyId;

    @Schema(description = "大类id")
    private Long categoryId;

    @Schema(description = "大类名称")
    private String category;

    @Schema(description = "成色id")
    private Long qualityId;

    @Schema(description = "成色名称")
    private String quality;

    @Schema(description = "销售价(元/ 克)")
    private BigDecimal salePrice;

    @Schema(description = "回收价(元/ 克)")
    private BigDecimal recyclePrice;

    @Schema(description = "生效时间")
    private Date activeTime;

    @Schema(description = "过期时间")
    private Date expireTime;

    @Schema(description = "状态(-1:已失效|0:待生效|1:生效中)")
    @Excel(type = 3, key = {"-1", "0", "1"}, value = {"已失效", "待生效", "生效中"})
    private Integer status;

    @Schema(description = "调价原因")
    private String remark;

    @Schema(description = "创建人")
    private Long createdBy;

    @Schema(description = "创建人名称")
    private String createdByName;

    @Schema(description = "更新人")
    private Long updatedBy;

    @Schema(description = "更新人名称")
    private String updatedByName;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;
}
