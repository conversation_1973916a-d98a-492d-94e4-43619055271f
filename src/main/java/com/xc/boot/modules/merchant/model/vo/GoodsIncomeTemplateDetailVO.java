package com.xc.boot.modules.merchant.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 入库模板明细视图对象
 */
@Schema(description = "入库模板明细视图对象")
@Data
public class GoodsIncomeTemplateDetailVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "字段标识")
    private String sign;

    @Schema(description = "是否启用")
    private Integer enabled;

    @Schema(description = "是否必填")
    private Integer requiredFlag;

    @Schema(description = "默认值")
    private String defaultValue;

    @Schema(description = "图片ID")
    private Long imageId;

    @Schema(description = "字段信息")
    private GoodsColumnVO column;
} 