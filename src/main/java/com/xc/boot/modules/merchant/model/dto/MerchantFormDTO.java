package com.xc.boot.modules.merchant.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "门店表单对象")
public class MerchantFormDTO {
    @Schema(description = "门店ID")
    private Long id;

    @Schema(description = "门店名称")
    @NotBlank(message = "门店名称不能为空")
    @Size(max = 50, message = "门店名称长度不能超过50个字符")
    private String name;

    @Schema(description = "门店地址")
    @Size(max = 200, message = "门店地址长度不能超过200个字符")
    private String address;

    @Schema(description = "门店电话")
    @Size(max = 20, message = "门店电话长度不能超过20个字符")
    private String phone;

    @Schema(description = "联系人")
    @Size(max = 20, message = "联系人长度不能超过20个字符")
    private String contact;

    @Schema(description = "联系人电话")
    @Size(max = 20, message = "联系人电话长度不能超过20个字符")
    private String contactPhone;

    @Schema(description = "状态(0:禁用|1:启用)")
    private Integer status;

    @Schema(description = "备注")
    @Size(max = 200, message = "备注长度不能超过200个字符")
    private String remark;
} 