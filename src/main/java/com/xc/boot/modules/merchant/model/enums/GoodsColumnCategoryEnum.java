package com.xc.boot.modules.merchant.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 商品字段类目枚举
 */
@Getter
public enum GoodsColumnCategoryEnum implements IBaseEnum<Integer> {
    COMMON(1, "通用"),
    CUSTOM(2, "自定义");

    private final Integer value;
    private final String label;

    GoodsColumnCategoryEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
} 