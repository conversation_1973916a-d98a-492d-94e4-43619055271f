package com.xc.boot.modules.merchant.model.vo;

import com.xc.boot.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店VO")
public class MerchantVO extends BaseEntity {
    @Schema(description = "商户ID")
    private Integer companyId;

    @Schema(description = "门店名称")
    private String name;

    @Schema(description = "门店地址")
    private String address;

    @Schema(description = "门店联系电话")
    private String phone;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "联系人电话")
    private String contactPhone;

    @Schema(description = "状态(0:禁用|1:启用)")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDesc;

    public String getStatusDesc() {
        return status == 1 ? "启用" : "禁用";
    }

    @Schema(description = "备注")
    private String remark;
}
