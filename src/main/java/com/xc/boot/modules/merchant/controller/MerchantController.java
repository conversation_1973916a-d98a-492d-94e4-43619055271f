package com.xc.boot.modules.merchant.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.merchant.model.dto.MerchantFormDTO;
import com.xc.boot.modules.merchant.model.query.MerchantQuery;
import com.xc.boot.modules.merchant.model.vo.MerchantVO;
import com.xc.boot.modules.merchant.service.MerchantService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@Tag(name = "系统配置-门店管理")
@RestController
@RequestMapping("/api/merchant")
@RequiredArgsConstructor
public class MerchantController {

    private final MerchantService merchantService;

    @Operation(summary = "门店分页列表")
    @GetMapping("/page")
    public PageResult<MerchantVO> list(MerchantQuery query) {
        Page<MerchantVO> result = merchantService.getMerchantPage(query);
        return PageResult.success(result);
    }

    @Operation(summary = "新增门店")
    @PostMapping
    public Result<Boolean> addMerchant(@RequestBody @Valid MerchantFormDTO form) {
        boolean result = merchantService.saveMerchant(form);
        return Result.success(result);
    }

    @Operation(summary = "编辑门店")
    @PutMapping
    public Result<Boolean> updateMerchant(@RequestBody @Valid MerchantFormDTO form) {
        boolean result = merchantService.updateMerchant(form);
        return Result.success(result);
    }
}
