package com.xc.boot.modules.merchant.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 品牌实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "brands")
public class BrandEntity extends BaseEntity {
    
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 品牌名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 品牌所属的公司名称
     */
    @Column(value = "company_name")
    private String companyName;

    /**
     * 网址
     */
    @Column(value = "url")
    private String url;

    /**
     * 地址
     */
    @Column(value = "address")
    private String address;

    /**
     * 状态(0:禁用|1:启用)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 说明
     */
    @Column(value = "remark")
    private String remark;
} 