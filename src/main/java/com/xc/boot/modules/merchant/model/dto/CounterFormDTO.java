package com.xc.boot.modules.merchant.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 柜台表单数据对象
 */
@Data
@Schema(description = "柜台表单数据对象")
public class CounterFormDTO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "门店ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "门店不能为空")
    private Integer merchantId;

    @Schema(description = "柜台名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "柜台名称不能为空")
    @Size(max = 50, message = "柜台名称长度不能超过50个字符")
    private String name;

    @Schema(description = "柜台类型(1-销售 2-仓库 3-旧料)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "柜台类型不能为空")
    private Integer type;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    @Min(value = 0, message = "排序值不能小于0")
    @Max(value = 999, message = "排序值不能大于999")
    private Integer sort;

    @Schema(description = "状态(0-禁用 1-启用)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "备注")
    @Size(max = 200, message = "备注长度不能超过200个字符")
    private String remark;
} 