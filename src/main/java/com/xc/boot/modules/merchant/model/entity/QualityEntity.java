package com.xc.boot.modules.merchant.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 成色实体
 */
@Builder
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "quality")
@NoArgsConstructor
@AllArgsConstructor
public class QualityEntity extends BaseEntity {

    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 成色名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 含量
     */
    @Column(value = "content")
    private String content;

    /**
     * 大类ID
     */
    @Column(value = "category_id")
    private Long categoryId;

    /**
     * 状态(0:禁用|1:启用)
     */
    @Column(value = "status")
    private Integer status;
}