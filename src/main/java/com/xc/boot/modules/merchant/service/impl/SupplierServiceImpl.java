package com.xc.boot.modules.merchant.service.impl;

import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.income.mapper.GoodsIncomeDetailMapper;
import com.xc.boot.modules.income.model.entity.GoodsIncomeDetailEntity;
import com.xc.boot.modules.merchant.mapper.SupplierMapper;
import com.xc.boot.modules.merchant.model.entity.SupplierEntity;
import com.xc.boot.modules.merchant.model.query.SupplierPageQuery;
import com.xc.boot.modules.merchant.model.vo.SupplierPageVO;
import com.xc.boot.modules.merchant.service.SupplierService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Map;

import static com.mybatisflex.core.query.QueryMethods.*;

/**
 * 供应商服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierServiceImpl extends ServiceImpl<SupplierMapper, SupplierEntity> implements SupplierService {

    private final GoodsMapper goodsMapper;
    private final GoodsIncomeDetailMapper goodsIncomeDetailMapper;

    @Override
    public Page<SupplierPageVO> getSupplierPage(SupplierPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        column(SupplierEntity::getId),
                        column(SupplierEntity::getCompanyId),
                        column(SupplierEntity::getSign),
                        column(SupplierEntity::getName),
                        column(SupplierEntity::getContact),
                        column(SupplierEntity::getContactPhone),
                        column(SupplierEntity::getAddress),
                        column(SupplierEntity::getStatus),
                        column(SupplierEntity::getRemark),
                        column(SupplierEntity::getCreatedAt),
                        column(SupplierEntity::getUpdatedAt))
                .from(SupplierEntity.class)
                // 供应商编码模糊查询
                .where(SupplierEntity::getSign)
                .like(queryParams.getSign(), StrUtil.isNotBlank(queryParams.getSign()))
                // 供应商名称模糊查询
                .and(SupplierEntity::getName)
                .like(queryParams.getName(), StrUtil.isNotBlank(queryParams.getName()))
                // 联系人模糊查询
                .and(SupplierEntity::getContact)
                .like(queryParams.getContact(), StrUtil.isNotBlank(queryParams.getContact()))
                // 联系人电话模糊查询
                .and(SupplierEntity::getContactPhone)
                .like(queryParams.getContactPhone(), StrUtil.isNotBlank(queryParams.getContactPhone()))
                // 状态精确匹配
                .and(SupplierEntity::getStatus)
                .eq(queryParams.getStatus(), queryParams.getStatus() != null)
                // 商户ID精确匹配
                .and(SupplierEntity::getCompanyId)
                .eq(SecurityUtils.getCompanyId());

        // 添加排序条件
        queryWrapper.orderBy(SupplierEntity::getId, false);

        // 执行分页查询并直接映射到VO
        return mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(), queryWrapper,
                SupplierPageVO.class);
    }

    @Override
    public boolean saveSupplier(SupplierEntity entity) {
        // 检查供应商编码是否已存在
        long count = this.count(
                QueryWrapper.create()
                        .from(SupplierEntity.class)
                        .where(SupplierEntity::getSign).eq(entity.getSign())
                        .and(SupplierEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .and(SupplierEntity::getId).ne(entity.getId(), entity.getId() != null));
        CommonUtils.abortIf(count > 0, "供应商编码已存在");

        // 检查供应商名称是否已存在
        count = this.count(
                QueryWrapper.create()
                        .from(SupplierEntity.class)
                        .where(SupplierEntity::getName).eq(entity.getName())
                        .and(SupplierEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .and(SupplierEntity::getId).ne(entity.getId(), entity.getId() != null));
        CommonUtils.abortIf(count > 0, "供应商名称已存在");

        // 设置商户ID
        entity.setCompanyId(SecurityUtils.getCompanyId().intValue());

        // 记录操作日志
        if (entity.getId() == null) {
            OpLogUtils.appendOpLog("供应商管理-新增供应商", "新增供应商: " + entity.getName(), entity);
        } else {
            SupplierEntity oldEntity = this.getById(entity.getId());
            OpLogUtils.appendOpLog("供应商管理-编辑供应商", "编辑供应商: " + entity.getName(),
                    Map.of("修改前", oldEntity, "修改后", entity));
        }

        // 保存或更新供应商
        return this.saveOrUpdate(entity);
    }

    @Override
    public boolean updateSupplierStatus(Long id, Integer status) {
        // 获取供应商信息
        SupplierEntity supplier = this.getById(id);
        CommonUtils.abortIf(supplier == null, "供应商不存在");

        // 检查商户权限
        CommonUtils.abortIf(!supplier.getCompanyId().equals(SecurityUtils.getCompanyId().intValue()), "无权操作该供应商");

        // 记录操作日志
        OpLogUtils.appendOpLog("供应商管理-修改供应商状态",
                String.format("修改供应商[%s]状态: %d -> %d", supplier.getName(), supplier.getStatus(), status),
                Map.of("供应商ID", id, "原状态", supplier.getStatus(), "新状态", status));

        // 更新状态
        supplier.setStatus(status);
        return this.updateById(supplier);
    }

    @Override
    public boolean deleteSupplier(Long id) {
        // 获取供应商信息
        SupplierEntity supplier = this.getById(id);
        CommonUtils.abortIf(supplier == null, "供应商不存在");

        // 检查商户权限
        CommonUtils.abortIf(!supplier.getCompanyId().equals(SecurityUtils.getCompanyId().intValue()), "无权操作该供应商");

        // 检查是否有关联的货品数据
        // 1. 检查商品表中是否使用了该供应商
        Long goodsCount = goodsMapper.selectCountByQuery(
            QueryWrapper.create()
                .from(GoodsEntity.class)
                .where(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsEntity::getSupplierId).eq(id)
        );

        CommonUtils.abortIf(goodsCount > 0, "该供应商已被商品使用，不允许删除");

        // 2. 检查入库单中是否使用了该供应商
        Long incomeCount = goodsIncomeDetailMapper.selectCountByQuery(
            QueryWrapper.create()
                .from(GoodsIncomeDetailEntity.class)
                .where(GoodsIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsIncomeDetailEntity::getSupplierId).eq(id)
        );

        CommonUtils.abortIf(incomeCount > 0, "该供应商已被入库单使用，不允许删除");

        // 记录操作日志
        OpLogUtils.appendOpLog("供应商管理-删除供应商", "删除供应商: " + supplier.getName(), supplier);

        // 删除供应商
        return this.removeById(id);
    }
}