package com.xc.boot.modules.merchant.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

/**
 * 成色表单数据
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "成色表单数据对象")
public class QualityFormDTO {

    /**
     * 成色ID
     */
    @Schema(description = "ID")
    private Long id;

    /**
     * 所属大类ID
     */
    @NotNull(message = "所属大类ID不能为空")
    private Long categoryId;

    /**
     * 成色名称
     */
    @Schema(description = "成色名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "成色名称不能为空")
    @Length(max = 50, message = "成色名称长度不能超过50个字符")
    private String name;

    /**
     * 含量
     */
    @NotBlank(message = "含量不能为空")
    @Length(max = 255, message = "含量长度不能超过200个字符")
    private String content;

    /**
     * 状态(0:禁用|1:启用)
     */
    @Schema(description = "状态(0-禁用 1-启用)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "备注")
    @Size(max = 200, message = "备注长度不能超过200个字符")
    private String remark;
}