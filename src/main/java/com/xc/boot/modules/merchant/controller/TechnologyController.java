package com.xc.boot.modules.merchant.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.merchant.model.entity.TechnologyEntity;
import com.xc.boot.modules.merchant.model.query.TechnologyPageQuery;
import com.xc.boot.modules.merchant.model.vo.TechnologyPageVO;
import com.xc.boot.modules.merchant.service.TechnologyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 工艺管理控制层
 */
@Tag(name = "系统配置-工艺管理")
@RestController
@RequestMapping("/api/technology")
@RequiredArgsConstructor
public class TechnologyController {

    private final TechnologyService technologyService;

    @Operation(summary = "工艺分页列表")
    @GetMapping("/page")
    public PageResult<TechnologyPageVO> getTechnologyPage(TechnologyPageQuery queryParams) {
        Page<TechnologyPageVO> result = technologyService.getTechnologyPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增工艺")
    @PostMapping
    public Result<Boolean> addTechnology(@RequestBody @Valid TechnologyEntity entity) {
        boolean result = technologyService.saveTechnology(entity);
        return Result.success(result);
    }

    @Operation(summary = "编辑工艺")
    @PutMapping
    public Result<Boolean> editTechnology(@RequestBody @Valid TechnologyEntity entity) {
        boolean result = technologyService.saveTechnology(entity);
        return Result.success(result);
    }

    @Operation(summary = "修改工艺状态")
    @PutMapping("/status")
    public Result<Boolean> updateTechnologyStatus(@RequestParam Long id, @RequestParam Integer status) {
        boolean result = technologyService.updateTechnologyStatus(id, status);
        return Result.success(result);
    }

    @Operation(summary = "删除工艺")
    @DeleteMapping
    public Result<?> deleteTechnology(@RequestBody @Valid DeleteRequest request) {
        boolean result = technologyService.deleteTechnology(request.getId());
        return Result.judge(result);
    }
} 