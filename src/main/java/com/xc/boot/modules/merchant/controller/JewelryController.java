package com.xc.boot.modules.merchant.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.base.SwitchRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.merchant.model.entity.JewelryEntity;
import com.xc.boot.modules.merchant.model.query.JewelryPageQuery;
import com.xc.boot.modules.merchant.model.vo.JewelryPageVO;
import com.xc.boot.modules.merchant.service.JewelryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 珠石管理控制层
 */
@Tag(name = "系统配置-珠石管理")
@RestController
@RequestMapping("/api/jewelry")
@RequiredArgsConstructor
public class JewelryController {

    private final JewelryService jewelryService;

    @Operation(summary = "珠石分页列表")
    @GetMapping("/page")
    public PageResult<JewelryPageVO> getJewelryPage(JewelryPageQuery queryParams) {
        Page<JewelryPageVO> result = jewelryService.getJewelryPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增珠石")
    @PostMapping
    public Result<Boolean> addJewelry(@RequestBody @Valid JewelryEntity entity) {
        boolean result = jewelryService.saveJewelry(entity);
        return Result.success(result);
    }

    @Operation(summary = "编辑珠石")
    @PutMapping
    public Result<Boolean> editJewelry(@RequestBody @Valid JewelryEntity entity) {
        boolean result = jewelryService.saveJewelry(entity);
        return Result.success(result);
    }

    @Operation(summary = "修改珠石状态")
    @PutMapping("/status")
    public Result<Boolean> updateJewelryStatus(@RequestBody @Valid SwitchRequest request) {
        boolean result = jewelryService.updateJewelryStatus(request.getId(), request.getStatus());
        return Result.success(result);
    }

    @Operation(summary = "删除珠石")
    @DeleteMapping
    public Result<?> deleteJewelry(@RequestBody @Valid DeleteRequest request) {
        boolean result = jewelryService.deleteJewelry(request.getId());
        return Result.judge(result);
    }
} 