package com.xc.boot.modules.merchant.service.impl;

import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.income.mapper.GoodsIncomeDetailMapper;
import com.xc.boot.modules.income.model.entity.GoodsIncomeDetailEntity;
import com.xc.boot.modules.merchant.mapper.JewelryMapper;
import com.xc.boot.modules.merchant.model.entity.JewelryEntity;
import com.xc.boot.modules.merchant.model.query.JewelryPageQuery;
import com.xc.boot.modules.merchant.model.vo.JewelryPageVO;
import com.xc.boot.modules.merchant.service.JewelryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 珠宝服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JewelryServiceImpl extends ServiceImpl<JewelryMapper, JewelryEntity> implements JewelryService {

    private final GoodsMapper goodsMapper;
    private final GoodsIncomeDetailMapper goodsIncomeDetailMapper;

    @Override
    public Page<JewelryPageVO> getJewelryPage(JewelryPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(JewelryEntity::getId),
                        QueryMethods.column(JewelryEntity::getCompanyId),
                        QueryMethods.column(JewelryEntity::getName),
                        QueryMethods.column(JewelryEntity::getRemark),
                        QueryMethods.column(JewelryEntity::getSort),
                        QueryMethods.column(JewelryEntity::getStatus),
                        QueryMethods.column(JewelryEntity::getCreatedAt),
                        QueryMethods.column(JewelryEntity::getUpdatedAt)
                )
                .from(JewelryEntity.class)
                // 珠石名称模糊查询
                .where(JewelryEntity::getName)
                .like(queryParams.getName(), StrUtil.isNotBlank(queryParams.getName()))
                // 状态精确匹配
                .and(JewelryEntity::getStatus)
                .eq(queryParams.getStatus(), queryParams.getStatus() != null)
                // 商户ID精确匹配
                .and(JewelryEntity::getCompanyId)
                .eq(SecurityUtils.getCompanyId());

        // 添加排序条件
        queryWrapper.orderBy(JewelryEntity::getId, false);

        // 执行分页查询并直接映射到VO
        return mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(), queryWrapper,
                JewelryPageVO.class);
    }

    @Override
    public boolean saveJewelry(JewelryEntity entity) {
        // 设置商户ID
        entity.setCompanyId(SecurityUtils.getCompanyId().intValue());

        // 检查同一商家下珠石名称是否重复
        long count = this.count(
                QueryWrapper.create()
                        .from(JewelryEntity.class)
                        .where(JewelryEntity::getCompanyId)
                        .eq(entity.getCompanyId())
                        .and(JewelryEntity::getName)
                        .eq(entity.getName())
                        .and(JewelryEntity::getId)
                        .ne(entity.getId(), entity.getId() != null));
        if (count > 0) {
            throw new RuntimeException("珠石名称不能重复");
        }

        // 记录操作日志
        if (entity.getId() == null) {
            OpLogUtils.appendOpLog("珠宝管理-新增珠宝", "新增珠宝: " + entity.getName(), entity);
        } else {
            JewelryEntity oldEntity = this.getById(entity.getId());
            OpLogUtils.appendOpLog("珠宝管理-编辑珠宝", "编辑珠宝: " + entity.getName(), 
                Map.of("修改前", oldEntity, "修改后", entity));
        }

        return this.saveOrUpdate(entity);
    }

    @Override
    public boolean updateJewelryStatus(Long id, Integer status) {
        JewelryEntity entity = this.getById(id);
        if (entity != null) {
            // 记录操作日志
            OpLogUtils.appendOpLog("珠宝管理-修改珠宝状态", 
                String.format("修改珠宝[%s]状态: %d -> %d", entity.getName(), entity.getStatus(), status),
                Map.of("珠宝ID", id, "原状态", entity.getStatus(), "新状态", status));
            
            entity.setStatus(status);
            return this.updateById(entity);
        }
        return false;
    }

    @Override
    public boolean deleteJewelry(Long id) {
        // 查询珠石是否存在
        JewelryEntity entity = this.getById(id);
        if (entity != null) {
            // 验证是否为当前商户的珠石
            if (!entity.getCompanyId().equals(SecurityUtils.getCompanyId().intValue())) {
                return false;
            }

            // 检查是否有关联的货品数据
            // 1. 检查商品表中是否使用了该珠宝
            Long goodsCount = goodsMapper.selectCountByQuery(
                QueryWrapper.create()
                    .from(GoodsEntity.class)
                    .where(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(GoodsEntity::getMainStoneId).eq(id)
                    .or(GoodsEntity::getSubStoneId).eq(id)
            );

            CommonUtils.abortIf(goodsCount > 0, "该珠宝已被商品使用，不允许删除");

            // 2. 检查入库单中是否使用了该珠宝
            Long incomeCount = goodsIncomeDetailMapper.selectCountByQuery(
                QueryWrapper.create()
                    .from(GoodsIncomeDetailEntity.class)
                    .where(GoodsIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(GoodsIncomeDetailEntity::getMainStoneId).eq(id)
                    .or(GoodsIncomeDetailEntity::getSubStoneId).eq(id)
            );

            CommonUtils.abortIf(incomeCount > 0, "该珠宝已被入库单使用，不允许删除");

            // 记录操作日志
            OpLogUtils.appendOpLog("珠宝管理-删除珠宝", "删除珠宝: " + entity.getName(), entity);

            return this.removeById(id);
        }
        return false;
    }
} 