package com.xc.boot.modules.merchant.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 柜台实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "counter")
public class CounterEntity extends BaseEntity {
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 门店ID
     */
    @Column(value = "merchant_id")
    private Integer merchantId;

    /**
     * 柜台名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 柜台类型(1-销售 2-仓库 3-旧料)
     */
    @Column(value = "type")
    private Integer type;

    /**
     * 状态(0-禁用 1-启用)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 排序
     */
    @Column(value = "sort")
    private Integer sort;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;
} 