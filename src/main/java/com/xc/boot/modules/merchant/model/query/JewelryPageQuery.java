package com.xc.boot.modules.merchant.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 珠石分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "珠石分页查询对象")
public class JewelryPageQuery extends BasePageQuery {

    @Schema(description = "珠石名称")
    private String name;

    @Schema(description = "状态(0:禁用;1:启用)")
    private Integer status;
} 