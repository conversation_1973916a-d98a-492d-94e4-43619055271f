package com.xc.boot.modules.merchant.service.impl;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.baseColum.JoinColumEnum;
import com.xc.boot.common.util.DataCheckUtil;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.merchant.mapper.QualityMapper;
import com.xc.boot.modules.merchant.model.dto.QualityFormDTO;
import com.xc.boot.modules.merchant.model.entity.QualityEntity;
import com.xc.boot.modules.merchant.model.query.QualityPageQuery;
import com.xc.boot.modules.merchant.model.vo.QualityPageVO;
import com.xc.boot.modules.merchant.service.QualityService;
import com.xc.boot.common.enums.CategoryEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 成色服务实现类
 */
@Service
@RequiredArgsConstructor
public class QualityServiceImpl extends ServiceImpl<QualityMapper, QualityEntity> implements QualityService {

    @Override
    public Page<QualityPageVO> getQualityPage(QualityPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(QualityEntity::getId),
                        QueryMethods.column(QualityEntity::getName),
                        QueryMethods.column(QualityEntity::getContent),
                        QueryMethods.column(QualityEntity::getCategoryId),
                        QueryMethods.column(QualityEntity::getStatus),
                        QueryMethods.column(QualityEntity::getCreatedAt),
                        QueryMethods.column(QualityEntity::getUpdatedAt)
                )
                .from(QualityEntity.class)
                // 所属大类ID精确匹配
                .where(QualityEntity::getCategoryId).eq(queryParams.getCategoryId(), queryParams.getCategoryId() != null)
                // 成色名称模糊查询
                .and(QualityEntity::getName).like(queryParams.getName(), StringUtils.hasText(queryParams.getName()))
                // 含量精确查询
                .and(QualityEntity::getContent).eq(queryParams.getContent(), StringUtils.hasText(queryParams.getContent()))
                // 状态精确匹配
                .and(QualityEntity::getStatus).eq(queryParams.getStatus(), queryParams.getStatus() != null);

        // 添加排序条件
        queryWrapper.orderBy(QualityEntity::getId, false);

        // 执行分页查询并直接映射到VO
        return mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(), queryWrapper, QualityPageVO.class);
    }

    @Override
    public boolean saveQuality(QualityFormDTO form) {
        Long qualityId = form.getId();
        Long companyId = SecurityUtils.getCompanyId();
        Assert.notNull(companyId, "未获取到商户信息");

        // 编辑成色时，判断成色是否存在
        QualityEntity oldQuality = null;
        if (qualityId != null) {
            oldQuality = this.getById(qualityId);
            Assert.isTrue(oldQuality != null, "成色不存在");
            Assert.isTrue(oldQuality.getCompanyId().equals(companyId), "无权操作该成色");
        }

        // 检查成色名称是否已存在
        long count = this.count(
                QueryWrapper.create()
                        .from(QualityEntity.class)
                        .ne(QualityEntity::getId, qualityId, qualityId != null)
                        .and(QualityEntity::getCompanyId).eq(companyId)
                        .and(QualityEntity::getName).eq(form.getName()));
        Assert.isTrue(count == 0, "成色名称已存在");

        // 创建成色实体
        QualityEntity quality = new QualityEntity();
        quality.setName(form.getName());
        quality.setContent(form.getContent());
        quality.setCategoryId(form.getCategoryId());
        quality.setCompanyId(companyId);

        // 如果是编辑，设置ID和状态
        if (oldQuality != null) {
            quality.setId(oldQuality.getId());
            quality.setStatus(form.getStatus());
        } else {
            // 新增时默认启用状态
            quality.setStatus(form.getStatus());
        }

        // 记录操作日志
        if (oldQuality == null) {
            OpLogUtils.appendOpLog("成色管理-新增成色", "新增成色: " + quality.getName(), quality);
        } else {
            OpLogUtils.appendOpLog("成色管理-编辑成色", "编辑成色: " + quality.getName(), 
                Map.of("修改前", oldQuality, "修改后", quality));
        }

        // 保存成色信息
        return this.saveOrUpdate(quality);
    }

    @Override
    public boolean deleteQuality(Long id) {
        QualityEntity quality = this.getById(id);
        Assert.isTrue(quality != null, "成色不存在");
        Assert.isTrue(quality.getCompanyId().equals(SecurityUtils.getCompanyId()), "无权操作该成色");
        Assert.isTrue(DataCheckUtil.checkJoinFieldDelete(id, JoinColumEnum.QUALITY_ID), "成色已被使用，请先解除关联关系");
        // 记录操作日志
        OpLogUtils.appendOpLog("成色管理-删除成色", "删除成色: " + quality.getName(), quality);

        return this.removeById(id);
    }

    @Override
    public void initDefaultQualities() {
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return;
        }

        // 检查是否已存在成色数据
        long count = this.count(
                QueryWrapper.create()
                        .from(QualityEntity.class)
                        .where(QualityEntity::getCompanyId).eq(companyId));

        if (count > 0) {
            // 已存在成色数据，跳过初始化
            return;
        }

        // 默认成色数据
        DefaultQuality[] defaultQualities = {
            new DefaultQuality("铂金 990", CategoryEnum.PLATINUM.getValue(), "99"),
            new DefaultQuality("足银 9999", CategoryEnum.SILVER.getValue(), "99.99"),
            new DefaultQuality("足银 999", CategoryEnum.SILVER.getValue(), "99.9"),
            new DefaultQuality("足银 990", CategoryEnum.SILVER.getValue(), "99"),
            new DefaultQuality("足金 999", CategoryEnum.GOLD.getValue(), "99.9"),
            new DefaultQuality("18K 金", CategoryEnum.K_GOLD.getValue(), "75"),
            new DefaultQuality("足金 9999", CategoryEnum.GOLD.getValue(), "99.99"),
            new DefaultQuality("足银 925", CategoryEnum.SILVER.getValue(), "92.5"),
            new DefaultQuality("铂金 950", CategoryEnum.PLATINUM.getValue(), "95"),
            new DefaultQuality("足金 99999", CategoryEnum.GOLD.getValue(), "99.999")
        };

        // 批量创建默认成色
        List<QualityEntity> qualityList = new ArrayList<>();
        for (DefaultQuality defaultQuality : defaultQualities) {
            QualityEntity quality = QualityEntity.builder()
                    .companyId(companyId)
                    .name(defaultQuality.name)
                    .categoryId(defaultQuality.categoryId)
                    .content(defaultQuality.content)
                    .status(1) // 启用状态
                    .build();

            qualityList.add(quality);
        }

        // 批量插入
        this.saveBatch(qualityList);
    }

    /**
     * 默认成色数据内部类
     */
    private static class DefaultQuality {
        final String name;
        final Long categoryId;
        final String content;

        DefaultQuality(String name, Long categoryId, String content) {
            this.name = name;
            this.categoryId = categoryId;
            this.content = content;
        }
    }
}