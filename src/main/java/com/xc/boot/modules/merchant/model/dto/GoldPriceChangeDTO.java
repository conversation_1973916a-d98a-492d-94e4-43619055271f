package com.xc.boot.modules.merchant.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "批量调价对象")
public class GoldPriceChangeDTO {
    @Schema(description = "大类id列表")
    @NotEmpty(message = "大类id列表不能为空")
    private List<Long> categoryIds;

    @Schema(description = "调价类型(1:百分比调价|2:固定值调价)")
    @NotNull(message = "调价类型不能为空")
    private Integer type;

    @Schema(description = "调价数值(百分比eg:22.3、-63.2 | 固定值eg: 22、-31)")
    @NotNull(message = "调价数值不能为空")
    private BigDecimal number;

    @Schema(description = "调价原因")
    @Size(max = 200, message = "调价原因长度不能超过200个字符")
    private String remark;
}
