package com.xc.boot.modules.merchant.model.dto;


import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "金价表单对象")
public class GoldPriceDTO {
    @Schema(description = "ID")
    @NotNull(message = "ID不能为空", groups = Update.class)
    private Long id;

    @Schema(description = "大类id")
    @NotNull(message = "大类id不能为空", groups = Create.class)
    private Long categoryId;

    @Schema(description = "成色id")
    @NotNull(message = "成色id不能为空", groups = Create.class)
    private Long qualityId;

    @Schema(description = "销售价(元/克)")
    @NotNull(message = "销售价不能为空")
    private BigDecimal salePrice;

    @Schema(description = "回收价(元/克)")
    @NotNull(message = "回收价不能为空")
    private BigDecimal recyclePrice;

    @Schema(description = "调价原因")
    @Size(max = 200, message = "调价原因长度不能超过200个字符")
    private String remark;

}
