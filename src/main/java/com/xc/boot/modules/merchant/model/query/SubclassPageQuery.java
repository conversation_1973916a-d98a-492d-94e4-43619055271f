package com.xc.boot.modules.merchant.model.query;

import com.xc.boot.common.base.BasePageQuery;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 小类分页查询参数
 */
@Getter
@Setter
@Accessors(chain = true)
public class SubclassPageQuery extends BasePageQuery {

    /**
     * 小类名称
     */
    private String name;



    /**
     * 状态(0:禁用|1:启用)
     */
    private Integer status;
}