package com.xc.boot.modules.merchant.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.merchant.model.entity.TechnologyEntity;
import com.xc.boot.modules.merchant.model.query.TechnologyPageQuery;
import com.xc.boot.modules.merchant.model.vo.TechnologyPageVO;

/**
 * 工艺服务接口
 */
public interface TechnologyService extends IService<TechnologyEntity> {
    
    /**
     * 获取工艺分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<TechnologyPageVO> getTechnologyPage(TechnologyPageQuery queryParams);

    /**
     * 保存工艺
     *
     * @param entity 工艺实体
     * @return 是否成功
     */
    boolean saveTechnology(TechnologyEntity entity);

    /**
     * 修改工艺状态
     *
     * @param id 工艺ID
     * @param status 工艺状态(1:启用；0:禁用)
     * @return 是否成功
     */
    boolean updateTechnologyStatus(Long id, Integer status);

    /**
     * 删除工艺
     *
     * @param id 工艺ID
     * @return 是否成功
     */
    boolean deleteTechnology(Long id);
} 