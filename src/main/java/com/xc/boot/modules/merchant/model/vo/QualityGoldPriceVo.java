package com.xc.boot.modules.merchant.model.vo;

import com.xc.boot.common.util.PriceUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 成色金价视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "成色金价视图对象")
public class QualityGoldPriceVo {
    @Schema(description = "大类ID")
    private Long categoryId;

    @Schema(description = "大类名称")
    private String categoryName;

    @Schema(description = "成色ID")
    private Long qualityId;

    @Schema(description = "成色名称")
    private String qualityName;

    @Schema(description = "金价销售价(元/克)")
    private BigDecimal goldSalePrice;

    @Schema(description = "金价回收价(元/克)")
    private BigDecimal goldRecyclePrice;

    @Schema(description = "银价销售价(元/克)")
    private BigDecimal silverSalePrice;

    @Schema(description = "银价回收价(元/克)")
    private BigDecimal silverRecyclePrice;

    @Schema(description = "生效时间")
    private Date activeTime;

    @Schema(description = "更新时间")
    private Date updatedAt;

    public BigDecimal getGoldSalePrice() {
        return PriceUtil.formatTwoDecimal(goldSalePrice);
    }

    public BigDecimal getGoldRecyclePrice() {
        return PriceUtil.formatTwoDecimal(goldRecyclePrice);
    }

    public BigDecimal getSilverSalePrice() {
        return PriceUtil.formatTwoDecimal(silverSalePrice);
    }

    public BigDecimal getSilverRecyclePrice() {
        return PriceUtil.formatTwoDecimal(silverRecyclePrice);
    }
}