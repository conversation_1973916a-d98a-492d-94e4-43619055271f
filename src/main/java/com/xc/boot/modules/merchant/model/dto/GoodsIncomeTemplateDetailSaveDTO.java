package com.xc.boot.modules.merchant.model.dto;

import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateDetailEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 入库模板明细保存DTO
 */
@Data
@Schema(description = "入库模板明细保存DTO")
public class GoodsIncomeTemplateDetailSaveDTO {

    @Schema(description = "模板ID")
    @NotNull(message = "模板ID不能为空")
    private Long templateId;

    @Schema(description = "明细列表")
    @Valid
    private List<GoodsIncomeTemplateDetailEntity> details;
} 