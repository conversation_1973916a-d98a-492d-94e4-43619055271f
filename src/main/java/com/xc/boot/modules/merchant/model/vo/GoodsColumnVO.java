package com.xc.boot.modules.merchant.model.vo;

import com.xc.boot.modules.merchant.model.enums.GoodsColumnCategoryEnum;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnSecretLevelEnum;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnTypeEnum;
import com.xc.boot.common.base.IBaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 商品字段视图对象
 */
@Schema(description = "商品字段视图对象")
@Data
public class GoodsColumnVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "商户ID")
    private Integer companyId;

    @Schema(description = "字段名称")
    private String name;

    @Schema(description = "字段标识")
    private String sign;

    @Schema(description = "机密级别(1-常规 2-敏感 3-机密)")
    private Integer secretLevel;

    @Schema(description = "机密级别文本")
    private String secretLevelLabel;

    @Schema(description = "类目(1-通用 2-自定义)")
    private Integer category;

    @Schema(description = "类目文本")
    private String categoryLabel;

    @Schema(description = "字段属性(1-文本 2-数字 3-多行文本 4-日期 5-下拉列表 6-图片)")
    private Integer type;

    @Schema(description = "字段属性文本")
    private String typeLabel;

    @Schema(description = "是否可多选")
    private Integer isMultiple;

    @Schema(description = "下拉选项(最多20个选项,json)")
    private String options;

    @Schema(description = "小数位数")
    private Integer numberPrecision;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;

    /**
     * 获取机密级别文本
     */
    public String getSecretLevelLabel() {
        if (secretLevelLabel != null) {
            return secretLevelLabel;
        }
        return IBaseEnum.getLabelByValue(secretLevel, GoodsColumnSecretLevelEnum.class);
    }

    /**
     * 获取类目文本
     */
    public String getCategoryLabel() {
        if (categoryLabel != null) {
            return categoryLabel;
        }
        return IBaseEnum.getLabelByValue(category, GoodsColumnCategoryEnum.class);
    }

    /**
     * 获取字段属性文本
     */
    public String getTypeLabel() {
        if (typeLabel != null) {
            return typeLabel;
        }
        return IBaseEnum.getLabelByValue(type, GoodsColumnTypeEnum.class);
    }
} 