package com.xc.boot.modules.merchant.model.query;

import com.xc.boot.common.base.BasePageQuery;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 成色分页查询参数
 */
@Getter
@Setter
@Accessors(chain = true)
public class QualityPageQuery extends BasePageQuery {

    /**
     * 所属大类ID
     */
    private Long categoryId;

    /**
     * 成色名称
     */
    private String name;

    /**
     * 含量
     */
    private String content;

    /**
     * 状态(0:禁用|1:启用)
     */
    private Integer status;
}