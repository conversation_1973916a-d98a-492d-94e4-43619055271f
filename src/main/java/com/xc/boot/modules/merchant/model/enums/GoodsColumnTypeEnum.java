package com.xc.boot.modules.merchant.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 商品字段类型枚举
 */
@Getter
public enum GoodsColumnTypeEnum implements IBaseEnum<Integer> {
    TEXT(1, "文本"),
    NUMBER(2, "数字"),
    TEXTAREA(3, "多行文本"),
    DATE(4, "日期"),
    SELECT(5, "下拉列表"),
    IMAGE(6, "图片");

    private final Integer value;
    private final String label;

    GoodsColumnTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
} 