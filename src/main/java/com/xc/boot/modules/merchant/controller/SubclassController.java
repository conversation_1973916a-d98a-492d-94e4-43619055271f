package com.xc.boot.modules.merchant.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.merchant.model.dto.SubclassFormDTO;
import com.xc.boot.modules.merchant.model.query.SubclassPageQuery;
import com.xc.boot.modules.merchant.model.vo.SubclassPageVO;
import com.xc.boot.modules.merchant.service.SubclassService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 小类管理控制层
 */
@Tag(name = "系统配置-小类管理")
@RestController
@RequestMapping("/api/subclasses")
@RequiredArgsConstructor
public class SubclassController {

    private final SubclassService subclassService;

    @Operation(summary = "小类分页列表")
    @GetMapping("/page")
    public PageResult<SubclassPageVO> getSubclassPage(SubclassPageQuery queryParams) {
        Page<SubclassPageVO> result = subclassService.getSubclassPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增小类")
    @PostMapping
    public Result<Boolean> addSubclass(@RequestBody @Valid SubclassFormDTO form) {
        boolean result = subclassService.saveSubclass(form);
        return Result.success(result);
    }

    @Operation(summary = "编辑小类")
    @PutMapping
    public Result<Boolean> editSubclass(@RequestBody @Valid SubclassFormDTO form) {
        boolean result = subclassService.saveSubclass(form);
        return Result.success(result);
    }

    @Operation(summary = "删除小类")
    @DeleteMapping
    public Result<?> deleteSubclass(@RequestBody @Valid DeleteRequest dto) {
        boolean result = subclassService.deleteSubclass(dto.getId());
        return Result.judge(result);
    }
}