package com.xc.boot.modules.merchant.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 商品字段实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_columns")
public class GoodsColumnEntity extends BaseEntity {
    
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 字段名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 字段标识
     */
    @Column(value = "sign")
    private String sign;

    /**
     * 机密级别(1-常规 2-敏感 3-机密)
     */
    @Column(value = "secret_level")
    private Integer secretLevel;

    /**
     * 类目(1-通用 2-自定义)
     */
    @Column(value = "category")
    private Integer category;

    /**
     * 字段属性(1-文本 2-数字 3-多行文本 4-日期 5-下拉列表 6-图片)
     */
    @Column(value = "type")
    private Integer type;

    /**
     * 是否可多选
     */
    @Column(value = "is_multiple")
    private Integer isMultiple;

    /**
     * 下拉选项(最多20个选项,json)
     */
    @Column(value = "options")
    private String options;

    /**
     * 小数位数
     */
    @Column(value = "number_precision")
    private Integer numberPrecision;
} 