package com.xc.boot.modules.merchant.model.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 款式分页展示VO
 */
@Getter
@Setter
@Accessors(chain = true)
public class StylePageVO {

    /**
     * 款式ID
     */
    private Long id;

    /**
     * 款式名称
     */
    private String name;

    /**
     * 状态(0:禁用|1:启用)
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 说明
     */
    private String remark;

    /**
     * 创建时间
     */
    private String createdAt;

    /**
     * 更新时间
     */
    private String updatedAt;
}