package com.xc.boot.modules.merchant.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 款式实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "style")
public class StyleEntity extends BaseEntity {

    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 款式名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 状态(0:禁用|1:启用)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 排序
     */
    @Column(value = "sort")
    private Integer sort;

    /**
     * 说明
     */
    @Column(value = "remark")
    private String remark;
}