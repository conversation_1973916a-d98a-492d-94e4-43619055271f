package com.xc.boot.modules.merchant.service;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.modules.merchant.model.dto.GoldPriceChangeDTO;
import com.xc.boot.modules.merchant.model.dto.GoldPriceDTO;
import com.xc.boot.modules.merchant.model.query.GoldPricePageQuery;
import com.xc.boot.modules.merchant.model.vo.GoldPricePageVO;
import com.xc.boot.modules.merchant.model.vo.GoldPriceVo;
import com.xc.boot.modules.merchant.model.vo.QualityGoldPriceVo;

public interface GoldPriceService {


    /**
     * 同步金价
     * @return 金价vo
     */
    GoldPriceVo syncGoldPrice();

    /**
     * 最新金价
     * @return 金价vo
     */
    GoldPriceVo realtime();

    /**
     * 金价分页
     * @param queryParams
     * @return
     */
    Page<GoldPricePageVO> pageList(GoldPricePageQuery queryParams);

    /**
     * 新增或修改金价
     * @param goldPriceDTO
     * @return
     */
    Boolean saveGoldPrice(GoldPriceDTO goldPriceDTO);

    /**
     * 启用/停用金价
     * @param id
     * @return
     */
    Boolean updateGoldPriceStatus(Long id);

    /**
     * 批量调价
     * @param changeDTO
     * @return
     */
    Boolean changePrice(GoldPriceChangeDTO changeDTO);

    /**
     * 根据成色ID或大类ID查询金价
     * @param qualityId 成色ID（可选）
     * @param categoryId 大类ID（可选）
     * @return 金价信息
     */
    QualityGoldPriceVo getGoldPriceByQuality(Long qualityId, Long categoryId);

    /**
     * 获取当前金价(生效金价)
     * @return 如果未获取到会返回null
     */
    QualityGoldPriceVo getPriceByCategory(CategoryEnum  category);
}
