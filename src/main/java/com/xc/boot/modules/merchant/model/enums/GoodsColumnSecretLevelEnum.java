package com.xc.boot.modules.merchant.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 商品字段机密级别枚举
 */
@Getter
public enum GoodsColumnSecretLevelEnum implements IBaseEnum<Integer> {
    NORMAL(1, "常规"),
    SENSITIVE(2, "敏感"),
    SECRET(3, "机密");

    private final Integer value;
    private final String label;

    GoodsColumnSecretLevelEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
} 