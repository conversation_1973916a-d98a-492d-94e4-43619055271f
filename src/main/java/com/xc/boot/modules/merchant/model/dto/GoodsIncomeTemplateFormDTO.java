package com.xc.boot.modules.merchant.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 入库模板表单对象
 */
@Schema(description = "入库模板表单对象")
@Data
public class GoodsIncomeTemplateFormDTO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "模板名称")
    @NotBlank(message = "模板名称不能为空")
    private String name;

    @Schema(description = "所属大类")
    @NotNull(message = "所属大类不能为空")
    private Integer categoryId;

    @Schema(description = "是否默认")
    @NotNull(message = "是否默认不能为空")
    private Integer defaultFlag;

    @Schema(description = "状态")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "说明")
    private String remark;
} 