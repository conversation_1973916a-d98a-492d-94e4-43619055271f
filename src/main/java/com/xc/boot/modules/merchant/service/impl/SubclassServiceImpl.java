package com.xc.boot.modules.merchant.service.impl;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.baseColum.JoinColumEnum;
import com.xc.boot.common.util.DataCheckUtil;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.merchant.mapper.SubclassMapper;
import com.xc.boot.modules.merchant.model.dto.SubclassFormDTO;
import com.xc.boot.modules.merchant.model.entity.SubclassEntity;
import com.xc.boot.modules.merchant.model.query.SubclassPageQuery;
import com.xc.boot.modules.merchant.model.vo.SubclassPageVO;
import com.xc.boot.modules.merchant.service.SubclassService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

import java.util.Map;

/**
 * 小类服务实现类
 */
@Service
@RequiredArgsConstructor
public class SubclassServiceImpl extends ServiceImpl<SubclassMapper, SubclassEntity> implements SubclassService {

    @Override
    public Page<SubclassPageVO> getSubclassPage(SubclassPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(SubclassEntity::getId),
                        QueryMethods.column(SubclassEntity::getName),
                        QueryMethods.column(SubclassEntity::getStatus),
                        QueryMethods.column(SubclassEntity::getSort),
                        QueryMethods.column(SubclassEntity::getRemark),
                        QueryMethods.column(SubclassEntity::getCreatedAt),
                        QueryMethods.column(SubclassEntity::getUpdatedAt)
                )
                .from(SubclassEntity.class)
                // 小类名称模糊查询
                .where(SubclassEntity::getName).like(queryParams.getName(), StringUtils.hasText(queryParams.getName()))
                // 状态精确匹配
                .and(SubclassEntity::getStatus).eq(queryParams.getStatus(), queryParams.getStatus() != null)
                // 添加排序条件
                .orderBy(SubclassEntity::getId, false);

        // 执行分页查询并直接映射到VO
        return mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(), queryWrapper, SubclassPageVO.class);
    }

    @Override
    public boolean saveSubclass(SubclassFormDTO form) {
        Long subclassId = form.getId();
        Long companyId = SecurityUtils.getCompanyId();
        Assert.notNull(companyId, "未获取到商户信息");

        // 编辑小类时，判断小类是否存在
        SubclassEntity oldSubclass = null;
        if (subclassId != null) {
            oldSubclass = this.getById(subclassId);
            Assert.isTrue(oldSubclass != null, "小类不存在");
            Assert.isTrue(oldSubclass.getCompanyId().equals(companyId), "无权操作该小类");
        }

        // 检查小类名称是否已存在
        long count = this.count(
                QueryWrapper.create()
                        .from(SubclassEntity.class)
                        .ne(SubclassEntity::getId, subclassId, subclassId != null)
                        .and(SubclassEntity::getCompanyId).eq(companyId)
                        .and(SubclassEntity::getName).eq(form.getName()));
        Assert.isTrue(count == 0, "小类名称已存在");

        // 创建小类实体
        SubclassEntity subclass = new SubclassEntity();
        subclass.setName(form.getName());
        subclass.setStatus(form.getStatus());
        subclass.setSort(form.getSort());
        subclass.setRemark(form.getRemark());
        subclass.setCompanyId(companyId);

        // 如果是编辑，设置ID
        if (oldSubclass != null) {
            subclass.setId(oldSubclass.getId());
        }

        // 记录操作日志
        if (subclassId == null) {
            OpLogUtils.appendOpLog("小类管理-新增小类", "新增小类: " + subclass.getName(), subclass);
        } else {
            OpLogUtils.appendOpLog("小类管理-编辑小类", "编辑小类: " + subclass.getName(), 
                Map.of("修改前", oldSubclass, "修改后", subclass));
        }

        // 保存小类信息
        return this.saveOrUpdate(subclass);
    }

    @Override
    public boolean deleteSubclass(Long id) {
        SubclassEntity subclass = this.getById(id);
        Assert.isTrue(subclass != null, "小类不存在");
        Assert.isTrue(subclass.getCompanyId().equals(SecurityUtils.getCompanyId()), "无权操作该小类");
        Assert.isTrue(DataCheckUtil.checkJoinFieldDelete(id, JoinColumEnum.SUBCLASS_ID), "小类已被使用，请先解除关联关系");
        // 记录操作日志
        OpLogUtils.appendOpLog("小类管理-删除小类", "删除小类: " + subclass.getName(), subclass);

        return this.removeById(id);
    }

    @Override
    public void initDefaultSubclasses() {
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return;
        }

        // 检查是否已存在小类数据
        long count = this.count(
                QueryWrapper.create()
                        .from(SubclassEntity.class)
                        .where(SubclassEntity::getCompanyId).eq(companyId));

        if (count > 0) {
            // 已存在小类数据，跳过初始化
            return;
        }

        // 默认小类列表：珠子、手串、套链、吊坠、项链、手链、耳饰、戒指、手镯
        String[] defaultSubclasses = {
            "珠子", "手串", "套链", "吊坠", "项链", "手链", "耳饰", "戒指", "手镯"
        };

        // 批量创建默认小类
        List<SubclassEntity> subclassList = new ArrayList<>();
        for (int i = 0; i < defaultSubclasses.length; i++) {
            SubclassEntity subclass = new SubclassEntity();
            subclass.setCompanyId(companyId);
            subclass.setName(defaultSubclasses[i]);
            subclass.setStatus(1); // 启用状态
            subclass.setSort(i + 1); // 排序从1开始
            subclass.setRemark("系统默认小类");

            subclassList.add(subclass);
        }

        // 批量插入
        this.saveBatch(subclassList);
    }
}