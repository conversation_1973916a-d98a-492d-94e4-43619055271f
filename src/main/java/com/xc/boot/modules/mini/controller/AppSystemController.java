package com.xc.boot.modules.mini.controller;

import com.xc.boot.common.enums.SideEnum;
import com.xc.boot.common.result.Result;
import com.xc.boot.system.model.vo.RouteVO;
import com.xc.boot.system.service.MenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "小程序-公共接口")
@RequestMapping("/api/mini/system")
public class AppSystemController {

    private final MenuService menuService;

    @Operation(summary = "获取用户菜单路由列表")
    @GetMapping("/menus")
    public Result<List<RouteVO>> getUserMenus() {
        List<RouteVO> routeList = menuService.getCurrentUserRoutes(SideEnum.MINI_APP);
        return Result.success(routeList);
    }
}
