package com.xc.boot.modules.mini.service.impl;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.SideEnum;
import com.xc.boot.common.result.ResultCode;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.mini.model.form.AppUserUpdateForm;
import com.xc.boot.modules.mini.service.AppUserService;
import com.xc.boot.shared.auth.service.TokenService;
import com.xc.boot.system.mapper.UserMapper;
import com.xc.boot.system.model.entity.SysUserEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AppUserServiceImpl extends ServiceImpl<UserMapper, SysUserEntity> implements AppUserService {

    private final TokenService tokenService;

    @Override
    @Transactional
    public boolean updateUserInfo(AppUserUpdateForm form) {
        // 获取当前登录用户信息
        SysUserDetails userDetails = SecurityUtils.getUser().orElse(new SysUserDetails());
        
        // 验证用户ID是否匹配
        Assert.isTrue(userDetails.getUserId().equals(form.getId()), "只能修改自己的信息");
        
        // 获取用户实体
        SysUserEntity userEntity = this.getById(form.getId());
        Assert.notNull(userEntity, ResultCode.USER_NOT_EXIST.getMsg());
        
        // 记录操作日志
        String avatarOld = userEntity.getAvatar();
        String nicknameOld = userEntity.getNickname();

        // 头像文件状态处理
        if (Objects.nonNull(userDetails.getAvatarId()) && !userDetails.getAvatarId().equals(form.getAvatarId())) {
            // 如果头像有更新，原文件设置为未使用
            CommonUtils.updateFileStatus(userDetails.getAvatarId(), 0);
        }
        // 将新头像设为启用
        if (Objects.nonNull(form.getAvatarId())) {
            CommonUtils.updateFileStatus(form.getAvatarId(), 1);
        }

        // 更新用户信息
        updateChain().from(SysUserEntity.class)
                .set(SysUserEntity::getAvatarId, form.getAvatarId())
                .set(SysUserEntity::getAvatar, form.getAvatar())
                .set(SysUserEntity::getNickname, form.getNickname())
                .where(SysUserEntity::getId).eq(form.getId())
                .update();
        
        // 更新用户详情对象
        userDetails.setAvatar(form.getAvatar());
        userDetails.setAvatarId(form.getAvatarId());
        userDetails.setNickname(form.getNickname());

        OpLogUtils.appendOpLog("小程序-修改个人信息", String.format("""
                头像: %s 修改为 %s,
                昵称: %s 修改为 %s""", Optional.ofNullable(avatarOld).orElse(""),
                Optional.ofNullable(form.getAvatar()).orElse(""), nicknameOld, form.getNickname()), null);
        
        // 刷新所有端的Redis缓存
        for (SideEnum value : SideEnum.values()) {
            userDetails.setSideCode(value.getValue());
            tokenService.refreshToken(userDetails.getUserId().toString(), userDetails);
        }
        return true;
    }

}
