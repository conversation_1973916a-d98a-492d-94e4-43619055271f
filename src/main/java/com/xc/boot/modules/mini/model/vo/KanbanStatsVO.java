package com.xc.boot.modules.mini.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 看板统计结果VO
 */
@Data
@Schema(description = "看板统计结果VO")
public class KanbanStatsVO {

    @Schema(description = "销售统计")
    private SalesStats salesStats;

    @Schema(description = "回收统计")
    private RecycleStats recycleStats;

    @Schema(description = "退货统计")
    private ReturnStats returnStats;

    @Schema(description = "库存统计")
    private InventoryStats inventoryStats;

    @Schema(description = "旧料统计")
    private OldMaterialStats oldMaterialStats;

    @Schema(description = "销售占比统计")
    private List<SalesRatioStats> salesRatioStats;

    /**
     * 销售统计
     */
    @Data
    @Schema(description = "销售统计")
    public static class SalesStats {
        @Schema(description = "销售金额（元）")
        private BigDecimal salesAmount;

        @Schema(description = "销售金额涨幅（%）")
        private BigDecimal salesAmountGrowth;

        @Schema(description = "优惠金额（元）")
        private BigDecimal discountAmount;

        @Schema(description = "优惠金额涨幅（%）")
        private BigDecimal discountAmountGrowth;

        @Schema(description = "旧料抵扣（元）")
        private BigDecimal deductionAmount;

        @Schema(description = "旧料抵扣涨幅（%）")
        private BigDecimal deductionAmountGrowth;

        @Schema(description = "实收金额（元）")
        private BigDecimal actualAmount;

        @Schema(description = "实收金额涨幅（%）")
        private BigDecimal actualAmountGrowth;

        @Schema(description = "毛利（元）")
        private BigDecimal grossProfit;

        @Schema(description = "毛利涨幅（%）")
        private BigDecimal grossProfitGrowth;
    }

    /**
     * 回收统计
     */
    @Data
    @Schema(description = "回收统计")
    public static class RecycleStats {
        @Schema(description = "总数")
        private Integer totalCount;

        @Schema(description = "总数涨幅（%）")
        private BigDecimal totalCountGrowth;

        @Schema(description = "总金重（g）")
        private BigDecimal totalGoldWeight;

        @Schema(description = "总金重涨幅（%）")
        private BigDecimal totalGoldWeightGrowth;

        @Schema(description = "总银重（g）")
        private BigDecimal totalSilverWeight;

        @Schema(description = "总银重涨幅（%）")
        private BigDecimal totalSilverWeightGrowth;

        @Schema(description = "回收金额（元）")
        private BigDecimal recycleAmount;

        @Schema(description = "回收金额涨幅（%）")
        private BigDecimal recycleAmountGrowth;
    }

    /**
     * 退货统计
     */
    @Data
    @Schema(description = "退货统计")
    public static class ReturnStats {
        @Schema(description = "总数")
        private Integer totalCount;

        @Schema(description = "总数涨幅（%）")
        private BigDecimal totalCountGrowth;

        @Schema(description = "总金重（g）")
        private BigDecimal totalGoldWeight;

        @Schema(description = "总金重涨幅（%）")
        private BigDecimal totalGoldWeightGrowth;

        @Schema(description = "总银重（g）")
        private BigDecimal totalSilverWeight;

        @Schema(description = "总银重涨幅（%）")
        private BigDecimal totalSilverWeightGrowth;

        @Schema(description = "实售金额（元）")
        private BigDecimal actualSalesAmount;

        @Schema(description = "实售金额涨幅（%）")
        private BigDecimal actualSalesAmountGrowth;

        @Schema(description = "折旧金额（元）")
        private BigDecimal depreciationAmount;

        @Schema(description = "折旧金额涨幅（%）")
        private BigDecimal depreciationAmountGrowth;

        @Schema(description = "实退金额（元）")
        private BigDecimal actualRefundAmount;

        @Schema(description = "实退金额涨幅（%）")
        private BigDecimal actualRefundAmountGrowth;
    }

    /**
     * 库存统计
     */
    @Data
    @Schema(description = "库存统计")
    public static class InventoryStats {
        @Schema(description = "总数")
        private Integer totalCount;

        @Schema(description = "金重（g）")
        private BigDecimal goldWeight;

        @Schema(description = "银重（g）")
        private BigDecimal silverWeight;

        @Schema(description = "价值（元）")
        private BigDecimal value;

        @Schema(description = "成本总价（元）")
        private String totalCostPrice;
    }

    /**
     * 旧料统计
     */
    @Data
    @Schema(description = "旧料统计")
    public static class OldMaterialStats {
        @Schema(description = "总数")
        private Integer totalCount;

        @Schema(description = "金重（g）")
        private BigDecimal goldWeight;

        @Schema(description = "银重（g）")
        private BigDecimal silverWeight;

        @Schema(description = "价值（元）")
        private BigDecimal value;

        @Schema(description = "回收金额（元）")
        private BigDecimal totalRecycleAmount;
    }

    /**
     * 销售占比统计
     */
    @Data
    @Schema(description = "销售占比统计")
    public static class SalesRatioStats {
        @Schema(description = "大类ID")
        private Long categoryId;

        @Schema(description = "大类名称")
        private String categoryName;

        @Schema(description = "销售金额（元）")
        private BigDecimal salesAmount;

        @Schema(description = "占比（%）")
        private BigDecimal ratio;
    }
}
