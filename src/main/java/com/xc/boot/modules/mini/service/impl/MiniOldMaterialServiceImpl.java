package com.xc.boot.modules.mini.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.common.enums.baseColum.OtherColumEnum;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtilV2;
import com.xc.boot.modules.merchant.mapper.ActualGoldPriceMapper;
import com.xc.boot.modules.merchant.model.entity.ActualGoldPriceEntity;
import com.xc.boot.modules.mini.model.query.MiniOldMaterialPageQuery;
import com.xc.boot.modules.mini.model.vo.MiniOldMaterialStatisticVo;
import com.xc.boot.modules.mini.model.vo.MiniOldMaterialVo;
import com.xc.boot.modules.mini.service.MiniOldMaterialService;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialMapper;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

import static com.mybatisflex.core.query.QueryMethods.*;
import static com.xc.boot.modules.merchant.model.entity.table.ActualGoldPriceTableDef.ACTUAL_GOLD_PRICE;
import static com.xc.boot.modules.oldmaterial.model.entity.table.OldMaterialTableDef.OLD_MATERIAL;

/**
 * 小程序旧料服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MiniOldMaterialServiceImpl extends ServiceImpl<OldMaterialMapper, OldMaterialEntity> implements MiniOldMaterialService {
    
    private final ListFillService listFillService;
    private final ActualGoldPriceMapper actualGoldPriceMapper;
    
    @Override
    public Page<MiniOldMaterialVo> miniOldMaterialPage(MiniOldMaterialPageQuery query) {
        QueryWrapper wrapper = buildWrapper(query);
        wrapper.orderBy(OLD_MATERIAL.ID, false);
        
        int pageNum = query.getPageNum();
        int pageSize = query.getPageSize();
        
        Page<MiniOldMaterialVo> page = this.mapper.paginateAs(pageNum, pageSize, wrapper, MiniOldMaterialVo.class);
        List<MiniOldMaterialVo> records = page.getRecords();
        this.fillList(records);
        return page;
    }
    
    @Override
    public MiniOldMaterialStatisticVo miniOldMaterialStatistic(MiniOldMaterialPageQuery query) {
        QueryWrapper wrapper = buildWrapper(query);

        // 获取实时金价
        ActualGoldPriceEntity priceEntity = actualGoldPriceMapper.selectOneByQuery(QueryWrapper.create()
                .where(ACTUAL_GOLD_PRICE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .orderBy(ACTUAL_GOLD_PRICE.ID, false)
                .limit(1));

        // 获取原始金价银价（分为单位），用于SQL计算
        BigDecimal goldPriceFen = priceEntity != null ? priceEntity.getGoldPrice() : null;
        BigDecimal silverPriceFen = priceEntity != null ? priceEntity.getSilverPrice() : null;

        // 一次性选择所有需要的字段
        wrapper.select(
            sum(OLD_MATERIAL.NUM).as("totalStock"),
            sum(OLD_MATERIAL.NET_GOLD_WEIGHT.multiply(OLD_MATERIAL.NUM)).as("totalGoldWeight"),
            sum(OLD_MATERIAL.NET_SILVER_WEIGHT.multiply(OLD_MATERIAL.NUM)).as("totalSilverWeight"),
            sum(OLD_MATERIAL.WEIGHT.multiply(OLD_MATERIAL.NUM)).as("totalWeight"),
            sum(OLD_MATERIAL.RECYCLE_PRICE.multiply(OLD_MATERIAL.NUM)).as("totalAmount"),
            // 计算总价值，参考看板中的旧料价值统计逻辑
            sum(case_()
                // 银饰
                .when(OLD_MATERIAL.CATEGORY_ID.eq(CategoryEnum.SILVER.getValue()))
                .then(silverPriceFen != null ?
                    OLD_MATERIAL.NET_SILVER_WEIGHT.multiply(QueryMethods.column(silverPriceFen.toString())).multiply(OLD_MATERIAL.NUM)
                    : OLD_MATERIAL.RECYCLE_PRICE.multiply(OLD_MATERIAL.NUM))
                // 黄金
                .when(OLD_MATERIAL.CATEGORY_ID.eq(CategoryEnum.GOLD.getValue()))
                .then(goldPriceFen != null ?
                    OLD_MATERIAL.NET_GOLD_WEIGHT.multiply(QueryMethods.column(goldPriceFen.toString())).multiply(OLD_MATERIAL.NUM)
                    : OLD_MATERIAL.RECYCLE_PRICE.multiply(OLD_MATERIAL.NUM))
                // 金包银
                .when(OLD_MATERIAL.CATEGORY_ID.eq(CategoryEnum.GOLD_SILVER.getValue()))
                .then(goldPriceFen != null && silverPriceFen != null ?
                    OLD_MATERIAL.NET_GOLD_WEIGHT.multiply(QueryMethods.column(goldPriceFen.toString())).multiply(OLD_MATERIAL.NUM)
                        .add(OLD_MATERIAL.NET_SILVER_WEIGHT.multiply(QueryMethods.column(silverPriceFen.toString())).multiply(OLD_MATERIAL.NUM))
                    : OLD_MATERIAL.RECYCLE_PRICE.multiply(OLD_MATERIAL.NUM))
                // 其他类别直接使用回收价
                .else_(OLD_MATERIAL.RECYCLE_PRICE.multiply(OLD_MATERIAL.NUM))
                .end()
            ).as("totalValue")
        );
        
        // 直接映射到VO类，避免类型转换问题
        MiniOldMaterialStatisticVo vo = mapper.selectOneByQueryAs(wrapper, MiniOldMaterialStatisticVo.class);
        if (vo == null) {
            return new MiniOldMaterialStatisticVo();
        }

        // 格式化数据
        vo.setTotalGoldWeight(PriceUtil.formatThreeDecimal(vo.getTotalGoldWeight()));
        vo.setTotalSilverWeight(PriceUtil.formatThreeDecimal(vo.getTotalSilverWeight()));
        vo.setTotalWeight(PriceUtil.formatThreeDecimal(vo.getTotalWeight()));

        // 处理金额格式化 - 确保金额字段不为null，并进行分转元处理
        if (vo.getTotalAmount() != null && !vo.getTotalAmount().equals("0.00")) {
            try {
                // 先进行分转元，再格式化
                BigDecimal amountInYuan = PriceUtil.fen2yuan(new BigDecimal(vo.getTotalAmount()));
                vo.setTotalAmount(PriceUtil.formatTwoDecimal(amountInYuan).toPlainString());
            } catch (NumberFormatException e) {
                vo.setTotalAmount("0.00");
            }
        }
        if (vo.getTotalValue() != null && !vo.getTotalValue().equals("0.00")) {
            try {
                // 先进行分转元，再格式化
                BigDecimal valueInYuan = PriceUtil.fen2yuan(new BigDecimal(vo.getTotalValue()));
                vo.setTotalValue(PriceUtil.formatTwoDecimal(valueInYuan).toPlainString());
            } catch (NumberFormatException e) {
                vo.setTotalValue("0.00");
            }
        }

        return vo;
    }
    
    /**
     * 构建查询条件
     */
    private QueryWrapper buildWrapper(MiniOldMaterialPageQuery query) {
        // 固定条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(OLD_MATERIAL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(OLD_MATERIAL.MERCHANT_ID.in(SecurityUtils.getMerchantIds(), !SecurityUtils.isMain()))
                .where(OLD_MATERIAL.NUM.gt(0)); // 只查询有库存的旧料
        
        // 旧料编号
        if (StringUtils.isNotBlank(query.getOldMaterialSn())) {
            queryWrapper.and(OLD_MATERIAL.OLD_MATERIAL_SN.like(query.getOldMaterialSn()));
        }

        // 旧料名称
        if (StringUtils.isNotBlank(query.getName())) {
            queryWrapper.and(OLD_MATERIAL.NAME.like(query.getName()));
        }

        // 所属大类
        if (StringUtils.isNotBlank(query.getCategoryIds())) {
            queryWrapper.and(OLD_MATERIAL.CATEGORY_ID.in(List.of(query.getCategoryIds().split(","))));
        }

        // 所属小类
        if (StringUtils.isNotBlank(query.getSubclassIds())) {
            queryWrapper.and(OLD_MATERIAL.SUBCLASS_ID.in(List.of(query.getSubclassIds().split(","))));
        }

        // 成色
        if (StringUtils.isNotBlank(query.getQualityIds())) {
            queryWrapper.and(OLD_MATERIAL.QUALITY_ID.in(List.of(query.getQualityIds().split(","))));
        }

        // 商户筛选（前端传入的merchantIds会与权限范围取交集）
        if (StringUtils.isNotBlank(query.getMerchantIds())) {
            queryWrapper.and(OLD_MATERIAL.MERCHANT_ID.in(List.of(query.getMerchantIds().split(","))));
        }

        return queryWrapper;
    }
    
    /**
     * 填充关联数据
     */
    private void fillList(List<MiniOldMaterialVo> records) {
        ListFillUtilV2.of(records)
                .build(listFillService::getMerchantNameById, MiniOldMaterialVo::getMerchantId, MiniOldMaterialVo::setMerchantName)
                .build(listFillService::getCategoryNameById, MiniOldMaterialVo::getCategoryId, MiniOldMaterialVo::setCategoryName)
                .build(listFillService::getSubclassNameById, MiniOldMaterialVo::getSubclassId, MiniOldMaterialVo::setSubclassName)
                .build(listFillService::getQualityNameById, MiniOldMaterialVo::getQualityId, MiniOldMaterialVo::setQualityName)
                .build(listFillService::getOldMaterialImgByOldMaterialId, MiniOldMaterialVo::getId, MiniOldMaterialVo::setImage)
                .peek(vo -> {
                    // 设置计价方式名称
                    vo.setSalesTypeName(OtherColumEnum.getSalesTypeText(vo.getSalesType() != null ? vo.getSalesType().toString() : ""));
                    // 计算回收金额
                    if (vo.getRawRecyclePrice() != null && vo.getNum() != null) {
                        // 直接使用Long类型计算，保持分为单位
                        vo.setRecycleAmount(vo.getRawRecyclePrice() * vo.getNum());
                    }
                })
                .handle();
    }
}
