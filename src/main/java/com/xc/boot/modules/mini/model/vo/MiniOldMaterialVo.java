package com.xc.boot.modules.mini.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mybatisflex.annotation.Column;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 小程序旧料分页列表VO
 */
@Data
@Schema(description = "小程序旧料分页列表VO")
public class MiniOldMaterialVo {
    
    @Schema(description = "主键ID")
    private Long id;
    
    @Schema(description = "创建时间")
    private Date createdAt;
    
    @Schema(description = "更新时间")
    private Date updatedAt;
    
    @JsonIgnore
    @Schema(description = "所属商户ID")
    private Long companyId;
    
    @JsonIgnore
    @Schema(description = "所属门店ID")
    private Long merchantId;
    
    @Schema(description = "所属门店名称")
    private String merchantName;
    

    
    @JsonIgnore
    @Schema(description = "所属大类ID")
    private Long categoryId;
    
    @Schema(description = "大类名称")
    private String categoryName;
    
    @JsonIgnore
    @Schema(description = "所属小类ID")
    private Long subclassId;
    
    @Schema(description = "小类名称")
    private String subclassName;
    
    @JsonIgnore
    @Schema(description = "成色ID")
    private Long qualityId;
    
    @Schema(description = "成色名称")
    private String qualityName;
    
    @Schema(description = "旧料编号")
    private String oldMaterialSn;
    
    @Schema(description = "旧料名称")
    private String name;
    
    @Schema(description = "旧料图片列表")
    private List<OldMaterialHasImagesEntity> image = new ArrayList<>();
    
    @Schema(description = "重量(g)")
    private BigDecimal weight;
    
    @Schema(description = "净金重(g)")
    private BigDecimal netGoldWeight;
    
    @Schema(description = "净银重(g)")
    private BigDecimal netSilverWeight;
    
    @Schema(description = "数量")
    private Integer num;
    
    @Schema(description = "计价方式(1:按重量,2:按数量)")
    private Integer salesType;
    
    @Schema(description = "计价方式名称")
    private String salesTypeName;
    
    // 数据库映射字段（分为单位）- 使用不同的字段名避免与getter方法冲突
    @Column("gold_price")
    private Long goldPriceFen;

    @Column("silver_price")
    private Long silverPriceFen;

    @Column("recycle_price")
    private Long recyclePriceFen;

    private Long recycleAmountFen;

    // Getter方法进行格式化
    public BigDecimal getWeight() {
        return PriceUtil.formatThreeDecimal(weight);
    }

    public BigDecimal getNetGoldWeight() {
        return PriceUtil.formatThreeDecimal(netGoldWeight);
    }

    public BigDecimal getNetSilverWeight() {
        return PriceUtil.formatThreeDecimal(netSilverWeight);
    }

    @Schema(description = "回收金价(元)")
    public BigDecimal getGoldPrice() {
        return PriceUtil.formatTwoDecimal(PriceUtil.fen2yuan(goldPriceFen));
    }

    @Schema(description = "回收银价(元)")
    public BigDecimal getSilverPrice() {
        return PriceUtil.formatTwoDecimal(PriceUtil.fen2yuan(silverPriceFen));
    }

    @Schema(description = "回收单价(元)")
    public BigDecimal getRecyclePrice() {
        return PriceUtil.formatTwoDecimal(PriceUtil.fen2yuan(recyclePriceFen));
    }

    @Schema(description = "回收金额(元)")
    public BigDecimal getRecycleAmount() {
        return PriceUtil.formatTwoDecimal(PriceUtil.fen2yuan(recycleAmountFen));
    }

    // 获取原始价格值的方法，用于内部计算
    public Long getRawRecyclePrice() {
        return recyclePriceFen;
    }

    // 设置回收金额的方法，用于内部计算
    public void setRecycleAmount(Long recycleAmount) {
        this.recycleAmountFen = recycleAmount;
    }
}
