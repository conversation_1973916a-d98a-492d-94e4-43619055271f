package com.xc.boot.modules.mini.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金价详情分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "金价详情分页查询对象")
public class GoldPriceDetailQuery extends BasePageQuery {
    
    @Schema(description = "大类ID")
    private Long categoryId;
    
    @Schema(description = "成色ID")
    private Long qualityId;
} 