package com.xc.boot.modules.mini.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 小程序会员列表分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "小程序会员列表分页查询对象")
public class MiniMemberPageQuery extends BasePageQuery {
    
    @Schema(description = "入会门店(英文逗号分割)")
    private String merchantIds;

    @Schema(description = "会员姓名")
    private String name;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "性别(0:未知｜1:男｜2:女)")
    private Integer gender;

    @Schema(description = "用户来源(0:自然访问|1:朋友推荐|2:社交媒体|3:线下活动)")
    private Integer source;

    @Schema(description = "创建时间范围")
    private Date[] timeRange;
} 