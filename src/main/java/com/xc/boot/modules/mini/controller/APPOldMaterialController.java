package com.xc.boot.modules.mini.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.mini.model.query.MiniOldMaterialPageQuery;
import com.xc.boot.modules.mini.model.vo.MiniOldMaterialStatisticVo;
import com.xc.boot.modules.mini.model.vo.MiniOldMaterialVo;
import com.xc.boot.modules.mini.service.MiniOldMaterialService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "小程序-旧料接口")
@RequestMapping("/api/mini/old_material")
public class APPOldMaterialController {
    
    private final MiniOldMaterialService miniOldMaterialService;

    @Operation(summary = "小程序旧料分页列表")
    @PostMapping("/page")
    public PageResult<MiniOldMaterialVo> page(@Validated @RequestBody MiniOldMaterialPageQuery query) {
        Page<MiniOldMaterialVo> page = miniOldMaterialService.miniOldMaterialPage(query);
        return PageResult.success(page);
    }

    @Operation(summary = "小程序旧料分页列表统计")
    @PostMapping("/stats")
    public Result<MiniOldMaterialStatisticVo> statistic(@Validated @RequestBody MiniOldMaterialPageQuery query) {
        MiniOldMaterialStatisticVo statistic = miniOldMaterialService.miniOldMaterialStatistic(query);
        return Result.success(statistic);
    }
}
