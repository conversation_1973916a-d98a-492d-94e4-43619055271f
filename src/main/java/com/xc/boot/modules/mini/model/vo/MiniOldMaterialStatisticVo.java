package com.xc.boot.modules.mini.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 小程序旧料统计VO
 */
@Data
@Schema(description = "小程序旧料统计VO")
public class MiniOldMaterialStatisticVo {
    
    @Schema(description = "总库存数")
    private Long totalStock = 0L;
    
    @Schema(description = "总金重(g)")
    private BigDecimal totalGoldWeight = BigDecimal.ZERO;
    
    @Schema(description = "总银重(g)")
    private BigDecimal totalSilverWeight = BigDecimal.ZERO;
    
    @Schema(description = "总重量(g)")
    private BigDecimal totalWeight = BigDecimal.ZERO;
    
    @Schema(description = "总金额(元)")
    private String totalAmount = "0.00";
    
    @Schema(description = "总价值(元)")
    private String totalValue = "0.00";
}
