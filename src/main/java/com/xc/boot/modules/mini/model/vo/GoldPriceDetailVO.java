package com.xc.boot.modules.mini.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 金价详情视图对象
 */
@Data
@Schema(description = "金价详情视图对象")
public class GoldPriceDetailVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "大类ID")
    private Long categoryId;

    @Schema(description = "大类名称")
    private String category;

    @Schema(description = "成色ID")
    private Long qualityId;

    @Schema(description = "成色名称")
    private String quality;

    @Schema(description = "销售价(元/克)")
    private BigDecimal salePrice;

    @Schema(description = "回收价(元/克)")
    private BigDecimal recyclePrice;

    @Schema(description = "生效时间")
    private Date activeTime;

    @Schema(description = "更新时间")
    private Date updatedAt;
} 