package com.xc.boot.modules.mini.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 小程序旧料列表分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "小程序旧料列表分页查询对象")
public class MiniOldMaterialPageQuery extends BasePageQuery {
    
    @Schema(description = "旧料编号")
    private String oldMaterialSn;

    @Schema(description = "旧料名称")
    private String name;

    @Schema(description = "所属大类ID(英文逗号分割)")
    private String categoryIds;

    @Schema(description = "所属小类ID(英文逗号分割)")
    private String subclassIds;

    @Schema(description = "成色ID(英文逗号分割)")
    private String qualityIds;

    @Schema(description = "商户ID(英文逗号分割)")
    private String merchantIds;
}
