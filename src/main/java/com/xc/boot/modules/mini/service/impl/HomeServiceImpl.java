package com.xc.boot.modules.mini.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtilV2;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.mini.model.query.GoldPriceDetailQuery;
import com.xc.boot.modules.mini.model.vo.GoldPriceDetailVO;
import com.xc.boot.modules.mini.model.vo.GoldPricePreviewVO;
import com.xc.boot.modules.mini.service.HomeService;
import com.xc.boot.modules.merchant.mapper.GoldPriceMapper;
import com.xc.boot.modules.merchant.model.entity.GoldPriceEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.xc.boot.modules.merchant.model.entity.table.GoldPriceTableDef.GOLD_PRICE;

/**
 * 首页服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HomeServiceImpl implements HomeService {

    private final GoldPriceMapper goldPriceMapper;
    private final ListFillService fillService;

    @Override
    public List<GoldPricePreviewVO> getGoldPricePreview() {
        // 查询生效中的金价，按大类分组，每个大类只取一个
        List<GoldPriceEntity> goldPrices = goldPriceMapper.selectListByQuery(
                QueryWrapper.create()
                        .where(GOLD_PRICE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .where(GOLD_PRICE.STATUS.eq(1))
                        .orderBy(GOLD_PRICE.CATEGORY_ID, true)
                        .orderBy(GOLD_PRICE.UPDATED_AT, false)
        );

        // 按大类分组，每个大类只取第一个（最新的）
        List<GoldPriceEntity> distinctByCategory = goldPrices.stream()
                .collect(Collectors.toMap(
                        GoldPriceEntity::getCategoryId,
                        price -> price,
                        (existing, replacement) -> existing
                ))
                .values()
                .stream()
                .limit(4) // 最多展示4个
                .toList();

        // 转换为VO
        List<GoldPricePreviewVO> previewVOs = distinctByCategory.stream()
                .map(entity -> {
                    GoldPricePreviewVO vo = new GoldPricePreviewVO();
                    vo.setCategoryId(entity.getCategoryId());
                    vo.setSalePrice(PriceUtil.fen2yuan(entity.getSalePrice()));
                    vo.setRecyclePrice(PriceUtil.fen2yuan(entity.getRecyclePrice()));
                    return vo;
                })
                .collect(Collectors.toList());

        // 使用ListFillUtilV2填充大类名称
        ListFillUtilV2.of(previewVOs)
                .build(fillService::getCategoryNameById, GoldPricePreviewVO::getCategoryId, GoldPricePreviewVO::setCategory)
                .handle();

        return previewVOs;
    }

    @Override
    public Page<GoldPriceDetailVO> getGoldPriceDetailPage(GoldPriceDetailQuery query) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(GOLD_PRICE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOLD_PRICE.STATUS.eq(1))
                .where(GOLD_PRICE.CATEGORY_ID.eq(query.getCategoryId(), query.getCategoryId() != null))
                .where(GOLD_PRICE.QUALITY_ID.eq(query.getQualityId(), query.getQualityId() != null))
                .orderBy(GOLD_PRICE.UPDATED_AT, false);

        Page<GoldPriceDetailVO> page = goldPriceMapper.paginateAs(
                new Page<>(query.getPageNum(), query.getPageSize()),
                queryWrapper,
                GoldPriceDetailVO.class
        );

        // 使用ListFillUtilV2填充数据
        ListFillUtilV2.of(page.getRecords())
                .build(fillService::getCategoryNameById, GoldPriceDetailVO::getCategoryId, GoldPriceDetailVO::setCategory)
                .build(fillService::getQualityNameById, GoldPriceDetailVO::getQualityId, GoldPriceDetailVO::setQuality)
                .peek(vo -> {
                    vo.setSalePrice(PriceUtil.fen2yuan(vo.getSalePrice()));
                    vo.setRecyclePrice(PriceUtil.fen2yuan(vo.getRecyclePrice()));
                })
                .handle();
        return page;
    }
} 