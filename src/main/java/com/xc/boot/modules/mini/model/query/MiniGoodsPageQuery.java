package com.xc.boot.modules.mini.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 小程序货品列表分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "小程序货品列表分页查询对象")
public class MiniGoodsPageQuery extends BasePageQuery {
    @Schema(description = "所属门店(英文逗号分割)")
    private String merchantIds;

    @Schema(description = "货品条码")
    private String goodsSn;

    @Schema(description = "货品名称")
    private String name;

    @Schema(description = "供应商ID(英文逗号分割)")
    private String supplierIds;

    @Schema(description = "所属大类ID(英文逗号分割)")
    private String categoryIds;

    @Schema(description = "所属小类ID(英文逗号分割)")
    private String subclassIds;

    @Schema(description = "柜台ID(英文逗号分割)")
    private String counterIds;

    @Schema(description = "成色ID(英文逗号分割)")
    private String qualityIds;

    @Schema(description = "所属品牌ID(英文逗号分割)")
    private String brandIds;

    @Schema(description = "圈口(英文逗号分割)")
    private String circleSize;

    @Schema(description = "所属款式ID(英文逗号分割)")
    private String styleIds;

    @Schema(description = "主石ID(英文逗号分割)")
    private String mainStoneIds;

    @Schema(description = "副石ID(英文逗号分割)")
    private String subStoneIds;

    @Schema(description = "库存状态(1:在库|2:已售)(英文逗号分割)")
    private String status;
} 