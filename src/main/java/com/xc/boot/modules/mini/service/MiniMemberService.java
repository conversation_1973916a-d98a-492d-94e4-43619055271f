package com.xc.boot.modules.mini.service;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.modules.mini.model.form.MiniMemberForm;
import com.xc.boot.modules.mini.model.query.MiniMemberPageQuery;
import com.xc.boot.modules.mini.model.vo.MiniMemberPageVo;

/**
 * 小程序会员Service接口
 */
public interface MiniMemberService {
    
    /**
     * 小程序会员分页列表
     */
    Page<MiniMemberPageVo> page(MiniMemberPageQuery query);
    
    /**
     * 小程序新增会员
     */
    Long add(MiniMemberForm form);
    
    /**
     * 小程序修改会员
     */
    void update(MiniMemberForm form);
} 