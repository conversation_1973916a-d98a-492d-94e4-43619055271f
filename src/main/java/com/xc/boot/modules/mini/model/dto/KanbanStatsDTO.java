package com.xc.boot.modules.mini.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;


/**
 * 看板统计查询DTO
 */
@Data
@Schema(description = "看板统计查询DTO")
public class KanbanStatsDTO {

    @Schema(description = "开始日期", required = true, example = "2025-07-01")
    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;

    @Schema(description = "结束日期", required = true, example = "2025-07-01")
    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;

    @Schema(description = "门店ID", example = "1")
    private Long merchantId;
}
