package com.xc.boot.modules.mini.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.query.QueryMethods;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.common.enums.baseColum.PriceColumEnum;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.merchant.mapper.ActualGoldPriceMapper;

import com.xc.boot.modules.merchant.model.entity.ActualGoldPriceEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.mini.model.dto.KanbanStatsDTO;
import com.xc.boot.modules.mini.model.vo.KanbanStatsVO;
import com.xc.boot.modules.mini.service.KanbanService;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialMapper;
import com.xc.boot.modules.order.mapper.MaterialRecycleMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptGoodsMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptMapper;
import com.xc.boot.modules.order.mapper.SoldReturnMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
import static com.xc.boot.modules.merchant.model.entity.table.ActualGoldPriceTableDef.ACTUAL_GOLD_PRICE;
import static com.xc.boot.modules.oldmaterial.model.entity.table.OldMaterialTableDef.OLD_MATERIAL;
import static com.xc.boot.modules.order.model.entity.table.MaterialRecycleTableDef.MATERIAL_RECYCLE;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptTableDef.SOLD_RECEIPT;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptGoodsTableDef.SOLD_RECEIPT_GOODS;
import static com.xc.boot.modules.order.model.entity.table.SoldReturnTableDef.SOLD_RETURN;

/**
 * 看板统计服务实现类
 *
 * 功能说明：
 * 1. 销售统计：基于已完成销售单的金额、优惠、抵扣等数据统计
 * 2. 回收统计：基于回收单的数量、重量、金额统计
 * 3. 退货统计：基于退货单的数量、重量、金额统计
 * 4. 库存统计：基于当前库存的数量、重量、价值统计
 * 5. 旧料统计：基于旧料的数量、重量、价值统计
 * 6. 销售占比：按大类统计销售金额占比
 *
 * 性能优化：
 * - 使用数据库聚合查询避免大量数据传输
 * - 批量查询回收价格避免N+1问题
 * - 统一的类型转换处理避免ClassCastException
 *
 * 安全措施：
 * - 所有查询都基于当前用户的公司ID和商户权限
 * - 使用参数化查询防止SQL注入
 * - 对异常情况进行安全处理，返回默认值
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KanbanServiceImpl implements KanbanService {

    private final SoldReceiptMapper soldReceiptMapper;
    private final SoldReceiptGoodsMapper soldReceiptGoodsMapper;
    private final MaterialRecycleMapper materialRecycleMapper;
    private final SoldReturnMapper soldReturnMapper;
    private final GoodsMapper goodsMapper;
    private final OldMaterialMapper oldMaterialMapper;
    private final ActualGoldPriceMapper actualGoldPriceMapper;

    @Override
    public KanbanStatsVO getKanbanStats(KanbanStatsDTO dto) {
        // 参数校验
        if (dto == null || dto.getStartDate() == null || dto.getEndDate() == null) {
            throw new IllegalArgumentException("查询参数不能为空");
        }

        // 日期范围校验 - 防止查询过大范围数据影响性能
        if (dto.getStartDate().isAfter(dto.getEndDate())) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        long daysBetween = dto.getEndDate().toEpochDay() - dto.getStartDate().toEpochDay();
        if (daysBetween > 365) {
            throw new BusinessException("查询日期范围不能超过365天");
        }

        KanbanStatsVO result = new KanbanStatsVO();

        try {
            // 一次性转换当期和上期的日期范围为 DateTime
            LocalDateTime[] currentPeriod = convertToDateTimeRange(dto.getStartDate(), dto.getEndDate());
            LocalDate[] previousPeriodDates = calculatePreviousPeriod(dto.getStartDate(), dto.getEndDate());
            LocalDateTime[] previousPeriod = convertToDateTimeRange(previousPeriodDates[0], previousPeriodDates[1]);

            // 获取销售统计
            result.setSalesStats(getSalesStats(dto.getMerchantId(), currentPeriod, previousPeriod));

            // 获取回收统计
            result.setRecycleStats(getRecycleStats(dto.getMerchantId(), currentPeriod, previousPeriod));

            // 获取退货统计
            result.setReturnStats(getReturnStats(dto.getMerchantId(), currentPeriod, previousPeriod));

            // 获取库存统计
            result.setInventoryStats(getInventoryStats(dto));

            // 获取旧料统计
            result.setOldMaterialStats(getOldMaterialStats(dto));

            // 获取销售占比统计
            result.setSalesRatioStats(getSalesRatioStats(dto));

        } catch (Exception e) {
            log.error("获取看板统计数据失败", e);
            // 返回空的统计结果而不是抛出异常，保证前端页面正常显示
            result = new KanbanStatsVO();
        }

        return result;
    }

    /**
     * 计算上个周期的日期范围
     * 公式：起始日期 = 原起始日期 - （原结束日期 - 原起始日期 + 1），结束日期 = 原结束日期 - （原结束日期 - 原起始日期 + 1）
     */
    private LocalDate[] calculatePreviousPeriod(LocalDate startDate, LocalDate endDate) {
        long periodDays = endDate.toEpochDay() - startDate.toEpochDay() + 1;
        LocalDate previousStartDate = startDate.minusDays(periodDays);
        LocalDate previousEndDate = endDate.minusDays(periodDays);
        return new LocalDate[] { previousStartDate, previousEndDate };
    }

    /**
     * 将日期范围转换为 DateTime 范围
     * startDate -> startDate 00:00:00
     * endDate -> endDate 23:59:59
     */
    private LocalDateTime[] convertToDateTimeRange(LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = startDate.atStartOfDay(); // 00:00:00
        LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX); // 23:59:59.999999999
        return new LocalDateTime[] { startDateTime, endDateTime };
    }

    /**
     * 计算涨幅百分比
     * 公式：(当期值 - 上期值) / 上期值 * 100
     */
    private BigDecimal calculateGrowthRate(BigDecimal currentValue, BigDecimal previousValue) {
        if (previousValue == null || previousValue.compareTo(BigDecimal.ZERO) == 0) {
            return currentValue != null && currentValue.compareTo(BigDecimal.ZERO) > 0 ? new BigDecimal("100")
                    : BigDecimal.ZERO;
        }
        if (currentValue == null) {
            return new BigDecimal("-100");
        }
        return currentValue.subtract(previousValue)
                .divide(previousValue, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    /**
     * 获取销售统计
     * 销售金额: 取 sold_receipt 中状态为已完成的, 货品金额
     * 优惠金额: 取 sold_receipt 中状态为已完成的, 优惠金额
     * 旧料抵扣: 取 sold_receipt 中状态为已完成的, 抵扣金额
     * 实收金额: 取 sold_receipt 中状态为已完成的, 订单金额
     * 毛利: 取 sold_receipt 中状态为已完成的, 货品金额 - 货品成本价(销售单货品明细表的成本价 * 数量)
     */
    private KanbanStatsVO.SalesStats getSalesStats(Long merchantId, LocalDateTime[] currentPeriod,
            LocalDateTime[] previousPeriod) {
        KanbanStatsVO.SalesStats stats = new KanbanStatsVO.SalesStats();

        // 当期数据
        Map<String, Object> currentData = getSalesStatsData(currentPeriod[0], currentPeriod[1], merchantId);

        // 上期数据
        Map<String, Object> previousData = getSalesStatsData(previousPeriod[0], previousPeriod[1], merchantId);

        // 设置当期数据
        stats.setSalesAmount(convertToYuan(currentData, "salesAmount"));
        stats.setDiscountAmount(convertToYuan(currentData, "discountAmount"));
        stats.setDeductionAmount(convertToYuan(currentData, "deductionAmount"));
        stats.setActualAmount(convertToYuan(currentData, "actualAmount"));
        stats.setGrossProfit(convertToYuan(currentData, "grossProfit"));

        // 计算涨幅
        stats.setSalesAmountGrowth(
                calculateGrowthRate(stats.getSalesAmount(), convertToYuan(previousData, "salesAmount")));
        stats.setDiscountAmountGrowth(
                calculateGrowthRate(stats.getDiscountAmount(), convertToYuan(previousData, "discountAmount")));
        stats.setDeductionAmountGrowth(
                calculateGrowthRate(stats.getDeductionAmount(), convertToYuan(previousData, "deductionAmount")));
        stats.setActualAmountGrowth(
                calculateGrowthRate(stats.getActualAmount(), convertToYuan(previousData, "actualAmount")));
        stats.setGrossProfitGrowth(
                calculateGrowthRate(stats.getGrossProfit(), convertToYuan(previousData, "grossProfit")));

        return stats;
    }

    /**
     * 获取销售统计数据
     */
    private Map<String, Object> getSalesStatsData(LocalDateTime startDateTime, LocalDateTime endDateTime,
            Long merchantId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.sum(SOLD_RECEIPT.GOODS_AMOUNT).as("salesAmount"),
                        QueryMethods.sum(SOLD_RECEIPT.DISCOUNT_AMOUNT).as("discountAmount"),
                        QueryMethods.sum(SOLD_RECEIPT.DEDUCTION_AMOUNT).as("deductionAmount"),
                        QueryMethods.sum(SOLD_RECEIPT.AMOUNT).as("actualAmount"))
                .from(SOLD_RECEIPT)
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RECEIPT.STATUS.eq(1)) // 已完成状态
                .and(SOLD_RECEIPT.SOLD_AT.between(startDateTime, endDateTime));

        if (merchantId != null) {
            wrapper.and(SOLD_RECEIPT.MERCHANT_ID.eq(merchantId));
        } else {
            wrapper.and(SOLD_RECEIPT.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        // 使用selectOneByQueryAs获取Map结果
        @SuppressWarnings("unchecked")
        Map<String, Object> result = soldReceiptMapper.selectOneByQueryAs(wrapper, Map.class);
        if (result == null) {
            result = new HashMap<>();
            result.put("salesAmount", BigDecimal.ZERO);
            result.put("discountAmount", BigDecimal.ZERO);
            result.put("deductionAmount", BigDecimal.ZERO);
            result.put("actualAmount", BigDecimal.ZERO);
        }

        // 计算毛利 = 货品金额 - 货品成本价(销售单货品明细表的成本价 * 数量)
        BigDecimal grossProfit = calculateGrossProfit(startDateTime, endDateTime, merchantId);
        result.put("grossProfit", grossProfit);

        return result;
    }

    /**
     * 计算毛利
     * 毛利 = 货品金额 - 货品成本价(销售单货品明细表的成本价 * 数量)
     */
    private BigDecimal calculateGrossProfit(LocalDateTime startDateTime, LocalDateTime endDateTime, Long merchantId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.sum(SOLD_RECEIPT.GOODS_AMOUNT).as("totalSalesAmount"),
                        QueryMethods.sum(SOLD_RECEIPT_GOODS.COST_PRICE.multiply(SOLD_RECEIPT_GOODS.NUM))
                                .as("totalCostAmount"))
                .from(SOLD_RECEIPT)
                .leftJoin(SOLD_RECEIPT_GOODS).on(SOLD_RECEIPT.ID.eq(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID))
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RECEIPT.STATUS.eq(1)) // 已完成状态
                .and(SOLD_RECEIPT.SOLD_AT.between(startDateTime, endDateTime));

        if (merchantId != null) {
            wrapper.and(SOLD_RECEIPT.MERCHANT_ID.eq(merchantId));
        } else {
            wrapper.and(SOLD_RECEIPT.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = soldReceiptMapper.selectOneByQueryAs(wrapper, Map.class);
        if (result == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalSalesAmount = convertToBigDecimal(result.getOrDefault("totalSalesAmount", BigDecimal.ZERO));
        BigDecimal totalCostAmount = convertToBigDecimal(result.getOrDefault("totalCostAmount", BigDecimal.ZERO));

        return totalSalesAmount.subtract(totalCostAmount);
    }

    /**
     * 获取回收统计
     * 总数: 取 material_recycle 表中的数量
     * 总金重: 取 material_recycle 表中的总金重
     * 总银重: 取 material_recycle 表中的总银重
     * 回收金额: 取 material_recycle 表中的总回收金额
     */
    private KanbanStatsVO.RecycleStats getRecycleStats(Long merchantId, LocalDateTime[] currentPeriod,
            LocalDateTime[] previousPeriod) {
        KanbanStatsVO.RecycleStats stats = new KanbanStatsVO.RecycleStats();

        // 当期数据
        Map<String, Object> currentData = getRecycleStatsData(currentPeriod[0], currentPeriod[1], merchantId);

        // 上期数据
        Map<String, Object> previousData = getRecycleStatsData(previousPeriod[0], previousPeriod[1], merchantId);

        // 设置当期数据，处理数据库聚合函数返回的类型转换
        Object totalCountObj = currentData.getOrDefault("totalCount", BigDecimal.ZERO);
        stats.setTotalCount(totalCountObj instanceof BigDecimal ? ((BigDecimal) totalCountObj).intValue()
                : totalCountObj instanceof Integer ? (Integer) totalCountObj
                        : totalCountObj instanceof Long ? ((Long) totalCountObj).intValue() : 0);
        stats.setTotalGoldWeight(convertToBigDecimal(currentData.getOrDefault("totalGoldWeight", BigDecimal.ZERO)));
        stats.setTotalSilverWeight(convertToBigDecimal(currentData.getOrDefault("totalSilverWeight", BigDecimal.ZERO)));
        stats.setRecycleAmount(convertToYuan(currentData, "recycleAmount"));

        // 计算涨幅，处理上期数据的类型转换
        Object previousTotalCountObj = previousData.getOrDefault("totalCount", 0);
        int previousTotalCount = previousTotalCountObj instanceof BigDecimal
                ? ((BigDecimal) previousTotalCountObj).intValue()
                : previousTotalCountObj instanceof Integer ? (Integer) previousTotalCountObj
                        : previousTotalCountObj instanceof Long ? ((Long) previousTotalCountObj).intValue() : 0;

        stats.setTotalCountGrowth(
                calculateGrowthRate(new BigDecimal(stats.getTotalCount()), new BigDecimal(previousTotalCount)));
        stats.setTotalGoldWeightGrowth(calculateGrowthRate(stats.getTotalGoldWeight(),
                convertToBigDecimal(previousData.getOrDefault("totalGoldWeight", BigDecimal.ZERO))));
        stats.setTotalSilverWeightGrowth(calculateGrowthRate(stats.getTotalSilverWeight(),
                convertToBigDecimal(previousData.getOrDefault("totalSilverWeight", BigDecimal.ZERO))));
        stats.setRecycleAmountGrowth(
                calculateGrowthRate(stats.getRecycleAmount(), convertToYuan(previousData, "recycleAmount")));

        return stats;
    }

    /**
     * 获取回收统计数据
     */
    private Map<String, Object> getRecycleStatsData(LocalDateTime startDateTime, LocalDateTime endDateTime,
            Long merchantId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.sum(MATERIAL_RECYCLE.NUM).as("totalCount"),
                        QueryMethods.sum(MATERIAL_RECYCLE.TOTAL_NET_GOLD_WEIGHT).as("totalGoldWeight"),
                        QueryMethods.sum(MATERIAL_RECYCLE.TOTAL_NET_SILVER_WEIGHT).as("totalSilverWeight"),
                        QueryMethods.sum(MATERIAL_RECYCLE.TOTAL_PRICE).as("recycleAmount"))
                .from(MATERIAL_RECYCLE)
                .where(MATERIAL_RECYCLE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(MATERIAL_RECYCLE.CREATED_AT.between(startDateTime, endDateTime));

        if (merchantId != null) {
            wrapper.and(MATERIAL_RECYCLE.MERCHANT_ID.eq(merchantId));
        } else {
            wrapper.and(MATERIAL_RECYCLE.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = materialRecycleMapper.selectOneByQueryAs(wrapper, Map.class);
        if (result == null) {
            result = new HashMap<>();
            result.put("totalCount", 0);
            result.put("totalGoldWeight", BigDecimal.ZERO);
            result.put("totalSilverWeight", BigDecimal.ZERO);
            result.put("recycleAmount", BigDecimal.ZERO);
        }
        return result;
    }

    /**
     * 将对象转换为BigDecimal，处理数据库聚合函数返回的不同数据类型
     *
     * 支持的类型转换：
     * - BigDecimal: 直接返回
     * - Long/Integer: 转换为BigDecimal
     * - Double: 使用BigDecimal.valueOf避免精度问题
     * - String: 尝试解析，失败时返回0
     * - null或其他类型: 返回BigDecimal.ZERO
     *
     * @param value 待转换的值
     * @return 转换后的BigDecimal，异常情况返回BigDecimal.ZERO
     */
    private BigDecimal convertToBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Long) {
            return new BigDecimal((Long) value);
        }
        if (value instanceof Integer) {
            return new BigDecimal((Integer) value);
        }
        if (value instanceof Double) {
            return BigDecimal.valueOf((Double) value);
        }
        if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为BigDecimal: {}", value);
                return BigDecimal.ZERO;
            }
        }
        log.warn("不支持的数据类型转换为BigDecimal: {} ({})", value, value.getClass().getSimpleName());
        return BigDecimal.ZERO;
    }

    /**
     * 将Map中的值转换为BigDecimal并转换为元单位
     *
     * @param data Map数据
     * @param key  键名
     * @return 转换后的元单位金额
     */
    private BigDecimal convertToYuan(Map<String, Object> data, String key) {
        return PriceUtil.fen2yuan(convertToBigDecimal(data.getOrDefault(key, BigDecimal.ZERO)));
    }

    /**
     * 将BigDecimal值转换为元单位
     *
     * @param fen 分单位的金额
     * @return 转换后的元单位金额
     */
    private BigDecimal convertToYuan(BigDecimal fen) {
        return PriceUtil.fen2yuan(fen);
    }

    /**
     * 获取退货统计
     * 总数: 取 sold_return 表中的数量
     * 总金重: 取 sold_return 表中的总金重
     * 总银重: 取 sold_return 表中的总银重
     * 实售金额: 取 sold_return 表中的实售金额
     * 折旧金额: 取 sold_return 表中的折旧金额
     * 实退金额: 取 sold_return 表中的实退金额
     */
    private KanbanStatsVO.ReturnStats getReturnStats(Long merchantId, LocalDateTime[] currentPeriod,
            LocalDateTime[] previousPeriod) {
        KanbanStatsVO.ReturnStats stats = new KanbanStatsVO.ReturnStats();

        // 当期数据
        Map<String, Object> currentData = getReturnStatsData(currentPeriod[0], currentPeriod[1], merchantId);

        // 上期数据
        Map<String, Object> previousData = getReturnStatsData(previousPeriod[0], previousPeriod[1], merchantId);

        // 设置当期数据，处理数据库聚合函数返回的类型转换
        Object totalCountObj = currentData.getOrDefault("totalCount", BigDecimal.ZERO);
        stats.setTotalCount(totalCountObj instanceof BigDecimal ? ((BigDecimal) totalCountObj).intValue()
                : totalCountObj instanceof Integer ? (Integer) totalCountObj
                        : totalCountObj instanceof Long ? ((Long) totalCountObj).intValue() : 0);
        stats.setTotalGoldWeight(convertToBigDecimal(currentData.getOrDefault("totalGoldWeight", BigDecimal.ZERO)));
        stats.setTotalSilverWeight(convertToBigDecimal(currentData.getOrDefault("totalSilverWeight", BigDecimal.ZERO)));
        stats.setActualSalesAmount(convertToYuan(currentData, "actualSalesAmount"));
        stats.setDepreciationAmount(convertToYuan(currentData, "depreciationAmount"));
        stats.setActualRefundAmount(convertToYuan(currentData, "actualRefundAmount"));

        // 计算涨幅，处理上期数据的类型转换
        Object previousTotalCountObj = previousData.getOrDefault("totalCount", 0);
        int previousTotalCount = previousTotalCountObj instanceof BigDecimal
                ? ((BigDecimal) previousTotalCountObj).intValue()
                : previousTotalCountObj instanceof Integer ? (Integer) previousTotalCountObj
                        : previousTotalCountObj instanceof Long ? ((Long) previousTotalCountObj).intValue() : 0;

        stats.setTotalCountGrowth(
                calculateGrowthRate(new BigDecimal(stats.getTotalCount()), new BigDecimal(previousTotalCount)));
        stats.setTotalGoldWeightGrowth(calculateGrowthRate(stats.getTotalGoldWeight(),
                convertToBigDecimal(previousData.getOrDefault("totalGoldWeight", BigDecimal.ZERO))));
        stats.setTotalSilverWeightGrowth(calculateGrowthRate(stats.getTotalSilverWeight(),
                convertToBigDecimal(previousData.getOrDefault("totalSilverWeight", BigDecimal.ZERO))));
        stats.setActualSalesAmountGrowth(
                calculateGrowthRate(stats.getActualSalesAmount(), convertToYuan(previousData, "actualSalesAmount")));
        stats.setDepreciationAmountGrowth(
                calculateGrowthRate(stats.getDepreciationAmount(), convertToYuan(previousData, "depreciationAmount")));
        stats.setActualRefundAmountGrowth(
                calculateGrowthRate(stats.getActualRefundAmount(), convertToYuan(previousData, "actualRefundAmount")));

        return stats;
    }

    /**
     * 获取退货统计数据
     */
    private Map<String, Object> getReturnStatsData(LocalDateTime startDateTime, LocalDateTime endDateTime,
            Long merchantId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.sum(SOLD_RETURN.NUM).as("totalCount"),
                        QueryMethods.sum(SOLD_RETURN.TOTAL_NET_GOLD_WEIGHT).as("totalGoldWeight"),
                        QueryMethods.sum(SOLD_RETURN.TOTAL_NET_SILVER_WEIGHT).as("totalSilverWeight"),
                        QueryMethods.sum(SOLD_RETURN.TOTAL_REVENUE_PRICE).as("actualSalesAmount"),
                        QueryMethods.sum(SOLD_RETURN.TOTAL_DEPRECIATION_PRICE).as("depreciationAmount"),
                        QueryMethods.sum(SOLD_RETURN.TOTAL_PAY_PRICE).as("actualRefundAmount"))
                .from(SOLD_RETURN)
                .where(SOLD_RETURN.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RETURN.CREATED_AT.between(startDateTime, endDateTime));

        if (merchantId != null) {
            wrapper.and(SOLD_RETURN.MERCHANT_ID.eq(merchantId));
        } else {
            wrapper.and(SOLD_RETURN.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = soldReturnMapper.selectOneByQueryAs(wrapper, Map.class);
        if (result == null) {
            result = new HashMap<>();
            result.put("totalCount", 0);
            result.put("totalGoldWeight", BigDecimal.ZERO);
            result.put("totalSilverWeight", BigDecimal.ZERO);
            result.put("actualSalesAmount", BigDecimal.ZERO);
            result.put("depreciationAmount", BigDecimal.ZERO);
            result.put("actualRefundAmount", BigDecimal.ZERO);
        }
        return result;
    }

    /**
     * 获取库存统计
     * 总数: goods 表中的数量
     * 金重: goods 表中的净金重*数量
     * 银重: goods 表中的净银重*数量
     * 价值: 按实时价格计算逻辑
     */
    private KanbanStatsVO.InventoryStats getInventoryStats(KanbanStatsDTO dto) {
        KanbanStatsVO.InventoryStats stats = new KanbanStatsVO.InventoryStats();

        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.sum(GOODS.STOCK_NUM).as("totalCount"),
                        QueryMethods.sum(GOODS.NET_GOLD_WEIGHT.multiply(GOODS.STOCK_NUM)).as("goldWeight"),
                        QueryMethods.sum(GOODS.NET_SILVER_WEIGHT.multiply(GOODS.STOCK_NUM)).as("silverWeight"))
                .from(GOODS)
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()));

        if (dto.getMerchantId() != null) {
            wrapper.and(GOODS.MERCHANT_ID.eq(dto.getMerchantId()));
        } else {
            wrapper.and(GOODS.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = goodsMapper.selectOneByQueryAs(wrapper, Map.class);
        if (result == null) {
            result = new HashMap<>();
            result.put("totalCount", 0);
            result.put("goldWeight", BigDecimal.ZERO);
            result.put("silverWeight", BigDecimal.ZERO);
        }

        // 处理数据库聚合函数返回的类型转换
        Object totalCountObj = result.getOrDefault("totalCount", BigDecimal.ZERO);
        stats.setTotalCount(totalCountObj instanceof BigDecimal ? ((BigDecimal) totalCountObj).intValue()
                : totalCountObj instanceof Integer ? (Integer) totalCountObj
                        : totalCountObj instanceof Long ? ((Long) totalCountObj).intValue() : 0);
        stats.setGoldWeight(convertToBigDecimal(result.getOrDefault("goldWeight", BigDecimal.ZERO)));
        stats.setSilverWeight(convertToBigDecimal(result.getOrDefault("silverWeight", BigDecimal.ZERO)));

        // 计算库存价值
        stats.setValue(calculateInventoryValue(dto.getMerchantId()));

        // 计算成本总价（需要加密处理）
        stats.setTotalCostPrice(calculateInventoryTotalCostPrice(dto.getMerchantId()));

        return stats;
    }

    /**
     * 计算库存价值
     * 使用数据库聚合查询优化性能，避免查询大量记录
     * 同步实时价格：
     * 银饰 = 银重 * 实时银价
     * 金包银 = 金重 * 实时金价 + 银重 * 实时银价
     * 黄金 = 金重 * 实时金价
     * 其他 = 成本价
     * 未同步实时价格：价值 = 成本价
     */
    private BigDecimal calculateInventoryValue(Long merchantId) {
        // 获取最新实时价格
        ActualGoldPriceEntity actualPrice = getLatestActualPrice();

        if (actualPrice == null) {
            // 未同步实时价格，直接使用成本价聚合计算
            return calculateInventoryValueByCostPrice(merchantId);
        }

        // 按类别分组聚合计算，避免查询所有记录
        BigDecimal totalValue = BigDecimal.ZERO;

        // 银饰价值 = SUM(银重 * 库存数量) * 实时银价
        totalValue = totalValue.add(calculateCategoryValue(merchantId, CategoryEnum.SILVER.getValue(),
                actualPrice.getSilverPrice(), "silver"));

        // 黄金价值 = SUM(金重 * 库存数量) * 实时金价
        totalValue = totalValue.add(calculateCategoryValue(merchantId, CategoryEnum.GOLD.getValue(),
                actualPrice.getGoldPrice(), "gold"));

        // 金包银价值 = SUM(金重 * 库存数量) * 实时金价 + SUM(银重 * 库存数量) * 实时银价
        totalValue = totalValue.add(calculateGoldSilverCategoryValue(merchantId, actualPrice));

        // 其他类别价值 = SUM(成本价 * 库存数量)
        totalValue = totalValue.add(calculateOtherCategoryValue(merchantId));

        return PriceUtil.fen2yuan(totalValue);
    }

    /**
     * 未同步实时价格时，使用成本价聚合计算
     */
    private BigDecimal calculateInventoryValueByCostPrice(Long merchantId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select("SUM(cost_price * stock_num) as total_value")
                .from(GOODS)
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(GOODS.STOCK_NUM.gt(0));

        if (merchantId != null) {
            wrapper.and(GOODS.MERCHANT_ID.eq(merchantId));
        } else {
            wrapper.and(GOODS.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = goodsMapper.selectOneByQueryAs(wrapper, Map.class);
        BigDecimal totalValue = result != null
                ? convertToBigDecimal(result.getOrDefault("total_value", BigDecimal.ZERO))
                : BigDecimal.ZERO;
        return totalValue != null ? totalValue : BigDecimal.ZERO;
    }

    /**
     * 计算指定类别的价值（银饰或黄金）
     */
    private BigDecimal calculateCategoryValue(Long merchantId, Long categoryId, BigDecimal unitPrice,
            String weightType) {
        String weightColumn = "gold".equals(weightType) ? "net_gold_weight" : "net_silver_weight";

        QueryWrapper wrapper = QueryWrapper.create()
                .select("SUM(" + weightColumn + " * stock_num) as total_weight")
                .from(GOODS)
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(GOODS.STOCK_NUM.gt(0))
                .and(GOODS.CATEGORY_ID.eq(categoryId));

        if (merchantId != null) {
            wrapper.and(GOODS.MERCHANT_ID.eq(merchantId));
        } else {
            wrapper.and(GOODS.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = goodsMapper.selectOneByQueryAs(wrapper, Map.class);
        BigDecimal totalWeight = result != null
                ? convertToBigDecimal(result.getOrDefault("total_weight", BigDecimal.ZERO))
                : BigDecimal.ZERO;
        return totalWeight != null ? totalWeight.multiply(unitPrice) : BigDecimal.ZERO;
    }

    /**
     * 计算金包银类别的价值
     */
    private BigDecimal calculateGoldSilverCategoryValue(Long merchantId, ActualGoldPriceEntity actualPrice) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select("SUM(net_gold_weight * stock_num) as total_gold_weight, SUM(net_silver_weight * stock_num) as total_silver_weight")
                .from(GOODS)
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(GOODS.STOCK_NUM.gt(0))
                .and(GOODS.CATEGORY_ID.eq(CategoryEnum.GOLD_SILVER.getValue()));

        if (merchantId != null) {
            wrapper.and(GOODS.MERCHANT_ID.eq(merchantId));
        } else {
            wrapper.and(GOODS.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = goodsMapper.selectOneByQueryAs(wrapper, Map.class);
        if (result == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalGoldWeight = convertToBigDecimal(result.getOrDefault("total_gold_weight", BigDecimal.ZERO));
        BigDecimal totalSilverWeight = convertToBigDecimal(result.getOrDefault("total_silver_weight", BigDecimal.ZERO));

        BigDecimal goldValue = totalGoldWeight != null ? totalGoldWeight.multiply(actualPrice.getGoldPrice())
                : BigDecimal.ZERO;
        BigDecimal silverValue = totalSilverWeight != null ? totalSilverWeight.multiply(actualPrice.getSilverPrice())
                : BigDecimal.ZERO;

        return goldValue.add(silverValue);
    }

    /**
     * 计算其他类别的价值
     * 直接使用成本价计算
     */
    private BigDecimal calculateOtherCategoryValue(Long merchantId) {
        // 查询其他类别的商品，直接聚合计算成本价总值
        QueryWrapper goodsWrapper = QueryWrapper.create()
                .select(QueryMethods.sum(GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM)).as("total_cost_value"))
                .from(GOODS)
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(GOODS.STOCK_NUM.gt(0))
                .and(GOODS.CATEGORY_ID
                        .notIn(CategoryEnum.SILVER.getValue(), CategoryEnum.GOLD.getValue(),
                                CategoryEnum.GOLD_SILVER.getValue())
                        .or(GOODS.CATEGORY_ID.isNull()));

        if (merchantId != null) {
            goodsWrapper.and(GOODS.MERCHANT_ID.eq(merchantId));
        } else {
            goodsWrapper.and(GOODS.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = goodsMapper.selectOneByQueryAs(goodsWrapper, Map.class);

        if (result == null) {
            return BigDecimal.ZERO;
        }

        // 直接获取成本价总值
        BigDecimal totalCostValue = convertToBigDecimal(result.get("total_cost_value"));

        return totalCostValue != null ? totalCostValue : BigDecimal.ZERO;
    }

    /**
     * 计算库存成本总价（需要加密处理）
     */
    private String calculateInventoryTotalCostPrice(Long merchantId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(QueryMethods.sum(GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM)).as("totalCostPrice"))
                .from(GOODS)
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(GOODS.STOCK_NUM.gt(0));

        if (merchantId != null) {
            wrapper.and(GOODS.MERCHANT_ID.eq(merchantId));
        } else {
            wrapper.and(GOODS.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = goodsMapper.selectOneByQueryAs(wrapper, Map.class);
        BigDecimal totalCostPrice = result != null
                ? convertToBigDecimal(result.getOrDefault("totalCostPrice", BigDecimal.ZERO))
                : BigDecimal.ZERO;

        // 转换为元并处理加密
        String totalCostPriceStr = PriceUtil.fen2yuanString(totalCostPrice);
        GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(PriceColumEnum.COST_PRICE.getSign());
        return ColumnEncryptUtil.handleEncryptPrice(totalCostPriceStr, column);
    }

    /**
     * 获取最新实时价格
     */
    private ActualGoldPriceEntity getLatestActualPrice() {
        return actualGoldPriceMapper.selectOneByQuery(
                QueryWrapper.create()
                        .where(ACTUAL_GOLD_PRICE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .orderBy(ACTUAL_GOLD_PRICE.CREATED_AT, false)
                        .limit(1));
    }

    /**
     * 获取旧料统计
     * 总数: old_material 表中的数量
     * 金重: old_material 表中的净金重*数量
     * 银重: old_material 表中的净银重*数量
     * 价值: 按实时价格计算逻辑，但使用回收金额而非成本价作为兜底价格
     */
    private KanbanStatsVO.OldMaterialStats getOldMaterialStats(KanbanStatsDTO dto) {
        KanbanStatsVO.OldMaterialStats stats = new KanbanStatsVO.OldMaterialStats();

        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.sum(OLD_MATERIAL.NUM).as("totalCount"),
                        QueryMethods.sum(OLD_MATERIAL.NET_GOLD_WEIGHT.multiply(OLD_MATERIAL.NUM)).as("goldWeight"),
                        QueryMethods.sum(OLD_MATERIAL.NET_SILVER_WEIGHT.multiply(OLD_MATERIAL.NUM)).as("silverWeight"))
                .from(OLD_MATERIAL)
                .where(OLD_MATERIAL.COMPANY_ID.eq(SecurityUtils.getCompanyId()));

        if (dto.getMerchantId() != null) {
            wrapper.and(OLD_MATERIAL.MERCHANT_ID.eq(dto.getMerchantId()));
        } else {
            wrapper.and(OLD_MATERIAL.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = oldMaterialMapper.selectOneByQueryAs(wrapper, Map.class);
        if (result == null) {
            result = new HashMap<>();
            result.put("totalCount", 0);
            result.put("goldWeight", BigDecimal.ZERO);
            result.put("silverWeight", BigDecimal.ZERO);
        }

        // 处理数据库聚合函数返回的类型转换
        Object totalCountObj = result.getOrDefault("totalCount", BigDecimal.ZERO);
        stats.setTotalCount(totalCountObj instanceof BigDecimal ? ((BigDecimal) totalCountObj).intValue()
                : totalCountObj instanceof Integer ? (Integer) totalCountObj
                        : totalCountObj instanceof Long ? ((Long) totalCountObj).intValue() : 0);
        stats.setGoldWeight(convertToBigDecimal(result.getOrDefault("goldWeight", BigDecimal.ZERO)));
        stats.setSilverWeight(convertToBigDecimal(result.getOrDefault("silverWeight", BigDecimal.ZERO)));

        // 计算旧料价值
        stats.setValue(calculateOldMaterialValue(dto.getMerchantId()));

        // 计算回收金额（需要加密处理）
        stats.setTotalRecycleAmount(calculateOldMaterialTotalRecycleAmount(dto.getMerchantId()));

        return stats;
    }

    /**
     * 计算旧料价值
     * 使用数据库聚合查询优化性能，避免查询大量记录
     * 价值计算逻辑同库存，但使用回收金额而非成本价作为兜底价格
     */
    private BigDecimal calculateOldMaterialValue(Long merchantId) {
        // 获取最新实时价格
        ActualGoldPriceEntity actualPrice = getLatestActualPrice();

        if (actualPrice == null) {
            // 未同步实时价格，直接使用回收金额聚合计算
            return calculateOldMaterialValueByRecyclePrice(merchantId);
        }

        // 按类别分组聚合计算，避免查询所有记录
        BigDecimal totalValue = BigDecimal.ZERO;

        // 银饰价值 = SUM(银重 * 数量) * 实时银价
        totalValue = totalValue.add(calculateOldMaterialCategoryValue(merchantId, CategoryEnum.SILVER.getValue(),
                actualPrice.getSilverPrice(), "silver"));

        // 黄金价值 = SUM(金重 * 数量) * 实时金价
        totalValue = totalValue.add(calculateOldMaterialCategoryValue(merchantId, CategoryEnum.GOLD.getValue(),
                actualPrice.getGoldPrice(), "gold"));

        // 金包银价值 = SUM(金重 * 数量) * 实时金价 + SUM(银重 * 数量) * 实时银价
        totalValue = totalValue.add(calculateOldMaterialGoldSilverCategoryValue(merchantId, actualPrice));

        // 其他类别价值 = SUM(回收金额 * 数量)
        totalValue = totalValue.add(calculateOldMaterialOtherCategoryValue(merchantId));

        return PriceUtil.fen2yuan(totalValue);
    }

    /**
     * 未同步实时价格时，使用回收金额聚合计算
     */
    private BigDecimal calculateOldMaterialValueByRecyclePrice(Long merchantId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select("SUM(recycle_price * num) as total_value")
                .from(OLD_MATERIAL)
                .where(OLD_MATERIAL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(OLD_MATERIAL.NUM.gt(0));

        if (merchantId != null) {
            wrapper.and(OLD_MATERIAL.MERCHANT_ID.eq(merchantId));
        } else {
            wrapper.and(OLD_MATERIAL.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = oldMaterialMapper.selectOneByQueryAs(wrapper, Map.class);
        BigDecimal totalValue = result != null
                ? convertToBigDecimal(result.getOrDefault("total_value", BigDecimal.ZERO))
                : BigDecimal.ZERO;
        return totalValue != null ? totalValue : BigDecimal.ZERO;
    }

    /**
     * 计算指定类别的旧料价值（银饰或黄金）
     */
    private BigDecimal calculateOldMaterialCategoryValue(Long merchantId, Long categoryId, BigDecimal unitPrice,
            String weightType) {
        String weightColumn = "gold".equals(weightType) ? "net_gold_weight" : "net_silver_weight";

        QueryWrapper wrapper = QueryWrapper.create()
                .select("SUM(" + weightColumn + " * num) as total_weight")
                .from(OLD_MATERIAL)
                .where(OLD_MATERIAL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(OLD_MATERIAL.NUM.gt(0))
                .and(OLD_MATERIAL.CATEGORY_ID.eq(categoryId));

        if (merchantId != null) {
            wrapper.and(OLD_MATERIAL.MERCHANT_ID.eq(merchantId));
        } else {
            wrapper.and(OLD_MATERIAL.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = oldMaterialMapper.selectOneByQueryAs(wrapper, Map.class);
        BigDecimal totalWeight = result != null
                ? convertToBigDecimal(result.getOrDefault("total_weight", BigDecimal.ZERO))
                : BigDecimal.ZERO;
        return totalWeight != null ? totalWeight.multiply(unitPrice) : BigDecimal.ZERO;
    }

    /**
     * 计算金包银类别的旧料价值
     */
    private BigDecimal calculateOldMaterialGoldSilverCategoryValue(Long merchantId, ActualGoldPriceEntity actualPrice) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select("SUM(net_gold_weight * num) as total_gold_weight, SUM(net_silver_weight * num) as total_silver_weight")
                .from(OLD_MATERIAL)
                .where(OLD_MATERIAL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(OLD_MATERIAL.NUM.gt(0))
                .and(OLD_MATERIAL.CATEGORY_ID.eq(CategoryEnum.GOLD_SILVER.getValue()));

        if (merchantId != null) {
            wrapper.and(OLD_MATERIAL.MERCHANT_ID.eq(merchantId));
        } else {
            wrapper.and(OLD_MATERIAL.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = oldMaterialMapper.selectOneByQueryAs(wrapper, Map.class);
        if (result == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalGoldWeight = convertToBigDecimal(result.getOrDefault("total_gold_weight", BigDecimal.ZERO));
        BigDecimal totalSilverWeight = convertToBigDecimal(result.getOrDefault("total_silver_weight", BigDecimal.ZERO));

        BigDecimal goldValue = totalGoldWeight != null ? totalGoldWeight.multiply(actualPrice.getGoldPrice())
                : BigDecimal.ZERO;
        BigDecimal silverValue = totalSilverWeight != null ? totalSilverWeight.multiply(actualPrice.getSilverPrice())
                : BigDecimal.ZERO;

        return goldValue.add(silverValue);
    }

    /**
     * 计算其他类别的旧料价值
     * 直接使用回收价计算
     */
    private BigDecimal calculateOldMaterialOtherCategoryValue(Long merchantId) {
        // 查询其他类别的旧料，直接聚合计算回收价总值
        QueryWrapper oldMaterialWrapper = QueryWrapper.create()
                .select(QueryMethods.sum(OLD_MATERIAL.RECYCLE_PRICE.multiply(OLD_MATERIAL.NUM))
                        .as("total_recycle_value"))
                .from(OLD_MATERIAL)
                .where(OLD_MATERIAL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(OLD_MATERIAL.NUM.gt(0))
                .and(OLD_MATERIAL.CATEGORY_ID
                        .notIn(CategoryEnum.SILVER.getValue(), CategoryEnum.GOLD.getValue(),
                                CategoryEnum.GOLD_SILVER.getValue())
                        .or(OLD_MATERIAL.CATEGORY_ID.isNull()));

        if (merchantId != null) {
            oldMaterialWrapper.and(OLD_MATERIAL.MERCHANT_ID.eq(merchantId));
        } else {
            oldMaterialWrapper.and(OLD_MATERIAL.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = oldMaterialMapper.selectOneByQueryAs(oldMaterialWrapper, Map.class);

        if (result == null) {
            return BigDecimal.ZERO;
        }

        // 直接获取回收价总值
        BigDecimal totalRecycleValue = convertToBigDecimal(result.get("total_recycle_value"));

        return totalRecycleValue != null ? totalRecycleValue : BigDecimal.ZERO;
    }

    /**
     * 计算旧料回收金额（不需要加密处理）
     */
    private BigDecimal calculateOldMaterialTotalRecycleAmount(Long merchantId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(QueryMethods.sum(OLD_MATERIAL.RECYCLE_PRICE.multiply(OLD_MATERIAL.NUM)).as("totalRecycleAmount"))
                .from(OLD_MATERIAL)
                .where(OLD_MATERIAL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(OLD_MATERIAL.NUM.gt(0));

        if (merchantId != null) {
            wrapper.and(OLD_MATERIAL.MERCHANT_ID.eq(merchantId));
        } else {
            wrapper.and(OLD_MATERIAL.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> result = oldMaterialMapper.selectOneByQueryAs(wrapper, Map.class);
        BigDecimal totalRecycleAmount = result != null
                ? convertToBigDecimal(result.getOrDefault("totalRecycleAmount", BigDecimal.ZERO))
                : BigDecimal.ZERO;

        // 转换为元
        return PriceUtil.fen2yuan(totalRecycleAmount);
    }

    /**
     * 获取销售占比统计
     * 根据大类groupBy销售单货品明细，求和数量*应收金额
     * 如果没有大类的数据，需要保留大类的item，值为零
     */
    private List<KanbanStatsVO.SalesRatioStats> getSalesRatioStats(KanbanStatsDTO dto) {
        // 转换日期范围为 DateTime
        LocalDateTime[] dateTimeRange = convertToDateTimeRange(dto.getStartDate(), dto.getEndDate());

        // 查询销售占比数据
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        GOODS.CATEGORY_ID.as("categoryId"),
                        QueryMethods.sum(SOLD_RECEIPT_GOODS.NUM.multiply(SOLD_RECEIPT_GOODS.REAL_AMOUNT))
                                .as("salesAmount"))
                .from(SOLD_RECEIPT_GOODS)
                .leftJoin(SOLD_RECEIPT).on(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.eq(SOLD_RECEIPT.ID))
                .leftJoin(GOODS).on(SOLD_RECEIPT_GOODS.GOODS_ID.eq(GOODS.ID))
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RECEIPT.STATUS.eq(1)) // 已完成状态
                .and(SOLD_RECEIPT.SOLD_AT.between(dateTimeRange[0], dateTimeRange[1]))
                .groupBy(GOODS.CATEGORY_ID);

        if (dto.getMerchantId() != null) {
            wrapper.and(SOLD_RECEIPT.MERCHANT_ID.eq(dto.getMerchantId()));
        } else {
            wrapper.and(SOLD_RECEIPT.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        @SuppressWarnings("rawtypes")
        List<Map> rawSalesData = soldReceiptGoodsMapper.selectListByQueryAs(wrapper, Map.class);
        List<Map<String, Object>> salesData = new ArrayList<>();
        if (rawSalesData != null) {
            for (@SuppressWarnings("rawtypes")
            Map rawMap : rawSalesData) {
                @SuppressWarnings("unchecked")
                Map<String, Object> map = (Map<String, Object>) rawMap;
                salesData.add(map);
            }
        }

        // 转换为Map便于查找，处理数据库聚合函数返回的类型转换
        // 过滤掉categoryId为null的记录，并处理重复键的情况（合并金额）
        Map<Integer, BigDecimal> salesAmountMap = salesData.stream()
                .filter(data -> data.get("categoryId") != null) // 过滤掉categoryId为null的记录
                .collect(Collectors.toMap(
                        data -> (Integer) data.get("categoryId"),
                        data -> convertToBigDecimal(data.getOrDefault("salesAmount", BigDecimal.ZERO)),
                        BigDecimal::add)); // 如果有重复键，则合并金额

        // 计算总销售金额
        BigDecimal totalSalesAmount = salesAmountMap.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 构建所有大类的销售占比数据
        List<KanbanStatsVO.SalesRatioStats> result = new ArrayList<>();
        for (CategoryEnum category : CategoryEnum.values()) {
            KanbanStatsVO.SalesRatioStats stats = new KanbanStatsVO.SalesRatioStats();
            stats.setCategoryId(category.getValue());
            stats.setCategoryName(category.getLabel());

            BigDecimal categoryAmount = salesAmountMap.getOrDefault(category.getValue().intValue(), BigDecimal.ZERO);
            stats.setSalesAmount(convertToYuan(categoryAmount));

            // 计算占比
            if (totalSalesAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal ratio = categoryAmount
                        .divide(totalSalesAmount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                stats.setRatio(ratio);
            } else {
                stats.setRatio(BigDecimal.ZERO);
            }

            result.add(stats);
        }

        return result;
    }
}
