package com.xc.boot.modules.mini.service;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.modules.mini.model.query.MiniGoodsPageQuery;
import com.xc.boot.modules.mini.model.vo.MiniGoodsStatisticVo;
import com.xc.boot.modules.mini.model.vo.MiniGoodsVo;

/**
 * 小程序货品服务类
 */
public interface MiniGoodsService {

    /**
     * 小程序货品分页列表
     */
    Page<MiniGoodsVo> miniGoodsPage(MiniGoodsPageQuery query);

    /**
     * 小程序货品统计
     */
    MiniGoodsStatisticVo miniGoodsStatistic(MiniGoodsPageQuery query);
} 