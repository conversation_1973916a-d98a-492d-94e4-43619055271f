package com.xc.boot.modules.mini.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 小程序货品统计VO
 */
@Data
public class MiniGoodsStatisticVo {
    @Schema(description = "总库存数")
    private Long totalStock = 0L;
    
    @Schema(description = "总金重(g)")
    private BigDecimal totalNetGoldWeight = BigDecimal.ZERO;
    
    @Schema(description = "总银重(g)")
    private BigDecimal totalNetSilverWeight = BigDecimal.ZERO;
    
    @Schema(description = "总成本价(元)")
    private String totalCost = "0.00";

    @Schema(description = "总价值(元)")
    private String totalValue = "0.00";
} 