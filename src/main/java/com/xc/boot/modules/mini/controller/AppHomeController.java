package com.xc.boot.modules.mini.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.mini.model.query.GoldPriceDetailQuery;
import com.xc.boot.modules.mini.model.vo.GoldPriceDetailVO;
import com.xc.boot.modules.mini.model.vo.GoldPricePreviewVO;
import com.xc.boot.modules.mini.service.HomeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "小程序-首页接口")
@RequestMapping("/api/mini/home")
public class AppHomeController {

    private final HomeService homeService;

    @Operation(summary = "金价预览")
    @GetMapping("/price/preview")
    public Result<List<GoldPricePreviewVO>> getGoldPricePreview() {
        List<GoldPricePreviewVO> previewList = homeService.getGoldPricePreview();
        return Result.success(previewList);
    }

    @Operation(summary = "金价详情列表")
    @PostMapping("/price/detail")
    public PageResult<GoldPriceDetailVO> getGoldPriceDetailPage(@RequestBody GoldPriceDetailQuery query) {
        Page<GoldPriceDetailVO> page = homeService.getGoldPriceDetailPage(query);
        return PageResult.success(page);
    }
}
