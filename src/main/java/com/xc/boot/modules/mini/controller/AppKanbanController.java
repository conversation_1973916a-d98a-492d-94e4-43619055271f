package com.xc.boot.modules.mini.controller;

import com.xc.boot.common.result.Result;
import com.xc.boot.modules.mini.model.dto.KanbanStatsDTO;
import com.xc.boot.modules.mini.model.vo.KanbanStatsVO;
import com.xc.boot.modules.mini.service.KanbanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "小程序-看板")
@RequestMapping("/api/mini/kanban")
public class AppKanbanController {

    private final KanbanService kanbanService;

    @Operation(summary = "看板统计信息")
    @PostMapping("/stats")
    public Result<KanbanStatsVO> getKanbanStats(@RequestBody @Validated KanbanStatsDTO dto) {
        KanbanStatsVO result = kanbanService.getKanbanStats(dto);
        return Result.success(result);
    }
}
