package com.xc.boot.modules.mini.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 金价预览视图对象
 */
@Data
@Schema(description = "金价预览视图对象")
public class GoldPricePreviewVO {

    @Schema(description = "大类ID")
    private Long categoryId;

    @Schema(description = "大类名称")
    private String category;

    @Schema(description = "销售价(元/克)")
    private BigDecimal salePrice;

    @Schema(description = "回收价(元/克)")
    private BigDecimal recyclePrice;
} 