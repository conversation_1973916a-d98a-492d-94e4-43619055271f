package com.xc.boot.modules.mini.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 小程序用户信息修改表单对象
 * <AUTHOR>
 * @since 2025/1/20
 */
@Schema(description = "小程序用户信息修改表单对象")
@Data
public class AppUserUpdateForm {
    
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户ID不能为空")
    private Long id;

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户昵称不能为空")
    @Length(max = 200, message = "用户昵称长度不能超过200个字符")
    private String nickname;

    @Schema(description = "用户头像(如果不传 则将当前用户头像设为空)")
    private String avatar;

    @Schema(description = "用户头像文件ID(如果不传 则将当前用户头像设为空)")
    private Long avatarId;
} 