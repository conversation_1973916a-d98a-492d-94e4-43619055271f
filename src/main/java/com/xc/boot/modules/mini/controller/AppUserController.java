package com.xc.boot.modules.mini.controller;

import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.result.Result;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.mini.model.form.AppUserUpdateForm;
import com.xc.boot.modules.mini.service.AppUserService;
import com.xc.boot.system.model.form.PasswordChangeForm;
import com.xc.boot.system.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "小程序-个人中心接口")
@RequestMapping("/api/mini/user")
public class AppUserController {
    private final AppUserService appUserService;
    private final UserService userService;

    @Operation(summary = "修改用户信息")
    @PostMapping("/update")
    public Result<Void> updateUserInfo(@RequestBody @Validated AppUserUpdateForm form) {
        boolean result = appUserService.updateUserInfo(form);
        return Result.judge(result);
    }

    @Operation(summary = "修改个人密码")
    @PutMapping(value = "/password")
    public Result<?> changePassword(@RequestBody @Validated(Update.class) PasswordChangeForm data) {
        Long currUserId = SecurityUtils.getUserId();
        boolean result = userService.changePassword(currUserId, data);
        return Result.judge(result);
    }

}
