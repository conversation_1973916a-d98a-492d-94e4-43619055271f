package com.xc.boot.modules.mini.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.common.enums.baseColum.OtherColumEnum;
import com.xc.boot.common.enums.baseColum.PriceColumEnum;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtilV2;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.merchant.mapper.ActualGoldPriceMapper;
import com.xc.boot.modules.merchant.model.entity.ActualGoldPriceEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.mini.model.query.MiniGoodsPageQuery;
import com.xc.boot.modules.mini.model.vo.MiniGoodsStatisticVo;
import com.xc.boot.modules.mini.model.vo.MiniGoodsVo;
import com.xc.boot.modules.mini.service.MiniGoodsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.mybatisflex.core.query.QueryMethods.case_;
import static com.mybatisflex.core.query.QueryMethods.sum;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
import static com.xc.boot.modules.merchant.model.entity.table.ActualGoldPriceTableDef.ACTUAL_GOLD_PRICE;

/**
 * 小程序货品服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MiniGoodsServiceImpl extends ServiceImpl<GoodsMapper, GoodsEntity> implements MiniGoodsService {
    private final ListFillService fillService;
    private final ActualGoldPriceMapper actualGoldPriceMapper;

        @Override
    public Page<MiniGoodsVo> miniGoodsPage(MiniGoodsPageQuery query) {
        QueryWrapper wrapper = buildWrapper(query);
        wrapper.orderBy(GOODS.ID, false);

        int pageNum = query.getPageNum();
        int pageSize = query.getPageSize();
        
        Page<MiniGoodsVo> page = this.mapper.paginateAs(pageNum, pageSize, wrapper, MiniGoodsVo.class);
        List<MiniGoodsVo> records = page.getRecords();
        this.fillList(records);
        return page;
    }

    @Override
    public MiniGoodsStatisticVo miniGoodsStatistic(MiniGoodsPageQuery query) {
        QueryWrapper wrapper = buildWrapper(query);
        wrapper.select(sum(GOODS.STOCK_NUM).as("totalStock"),
                sum(GOODS.NET_GOLD_WEIGHT.multiply(GOODS.STOCK_NUM)).as("totalNetGoldWeight"),
                sum(GOODS.NET_SILVER_WEIGHT.multiply(GOODS.STOCK_NUM)).as("totalNetSilverWeight"),
                sum(GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM)).as("totalCost")
        );
        // 获取实时金价
        ActualGoldPriceEntity priceEntity = actualGoldPriceMapper.selectOneByQuery(QueryWrapper.create()
                .where(ACTUAL_GOLD_PRICE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .orderBy(ACTUAL_GOLD_PRICE.ID, false)
                .limit(1));

        String goldPrice = priceEntity != null && priceEntity.getGoldPrice() != null ? PriceUtil.fen2yuanString(priceEntity.getGoldPrice()) : null;
        String platinumPrice = priceEntity != null && priceEntity.getPlatinumPrice() != null ? PriceUtil.fen2yuanString(priceEntity.getPlatinumPrice()) : null;
        String silverPrice = priceEntity != null && priceEntity.getSilverPrice() != null ? PriceUtil.fen2yuanString(priceEntity.getSilverPrice()) : null;
        wrapper.select(
                sum(case_()
                        // 银
                        .when(GOODS.CATEGORY_ID.eq(CategoryEnum.SILVER.getValue()))
                        .then(silverPrice != null ?
                                GOODS.NET_SILVER_WEIGHT.multiply(QueryMethods.column(silverPrice)).multiply(GOODS.STOCK_NUM)
                                : GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM).divide(100.0))
                        // 金包银
                        .when(GOODS.CATEGORY_ID.eq(CategoryEnum.GOLD_SILVER.getValue()))
                        .then((goldPrice != null && silverPrice != null) ?
                                GOODS.NET_GOLD_WEIGHT.multiply(QueryMethods.column(goldPrice)).multiply(GOODS.STOCK_NUM)
                                        .add(GOODS.NET_SILVER_WEIGHT.multiply(QueryMethods.column(silverPrice)).multiply(GOODS.STOCK_NUM))
                                : GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM).divide(100.0))
                        // 金
                        .when(GOODS.CATEGORY_ID.eq(CategoryEnum.GOLD.getValue()))
                        .then(goldPrice != null ?
                                GOODS.NET_GOLD_WEIGHT.multiply(QueryMethods.column(goldPrice)).multiply(GOODS.STOCK_NUM)
                                : GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM).divide(100.0))
                        // 铂金
                        .when(GOODS.CATEGORY_ID.eq(CategoryEnum.PLATINUM.getValue()))
                        .then(platinumPrice != null ?
                                GOODS.NET_GOLD_WEIGHT.multiply(QueryMethods.column(platinumPrice)).multiply(GOODS.STOCK_NUM)
                                : GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM).divide(100.0))
                        .else_(GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM).divide(100.0))
                        .end()
                ).as("totalValue")
        );
        MiniGoodsStatisticVo vo = mapper.selectOneByQueryAs(wrapper, MiniGoodsStatisticVo.class);
        if (Objects.isNull(vo)) {
            return new MiniGoodsStatisticVo();
        }
        // 总成本价加密处理
        GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(PriceColumEnum.COST_PRICE.getSign());
        String cost = ColumnEncryptUtil.handleEncryptPrice(PriceUtil.fen2yuanString(vo.getTotalCost()), column);
        String value = ColumnEncryptUtil.handleEncryptPrice(PriceUtil.formatTwoDecimal(vo.getTotalValue()).toPlainString(), column);
        vo.setTotalCost(cost);
        vo.setTotalValue(value);
        vo.setTotalNetGoldWeight(PriceUtil.formatThreeDecimal(vo.getTotalNetGoldWeight()));
        vo.setTotalNetSilverWeight(PriceUtil.formatThreeDecimal(vo.getTotalNetSilverWeight()));
        return vo;
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper buildWrapper(MiniGoodsPageQuery query) {
        // 固定条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS.MERCHANT_ID.in(SecurityUtils.getMerchantIds(), !SecurityUtils.isMain()))
                .where(GOODS.STOCK_NUM.gt(0))
                .where(GOODS.NUM.gt(0));

        // 货品条码
        if (StringUtils.isNotBlank(query.getGoodsSn())) {
            queryWrapper.and(GOODS.GOODS_SN.eq(query.getGoodsSn()));
        }

        // 货品名称
        if (StringUtils.isNotBlank(query.getName())) {
            queryWrapper.and(GOODS.NAME.like(query.getName()));
        }

        // 所属门店
        if (StringUtils.isNotBlank(query.getMerchantIds())) {
            queryWrapper.and(GOODS.MERCHANT_ID.in(List.of(query.getMerchantIds().split(","))));
        }

        // 供应商ID
        if (StringUtils.isNotBlank(query.getSupplierIds())) {
            queryWrapper.and(GOODS.SUPPLIER_ID.in(List.of(query.getSupplierIds().split(","))));
        }

        // 所属大类ID
        if (StringUtils.isNotBlank(query.getCategoryIds())) {
            queryWrapper.and(GOODS.CATEGORY_ID.in(List.of(query.getCategoryIds().split(","))));
        }

        // 所属小类ID
        if (StringUtils.isNotBlank(query.getSubclassIds())) {
            queryWrapper.and(GOODS.SUBCLASS_ID.in(List.of(query.getSubclassIds().split(","))));
        }

        // 柜台ID
        if (StringUtils.isNotBlank(query.getCounterIds())) {
            queryWrapper.and(GOODS.COUNTER_ID.in(List.of(query.getCounterIds().split(","))));
        }

        // 成色ID
        if (StringUtils.isNotBlank(query.getQualityIds())) {
            queryWrapper.and(GOODS.QUALITY_ID.in(List.of(query.getQualityIds().split(","))));
        }

        // 所属品牌ID
        if (StringUtils.isNotBlank(query.getBrandIds())) {
            queryWrapper.and(GOODS.BRAND_ID.in(List.of(query.getBrandIds().split(","))));
        }

        // 所属款式ID
        if (StringUtils.isNotBlank(query.getStyleIds())) {
            queryWrapper.and(GOODS.STYLE_ID.in(List.of(query.getStyleIds().split(","))));
        }

        // 主石ID
        if (StringUtils.isNotBlank(query.getMainStoneIds())) {
            queryWrapper.and(GOODS.MAIN_STONE_ID.in(List.of(query.getMainStoneIds().split(","))));
        }

        // 副石ID
        if (StringUtils.isNotBlank(query.getSubStoneIds())) {
            queryWrapper.and(GOODS.SUB_STONE_ID.in(List.of(query.getSubStoneIds().split(","))));
        }

        // 圈口ID
        if (StringUtils.isNotBlank(query.getCircleSize())) {
            queryWrapper.and(GOODS.CIRCLE_SIZE.in(List.of(query.getCircleSize().split(","))));
        }

        // 库存状态
        if (StringUtils.isNotBlank(query.getStatus())) {
            if (query.getStatus().contains("1")) {
                // 在库
                queryWrapper.and(GOODS.STOCK_NUM.gt(0));
            }
            if (query.getStatus().contains("2")) {
                // 已售
                queryWrapper.and(GOODS.SOLD_NUM.gt(0));
            }
        }

        return queryWrapper;
    }

    /**
     * 填充关联字段
     */
    private void fillList(List<MiniGoodsVo> list) {
        ListFillUtilV2.of(list)
                .build(fillService::getMerchantNameById, MiniGoodsVo::getMerchantId, MiniGoodsVo::setMerchant)
                .build(fillService::getCounterNameById, MiniGoodsVo::getCounterId, MiniGoodsVo::setCounter)
                .build(fillService::getSupplierNameById, MiniGoodsVo::getSupplierId, MiniGoodsVo::setSupplier)
                .build(fillService::getCategoryNameById, MiniGoodsVo::getCategoryId, MiniGoodsVo::setCategory)
                .build(fillService::getSubclassNameById, MiniGoodsVo::getSubclassId, MiniGoodsVo::setSubclass)
                .build(fillService::getBrandNameById, MiniGoodsVo::getBrandId, MiniGoodsVo::setBrand)
                .build(fillService::getStyleNameById, MiniGoodsVo::getStyleId, MiniGoodsVo::setStyle)
                .build(fillService::getQualityNameById, MiniGoodsVo::getQualityId, MiniGoodsVo::setQuality)
                .build(fillService::getTechnologyNameById, MiniGoodsVo::getTechnologyId, MiniGoodsVo::setTechnology)
                .build(fillService::getJewelryMapperNameById, MiniGoodsVo::getMainStoneId, MiniGoodsVo::setMainStone)
                .build(fillService::getJewelryMapperNameById, MiniGoodsVo::getSubStoneId, MiniGoodsVo::setSubStone)
                .build(fillService::getColumnVosById, MiniGoodsVo::getId, MiniGoodsVo::setCustomerColumns)
                .build(fillService::getGoodsImgByGoodsId, MiniGoodsVo::getId, MiniGoodsVo::setImages)
                .peek(vo -> {
                    vo.setSalesType(OtherColumEnum.getSalesTypeText(vo.getSalesType()));
                    // 价格格式化
                    vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
                    vo.setSaleWorkPrice(PriceUtil.fen2yuan(vo.getSaleWorkPrice()));
                    vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
                    vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
                    vo.setWorkPrice(PriceUtil.fen2yuan(vo.getWorkPrice()));
                    vo.setCertPrice(PriceUtil.fen2yuan(vo.getCertPrice()));
                    vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
                })
                .handle();
    }
} 