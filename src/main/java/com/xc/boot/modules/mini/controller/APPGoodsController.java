package com.xc.boot.modules.mini.controller;

import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.modules.mini.model.query.MiniGoodsPageQuery;
import com.xc.boot.modules.mini.model.vo.MiniGoodsStatisticVo;
import com.xc.boot.modules.mini.model.vo.MiniGoodsVo;
import com.xc.boot.modules.mini.service.MiniGoodsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "小程序-货品接口")
@RequestMapping("/api/mini/goods")
public class APPGoodsController {
    
    private final MiniGoodsService miniGoodsService;

    @Operation(summary = "小程序货品分页列表")
    @PostMapping("/page")
    public PageResult<JSONObject> page(@Validated @RequestBody MiniGoodsPageQuery query) {
        Page<MiniGoodsVo> page = miniGoodsService.miniGoodsPage(query);
        Page<JSONObject> result = ColumnEncryptUtil.encrypt(page, MiniGoodsVo.class, "customerColumns");
        return PageResult.success(result);
    }

    @Operation(summary = "小程序货品分页列表统计")
    @PostMapping("/statistic")
    public Result<MiniGoodsStatisticVo> statistic(@Validated @RequestBody MiniGoodsPageQuery query) {
        MiniGoodsStatisticVo statistic = miniGoodsService.miniGoodsStatistic(query);
        return Result.success(statistic);
    }
}
