package com.xc.boot.modules.mini.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.mini.model.form.MiniMemberForm;
import com.xc.boot.modules.mini.model.query.MiniMemberPageQuery;
import com.xc.boot.modules.mini.model.vo.MiniMemberPageVo;
import com.xc.boot.modules.mini.service.MiniMemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 小程序会员管理控制层
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "小程序-会员管理")
@RequestMapping("/api/mini/member")
public class AppMemberController {
    
    private final MiniMemberService miniMemberService;

    @Operation(summary = "小程序会员分页列表")
    @PostMapping("/page")
    public PageResult<MiniMemberPageVo> page(@Validated @RequestBody MiniMemberPageQuery query) {
        Page<MiniMemberPageVo> page = miniMemberService.page(query);
        return PageResult.success(page);
    }

    @Operation(summary = "小程序新增会员")
    @PostMapping
    public Result<Long> add(@Validated(Create.class) @RequestBody MiniMemberForm form) {
        Long id = miniMemberService.add(form);
        return Result.success(id);
    }

    @Operation(summary = "小程序修改会员")
    @PutMapping
    public Result<?> update(@Validated(Update.class) @RequestBody MiniMemberForm form) {
        miniMemberService.update(form);
        return Result.success();
    }
} 