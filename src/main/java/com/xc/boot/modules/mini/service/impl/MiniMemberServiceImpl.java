package com.xc.boot.modules.mini.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtilV2;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.member.mapper.MemberMapper;
import com.xc.boot.modules.member.model.entity.MemberEntity;
import com.xc.boot.modules.member.model.enums.GenderEnum;
import com.xc.boot.modules.member.model.enums.SourceEnum;
import com.xc.boot.modules.mini.model.form.MiniMemberForm;
import com.xc.boot.modules.mini.model.query.MiniMemberPageQuery;
import com.xc.boot.modules.mini.model.vo.MiniMemberPageVo;
import com.xc.boot.modules.mini.service.MiniMemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.xc.boot.modules.member.model.entity.table.MemberTableDef.MEMBER;

/**
 * 小程序会员服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MiniMemberServiceImpl extends ServiceImpl<MemberMapper, MemberEntity> implements MiniMemberService {

    private final ListFillService fillService;

    @Override
    public Page<MiniMemberPageVo> page(MiniMemberPageQuery query) {
        QueryWrapper wrapper = buildWrapper(query);
        wrapper.orderBy(MEMBER.ID, false);

        int pageNum = query.getPageNum();
        int pageSize = query.getPageSize();
        
        Page<MiniMemberPageVo> page = this.mapper.paginateAs(pageNum, pageSize, wrapper, MiniMemberPageVo.class);
        List<MiniMemberPageVo> records = page.getRecords();
        this.fillList(records);
        return page;
    }

    @Override
    @Transactional
    public Long add(MiniMemberForm form) {
        // 检查手机号是否已存在
        checkMobileExists(form.getMobile(), null);
        
        MemberEntity member = BeanUtil.copyProperties(form, MemberEntity.class);
        member.setCompanyId(SecurityUtils.getCompanyId());
        
        this.save(member);
        OpLogUtils.appendOpLog("小程序-会员管理-新增会员", String.format("""
                会员姓名: %s,
                手机号: %s""", member.getName(), member.getMobile()), member);
        return member.getId();
    }

    @Override
    @Transactional
    public void update(MiniMemberForm form) {
        // 检查手机号是否已存在（排除自己）
        checkMobileExists(form.getMobile(), form.getId());
        
        MemberEntity member = this.getById(form.getId());
        if (member == null) {
            throw new BusinessException("会员不存在");
        }
        
        // 检查权限
        if (!member.getCompanyId().equals(SecurityUtils.getCompanyId())) {
            throw new BusinessException("无权操作该会员");
        }
        OpLogUtils.appendOpLog("小程序-会员管理-修改会员", String.format("""
                会员姓名: %s 改为 %s,
                手机号: %s 改为 %s""",
                member.getName(), form.getName(), member.getMobile(), form.getMobile()), member);
        BeanUtil.copyProperties(form, member);
        this.updateById(member);
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper buildWrapper(MiniMemberPageQuery query) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(MEMBER.COMPANY_ID.eq(SecurityUtils.getCompanyId()));

        // 会员姓名
        if (StringUtils.isNotBlank(query.getName())) {
            queryWrapper.and(MEMBER.NAME.like(query.getName()));
        }

        // 手机号
        if (StringUtils.isNotBlank(query.getMobile())) {
            queryWrapper.and(MEMBER.MOBILE.like(query.getMobile()));
        }

        // 入会门店
        if (StringUtils.isNotBlank(query.getMerchantIds())) {
            queryWrapper.and(MEMBER.MERCHANT_ID.in(List.of(query.getMerchantIds().split(","))));
        }

        // 性别
        if (ObjectUtil.isNotNull(query.getGender())) {
            queryWrapper.and(MEMBER.GENDER.eq(query.getGender()));
        }

        // 用户来源
        if (ObjectUtil.isNotNull(query.getSource())) {
            queryWrapper.and(MEMBER.SOURCE.eq(query.getSource()));
        }

        // 创建时间范围
        if (query.getTimeRange() != null && query.getTimeRange().length == 2) {
            queryWrapper.and(MEMBER.CREATED_AT.between(query.getTimeRange()[0], query.getTimeRange()[1]));
        }

        return queryWrapper;
    }

    /**
     * 填充关联字段
     */
    private void fillList(List<MiniMemberPageVo> list) {
        ListFillUtilV2.of(list)
                .build(fillService::getMerchantNameById, MiniMemberPageVo::getMerchantId, MiniMemberPageVo::setMerchant)
                .build(fillService::getUserNameByUserId, MiniMemberPageVo::getAdviserId, MiniMemberPageVo::setAdviser)
                .build(fillService::getUserNameByUserId, MiniMemberPageVo::getCreatedBy, MiniMemberPageVo::setCreatedByName)
                .peek(vo -> {
                    // 性别名称
                    if (vo.getGender() != null) {
                        vo.setGenderName(GenderEnum.getLabelByValue(vo.getGender()));
                    }
                    // 用户来源名称
                    if (vo.getSource() != null) {
                        vo.setSourceName(SourceEnum.getLabelByValue(vo.getSource()));
                    }
                })
                .handle();
    }

    /**
     * 检查手机号是否已存在
     */
    private void checkMobileExists(String mobile, Long excludeId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .where(MEMBER.MOBILE.eq(mobile))
                .where(MEMBER.COMPANY_ID.eq(SecurityUtils.getCompanyId()));
        
        if (excludeId != null) {
            wrapper.and(MEMBER.ID.ne(excludeId));
        }
        
        long count = this.count(wrapper);
        if (count > 0) {
            throw new BusinessException("手机号已存在");
        }
    }
} 