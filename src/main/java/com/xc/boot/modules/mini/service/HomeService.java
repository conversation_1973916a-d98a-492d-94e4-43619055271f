package com.xc.boot.modules.mini.service;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.modules.mini.model.query.GoldPriceDetailQuery;
import com.xc.boot.modules.mini.model.vo.GoldPriceDetailVO;
import com.xc.boot.modules.mini.model.vo.GoldPricePreviewVO;

import java.util.List;

/**
 * 首页服务接口
 */
public interface HomeService {

    /**
     * 获取金价预览列表
     * @return 金价预览列表
     */
    List<GoldPricePreviewVO> getGoldPricePreview();

    /**
     * 获取金价详情分页列表
     * @param query 查询参数
     * @return 金价详情分页列表
     */
    Page<GoldPriceDetailVO> getGoldPriceDetailPage(GoldPriceDetailQuery query);
} 