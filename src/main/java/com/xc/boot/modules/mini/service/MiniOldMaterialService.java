package com.xc.boot.modules.mini.service;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.modules.mini.model.query.MiniOldMaterialPageQuery;
import com.xc.boot.modules.mini.model.vo.MiniOldMaterialStatisticVo;
import com.xc.boot.modules.mini.model.vo.MiniOldMaterialVo;

/**
 * 小程序旧料服务接口
 */
public interface MiniOldMaterialService {
    
    /**
     * 小程序旧料分页列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    Page<MiniOldMaterialVo> miniOldMaterialPage(MiniOldMaterialPageQuery query);
    
    /**
     * 小程序旧料统计
     *
     * @param query 查询条件
     * @return 统计结果
     */
    MiniOldMaterialStatisticVo miniOldMaterialStatistic(MiniOldMaterialPageQuery query);
}
