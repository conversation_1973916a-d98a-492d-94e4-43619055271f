package com.xc.boot.modules.member.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "会员列表分页查询对象")
public class MemberPageQuery extends BasePageQuery {
    
    @Schema(description = "入会门店(英文逗号分割)")
    private String merchantIds;

    @Schema(description = "会员昵称")
    private String nickname;

    @Schema(description = "会员姓名")
    private String name;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "性别(0:未知｜1:男｜2:女)")
    private Integer gender;

    @Schema(description = "专属顾问(英文逗号分割)")
    private String adviserIds;

    @Schema(description = "创建人(英文逗号分割)")
    private String createdByIds;

    @Schema(description = "创建时间范围")
    private Date[] timeRange;
} 