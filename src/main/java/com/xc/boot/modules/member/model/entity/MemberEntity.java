package com.xc.boot.modules.member.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 会员实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "member")
public class MemberEntity extends BaseEntity implements CreatedByListenerFlag {
    
    /**
     * 姓名
     */
    @Column(value = "name")
    private String name;

    /**
     * 手机号
     */
    @Column(value = "mobile")
    private String mobile;

    /**
     * 昵称
     */
    @Column(value = "nickname")
    private String nickname;

    /**
     * 商家ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 入会门店
     */
    @Column(value = "merchant_id")
    private Long merchantId;

    /**
     * 用户头像
     */
    @Column(value = "avatar")
    private String avatar;

    /**
     * 文件表id
     */
    @Column(value = "avatar_id")
    private Long avatarId;

    /**
     * 性别(0:未知｜1:男｜2:女)
     */
    @Column(value = "gender")
    private Integer gender;

    /**
     * 生日
     */
    @Column(value = "birthday")
    private Date birthday;

    /**
     * 邀请人
     */
    @Column(value = "invite_id")
    private Integer inviteId;

    /**
     * 顾问ID
     */
    @Column(value = "adviser_id")
    private Long adviserId;

    /**
     * 用户来源(0:自然访问|1:朋友推荐|2:社交媒体|3:线下活动)
     */
    @Column(value = "source")
    private Integer source;

    /**
     * 创建人ID
     */
    @Column(value = "created_by")
    private Long createdBy;

    /**
     * 逻辑删除
     */
    @Column(value = "deleted_at", isLogicDelete = true)
    private Date deletedAt;
} 