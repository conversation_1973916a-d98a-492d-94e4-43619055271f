package com.xc.boot.modules.member.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户来源枚举
 */
@Getter
@AllArgsConstructor
public enum SourceEnum {
    
    NATURAL(0, "自然访问"),
    FRIEND_RECOMMEND(1, "朋友推荐"),
    SOCIAL_MEDIA(2, "社交媒体"),
    OFFLINE_ACTIVITY(3, "线下活动");
    
    private final Integer value;
    private final String label;
    
    /**
     * 根据值获取枚举
     */
    public static SourceEnum getByValue(Integer value) {
        if (value == null) {
            return NATURAL;
        }
        for (SourceEnum source : values()) {
            if (source.getValue().equals(value)) {
                return source;
            }
        }
        return NATURAL;
    }
    
    /**
     * 根据值获取标签
     */
    public static String getLabelByValue(Integer value) {
        return getByValue(value).getLabel();
    }
} 