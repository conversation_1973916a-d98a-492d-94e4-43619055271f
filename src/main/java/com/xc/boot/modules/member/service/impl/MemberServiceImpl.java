package com.xc.boot.modules.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.constant.SecurityConstants;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtilV2;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.member.mapper.MemberMapper;
import com.xc.boot.modules.member.model.entity.MemberEntity;
import com.xc.boot.modules.member.model.enums.GenderEnum;
import com.xc.boot.modules.member.model.enums.SourceEnum;
import com.xc.boot.modules.member.model.form.MemberForm;
import com.xc.boot.modules.member.model.form.MemberImportForm;
import com.xc.boot.modules.member.model.query.MemberPageQuery;
import com.xc.boot.modules.member.model.vo.MemberDetailVo;
import com.xc.boot.modules.member.model.vo.MemberImportVo;
import com.xc.boot.modules.member.model.vo.MemberPageVo;
import com.xc.boot.modules.member.service.MemberService;
import com.xc.boot.modules.order.mapper.MaterialRecycleMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptMapper;
import com.xc.boot.system.mapper.MerchantMapper;
import com.xc.boot.system.model.entity.MerchantEntity;
import com.xc.boot.system.model.vo.HeadingColumns;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.xc.boot.modules.member.model.entity.table.MemberTableDef.MEMBER;
import static com.xc.boot.modules.order.model.entity.table.MaterialRecycleTableDef.MATERIAL_RECYCLE;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptTableDef.SOLD_RECEIPT;


@Slf4j
@Service
@RequiredArgsConstructor
public class MemberServiceImpl extends ServiceImpl<MemberMapper, MemberEntity> implements MemberService {

    private final ListFillService listFillService;
    private final MerchantMapper merchantMapper;
    private final SoldReceiptMapper soldReceiptMapper;
    private final MaterialRecycleMapper materialRecycleMapper;

    @Override
    public Page<MemberPageVo> page(MemberPageQuery query) {
        QueryWrapper wrapper = buildQuery(query);
        wrapper.orderBy(MEMBER.ID, false);

        if (query.getExport().equals(1)) {
            doExport(wrapper);
            return new Page<>();
        }
        if (query.getPrint().equals(1)) {
            long count = this.count(wrapper);
            query.setPrintNum(count);
        }
        Page<MemberPageVo> page = this.mapper.paginateAs(query.getPageNum(), query.getPageSize(), wrapper, MemberPageVo.class);
        // 填充列表
        fillList(page.getRecords());
        return page;
    }

    @Override
    public MemberDetailVo detail(Long id) {
        MemberDetailVo member = this.mapper.selectOneByQueryAs(QueryWrapper.create()
                .where(MEMBER.ID.eq(id))
                .where(MEMBER.COMPANY_ID.eq(SecurityUtils.getCompanyId())), MemberDetailVo.class);
        Assert.notNull(member, "会员不存在");
        Assert.isTrue(SecurityUtils.getMerchantIds().contains(member.getMerchantId()), "无权操作该会员");
        fillDetail(member);
        return member;
    }

    @Override
    @Transactional
    public Long add(MemberForm form) {
        // 校验手机号是否已存在
        checkMobileExists(form.getMobile(), null, null);
        
        MemberEntity memberEntity = BeanUtil.copyProperties(form, MemberEntity.class);
        memberEntity.setCompanyId(SecurityUtils.getCompanyId());
        memberEntity.setId(null);
        memberEntity.setName(StringUtils.isNotBlank(form.getName()) ? form.getName() : "");
        this.mapper.insertSelective(memberEntity);

        OpLogUtils.appendOpLog("会员管理-新增会员", String.format("""
                会员姓名: %s,
                手机号: %s""", memberEntity.getName(), memberEntity.getMobile()), memberEntity);
        return memberEntity.getId();
    }

    @Override
    @Transactional
    public void update(MemberForm form) {
        MemberEntity memberEntity = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(MEMBER.ID.eq(form.getId()))
                .where(MEMBER.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        Assert.notNull(memberEntity, "会员不存在");
        Assert.isTrue(SecurityUtils.getMerchantIds().contains(memberEntity.getMerchantId()), "无权操作该会员");

        // 校验手机号是否已存在
        checkMobileExists(form.getMobile(), form.getId(), null);
        // 头像文件使用状态
        if (memberEntity.getAvatarId() != null && !memberEntity.getAvatarId().equals(form.getAvatarId())) {
            CommonUtils.updateFileStatus(memberEntity.getAvatarId(), 0);
            CommonUtils.updateFileStatus(form.getAvatarId(), 1);
        }
        
        String oldName = memberEntity.getName();
        String oldMobile = memberEntity.getMobile();
        
        BeanUtil.copyProperties(form, memberEntity);
        if(memberEntity.getAvatarId() == null) {
            memberEntity.setAvatarId(0L);
        }
        if (memberEntity.getAdviserId() == null) {
            memberEntity.setAdviserId(0L);
        }
        if (memberEntity.getGender() ==  null) {
            memberEntity.setGender(0);
        }
        if (memberEntity.getSource() == null) {
            memberEntity.setSource(0);
        }
        if (memberEntity.getInviteId() == null) {
            memberEntity.setInviteId(0);
        }
        Assert.isTrue(memberEntity.getId() != memberEntity.getInviteId().longValue(), "邀请人不能是自己");
        this.mapper.update(memberEntity, false);
        
        OpLogUtils.appendOpLog("会员管理-修改会员", String.format("""
                会员姓名: %s 修改为 %s,
                手机号: %s 修改为 %s""", oldName, memberEntity.getName(), oldMobile, memberEntity.getMobile()), memberEntity);
    }

    @Override
    @Transactional
    public void delete(Long id) {
        MemberEntity memberEntity = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(MEMBER.ID.eq(id))
                .where(MEMBER.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        Assert.notNull(memberEntity, "会员不存在");
        Assert.isTrue(SecurityUtils.getMerchantIds().contains(memberEntity.getMerchantId()), "无权操作该会员");

        // 是否可删除校验(销售、回收)
        long count = soldReceiptMapper.selectCountByQuery(QueryWrapper.create()
                .where(SOLD_RECEIPT.MEMBER_ID.eq(id))
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        Assert.isTrue(count == 0, "该会员存在销售或回收记录，无法删除");
        count += materialRecycleMapper.selectCountByQuery(QueryWrapper.create()
                .where(MATERIAL_RECYCLE.MEMBER_ID.eq(id))
                .where(MATERIAL_RECYCLE.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        Assert.isTrue(count == 0, "该会员存在销售或回收记录，无法删除");

        memberEntity.setDeletedAt(new Date());
        this.mapper.delete(memberEntity);
        
        OpLogUtils.appendOpLog("会员管理-删除会员", String.format("""
                会员姓名: %s,
                手机号: %s""", memberEntity.getName(), memberEntity.getMobile()), memberEntity.getId());
    }

    @Override
    public void template(HttpServletResponse response) {
        List<String> headers = List.of("手机号", "入会门店", "姓名", "性别", "生日");
        try {
            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("会员导入模板", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");
            // 创建空数据列表
            List<Map<String, String>> dataList = new ArrayList<>();
            // 使用EasyExcel导出，并添加注释处理器
            EasyExcel.write(response.getOutputStream())
                    .head(headers.stream().map(List::of).collect(Collectors.toList()))
                    .sheet("会员导入模板")
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(15)) // 设置固定列宽为15个字符
                    .registerWriteHandler(new CellWriteHandler() {
                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            // 为手机号列添加注释
                            if (context.getRowIndex() == 0 && context.getColumnIndex() == 0) {
                                addCellComment(context, "手机号为必填项，请确保格式正确");
                            }
                            // 为生日列添加注释
                            if (context.getRowIndex() == 0 && context.getColumnIndex() == 4) {
                                addCellComment(context, "生日格式为2024/1/1");
                            }
                        }
                        
                        /**
                         * 添加单元格注释
                         */
                        private void addCellComment(CellWriteHandlerContext context, String commentText) {
                            Sheet sheet = context.getWriteSheetHolder().getSheet();
                            Drawing<?> drawing = sheet.createDrawingPatriarch();
                            // 创建注释
                            Comment comment = drawing.createCellComment(
                                    new XSSFClientAnchor(0, 0, 0, 0, 
                                            context.getColumnIndex(), context.getRowIndex(), 
                                            context.getColumnIndex() + 3, context.getRowIndex() + 2)
                            );
                            comment.setString(new XSSFRichTextString(commentText));
                            comment.setAuthor("系统");
                            // 设置单元格注释
                            context.getCell().setCellComment(comment);
                        }
                    })
                    .doWrite(dataList);
        } catch (IOException e) {
            throw new RuntimeException("导出模板失败", e);
        }
    }

    /**
     * 校验手机号是否已存在
     */
    private void checkMobileExists(String mobile, Long excludeId, Integer rowNum) {
        QueryWrapper wrapper = QueryWrapper.create()
                .where(MEMBER.MOBILE.eq(mobile))
                .where(MEMBER.COMPANY_ID.eq(SecurityUtils.getCompanyId()));
        if (excludeId != null) {
            wrapper.where(MEMBER.ID.ne(excludeId));
        }
        long count = this.count(wrapper);
        Assert.isTrue(count == 0, (rowNum != null ? "第" + rowNum + "行：" : "") + "手机号已存在");
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper buildQuery(MemberPageQuery query) {
        QueryWrapper wrapper = QueryWrapper.create()
                .where(MEMBER.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(MEMBER.DELETED_AT.isNull());

        // 入会门店
        if (StringUtils.isNotBlank(query.getMerchantIds())) {
            List<String> merchantIdList = Arrays.asList(query.getMerchantIds().split(","));
            wrapper.where(MEMBER.MERCHANT_ID.in(merchantIdList));
        }
        
        // 会员昵称
        if (StringUtils.isNotBlank(query.getNickname())) {
            wrapper.where(MEMBER.NICKNAME.like(query.getNickname()));
        }
        
        // 会员姓名
        if (StringUtils.isNotBlank(query.getName())) {
            wrapper.where(MEMBER.NAME.like(query.getName()));
        }
        
        // 手机号
        if (StringUtils.isNotBlank(query.getMobile())) {
            wrapper.where(MEMBER.MOBILE.eq(query.getMobile()));
        }
        
        // 性别
        if (query.getGender() != null) {
            wrapper.where(MEMBER.GENDER.eq(query.getGender()));
        }
        
        // 专属顾问
        if (StringUtils.isNotBlank(query.getAdviserIds())) {
            List<String> adviserIdList = Arrays.asList(query.getAdviserIds().split(","));
            wrapper.where(MEMBER.ADVISER_ID.in(adviserIdList));
        }
        
        // 创建人
        if (StringUtils.isNotBlank(query.getCreatedByIds())) {
            List<String> createdByIdList = Arrays.asList(query.getCreatedByIds().split(","));
            wrapper.where(MEMBER.CREATED_BY.in(createdByIdList));
        }
        
        // 创建时间
        if (query.getTimeRange() != null && query.getTimeRange().length == 2) {
            wrapper.where(MEMBER.CREATED_AT.between(query.getTimeRange()));
        }

        // ids
        if (CollectionUtil.isNotEmpty(query.getIds())) {
            wrapper.where(MEMBER.ID.in(query.getIds()));
        }
        return wrapper;
    }

    /**
     * 填充列表数据
     */
    private void fillList(List<MemberPageVo> list) {
        ListFillUtilV2.of(list)
                .build(listFillService::getMerchantNameById, MemberPageVo::getMerchantId, MemberPageVo::setMerchant)
                .build(listFillService::getUserNameByUserId, MemberPageVo::getAdviserId, MemberPageVo::setAdviser)
                .build(listFillService::getUserNameByUserId, MemberPageVo::getCreatedBy, MemberPageVo::setCreatedByName)
                .build(listFillService::getMemberNameById, MemberPageVo::getInviteId, MemberPageVo::setInvite)
                .peek(vo -> {
                    vo.setGenderName(GenderEnum.getLabelByValue(vo.getGender()));
                    vo.setSourceName(SourceEnum.getLabelByValue(vo.getSource()));
                    if (vo.getBirthday() != null) {
                        LocalDate date = LocalDate.parse(vo.getBirthday(), DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss"));
                        vo.setBirthday(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    }
                })
                .handle();
    }

    /**
     * 填充详情数据
     */
    private void fillDetail(MemberDetailVo member) {
        if (member.getMerchantId() != null) {
            String merchantName = listFillService.getMerchantNameById(Set.of(member.getMerchantId())).get(member.getMerchantId().toString());
            member.setMerchant(merchantName != null ? merchantName : "");
        }
        if (member.getAdviserId() != null) {
            String adviserName = listFillService.getUserNameByUserId(Set.of(member.getAdviserId())).get(member.getAdviserId().toString());
            member.setAdviser(adviserName != null ? adviserName : "");
        }
        if (member.getCreatedBy() != null) {
            String userName = listFillService.getUserNameByUserId(Set.of(member.getCreatedBy())).get(member.getCreatedBy().toString());
            member.setCreatedByName(userName != null ? userName : "");
        }
        member.setGenderName(GenderEnum.getLabelByValue(member.getGender()));
        member.setSourceName(SourceEnum.getLabelByValue(member.getSource()));
    }

    /**
     * 导出
     */
    private void doExport(QueryWrapper query) {
        ExcelUtil.of(this.mapper, query, MemberPageVo.class, "members", "会员列表")
                .getData((mapper, wrapper) -> {
                    List<MemberPageVo> voList = mapper.selectListByQueryAs(wrapper, MemberPageVo.class);
                    fillList(voList);
                    return voList;
                }).signHeadingConfig(headingColumns -> {
                    ArrayList<HeadingColumns> newColumns = new ArrayList<>();
                    for (HeadingColumns columns : headingColumns) {
                        if (columns.getProp().equals("gender")) {
                            HeadingColumns newColumn = BeanUtil.copyProperties(columns, HeadingColumns.class);
                            newColumn.setProp("genderName");
                            newColumns.add(newColumn);
                        }else {
                            newColumns.add(columns);
                        }
                    }
                    return newColumns;
                })
                .doExport();
    }

    @Override
    @Transactional
    public void importMembers(MemberImportForm form) {
        try {
            // 使用EasyExcel读取Excel文件
            List<MemberImportVo> importDataList = EasyExcel.read(form.getFile().getInputStream())
                    .head(MemberImportVo.class)
                    .sheet()
                    .doReadSync();
            
            if (CollectionUtil.isEmpty(importDataList)) {
                throw new BusinessException("导入文件为空");
            }
            Long merchantId = SecurityUtils.getMerchantIds().stream().max(Comparator.comparingInt(Long::intValue)).orElse(0L);
            Assert.isTrue(merchantId > 0, "当前登录人未配置门店");

            // 校验数据并处理
            List<MemberEntity> memberEntities = new ArrayList<>();
            Set<String> mobileSet = new HashSet<>();
            Set<String> merchantNames = importDataList.stream().map(MemberImportVo::getMerchantName).collect(Collectors.toSet());
            Map<String, MerchantEntity> merchantNameMap = merchantMapper.selectListByQuery(QueryWrapper.create()
                            .where(MerchantEntity::getName).in(merchantNames)
                            .where(MerchantEntity::getCompanyId).eq(SecurityUtils.getCompanyId()))
                    .stream().collect(Collectors.toMap(MerchantEntity::getName, e -> e, (e1, e2) -> e1));
            for (int i = 0; i < importDataList.size(); i++) {
                MemberImportVo importVo = importDataList.get(i);
                int rowNum = i + 2; // Excel行号从2开始（第1行是表头）
                // 校验手机号
                if (StringUtils.isBlank(importVo.getMobile())) {
                    throw new BusinessException(String.format("第%d行：手机号不能为空", rowNum));
                }
                // 校验手机号格式
                if (!Pattern.matches(SecurityConstants.MOBILE_PATTERN, importVo.getMobile())) {
                    throw new BusinessException(String.format("第%d行：手机号格式不正确", rowNum));
                }
                // 校验手机号唯一性
                if (mobileSet.contains(importVo.getMobile())) {
                    throw new BusinessException(String.format("第%d行：手机号重复", rowNum));
                }
                mobileSet.add(importVo.getMobile());
                // 校验手机号在系统中是否已存在
                checkMobileExists(importVo.getMobile(), null, rowNum);
                
                // 创建会员实体
                MemberEntity memberEntity = new MemberEntity();
                memberEntity.setMobile(importVo.getMobile());
                memberEntity.setName(StringUtils.isNotBlank(importVo.getName()) ? importVo.getName() : "");
                memberEntity.setCompanyId(SecurityUtils.getCompanyId());
                // 如果未查询到该门店名称 提示门店不存在 如果未填写门店 默认取当前登录人门店id倒序第一个
                if (StringUtils.isNotBlank(importVo.getMerchantName()) && !merchantNameMap.containsKey(importVo.getMerchantName())) {
                    throw new BusinessException(String.format("第%d行：门店不存在", rowNum));
                }
                if (merchantNameMap.containsKey(importVo.getMerchantName())) {
                    memberEntity.setMerchantId(merchantNameMap.get(importVo.getMerchantName()).getId());
                }else {
                    memberEntity.setMerchantId(merchantId);
                }
                // 处理性别
                if (StringUtils.isNotBlank(importVo.getGender())) {
                    if ("男".equals(importVo.getGender()) || "1".equals(importVo.getGender())) {
                        memberEntity.setGender(1);
                    } else if ("女".equals(importVo.getGender()) || "2".equals(importVo.getGender())) {
                        memberEntity.setGender(2);
                    } else {
                        memberEntity.setGender(0);
                    }
                } else {
                    memberEntity.setGender(0);
                }
                
                // 处理生日
                memberEntity.setBirthday(importVo.getBirthday());
                
                // 设置默认值
                memberEntity.setSource(0); // 默认来源为自然访问
                
                memberEntities.add(memberEntity);
            }
            
            // 批量插入会员
            if (!memberEntities.isEmpty()) {
                this.saveBatch(memberEntities);
                // 记录操作日志
                OpLogUtils.appendOpLog("会员管理-批量导入会员", 
                    String.format("成功导入%d个会员", memberEntities.size()), 
                    memberEntities.stream().map(MemberEntity::getMobile).collect(Collectors.toList()));
            }
        } catch (IOException e) {
            throw new RuntimeException("读取导入文件失败", e);
        }
    }

    @Override
    public String getMemberName(Long memberId) {
        MemberEntity memberEntity = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(MEMBER.ID.eq(memberId))
                .select(MEMBER.ID, MEMBER.NAME, MEMBER.MOBILE));
        return memberEntity == null ? "" : memberEntity.getName() + "(" + memberEntity.getMobile() + ")";
    }

} 