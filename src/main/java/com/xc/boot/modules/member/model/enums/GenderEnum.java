package com.xc.boot.modules.member.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 性别枚举
 */
@Getter
@AllArgsConstructor
public enum GenderEnum {
    
    UNKNOWN(0, "未知"),
    MALE(1, "男"),
    FEMALE(2, "女");
    
    private final Integer value;
    private final String label;
    
    /**
     * 根据值获取枚举
     */
    public static GenderEnum getByValue(Integer value) {
        if (value == null) {
            return UNKNOWN;
        }
        for (GenderEnum gender : values()) {
            if (gender.getValue().equals(value)) {
                return gender;
            }
        }
        return UNKNOWN;
    }
    
    /**
     * 根据值获取标签
     */
    public static String getLabelByValue(Integer value) {
        return getByValue(value).getLabel();
    }
} 