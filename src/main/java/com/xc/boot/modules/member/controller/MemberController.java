package com.xc.boot.modules.member.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.member.model.form.MemberForm;
import com.xc.boot.modules.member.model.form.MemberImportForm;
import com.xc.boot.modules.member.model.query.MemberPageQuery;
import com.xc.boot.modules.member.model.vo.MemberDetailVo;
import com.xc.boot.modules.member.model.vo.MemberPageVo;
import com.xc.boot.modules.member.service.MemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 会员控制层
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "会员管理-会员列表")
@RequestMapping("/api/member")
public class MemberController {
    private final MemberService memberService;

    @Operation(summary = "会员分页列表")
    @PostMapping("/page")
    public PageResult<MemberPageVo> page(@Validated @RequestBody MemberPageQuery query) {
        Page<MemberPageVo> page = memberService.page(query);
        return PageResult.success(page);
    }

    @Operation(summary = "会员详情")
    @GetMapping("/detail")
    public Result<MemberDetailVo> detail(@RequestParam @Valid @NotNull(message = "id不能为空") Long id) {
        MemberDetailVo member = memberService.detail(id);
        return Result.success(member);
    }

    @Operation(summary = "新增会员")
    @PostMapping
    public Result<Long> add(@Validated(Create.class) @RequestBody MemberForm form) {
        Long id = memberService.add(form);
        return Result.success(id);
    }

    @Operation(summary = "修改会员")
    @PutMapping
    public Result<?> update(@Validated(Update.class) @RequestBody MemberForm form) {
        memberService.update(form);
        return Result.success();
    }

    @Operation(summary = "删除会员")
    @DeleteMapping("/delete")
    public Result<?> delete(@RequestBody DeleteRequest form) {
        memberService.delete(form.getId());
        return Result.success();
    }

    @Operation(summary = "会员导入模板")
    @GetMapping("/template")
    public void template(HttpServletResponse response) {
        memberService.template(response);
    }

    @Operation(summary = "导入会员")
    @PostMapping("/import")
    public Result<?> importMembers(@Validated @ModelAttribute MemberImportForm form) {
        memberService.importMembers(form);
        return Result.success();
    }

}
