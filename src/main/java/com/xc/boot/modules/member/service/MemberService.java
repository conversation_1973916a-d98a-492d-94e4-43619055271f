package com.xc.boot.modules.member.service;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.modules.member.model.entity.MemberEntity;
import com.xc.boot.modules.member.model.form.MemberForm;
import com.xc.boot.modules.member.model.form.MemberImportForm;
import com.xc.boot.modules.member.model.query.MemberPageQuery;
import com.xc.boot.modules.member.model.vo.MemberDetailVo;
import com.xc.boot.modules.member.model.vo.MemberPageVo;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 会员Service接口
 */
public interface MemberService {
    
    /**
     * 会员分页列表
     */
    Page<MemberPageVo> page(MemberPageQuery query);
    
    /**
     * 会员详情
     */
    MemberDetailVo detail(Long id);
    
    /**
     * 新增会员
     */
    Long add(MemberForm form);
    
    /**
     * 修改会员
     */
    void update(MemberForm form);
    
    /**
     * 删除会员
     */
    void delete(Long id);

    /**
     * 获取导入模板
     */
    void template(HttpServletResponse response);

    /**
     * 导入会员
     */
    void importMembers(MemberImportForm form);

    /**
     * 查询会员名称
     */
    String getMemberName(Long memberId);

}
