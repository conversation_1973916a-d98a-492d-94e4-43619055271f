package com.xc.boot.modules.member.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "会员导入VO")
public class MemberImportVo {
    
    @ExcelProperty("手机号")
    @Schema(description = "手机号")
    private String mobile;
    
    @ExcelProperty("入会门店")
    @Schema(description = "入会门店")
    private String merchantName;
    
    @ExcelProperty("姓名")
    @Schema(description = "姓名")
    private String name;
    
    @ExcelProperty("性别")
    @Schema(description = "性别")
    private String gender;
    
    @ExcelProperty("生日")
    @Schema(description = "生日")
    private Date birthday;
} 