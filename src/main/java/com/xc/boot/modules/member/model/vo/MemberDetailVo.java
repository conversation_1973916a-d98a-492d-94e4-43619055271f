package com.xc.boot.modules.member.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "会员详情VO")
public class MemberDetailVo {
    
    @Schema(description = "id")
    private Long id;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "商家ID")
    private Long companyId;

    @Schema(description = "入会门店ID")
    private Long merchantId;

    @Schema(description = "入会门店")
    private String merchant;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "文件表id")
    private Long avatarId;

    @Schema(description = "性别(0:未知｜1:男｜2:女)")
    private Integer gender;

    @Schema(description = "性别名称")
    private String genderName;

    @Schema(description = "生日")
    private Date birthday;

    @Schema(description = "邀请人")
    private String invite;

    @Schema(description = "顾问ID")
    private Long adviserId;

    @Schema(description = "专属顾问")
    private String adviser;

    @Schema(description = "用户来源(0:自然访问|1:朋友推荐|2:社交媒体|3:线下活动)")
    private Integer source;

    @Schema(description = "用户来源名称")
    private String sourceName;

    @Schema(description = "创建人ID")
    private Long createdBy;

    @Schema(description = "创建人姓名")
    private String createdByName;
} 