package com.xc.boot.modules.member.model.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.constant.SecurityConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import java.util.Date;

@Data
@Schema(description = "会员表单")
public class MemberForm {
    
    @Schema(description = "会员id(修改时)")
    @NotNull(message = "会员id不能为空", groups = {Update.class})
    private Long id;

    @Schema(description = "姓名")
    @Length(max = 64, message = "姓名长度不能超过64")
    private String name;

    @Schema(description = "手机号")
    @NotBlank(message = "手机号不能为空", groups = {Create.class, Update.class})
    @Pattern(regexp = SecurityConstants.MOBILE_PATTERN, message = "手机号码格式不正确")
    @Length(max = 64, message = "手机号长度不能超过64")
    private String mobile;

    @Schema(description = "昵称")
    @Length(max = 64, message = "昵称长度不能超过64")
    private String nickname;

    @Schema(description = "入会门店")
    @NotNull(message = "入会门店不能为空", groups = {Create.class, Update.class})
    private Long merchantId;

    @Schema(description = "用户头像")
    @Length(max = 255, message = "用户头像长度不能超过255")
    private String avatar;

    @Schema(description = "文件表id")
    private Long avatarId;

    @Schema(description = "性别(0:未知｜1:男｜2:女)")
    @Range(min = 0, max = 2, message = "数值范围错误")
    private Integer gender;

    @Schema(description = "生日")
    private Date birthday;

    @Schema(description = "邀请人")
    private Integer inviteId;

    @Schema(description = "顾问ID")
    private Long adviserId;

    @Schema(description = "用户来源(0:自然访问|1:朋友推荐|2:社交媒体|3:线下活动)")
    @Range(min = 0, max = 3, message = "数值范围错误")
    private Integer source;
} 