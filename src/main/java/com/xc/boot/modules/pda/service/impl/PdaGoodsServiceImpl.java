package com.xc.boot.modules.pda.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrBuilder;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import com.xc.boot.modules.pda.mapper.GoodsHasRfidMapper;
import com.xc.boot.modules.pda.model.entity.GoodsHasRfidEntity;
import com.xc.boot.modules.pda.model.form.RfidBindForm;
import com.xc.boot.modules.pda.model.vo.goodsBindVo;
import com.xc.boot.modules.pda.service.PdaGoodsService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;


@Service
@RequiredArgsConstructor
public class PdaGoodsServiceImpl extends ServiceImpl<GoodsMapper, GoodsEntity> implements PdaGoodsService {
    private final GoodsHasRfidMapper goodsHasRfidMapper;
    private final ListFillService listFillService;

    @Override
    public goodsBindVo selectOne(String goodsSn, Boolean isBind) {
        QueryWrapper wrapper = QueryWrapper.create()
                .where(GoodsEntity::getGoodsSn).eq(goodsSn)
                .where(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .where(GoodsEntity::getNum).gt(0);
        long count = this.count(wrapper);
        Assert.isTrue(count <= 1, isBind? "货品未绑定rfid" : "批货不支持绑定rfid");
        Assert.isTrue(count > 0, "未查询到货品");
        GoodsEntity goods = this.getOne(QueryWrapper.create()
                .where(GoodsEntity::getGoodsSn).eq(goodsSn)
                .where(GoodsEntity::getNum).gt(0)
                .where(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .where(GoodsEntity::getMerchantId).in(SecurityUtils.getMerchantIds()));
        Assert.notNull(goods, "未查询到货品，请检查货品库存");
        Assert.isTrue(goods.getStockNum() + goods.getFrozenNum() > 0, "未查询到货品，请检查货品库存");
        Assert.isTrue(goods.getNum() == 1, isBind? "货品未绑定rfid" : "批货不支持绑定rfid");

        GoodsHasRfidEntity rfid = goodsHasRfidMapper.selectOneByQuery(QueryWrapper.create()
                .where(GoodsHasRfidEntity::getGoodsId).eq(goods.getId())
                .where(GoodsHasRfidEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));
        String merchant = listFillService.getMerchantNameById(Set.of(goods.getMerchantId())).get(goods.getMerchantId().toString());
        String category = CategoryEnum.getByValue(goods.getCategoryId().longValue()).getLabel();
        String subclass = listFillService.getSubclassNameById(Set.of(goods.getSubclassId())).get(goods.getSubclassId().toString());
        String counter = listFillService.getCounterNameById(Set.of(goods.getCounterId())).get(goods.getCounterId().toString());
        String rfidString = Objects.nonNull(rfid) ? rfid.getRfid() : "";
        goodsBindVo goodsBindVo = new goodsBindVo()
                .setId(goods.getId())
                .setGoodsSn(goods.getGoodsSn())
                .setName(goods.getName())
                .setMerchant(merchant)
                .setCategory(category)
                .setSubclass(subclass)
                .setRfid(rfidString)
                .setCounter(counter)
                .setStockNum(goods.getStockNum())
                .setRfidStatus(StringUtils.isNotBlank(rfidString));
        List<GoodsHasImagesEntity> list = listFillService.getGoodsImgByGoodsId(Set.of(goods.getId())).get(goods.getId().toString());
        goodsBindVo.setImages(list);
        if (CollectionUtil.isNotEmpty(list)) {
            goodsBindVo.setImage(list.getFirst().getUrl());
        }
        return goodsBindVo;
    }

    @Override
    public void bind(RfidBindForm form) {
        StrBuilder logBuilder = new StrBuilder().append("RFID: ");
        GoodsHasRfidEntity byRfid = goodsHasRfidMapper.selectOneByQuery(QueryWrapper.create()
                .where(GoodsHasRfidEntity::getRfid).eq(form.getRfid())
                .where(GoodsHasRfidEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));

        GoodsHasRfidEntity byGoodsId = goodsHasRfidMapper.selectOneByQuery(QueryWrapper.create()
                .where(GoodsHasRfidEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .where(GoodsHasRfidEntity::getGoodsId).eq(form.getGoodsId()));

        GoodsEntity goods = this.getById(form.getGoodsId());

        Assert.notNull(goods, "商品不存在");
        Assert.isTrue(byRfid == null || byRfid.getGoodsId().equals(form.getGoodsId()), "此RFID已绑定其他货品");
        Assert.isTrue(byGoodsId == null || !byGoodsId.getRfid().equals(form.getRfid()), "此货品已绑定该RFID");

        if (byGoodsId != null) {
            Assert.isTrue(goods.getTakeStatus().equals(0), "此货品正在盘点中，无法换绑RFID，请结束盘点后再试");
            logBuilder.append(byGoodsId.getRfid());
            byGoodsId.setRfid(form.getRfid());
            goodsHasRfidMapper.update(byGoodsId);
            OpLogUtils.appendGoodsLog("PDA货品-换绑RFID", logBuilder.append(OpLogUtils.MODIFY_STRING).append(form.getRfid()).toString(), null, goods);
            return;
        }

        GoodsHasRfidEntity goodsHasRfid = new GoodsHasRfidEntity()
                .setCompanyId(SecurityUtils.getCompanyId())
                .setRfid(form.getRfid())
                .setGoodsId(form.getGoodsId())
                .setGoodsSn(goods.getGoodsSn());
        OpLogUtils.appendGoodsLog("PDA货品-绑定RFID", logBuilder.append(form.getRfid()).toString(), null, goods);
        goodsHasRfidMapper.insertSelective(goodsHasRfid);
    }

    @Override
    public void unbind(RfidBindForm form) {
        GoodsHasRfidEntity rfid = goodsHasRfidMapper.selectOneByQuery(QueryWrapper.create()
                .where(GoodsHasRfidEntity::getGoodsId).eq(form.getGoodsId())
                .where(GoodsHasRfidEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));
        GoodsEntity goodsEntity = this.mapper.selectOneById(form.getGoodsId());
        Assert.notNull(goodsEntity, "货品不存在");
        Assert.notNull(rfid, "货品未绑定rfid");
        Assert.isTrue(goodsEntity.getTakeStatus().equals(0), "此货品正在盘点中，无法解绑RFID，请结束盘点后再试");
        goodsHasRfidMapper.delete(rfid);
        OpLogUtils.appendGoodsLog("PDA货品-解绑RFID",  new StrBuilder("RFID: ")
                .append(rfid.getRfid()).append(OpLogUtils.NEW_LINE)
                .append("货品条码: ").append(rfid.getGoodsSn()).toString(), null, goodsEntity);
    }
}
