package com.xc.boot.modules.pda.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "rfid盘点表单")
public class RfidTakeForm {
    @Schema(description = "盘点单id")
    @NotNull(message = "盘点单id不能为空")
    private Long id;

    @Schema(description = "rfid列表")
    private List<String> rfidList;
}
