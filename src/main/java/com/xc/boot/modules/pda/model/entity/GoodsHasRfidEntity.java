package com.xc.boot.modules.pda.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 货品rfid关联实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_has_rfid")
public class GoodsHasRfidEntity extends BaseEntity {
    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 货品ID
     */
    @Column(value = "goods_id")
    private Long goodsId;

    /**
     * 货品SN
     */
    @Column(value = "goods_sn")
    private String goodsSn;

    /**
     * RFID
     */
    @Column(value = "rfid")
    private String rfid;
}