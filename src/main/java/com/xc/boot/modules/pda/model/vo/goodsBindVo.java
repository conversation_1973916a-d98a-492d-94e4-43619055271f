package com.xc.boot.modules.pda.model.vo;

import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName vo
 * @Date: 2025/6/14 10:27
 * @Description: 描述
 */
@Data
@Schema(description = "绑定rfidVo")
@Accessors(chain = true)
public class goodsBindVo {
    private Long id;
    @Schema(description = "门店")
    private String merchant;
    @Schema(description = "大类")
    private String category;
    @Schema(description = "柜台")
    private String counter;
    @Schema(description = "小类")
    private String subclass;
    @Schema(description = "货品条码")
    private String goodsSn;
    @Schema(description = "货品名称")
    private String name;
    @Schema(description = "rfid")
    private String rfid;
    @Schema(description = "库存数")
    private Integer stockNum;
    @Schema(description = "绑定状态")
    private Boolean rfidStatus;
    @Schema(description = "图片")
    private String image;
    @Schema(description = "图片")
    private List<GoodsHasImagesEntity> images;
}
