package com.xc.boot.modules.pda.model.vo;

import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Schema(description = "盘点单货品明细vo")
public class PdaTakeGoodsPageVo {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "货品id")
    private Long goodsId;

    @Schema(description = "所属柜台ID")
    private Long counterId;

    @Schema(description = "柜台")
    private String counter;

    @Schema(description = "货品条码")
    private String goodsSn;

    @Schema(description = "货品名称")
    private String name;

    @Schema(description = "标签单价(元)")
    private String tagPrice;

    @Schema(description = "商品图片列表")
    private List<GoodsHasImagesEntity> image = new ArrayList<>();
}
