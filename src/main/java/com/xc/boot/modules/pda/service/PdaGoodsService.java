package com.xc.boot.modules.pda.service;

import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.pda.model.form.RfidBindForm;
import com.xc.boot.modules.pda.model.vo.goodsBindVo;

public interface PdaGoodsService extends IService<GoodsEntity> {

    /**
     * 待绑/已绑货品查询
     * @param goodsSn 货品条码
     * @param isBind 是否是查询已绑货品
     * @return
     */
    goodsBindVo selectOne(String goodsSn, Boolean isBind);

    void bind(RfidBindForm form);

    void unbind(RfidBindForm form);
}
