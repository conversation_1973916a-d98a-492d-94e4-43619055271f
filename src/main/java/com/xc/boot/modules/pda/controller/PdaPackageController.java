package com.xc.boot.modules.pda.controller;

import com.xc.boot.common.result.Result;
import com.xc.boot.system.model.vo.PdaPackageVo;
import com.xc.boot.system.service.PdaPackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "PDA-包管理")
@RequestMapping("/api/pda/package")
public class PdaPackageController {
    private final PdaPackageService pdaPackageService;

    @Operation(summary = "根据PDA类型获取当前生效的最新包")
    @GetMapping("/latest")
    public Result<PdaPackageVo> getLatestActivePackageByType(@RequestParam @Valid @NotNull(message = "PDA类型不能为空") String type,
                                                             @RequestParam @Valid @NotNull(message = "当前版本号不能为空") String version) {
        return Result.success(pdaPackageService.getLatestActivePackageByType(type, version));
    }

}
