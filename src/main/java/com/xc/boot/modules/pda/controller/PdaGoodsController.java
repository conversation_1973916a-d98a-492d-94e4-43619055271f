package com.xc.boot.modules.pda.controller;

import cn.hutool.core.lang.Assert;
import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Delete;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.pda.model.form.RfidBindForm;
import com.xc.boot.modules.pda.model.vo.goodsBindVo;
import com.xc.boot.modules.pda.service.PdaGoodsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "PDA-货品")
@RequestMapping("/api/pda/goods")
public class PdaGoodsController {
    private final PdaGoodsService pdaGoodsService;

    @Operation(summary = "绑定货品查询")
    @GetMapping
    public Result<goodsBindVo> selectOne(@Validated @RequestParam @NotNull(message = "id不能为空") String goodsSn) {
        goodsBindVo vo = pdaGoodsService.selectOne(goodsSn, false);
        return Result.success(vo);
    }

    @Operation(summary = "已绑定rfid货品查询")
    @GetMapping("/rfid")
    public Result<goodsBindVo> selectRfidGoods(@Validated @RequestParam @NotNull(message = "id不能为空") String goodsSn) {
        goodsBindVo vo = pdaGoodsService.selectOne(goodsSn, true);
        Assert.isTrue(vo.getRfidStatus(), "货品未绑定rfid");
        return Result.success(vo);
    }

    @Operation(summary = "绑定/换绑rfid")
    @PostMapping("bind")
    public Result<?> bind(@Validated(Create.class) @RequestBody RfidBindForm form) {
        pdaGoodsService.bind(form);
        return Result.success();
    }

    @Operation(summary = "解绑")
    @PostMapping("unbind")
    public Result<?> unbind(@Validated(Delete.class) @RequestBody RfidBindForm form) {
        pdaGoodsService.unbind(form);
        return Result.success();
    }
}
