package com.xc.boot.modules.pda.model.result;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.ResultCode;
import com.xc.boot.config.MybatisFlexConfiguration;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页响应结构体
 *
 * <AUTHOR>
 * @since 2022/2/18
 */
@Data
public class PdaPageResult<T> implements Serializable {

    private String code;

    private List<T> data;

    private String msg;

    private List<String> sqlDebug;

    private long total;

    public static <T> PdaPageResult<T> success(Page<T> page) {
        PdaPageResult<T> result = new PdaPageResult<>();
        result.setCode(ResultCode.SUCCESS.getCode());
        result.setTotal(page.getTotalRow());
        result.setData(page.getRecords());
        result.setMsg(ResultCode.SUCCESS.getMsg());
        result.setSqlDebug(MybatisFlexConfiguration.getSqlDebugAndClear());
        return result;
    }

}
