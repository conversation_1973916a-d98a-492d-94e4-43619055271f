package com.xc.boot.modules.pda.model.form;

import com.xc.boot.common.annotation.validGroup.Create;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName RfidBindForm
 * @Date: 2025/6/14 10:52
 * @Description: 描述
 */
@Data
public class RfidBindForm {
    @NotNull(message = "goodsId不能为空")
    private Long goodsId;

    @NotNull(message = "rfid不能为空", groups = {Create.class})
    private String rfid;
}
