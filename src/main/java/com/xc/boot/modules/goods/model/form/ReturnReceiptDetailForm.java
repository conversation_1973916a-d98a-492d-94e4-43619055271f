package com.xc.boot.modules.goods.model.form;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "退货单详情查询form")
public class ReturnReceiptDetailForm extends BasePageQuery {
    @Schema(description = "出库单id")
    @NotNull(message = "ID不能为空")
    private Long id;
}
