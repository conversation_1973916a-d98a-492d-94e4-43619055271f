package com.xc.boot.modules.goods.model.vo;

import com.xc.boot.common.annotation.GoodsColumn;
import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@Schema(description = "盘点单货品明细vo")
public class TakeGoodsPageVo {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "货品id")
    private Long goodsId;

    @Schema(description = "应盘数量")
    private Integer totalNum;

    @Schema(description = "实盘数量")
    private Integer takeNum;

    @Schema(description = "盘盈数量")
    private Integer surplusNum;

    @Schema(description = "盘亏数量")
    private Integer lossNum;

    @Schema(description = "盘点人")
    private Long takeBy;

    @Schema(description = "盘点人名称")
    private String takeByName;

    @Schema(description = "盘点时间")
    private Date takeAt;

    @Schema(description = "所属商户ID")
    private Long companyId;

    @Schema(description = "所属门店ID")
    private Long merchantId;

    @Schema(description = "所属门店")
    @GoodsColumn(value = "merchant_id", signAddList = false)
    private String merchant;

    @Schema(description = "所属柜台ID")
    private Long counterId;

    @Schema(description = "柜台")
    @GoodsColumn(value = "counter_id", signAddList = false)
    private String counter;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "供应商")
    @GoodsColumn(value = "supplier_id", signAddList = false)
    private String supplier;

    @Schema(description = "所属大类ID")
    private Long categoryId;

    @Schema(description = "所属大类")
    @GoodsColumn(value = "category_id", signAddList = false)
    private String category;

    @Schema(description = "所属小类ID")
    private Long subclassId;

    @Schema(description = "所属小类")
    @GoodsColumn(value = "subclass_id", signAddList = false)
    private String subclass;

    @Schema(description = "品牌ID")
    private Long brandId;

    @Schema(description = "品牌")
    @GoodsColumn(value = "brand_id", signAddList = false)
    private String brand;

    @Schema(description = "款式ID")
    private Long styleId;

    @Schema(description = "款式")
    @GoodsColumn(value = "style_id", signAddList = false)
    private String style;

    @Schema(description = "成色ID")
    private Long qualityId;

    @Schema(description = "成色")
    @GoodsColumn(value = "quality_id", signAddList = false)
    private String quality;

    @Schema(description = "工艺ID")
    private Long technologyId;

    @Schema(description = "工艺")
    @GoodsColumn(value = "technology_id", signAddList = false)
    private String technology;

    @Schema(description = "主石ID")
    private Long mainStoneId;

    @Schema(description = "主石")
    @GoodsColumn(value = "main_stone_id", signAddList = false)
    private String mainStone;

    @Schema(description = "辅石ID")
    private Long subStoneId;

    @Schema(description = "辅石")
    @GoodsColumn(value = "sub_stone_id", signAddList = false)
    private String subStone;

    @Schema(description = "货品条码")
    @GoodsColumn(value = "goods_sn", signAddList = false)
    private String goodsSn;

    @Schema(description = "货品名称")
    @GoodsColumn(value = "name", signAddList = false)
    private String name;

    @Schema(description = "销售方式(1:按重量,2:按数量)")
    @GoodsColumn(value = "sales_type", signAddList = false)
    private String salesType;

    @Schema(description = "批次号")
    @GoodsColumn(value = "batch_no", signAddList = false)
    private String batchNo;

    @Schema(description = "证书号")
    @GoodsColumn(value = "cert_no", signAddList = false)
    private String certNo;

    @Schema(description = "备注")
    @GoodsColumn(value = "remark", signAddList = false)
    private String remark;

    @Schema(description = "重量(g)")
    @GoodsColumn(value = "weight", signAddList = false)
    private BigDecimal weight;

    @Schema(description = "净金重(g)")
    @GoodsColumn(value = "net_gold_weight", signAddList = false)
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")

    @GoodsColumn(value = "net_silver_weight", signAddList = false)
    private BigDecimal netSilverWeight;

    @Schema(description = "主石数")
    @GoodsColumn(value = "main_stone_count", signAddList = false)
    private String mainStoneCount;

    @Schema(description = "主石重(ct)")
    @GoodsColumn(value = "main_stone_weight", signAddList = false)
    private BigDecimal mainStoneWeight;

    @Schema(description = "辅石数")
    @GoodsColumn(value = "sub_stone_count", signAddList = false)
    private String subStoneCount;

    @Schema(description = "辅石重(ct)")
    @GoodsColumn(value = "sub_stone_weight", signAddList = false)
    private BigDecimal subStoneWeight;

    @Schema(description = "圈口")
    @GoodsColumn(value = "circle_size", signAddList = false)
    private String circleSize;

    @Schema(description = "成本单价(元)")
    @GoodsColumn(value = "cost_price", signAddList = false)
    private BigDecimal costPrice;

    @Schema(description = "金进单价(元)")
    @GoodsColumn(value = "gold_price", signAddList = false)
    private BigDecimal goldPrice;

    @Schema(description = "金进金额(元)")
    @GoodsColumn(value = "gold_price", signAddList = false)
    private BigDecimal goldAmount;

    @Schema(description = "银进单价(元)")
    @GoodsColumn(value = "silver_price", signAddList = false)
    private BigDecimal silverPrice;

    @Schema(description = "银进金额(元)")
    @GoodsColumn(value = "silver_price", signAddList = false)
    private BigDecimal silverAmount;

    @Schema(description = "进工费单价(元)")
    @GoodsColumn(value = "work_price", signAddList = false)
    private BigDecimal workPrice;

    @Schema(description = "进工费金额(元)")
    @GoodsColumn(value = "work_price", signAddList = false)
    private BigDecimal workAmount;

    @Schema(description = "证书费(元)")
    @GoodsColumn(value = "cert_price", signAddList = false)
    private BigDecimal certPrice;

    @Schema(description = "工费单价(元)")
    @GoodsColumn(value = "sale_work_price", signAddList = false)
    private BigDecimal saleWorkPrice;

    @Schema(description = "标签单价(元)")
    @GoodsColumn(value = "tag_price", signAddList = false)
    private BigDecimal tagPrice;

    @Schema(description = "自定义字段")
    private List<CustomColumnItemDTO> customerColumns = new ArrayList<>();

    @Schema(description = "商品图片列表")
    @GoodsColumn(value = "image", signAddList = false)
    private List<GoodsHasImagesEntity> image = new ArrayList<>();

    @Schema(description = "盘点状态")
    private Integer status;

    public BigDecimal getWeight() {
        return PriceUtil.formatThreeDecimal(weight);
    }

    public BigDecimal getNetGoldWeight() {
        return PriceUtil.formatThreeDecimal(netGoldWeight);
    }

    public BigDecimal getNetSilverWeight() {
        return PriceUtil.formatThreeDecimal(netSilverWeight);
    }

    public BigDecimal getMainStoneWeight() {
        return PriceUtil.formatThreeDecimal(mainStoneWeight);
    }

    public BigDecimal getSubStoneWeight() {
        return PriceUtil.formatThreeDecimal(subStoneWeight);
    }

    public BigDecimal getCostPrice() {
        return PriceUtil.formatTwoDecimal(costPrice);
    }

    public BigDecimal getGoldPrice() {
        return PriceUtil.formatTwoDecimal(goldPrice);
    }

    public BigDecimal getSilverPrice() {
        return PriceUtil.formatTwoDecimal(silverPrice);
    }

    public BigDecimal getWorkPrice() {
        return PriceUtil.formatTwoDecimal(workPrice);
    }

    public BigDecimal getCertPrice() {
        return PriceUtil.formatTwoDecimal(certPrice);
    }

    public BigDecimal getSaleWorkPrice() {
        return PriceUtil.formatTwoDecimal(saleWorkPrice);
    }

    public BigDecimal getTagPrice() {
        return PriceUtil.formatTwoDecimal(tagPrice);
    }
}
