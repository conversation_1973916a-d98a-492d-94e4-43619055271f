package com.xc.boot.modules.goods.model.form;

import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import java.util.Set;

@Data
@Schema(description = "盘点单表单")
public class GoodsTakeForm {
    @Schema(description = "盘点单id(修改时)")
    @NotNull(message = "盘点单id不能为空", groups = {Update.class})
    private Long id;

    @Schema(description = "盘点单名称")
    @NotNull(message = "盘点单名称不能为空", groups = {Update.class, Create.class})
    @Length(max = 200, message = "盘点单名称长度不能超过200")
    private String name;

    @Schema(description = "盘点单备注")
    @Length(max = 200, message = "盘点单备注长度不能超过200")
    private String remark;

    @Schema(description = "门店")
    @NotNull(message = "门店不能为空", groups = {Create.class})
    private Long merchantId;

    @Schema(description = "盘点单类型(pda端固定为1 明盘，不用传参)")
    @Range(min = 1, max = 2, message = "盘点单类型参数错误", groups = {Create.class})
    private Integer type = 1;

    @Schema(description = "盘点方式(pda端固定为2 rfid，不用传参)")
    @Range(min = 1, max = 2, message = "盘点方式参数错误", groups = {Create.class})
    private Integer takeMode = 1;

    @Schema(description = "盘点柜台ID列表(数组)")
    @NotEmpty(message = "盘点柜台ID列表不能为空", groups = {Create.class})
    private Set<Long> counterIds;

    @Schema(description = "盘点大类ID列表(数组)")
    private Set<Long> categoryIds;

    @Schema(description = "盘点小类ID列表(数组)(如果大类列表为空，则以小类列表为准，如果大类列表不为空，但是没有选择某类(比如 黄金)，但是小类列表选择了(比如 黄金-手镯)，则不会保存该小类)")
    private Set<Long> subclassIds;

    @Schema(description = "盘点款式ID列表(数组)")
    private Set<Long> styleIds;
}
