package com.xc.boot.modules.goods.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.common.annotation.Flatten;
import com.xc.boot.common.annotation.GoodsColumn;
import com.fasterxml.jackson.annotation.JsonIgnore;

@Data
@Schema(description = "调拨货品VO")
public class GoodsTransferGoodsVO {
    @Schema(description = "调拨单明细ID")
    private Long id;

    @Schema(description = "货品ID")
    private Long goodsId;

    @Schema(description = "所属门店")
    private String merchant;

    @GoodsColumn(value = "goods_sn")
    @Schema(description = "货品条码")
    private String goodsSn;

    @GoodsColumn(value = "name")
    @Schema(description = "货品名称")
    private String name;

    @GoodsColumn(value = "image")
    @Schema(description = "货品图片")
    private List<GoodsHasImagesEntity> images;

    @Schema(description = "所属大类")
    private String category;

    @Schema(description = "货品小类")
    private String subclass;

    @Schema(description = "柜台")
    private String counter;

    @GoodsColumn(value = "num")
    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "最大可出库数量")
    private Integer maxNum;

    @GoodsColumn(value = "weight")
    @Schema(description = "重量")
    private BigDecimal weight;

    @GoodsColumn(value = "net_gold_weight")
    @Schema(description = "净金重")
    private BigDecimal netGoldWeight;

    @GoodsColumn(value = "net_silver_weight")
    @Schema(description = "净银重")
    private BigDecimal netSilverWeight;

    @GoodsColumn(value = "gold_price")
    @Schema(description = "金进单价")
    private BigDecimal goldPrice;

    @GoodsColumn(value = "gold_price")
    @Schema(description = "金进金额")
    private BigDecimal goldAmount;

    @GoodsColumn(value = "silver_price")
    @Schema(description = "银进单价")
    private BigDecimal silverPrice;

    @GoodsColumn(value = "silver_price")
    @Schema(description = "银进金额")
    private BigDecimal silverAmount;

    @GoodsColumn(value = "work_price")
    @Schema(description = "进工费单价")
    private BigDecimal workPrice;

    @GoodsColumn(value = "work_price")
    @Schema(description = "进工费")
    private BigDecimal workAmount;

    @GoodsColumn(value = "cert_price")
    @Schema(description = "证书费")
    private BigDecimal certPrice;

    @GoodsColumn(value = "cost_price")
    @Schema(description = "成本单价")
    private BigDecimal costPrice;

    @GoodsColumn(value = "sales_type")
    @Schema(description = "销售方式")
    private String salesType;

    @GoodsColumn(value = "sale_work_price")
    @Schema(description = "工费单价")
    private BigDecimal saleWorkPrice;

    @GoodsColumn(value = "sale_work_price")
    @Schema(description = "工费")
    private BigDecimal saleWorkAmount;

    @GoodsColumn(value = "tag_price")
    @Schema(description = "标签单价")
    private BigDecimal tagPrice;

    @Schema(description = "供应商")
    private String supplier;

    @Schema(description = "成色")
    private String quality;

    @Schema(description = "款式")
    private String style;

    @Schema(description = "品牌")
    private String brand;

    @GoodsColumn(value = "circle_size")
    @Schema(description = "圈口")
    private String circleSize;

    @Schema(description = "工艺")
    private String technology;

    @GoodsColumn(value = "cert_no")
    @Schema(description = "证书号")
    private String certNo;

    @Schema(description = "主石")
    private String mainStone;

    @GoodsColumn(value = "main_stone_count")
    @Schema(description = "主石数")
    private Integer mainStoneCount;

    @GoodsColumn(value = "main_stone_weight")
    @Schema(description = "主石重")
    private BigDecimal mainStoneWeight;

    @Schema(description = "辅石")
    private String subStone;

    @GoodsColumn(value = "sub_stone_count")
    @Schema(description = "辅石数")
    private Integer subStoneCount;

    @GoodsColumn(value = "sub_stone_weight")
    @Schema(description = "辅石重")
    private BigDecimal subStoneWeight;

    @GoodsColumn(value = "batch_no")
    @Schema(description = "批次号")
    private String batchNo;

    @GoodsColumn(value = "merchant_id")
    @Schema(description = "所属门店ID")
    @JsonIgnore
    private Long merchantId;

    @GoodsColumn(value = "category_id")
    @Schema(description = "所属大类ID")
    @JsonIgnore
    private Integer categoryId;

    @GoodsColumn(value = "subclass_id")
    @Schema(description = "货品小类ID")
    @JsonIgnore
    private Integer subclassId;

    @GoodsColumn(value = "counter_id")
    @Schema(description = "柜台ID")
    @JsonIgnore
    private Integer counterId;

    @GoodsColumn(value = "supplier_id")
    @Schema(description = "供应商ID")
    @JsonIgnore
    private Integer supplierId;

    @GoodsColumn(value = "quality_id")
    @Schema(description = "成色ID")
    @JsonIgnore
    private Integer qualityId;

    @GoodsColumn(value = "style_id")
    @Schema(description = "款式ID")
    @JsonIgnore
    private Integer styleId;

    @GoodsColumn(value = "brand_id")
    @Schema(description = "品牌ID")
    @JsonIgnore
    private Integer brandId;

    @GoodsColumn(value = "technology_id")
    @Schema(description = "工艺ID")
    @JsonIgnore
    private Integer technologyId;

    @GoodsColumn(value = "main_stone_id")
    @Schema(description = "主石ID")
    @JsonIgnore
    private Integer mainStoneId;

    @GoodsColumn(value = "sub_stone_id")
    @Schema(description = "辅石ID")
    @JsonIgnore
    private Integer subStoneId;

    @Schema(description = "自定义字段")
    @Flatten
    private java.util.List<CustomColumnItemDTO> customColumns;

    @GoodsColumn(value = "remark")
    private String remark;
}
