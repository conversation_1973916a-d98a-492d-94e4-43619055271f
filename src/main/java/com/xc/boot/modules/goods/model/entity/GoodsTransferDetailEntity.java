package com.xc.boot.modules.goods.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 货品调拨单明细实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_transfer_detail")
public class GoodsTransferDetailEntity extends BaseEntity {
    
    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 调拨单ID
     */
    @Column(value = "transfer_id")
    private Long transferId;

    /**
     * 货品ID
     */
    @Column(value = "goods_id")
    private Long goodsId;

    /**
     * 调拨后的货品ID
     */
    @Column(value = "target_goods_id")
    private Long targetGoodsId;

    /**
     * 调拨数量
     */
    @Column(value = "num")
    private Integer num;
} 