package com.xc.boot.modules.goods.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "盘点单分页查询")
public class GoodsTakePageQuery extends BasePageQuery {
    @Schema(description = "关键字(单号全等)")
    private String keyword;

    @Schema(description = "盘点单号")
    private String takeCode;

    @Schema(description = "盘点名称")
    private String takeName;

    @Schema(description = "所属门店(多选,英文逗号分隔)")
    private String merchantIds;

    @Schema(description = "盘点类型(单选)(1:明盘|2:盲盘)(pda调用时默认为1，不用传值)")
    private Integer takeType;

    @Schema(description = "盘点方式(单选)(1:扫码盘点|2:rfid盘点)(pda调用时默认为2，不用传值)")
    private Integer takeMode;

    @Schema(description = "盘点状态(单选)(0:盘点中|1:盘点完成|2:取消盘点)")
    private Integer takeStatus;

    @Schema(description = "盘点结果(单选)(0:不一致|1:一致)")
    private Integer takeResult;

    @Schema(description = "盘点柜台(多选,英文逗号分隔)")
    private String counterIds;

    @Schema(description = "盘点范围-大类(多选,英文逗号分隔)")
    private String categoryIds;

    @Schema(description = "盘点范围-小类(多选,英文逗号分隔)")
    private String subclassIds;

    @Schema(description = "盘点范围-款式(多选,英文逗号分隔)")
    private String styleIds;

    @Schema(description = "盘点人员(单选)")
    private Integer createdBy;

    @Schema(description = "创建时间(数组，0起始 1结束 起始都需要有值(与pc一致)）)")
    private Date[] timeRange;

    @Schema(description = "结束时间(数组，0起始 1结束 起始都需要有值(与pc一致)）)")
    private Date[] finishTimeRange;
}