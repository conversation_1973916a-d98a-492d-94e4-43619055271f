package com.xc.boot.modules.goods.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 货品调拨单实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_transfer")
public class GoodsTransferEntity extends BaseEntity {
    
    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 调拨单号
     */
    @Column(value = "transfer_sn")
    private String transferSn;

    /**
     * 调拨类型 (1-门店调拨 2-柜台调拨)
     */
    @Column(value = "type")
    private Integer type;

    /**
     * 调出门店
     */
    @Column(value = "merchant_outcome")
    private Long merchantOutcome;

    /**
     * 调入门店
     */
    @Column(value = "merchant_income")
    private Long merchantIncome;

    /**
     * 调入柜台
     */
    @Column(value = "counter_id")
    private Integer counterId;

    /**
     * 状态 (0-待审核 1-调拨中 2-已完成)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 总数量
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 总重量(g)
     */
    @Column(value = "total_weight")
    private BigDecimal totalWeight;

    /**
     * 总金重(g)
     */
    @Column(value = "total_net_gold_weight")
    private BigDecimal totalNetGoldWeight;

    /**
     * 总银重(g)
     */
    @Column(value = "total_net_silver_weight")
    private BigDecimal totalNetSilverWeight;

    /**
     * 总成本价(分)
     */
    @Column(value = "total_cost_price")
    private Long totalCostPrice;

    /**
     * 调拨单备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 审核人
     */
    @Column(value = "audit_by")
    private Long auditBy;

    /**
     * 审核时间
     */
    @Column(value = "audit_at")
    private Date auditAt;

    /**
     * 收货人
     */
    @Column(value = "receipt_by")
    private Long receiptBy;

    /**
     * 收货时间
     */
    @Column(value = "receipt_at")
    private Date receiptAt;

    /**
     * 收货备注
     */
    @Column(value = "receipt_remark")
    private String receiptRemark;

    /**
     * 创建人
     */
    @Column(value = "created_by")
    private Long createdBy;

    /**
     * 删除时间
     */
    @Column(value = "deleted_at")
    private Date deletedAt;
} 