package com.xc.boot.modules.goods.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "调拨单明细编辑DTO")
public class GoodsTransferDetailUpdateDTO {
    @NotNull(message = "明细ID不能为空")
    @Schema(description = "明细ID")
    private Long id;

    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量必须大于零")
    @Schema(description = "数量")
    private Integer num;
} 