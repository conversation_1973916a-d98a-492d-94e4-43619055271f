package com.xc.boot.modules.goods.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "调拨单明细分页查询")
public class GoodsTransferDetailPageQuery extends BasePageQuery {
    @NotNull(message = "调拨单ID不能为空")
    @Schema(description = "调拨单ID")
    private Long transferId;
} 