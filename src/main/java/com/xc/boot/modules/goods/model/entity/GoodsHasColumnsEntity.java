package com.xc.boot.modules.goods.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 货品字段关联实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_has_columns")
public class GoodsHasColumnsEntity extends BaseEntity {
    
    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 货品ID
     */
    @Column(value = "goods_id")
    private Long goodsId;

    /**
     * 字段ID
     */
    @Column(value = "column_id")
    private Integer columnId;

    /**
     * 字段标识
     */
    @Column(value = "column_sign")
    private String columnSign;

    /**
     * 字段值
     */
    @Column(value = "value")
    private String value;

    /**
     * 图片ID
     */
    @Column(value = "image_id")
    private Integer imageId;
} 