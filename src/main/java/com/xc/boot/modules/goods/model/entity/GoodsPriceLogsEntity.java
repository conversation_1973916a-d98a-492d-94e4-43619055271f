package com.xc.boot.modules.goods.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.annotation.GoodsColumn;
import com.xc.boot.common.base.BaseEntity;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 价格变化记录
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_price_logs")
public class GoodsPriceLogsEntity extends BaseEntity implements CreatedByListenerFlag {
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 货品ID
     */
    @Column(value = "goods_id")
    private Long goodsId;

    /**
     * 变更描述
     */
    @Column(value = "comment")
    private String comment;

    /**
     * 成本单价(变化量)(分)
     */
    @Column(value = "cost_price")
    private Long costPrice;

    /**
     * 金进单价(变化量)(分)
     */
    @Column(value = "gold_price")
    private Long goldPrice;

    /**
     * 银进单价(变化量)(分)
     */
    @Column(value = "silver_price")
    private Long silverPrice;

    /**
     * 进工费单价(变化量)(分)
     */
    @Column(value = "work_price")
    private Long workPrice;

    /**
     * 证书费(变化量)(分)
     */
    @Column(value = "cert_price")
    private Long certPrice;

    /**
     * 工费单价(变化量)(分)
     */
    @Column(value = "sale_work_price")
    private Long saleWorkPrice;

    /**
     * 标签单价(变化量)(分)
     */
    @Column(value = "tag_price")
    private Long tagPrice;

    /**
     * 操作人
     */
    @Column(value = "created_by")
    private Long createdBy;
} 