package com.xc.boot.modules.goods.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 货品盘点单实体
 */
@Getter
@Setter
@Builder
@Accessors(chain = true)
@Table(value = "goods_take")
@AllArgsConstructor
@NoArgsConstructor
public class GoodsTakeEntity extends BaseEntity implements CreatedByListenerFlag {
    
    /**
     * 盘点单号
     */
    @Column(value = "take_code")
    private String takeCode;
    /**
     * 盘点名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 所属门店ID
     */
    @Column(value = "merchant_id")
    private Long merchantId;

    /**
     * 盘点类型(1:明盘|2:盲盘)
     */
    @Column(value = "type")
    private Integer type;

    /**
     * 盘点方式(1:扫码盘点|2:rfid盘点)
     */
    @Column(value = "take_mode")
    private Integer takeMode;

    /**
     * 盘点状态(0:盘点中|1:盘点完成|2:取消盘点)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 应盘数量
     */
    @Column(value = "total_num")
    private Integer totalNum;

    /**
     * 已盘数量
     */
    @Column(value = "take_num")
    private Integer takeNum;

    /**
     * 盘盈数量
     */
    @Column(value = "surplus_num")
    private Integer surplusNum;

    /**
     * 盘亏数量
     */
    @Column(value = "loss_num")
    private Integer lossNum;

    /**
     * 异常数量
     */
    @Column(value = "abnormal_num")
    private Integer abnormalNum;

    /**
     * 盘点结果(0:不一致|1:一致)
     */
    @Column(value = "result")
    private Integer result;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 创建人ID
     */
    @Column(value = "created_by")
    private Long createdBy;

    /**
     * 完成时间
     */
    @Column(value = "complete_at")
    private Date completeAt;
}