package com.xc.boot.modules.goods.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "小类树节点")
public class CategoryNodeVo {
    @Schema(description = "大类id")
    private Long categoryId;
    @Schema(description = "大类名称")
    private String name;
    @Schema(description = "小类id")
    private Long subclassId;
    @Schema(description = "小类名称")
    private String subclassName;
    @Schema(description = "盘点id")
    private Long takeId;
}
