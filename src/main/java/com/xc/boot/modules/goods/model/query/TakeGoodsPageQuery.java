package com.xc.boot.modules.goods.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
@Schema(description = "盘点货品列表查询")
public class TakeGoodsPageQuery extends BasePageQuery {
    @Schema(description = "盘点单id")
    @NotNull(message = "盘点单id不能为空")
    private Long takeId;

    @Schema(description = "查询类型(1:已盘|2:未盘|3:盘盈|4:盘亏|5:异常|6:一致|7:不一致)")
    private Integer type;

    @Schema(description = "关键字")
    @Length(max = 200, message = "关键字不能超过200个字符")
    private String keyword;
}
