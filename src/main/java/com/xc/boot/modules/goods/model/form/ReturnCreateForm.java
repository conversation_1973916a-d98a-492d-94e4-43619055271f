package com.xc.boot.modules.goods.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;


/**
 * <AUTHOR>
 * @ClassName ReturnCreateForm
 * @Date: 2025/6/16 14:35
 * @Description: 生成退货单form
 */
@Data
@Schema(description = "生成退货单form")
public class ReturnCreateForm {
    @Schema(description = "货品列表")
    @NotNull(message = "货品列表不能为空")
    @NotEmpty(message = "货品列表不能为空")
    List<ReturnGoods> goods;

    @Schema(description = "门店id")
    @NotNull(message = "门店id不能为空")
    private Long merchantId;

    @Schema(description = "供应商id")
    @NotNull(message = "供应商id不能为空")
    private Long supplierId;

    @Length(max = 200, message = "备注长度不能超过200字符")
    private String remark;

    @Data
    public static class ReturnGoods {
        @Schema(description = "货品id")
        private Long goodsId;
        @Schema(description = "出库数量")
        private Integer num;
    }
}
