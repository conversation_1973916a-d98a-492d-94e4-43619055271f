package com.xc.boot.modules.goods.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "货品打印信息查询对象")
public class GoodsPrintQuery {
    @Schema(description = "货品ID列表")
    private List<Long> goodsIds;

    @Schema(description = "入库明细ID列表")
    private List<Long> detailIds;

    @Schema(description = "入库单ID")
    private Integer incomeId;
}
