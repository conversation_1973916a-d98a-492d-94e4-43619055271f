package com.xc.boot.modules.goods.service;

import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.goods.model.query.GoodsPageQuery;
import com.xc.boot.modules.goods.model.query.GoodsPrintQuery;
import com.xc.boot.modules.goods.model.vo.GoodsLogVo;
import com.xc.boot.modules.goods.model.vo.GoodsPageVo;
import com.xc.boot.modules.goods.model.vo.GoodsPrintFieldVo;
import com.xc.boot.modules.goods.model.vo.GoodsStatisticVo;

import java.util.List;

/**
 * 货品服务类
 */
public interface GoodsService extends IService<GoodsEntity> {

    Page<GoodsPageVo> goodsPage(GoodsPageQuery query);

    void goodsUpdate(JSONObject form);

    GoodsStatisticVo statistic(GoodsPageQuery query);

    Page<GoodsLogVo> log(String goodsSn, Integer pageNum, Integer pageSize);

    List<JSONObject> print(GoodsPrintQuery query);

    List<GoodsPrintFieldVo> printFields();
}
