package com.xc.boot.modules.goods.controller;

import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.modules.goods.model.form.*;
import com.xc.boot.modules.goods.model.vo.ReturnDetailPageVo;
import com.xc.boot.modules.goods.model.vo.ReturnGoodsPageVo;
import com.xc.boot.modules.goods.model.vo.ReturnReceiptPageVo;
import com.xc.boot.modules.goods.service.ReturnOutcomeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 货品出库库控制层
 */
@Slf4j
@Tag(name = "货品管理-采购退货单")
@RestController
@RequestMapping("/api/outcome/return")
@RequiredArgsConstructor
public class ReturnOutcomeController {
    private final ReturnOutcomeService returnOutcomeService;

    @Operation(summary = "分页查询采购退货品")
    @PostMapping("/goodsPage")
    public PageResult<?> selectOutGoods(@RequestBody @Validated ReturnGoodsForm form) {
        Page<ReturnGoodsPageVo> voPage = returnOutcomeService.selectOutGoods(form);
        Page<JSONObject> result = ColumnEncryptUtil.encrypt(voPage, ReturnGoodsPageVo.class, "customerColumns");
        return PageResult.success(result);
    }

    @Operation(summary = "分页查询采购退货单")
    @PostMapping("/receiptPage")
    public PageResult<ReturnReceiptPageVo> receiptPage(@RequestBody @Validated ReturnReceiptForm form) {
        Page<ReturnReceiptPageVo> voPage = returnOutcomeService.receiptPage(form);
        return PageResult.success(voPage);
    }

    @Operation(summary = "删除采购退货单")
    @GetMapping("/delete")
    public Result<?> delete(@RequestParam @Validated @NotNull(message = "id不能为空") Long id) {
        returnOutcomeService.delete(id);
        return Result.success();
    }

    @Operation(summary = "采购退货单明细")
    @GetMapping("/receiptDetail")
    public Result<ReturnReceiptPageVo> receiptDetail(@RequestParam @Validated @NotNull(message = "id不能为空") Long id) {
        ReturnReceiptPageVo vo = returnOutcomeService.receiptDetail(id);
        return Result.success(vo);
    }

    @Operation(summary = "删除采购退货单明细")
    @GetMapping("/deleteDetail")
    public Result<?> deleteDetail(@RequestParam @Validated @NotNull(message = "id不能为空") Long id) {
        returnOutcomeService.deleteDetail(id);
        return Result.success();
    }

    @Operation(summary = "审核采购退货单")
    @PostMapping("/audit")
    public Result<?> audit(@RequestBody @Validated ReturnAuditForm form) {
        returnOutcomeService.audit(form.getIds());
        return Result.success();
    }

    @Operation(summary = "创建采购退货单")
    @PostMapping("/create")
    public Result<?> create(@RequestBody @Validated ReturnCreateForm form) {
        try {
            returnOutcomeService.create(form);
        }catch (Exception e) {
            SnUtils.syncOutcomeMaxSequence();
            log.error("创建采购退货单异常", e);
            throw new BusinessException(e.getMessage(), e);
        }
        return Result.success();
    }

    @Operation(summary = "采购退货单详情-编辑数量")
    @PostMapping("/edit")
    public Result<?> edit(@RequestBody @Validated ReturnEditForm form) {
        returnOutcomeService.edit(form);
        return Result.success();
    }

    @Operation(summary = "分页查询采购退货单详情")
    @PostMapping("/detailPage")
    public PageResult<?> detailPage(@RequestBody @Validated ReturnReceiptDetailForm form) {
        Page<ReturnDetailPageVo> voPage = returnOutcomeService.detailPage(form);
        Page<JSONObject> encrypt = ColumnEncryptUtil.encrypt(voPage, ReturnDetailPageVo.class, "customerColumns");
        return PageResult.success(encrypt);
    }

}
