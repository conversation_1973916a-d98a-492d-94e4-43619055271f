package com.xc.boot.modules.goods.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 货品盘点单详情实体
 */
@Getter
@Setter
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(value = "goods_take_detail")
public class GoodsTakeDetailEntity extends BaseEntity {

    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 所属门店ID
     */
    @Column(value = "merchant_id")
    private Long merchantId;

    /**
     * 所属柜台ID
     */
    @Column(value = "counter_id")
    private Long counterId;

    /**
     * 货品ID
     */
    @Column(value = "goods_id")
    private Long goodsId;

    /**
     * 货品条码
     */
    @Column(value = "goods_sn")
    private String goodsSn;

    /**
     * 货品名称
     */
    @Column(value = "name")
    private String name;

    /**
     * RFID
     */
    @Column(value = "rfid")
    private String rfid;

    /**
     * 盘点单ID
     */
    @Column(value = "take_id")
    private Long takeId;

    /**
     * 应盘数量
     */
    @Column(value = "total_num")
    private Integer totalNum;

    /**
     * 已盘数量
     */
    @Column(value = "take_num")
    private Integer takeNum;

    /**
     * 是否异常(0:否|1:是)
     */
    @Column(value = "abnormal")
    private Integer abnormal;

    /**
     * 盘点人
     */
    @Column(value = "take_by")
    private Long takeBy;

    /**
     * 盘点时间
     */
    @Column(value = "take_at")
    private Date takeAt;

    /**
     * 盘点状态(0:中|1:完成)
     */
    @Column(value = "status")
    private Integer status;
}