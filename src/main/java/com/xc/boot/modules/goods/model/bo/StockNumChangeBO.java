package com.xc.boot.modules.goods.model.bo;

import lombok.Builder;
import lombok.Data;

/**
 * 库存数量变更对象
 */
@Data
@Builder
public class StockNumChangeBO {
    /**
     * 货品ID
     */
    private Long goodsId;

    /**
     * 变更描述
     */
    private String comment;

    /**
     * 原始数量
     */
    @Builder.Default
    private Integer num = 0;

    /**
     * 库存数量变化量
     */
    @Builder.Default
    private Integer stockNum = 0;

    /**
     * 采购退数量变化量
     */
    @Builder.Default
    private Integer returnNum = 0;

    /**
     * 售出数量变化量
     */
    @Builder.Default
    private Integer soldNum = 0;

    /**
     * 调拨中数量变化量
     */
    @Builder.Default
    private Integer transferNum = 0;

    /**
     * 冻结数量变化量
     */
    @Builder.Default
    private Integer frozenNum = 0;
} 