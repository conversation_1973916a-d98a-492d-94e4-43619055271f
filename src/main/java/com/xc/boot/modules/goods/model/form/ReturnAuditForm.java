package com.xc.boot.modules.goods.model.form;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ReturnAuditForm
 * @Date: 2025/6/16 11:49
 * @Description: 描述
 */
@Data
public class ReturnAuditForm {
    @NotEmpty(message = "ID列表不能为空")
    @NotNull(message = "ID列表不能为空")
    private List<Long> ids;
}
