package com.xc.boot.modules.goods.model.vo;

import com.xc.boot.common.annotation.GoodsColumn;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Schema(description = "盘点货品vo")
public class TakeGoodsVo {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "货品id")
    private Long goodsId;

    @Schema(description = "货品名称")
    @GoodsColumn(value = "name")
    private String name;

    @Schema(description = "货品条码")
    @GoodsColumn(value = "goods_sn")
    private String goodsSn;

    @Schema(description = "应盘数量（盘点时在仓：库存+冻结）")
    private Integer totalNum;

    @Schema(description = "已盘数量")
    private Integer takeNum;

    @Schema(description = "所属柜台ID")
    private Long counterId;

    @Schema(description = "柜台")
    @GoodsColumn(value = "counter_id")
    private String counter;

    @Schema(description = "所属大类ID")
    private Long categoryId;

    @Schema(description = "所属大类")
    @GoodsColumn(value = "category_id")
    private String category;

    @Schema(description = "所属小类ID")
    private Long subclassId;

    @Schema(description = "所属小类")
    @GoodsColumn(value = "subclass_id")
    private String subclass;

    @Schema(description = "成色ID")
    private Long qualityId;

    @Schema(description = "成色")
    @GoodsColumn(value = "quality_id")
    private String quality;

    @Schema(description = "工艺ID")
    private Long technologyId;

    @Schema(description = "工艺")
    @GoodsColumn(value = "technology_id")
    private String technology;

    @Schema(description = "销售方式(1:按数量,2:按重量)")
    @GoodsColumn(value = "sales_type")
    private String salesType;

    @Schema(description = "重量(g)")
    @GoodsColumn(value = "weight")
    private BigDecimal weight;

    @Schema(description = "净金重(g)")
    @GoodsColumn(value = "net_gold_weight")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")
    @GoodsColumn(value = "net_silver_weight")
    private BigDecimal netSilverWeight;

    @Schema(description = "成本单价(元)")
    @GoodsColumn(value = "cost_price")
    private BigDecimal costPrice;

    @Schema(description = "商品图片列表")
    @GoodsColumn(value = "image")
    private List<GoodsHasImagesEntity> image = new ArrayList<>();

    public BigDecimal getWeight() {
        return PriceUtil.formatThreeDecimal(weight);
    }

    public BigDecimal getNetGoldWeight() {
        return PriceUtil.formatThreeDecimal(netGoldWeight);
    }

    public BigDecimal getNetSilverWeight() {
        return PriceUtil.formatThreeDecimal(netSilverWeight);
    }

    public BigDecimal getCostPrice() {
        return PriceUtil.formatTwoDecimal(costPrice);
    }
}
