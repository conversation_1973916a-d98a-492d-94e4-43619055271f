package com.xc.boot.modules.goods.model.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "货品标签打印字段")
public class GoodsPrintFieldVo {
    @Schema(description = "字段名称")
    private String label;
    @Schema(description = "字段值")
    private String value;
    @Schema(description = "是否自定义字段")
    private Integer isCustom;
}
