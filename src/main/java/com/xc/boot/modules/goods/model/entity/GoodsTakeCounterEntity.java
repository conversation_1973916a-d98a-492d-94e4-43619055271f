package com.xc.boot.modules.goods.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 盘点柜台关联实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_take_counter")
public class GoodsTakeCounterEntity{
    
    /**
     * 盘点ID
     */
    @Column(value = "take_id")
    private Long takeId;

    /**
     * 柜台ID
     */
    @Column(value = "counter_id")
    private Long counterId;

    @Column(value = "company_id")
    private Long companyId;
}