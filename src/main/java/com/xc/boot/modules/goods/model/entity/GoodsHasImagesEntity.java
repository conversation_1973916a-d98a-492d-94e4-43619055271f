package com.xc.boot.modules.goods.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 货品图片关联实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_has_images")
public class GoodsHasImagesEntity extends BaseEntity {
    
    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 货品ID
     */
    @Column(value = "goods_id")
    private Long goodsId;

    /**
     * 图片ID
     */
    @Column(value = "image_id")
    private Long imageId;

    /**
     * 图片URL
     */
    @Column(value = "url")
    private String url;

    /**
     * 排序号
     */
    @Column(value = "sort")
    private Integer sort;
} 