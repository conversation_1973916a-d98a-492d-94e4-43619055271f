package com.xc.boot.modules.goods.service;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.modules.goods.model.query.GoodsTransferPageQuery;
import com.xc.boot.modules.goods.model.vo.GoodsTransferPageVO;
import com.xc.boot.modules.goods.model.entity.GoodsTransferEntity;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.goods.model.query.GoodsTransferGoodsQuery;
import com.xc.boot.modules.goods.model.vo.GoodsTransferGoodsVO;
import java.util.List;
import com.xc.boot.modules.goods.model.query.GoodsTransferCreateDTO;
import com.xc.boot.modules.goods.model.dto.GoodsTransferReceiptDTO;
import com.xc.boot.modules.goods.model.dto.GoodsTransferUpdateDTO;
import com.xc.boot.modules.goods.model.dto.GoodsTransferInfoRequest;
import com.xc.boot.modules.goods.model.vo.GoodsTransferInfoVO;
import com.xc.boot.modules.goods.model.query.GoodsTransferDetailPageQuery;
import com.xc.boot.modules.goods.model.dto.GoodsTransferDetailUpdateDTO;

public interface GoodsTransferService extends IService<GoodsTransferEntity> {
    Page<GoodsTransferPageVO> pageTransfer(GoodsTransferPageQuery query);

    /**
     * 调拨货品查询
     */
    List<GoodsTransferGoodsVO> queryTransferGoods(GoodsTransferGoodsQuery query);

    /**
     * 创建调拨单
     */
    Long createTransfer(GoodsTransferCreateDTO dto);

    /**
     * 更新调拨单
     */
    boolean updateTransfer(GoodsTransferUpdateDTO dto);

    /**
     * 删除调拨单
     */
    boolean deleteTransfer(Long id);

    /**
     * 审核调拨单
     */
    boolean auditTransfer(String ids);

    /**
     * 审核调拨单
     * @param ids 调拨单ID
     * @param skipLog 是否跳过日志
     * @return boolean
     */
    boolean auditTransfer(String ids, boolean skipLog);

    /**
     * 收货调拨单
     */
    boolean receiptTransfer(GoodsTransferReceiptDTO dto);

    /**
     * 获取调拨单基础信息
     */
    GoodsTransferInfoVO getTransferInfo(GoodsTransferInfoRequest request);

    /**
     * 调拨单明细分页
     */
    Page<GoodsTransferGoodsVO> pageTransferDetail(GoodsTransferDetailPageQuery query);

    /**
     * 编辑调拨单明细
     */
    boolean updateTransferDetail(GoodsTransferDetailUpdateDTO dto);

    /**
     * 删除调拨单明细
     */
    boolean deleteTransferDetail(Long id);
} 