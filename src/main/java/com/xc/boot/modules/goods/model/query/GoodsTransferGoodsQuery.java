package com.xc.boot.modules.goods.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "调拨货品查询参数")
public class GoodsTransferGoodsQuery {
    @Schema(description = "调拨类型: 1-门店调拨 2-柜台调拨", required = true)
    private Integer type;

    @Schema(description = "调出门店", required = true)
    private Long merchantOutcome;

    @Schema(description = "调入柜台(可选, 排除该柜台货品)")
    private Long counterIncome;

    @Schema(description = "调入门店(可选, 排除该门店货品)")
    private Long merchantIncome;

    @Schema(description = "货品条码(与柜台二选一)")
    private String goodsSn;

    @Schema(description = "柜台(与条码二选一)")
    private Long counterId;
} 