package com.xc.boot.modules.goods.model.form;

import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName GoodsUpdateColumnForm
 * @Date: 2025/6/13 10:55
 * @Description: 描述
 */
@Data
public class GoodsUpdateColumnForm {

    /**
     * 字段名称
     */
    private String name;

    /**
     * 字段标识
     */
    private String sign;

    /**
     * 机密级别(1-常规 2-敏感 3-机密)
     */
    private Integer secretLevel;

    /**
     * 类目(1-通用 2-自定义)
     */
    private Integer category;

    /**
     * 字段属性(1-文本 2-数字 3-多行文本 4-日期 5-下拉列表 6-图片)
     */
    private Integer type;

    /**
     * 是否可多选
     */
    private Integer isMultiple;

    /**
     * 下拉选项(最多20个选项,json)
     */
    private String options;

    /**
     * 小数位数
     */
    private Integer numberPrecision;

    /**
     * 值
     */
    private String value;

    List<GoodsHasImagesEntity> images;
}
