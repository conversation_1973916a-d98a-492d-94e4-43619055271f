package com.xc.boot.modules.goods.controller;


import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.modules.goods.model.query.GoodsPageQuery;
import com.xc.boot.modules.goods.model.query.GoodsPrintQuery;
import com.xc.boot.modules.goods.model.vo.GoodsLogVo;
import com.xc.boot.modules.goods.model.vo.GoodsPageVo;
import com.xc.boot.modules.goods.model.vo.GoodsPrintFieldVo;
import com.xc.boot.modules.goods.model.vo.GoodsStatisticVo;
import com.xc.boot.modules.goods.service.GoodsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "货品管理-货品列表")
@RequestMapping("/api/goods")
public class GoodsController {
    private final GoodsService goodsService;

    @Operation(summary = "货品分页列表")
    @PostMapping("/page")
    public PageResult<?> page(@Validated @RequestBody GoodsPageQuery query) {
        Page<GoodsPageVo> page = goodsService.goodsPage(query);
        Page<JSONObject> result = ColumnEncryptUtil.encrypt(page, GoodsPageVo.class, "customerColumns");
        return PageResult.success(result);
    }

    @Operation(summary = "查询单个货品")
    @GetMapping("/detail")
    public Result<?> page(@RequestParam Long id) {
        GoodsPageQuery query = new GoodsPageQuery();
        query.setIds(List.of(id));
        Page<GoodsPageVo> page = goodsService.goodsPage(query);
        Page<JSONObject> result = ColumnEncryptUtil.encrypt(page, GoodsPageVo.class, "customerColumns");
        if (result.getRecords().isEmpty()) {
            return Result.success(null);
        }
        return Result.success(result.getRecords().getFirst());
    }

    @Operation(summary = "货品标签打印字段查询")
    @GetMapping("/printFields")
    public Result<List<GoodsPrintFieldVo>> printFields() {
        List<GoodsPrintFieldVo> list = goodsService.printFields();
        return Result.success(list);
    }

    @Operation(summary = "货品标签打印信息查询")
    @PostMapping("/print")
    public Result<List<JSONObject>> print(@Validated @RequestBody GoodsPrintQuery query) {
        List<JSONObject> list = goodsService.print(query);
        return Result.success(list);
    }

    @Operation(summary = "货品分页列表统计")
    @PostMapping("/statistic")
    public Result<GoodsStatisticVo> statistic(@Validated @RequestBody GoodsPageQuery query) {
        GoodsStatisticVo page = goodsService.statistic(query);
        return Result.success(page);
    }

    @Operation(summary = "货品编辑")
    @PostMapping
    public Result<?> updateGoods(@RequestBody JSONObject form) {
        goodsService.goodsUpdate(form);
        return Result.success();
    }

    @Operation(summary = "货品日志")
    @GetMapping("/log")
    public PageResult<GoodsLogVo> log(@RequestParam String goodsSn,
                                      @RequestParam(defaultValue = "1") Integer pageNum,
                                      @RequestParam(defaultValue = "20") Integer pageSize) {
        Page<GoodsLogVo> list = goodsService.log(goodsSn, pageNum, pageSize);
        return PageResult.success(list);
    }
}
