package com.xc.boot.modules.goods.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 盘点类别关联实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_take_category")
public class GoodsTakeCategoryEntity {
    
    /**
     * 盘点ID
     */
    @Column(value = "take_id")
    private Long takeId;

    /**
     * 大类ID
     */
    @Column(value = "category_id")
    private Long categoryId;

    /**
     * 小类ID
     */
    @Column(value = "subclass_id")
    private Long subclassId;

    @Column(value = "company_id")
    private Long companyId;
}