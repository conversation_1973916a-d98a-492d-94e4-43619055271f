package com.xc.boot.modules.goods.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
@Schema(description = "盘点货品查询")
public class TakeGoodsListQuery extends BasePageQuery {
    @Schema(description = "盘点单id")
    @NotNull(message = "盘点单id不能为空")
    private Long takeId;

    @Schema(description = "查询类型(1:条码查询|2:名称模糊查询)")
    @NotNull(message = "查询类型不能为空")
    private Integer type;

    @Schema(description = "关键字")
    @NotNull(message = "关键字不能为空")
    @Length(max = 200, message = "关键字不能超过200个字符")
    private String keyword;

    @Schema(description = "盘点详情id")
    private Long id;
}
