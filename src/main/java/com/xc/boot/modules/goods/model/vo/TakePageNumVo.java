package com.xc.boot.modules.goods.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "盘点单数量VO")
public class TakePageNumVo {
    @Schema(description = "盘点单ID")
    private Long takeId;

    @Schema(description = "应盘数量")
    private Integer totalNum;

    @Schema(description = "已盘数量")
    private Integer takeNum;

    @Schema(description = "盘盈数量")
    private Integer surplusNum;

    @Schema(description = "盘亏数量")
    private Integer lossNum;

    @Schema(description = "异常数量")
    private Integer abnormalNum;
}
