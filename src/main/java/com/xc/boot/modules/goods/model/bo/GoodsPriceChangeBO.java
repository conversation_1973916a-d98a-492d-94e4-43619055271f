package com.xc.boot.modules.goods.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @ClassName GoodsPriceChangeBO
 * @Date: 2025/6/16 10:49
 * @Description: 货品价格变化BO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class GoodsPriceChangeBO {
    /**
     * 货品ID
     */
    private Long goodsId;

    /**
     * 变更描述
     */
    private String comment;

    /**
     * 成本单价(变化量)(分)
     */
    @Builder.Default
    private Long costPrice = 0L;

    /**
     * 金进单价(变化量)(分)
     */
    @Builder.Default
    private Long goldPrice = 0L;

    /**
     * 银进单价(变化量)(分)
     */
    @Builder.Default
    private Long silverPrice = 0L;

    /**
     * 进工费单价(变化量)(分)
     */
    @Builder.Default
    private Long workPrice = 0L;

    /**
     * 证书费(变化量)(分)
     */
    @Builder.Default
    private Long certPrice = 0L;

    /**
     * 工费单价(变化量)(分)
     */
    @Builder.Default
    private Long saleWorkPrice = 0L;

    /**
     * 标签单价(变化量)(分)
     */
    @Builder.Default
    private Long tagPrice = 0L;
}
