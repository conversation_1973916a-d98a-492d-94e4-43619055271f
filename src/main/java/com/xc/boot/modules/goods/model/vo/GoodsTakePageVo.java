package com.xc.boot.modules.goods.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xc.boot.modules.merchant.model.entity.CounterEntity;
import com.xc.boot.modules.merchant.model.entity.StyleEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
@Schema(description = "盘点单分页VO")
public class GoodsTakePageVo {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "盘点单号")
    private String takeCode;

    @Schema(description = "盘点名称")
    private String name;

    @Schema(description = "所属门店")
    private String merchant;

    @Schema(description = "所属门店ID")
    private Long merchantId;

    @Schema(description = "盘点类型名称")
    private String typeName;

    @Schema(description = "盘点类型")
    private Integer type;

    @Schema(description = "盘点方式名称")
    private String takeModeName;

    @Schema(description = "盘点方式(1:扫码盘点|2:rfid盘点)")
    private Integer takeMode;

    @Schema(description = "应盘数量")
    private Integer totalNum;

    @Schema(description = "已盘数量")
    private Integer takeNum;

    @Schema(description = "盘盈数量")
    private Integer surplusNum;

    @Schema(description = "盘亏数量")
    private Integer lossNum;

    @Schema(description = "异常数量")
    private Integer abnormalNum;

    @Schema(description = "盘点状态名称")
    private String statusName;

    @Schema(description = "盘点状态")
    private Integer status;

    @Schema(description = "盘点结果名称")
    private String resultName;

    @Schema(description = "盘点结果")
    private Integer result;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建人名称")
    private String createdByName;

    @Schema(description = "创建人")
    private Long createdBy;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "完成时间")
    private Date completeAt;

    @Schema(description = "盘点柜台列表")
    private List<TakeCounterVo> counters;

    @Schema(description = "盘点柜台名称")
    private String counterName;

    @Schema(description = "盘点范围")
    private String tackRange;

    @Schema(description = "盘点款式列表")
    private List<TakeStyleVo> styles;

    @Schema(description = "盘点大类名称列表")
    private Set<String> categoryNames;

    @Schema(description = "盘点小类名称列表")
    private Set<String> subclassNames;

    @JsonIgnore
    @Schema(description = "", hidden = true)
    private List<CategoryNodeVo> categories;

    @JsonIgnore
    @Schema(description = "", hidden = true)
    private TakePageNumVo numVo;

}