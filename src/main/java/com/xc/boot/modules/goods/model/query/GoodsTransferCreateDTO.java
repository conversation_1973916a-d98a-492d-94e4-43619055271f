package com.xc.boot.modules.goods.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "调拨单创建参数")
public class GoodsTransferCreateDTO {
    @Schema(description = "调拨类型 (1-门店调拨 2柜台调拨)", required = true)
    private Integer type;
    @Schema(description = "调出门店ID", required = true)
    private Long merchantOutcome;
    @Schema(description = "调入门店ID (柜台调拨时, 等于调出门店ID)", required = true)
    private Long merchantIncome;
    @Schema(description = "调入柜台ID (门店调拨时, 传 0 即可)", required = true)
    private Integer counterId;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "明细列表", required = true)
    private List<Detail> details;

    @Data
    public static class Detail {
        @Schema(description = "货品ID", required = true)
        private Long id;
        @Schema(description = "数量", required = true)
        private Integer num;
    }
} 