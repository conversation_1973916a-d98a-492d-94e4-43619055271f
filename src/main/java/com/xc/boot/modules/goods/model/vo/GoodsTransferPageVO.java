package com.xc.boot.modules.goods.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "调拨单分页VO")
public class GoodsTransferPageVO {
    @Schema(description = "调拨单ID")
    private Long id;
    @Schema(description = "调拨单号")
    private String transferSn;
    @Schema(description = "调拨类型")
    private Object type;

    @Schema(description = "调拨类型")
    private String typeName;
    // getter
    public String getTypeName() {
        if (type instanceof Integer) {
            return ((Integer) type) == 1 ? "门店调拨" : "柜台调拨";
        } else if (type instanceof String) {
            return (String) type;
        }
        return type != null ? type.toString() : "";
    }

    @Schema(description = "调出门店")
    private Long merchantOutcome;
    @Schema(description = "调出柜台")
    private Integer counterOutcome;
    @Schema(description = "调入门店")
    private Long merchantIncome;
    @Schema(description = "调入柜台")
    private Integer counterId;
    @Schema(description = "调拨数量")
    private Integer num;
    @Schema(description = "调拨货重(g)")
    private BigDecimal totalWeight;
    @Schema(description = "调拨金重(g)")
    private BigDecimal totalNetGoldWeight;
    @Schema(description = "调拨银重(g)")
    private BigDecimal totalNetSilverWeight;
    @Schema(description = "总成本价")
    private Long totalCostPrice;
    @Schema(description = "总成本价(元)")
    private String totalCostPriceStr;
    @Schema(description = "收货备注")
    private String receiptRemark;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "创建人")
    private Long createdBy;
    @Schema(description = "创建时间")
    private Date createdAt;
    @Schema(description = "审核人")
    private Long auditBy;
    @Schema(description = "审核时间")
    private Date auditAt;
    @Schema(description = "收货人")
    private Long receiptBy;
    @Schema(description = "收货时间")
    private Date receiptAt;
    @Schema(description = "调出门店名称")
    private String merchantOutcomeName;
    @Schema(description = "调出柜台名称")
    private String counterOutcomeName;
    @Schema(description = "调入门店名称")
    private String merchantIncomeName;
    @Schema(description = "调入柜台名称")
    private String counterIdName;
    @Schema(description = "创建人名称")
    private String createdByName;
    @Schema(description = "审核人名称")
    private String auditByName;
    @Schema(description = "收货人名称")
    private String receiptByName;
    @Schema(description = "状态")
    private Object status;
    @Schema(description = "状态中文")
    private String statusLabel;
    @Schema(description = "调出柜台名称数组")
    private Object counterOutcomeNames;
}