package com.xc.boot.modules.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import com.mybatisflex.spring.service.impl.ServiceImpl;

import com.xc.boot.common.enums.baseColum.JoinColumEnum;
import com.xc.boot.common.enums.baseColum.NumColumEnum;
import com.xc.boot.common.enums.baseColum.OtherColumEnum;
import com.xc.boot.common.enums.baseColum.PriceColumEnum;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.model.Option;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.common.util.listFill.ListFillUtilV2;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsHasColumnsMapper;
import com.xc.boot.modules.goods.mapper.GoodsHasImagesMapper;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.bo.CustomColumnBO;
import com.xc.boot.modules.goods.model.bo.GoodsPriceChangeBO;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasColumnsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import com.xc.boot.modules.goods.model.query.GoodsPageQuery;
import com.xc.boot.modules.goods.model.query.GoodsPrintQuery;
import com.xc.boot.modules.goods.model.vo.*;
import com.xc.boot.modules.goods.service.GoodsService;
import com.xc.boot.modules.income.mapper.GoodsIncomeDetailMapper;
import com.xc.boot.modules.merchant.mapper.GoodsColumnMapper;
import com.xc.boot.modules.merchant.mapper.GoodsIncomeTemplateDetailMapper;
import com.xc.boot.modules.merchant.mapper.GoodsIncomeTemplateMapper;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateDetailEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateEntity;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnTypeEnum;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialHasImagesMapper;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialMapper;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialEntity;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialHasImagesEntity;
import com.xc.boot.modules.order.model.enums.SalesTypeEnum;
import com.xc.boot.system.mapper.GoodsLogMapper;
import com.xc.boot.system.model.entity.GoodsLogEntity;
import com.xc.boot.system.model.vo.HeadingColumns;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.mybatisflex.core.query.QueryMethods.sum;
import static com.xc.boot.common.util.OpLogUtils.*;
import static com.xc.boot.modules.goods.model.entity.table.GoodsHasColumnsTableDef.GOODS_HAS_COLUMNS;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
import static com.xc.boot.modules.income.model.entity.table.GoodsIncomeDetailTableDef.GOODS_INCOME_DETAIL;
import static com.xc.boot.modules.merchant.model.entity.table.GoodsColumnTableDef.GOODS_COLUMN;
import static com.xc.boot.modules.merchant.model.entity.table.GoodsIncomeTemplateDetailTableDef.GOODS_INCOME_TEMPLATE_DETAIL;
import static com.xc.boot.modules.merchant.model.entity.table.GoodsIncomeTemplateTableDef.GOODS_INCOME_TEMPLATE;
import static com.xc.boot.modules.oldmaterial.model.entity.table.OldMaterialHasImagesTableDef.OLD_MATERIAL_HAS_IMAGES;
import static com.xc.boot.modules.oldmaterial.model.entity.table.OldMaterialTableDef.OLD_MATERIAL;
import static com.xc.boot.modules.pda.model.entity.table.GoodsHasRfidTableDef.GOODS_HAS_RFID;

/**
 * 货品服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsServiceImpl extends ServiceImpl<GoodsMapper, GoodsEntity> implements GoodsService {
    private final ListFillService fillService;
    private final GoodsHasImagesMapper goodsHasImagesMapper;
    private final GoodsHasColumnsMapper goodsHasColumnsMapper;
    private final GoodsIncomeTemplateDetailMapper goodsIncomeTemplateDetailMapper;
    private final GoodsIncomeTemplateMapper goodsIncomeTemplateMapper;
    private final GoodsColumnMapper goodsColumnMapper;
    private final GoodsLogMapper goodsLogMapper;
    private final GoodsIncomeDetailMapper incomeDetailMapper;
    private final OldMaterialHasImagesMapper oldMaterialHasImagesMapper;
    private final OldMaterialMapper oldMaterialMapper;

    @Override
    public Page<GoodsPageVo> goodsPage(GoodsPageQuery query) {
        QueryWrapper wrapper = buildWrapper(query);
        wrapper.orderBy(GOODS.ID, false);

        // 导出
        if (query.getExport().equals(1)) {
            doExport(wrapper);
            return new Page<>();
        }

        int pageNum = query.getPageNum();
        int pageSize = query.getPageSize();
        // 打印
        if (query.getPrint().equals(1)) {
            long count = this.count(wrapper);
            Assert.isTrue(count <= CommonUtils.getMaxPrintSize(), String.format("打印数据条数超出最大限制%d条", CommonUtils.getMaxPrintSize()));
            pageNum = 1;
            pageSize = CommonUtils.getMaxPrintSize();
        }
        Page<GoodsPageVo> page = this.mapper.paginateAs(pageNum, pageSize, wrapper, GoodsPageVo.class);
        List<GoodsPageVo> records = page.getRecords();
        if (records.isEmpty()) {
            return page;
        }
        this.fillList(records);
        return page;
    }

    @Override
    @Transactional
    public void goodsUpdate(JSONObject form) {
        Long id = form.getLong("id");
        Asserts.notNull(id, "id不能为空");
        Long companyId = SecurityUtils.getCompanyId();
        GoodsEntity goods = this.getById(id);
        Assert.notNull(goods, "货品不存在");
        List<GoodsEntity> goodsEntities = this.mapper.selectListByQuery(QueryWrapper.create()
                .where(GOODS.GOODS_SN.eq(goods.getGoodsSn()))
                .where(GOODS.COMPANY_ID.eq(companyId)));
        Assert.notEmpty(goodsEntities, "货品不存在");
        // 查询用户可编辑字段
        List<CustomColumnBO> goodsDefaultColumn = getGoodsDefaultColumn(companyId, Long.valueOf(goods.getCategoryId()));
        List<String> signs = goodsDefaultColumn.stream().map(CustomColumnBO::getSign).toList();
        int index = 0;
        for (GoodsEntity entity : goodsEntities) {
            getCustomer(signs, entity.getId(), goodsDefaultColumn);
            handleGoodsUpdate(form, entity, companyId, index++, goodsDefaultColumn);
        }
    }

    private void handleGoodsUpdate(JSONObject form, GoodsEntity goods, Long companyId, int index, List<CustomColumnBO> goodsDefaultColumn) {
        Assert.isTrue(goods.getCompanyId().equals(companyId), "无权操作该货品");
        Assert.isTrue(SecurityUtils.getMerchantIds().contains(goods.getMerchantId()) || SecurityUtils.isMain(), "无权操作该货品");
        Map<String, Field> goodsFiledMap = ColumnEncryptUtil.getCloumnFieldMap(GoodsEntity.class);
        // 日志
        StringBuilder logString = new StringBuilder();
        // 价格变更日志
        GoodsPriceChangeBO priceChangeBO = null;

        // 销售方式校验
        String salesTypeStr = form.getStr(OtherColumEnum.SALES_TYPE.getSign());
        Assert.notBlank(salesTypeStr, "销售方式不能为空");
        Integer salesType = Integer.valueOf(salesTypeStr);
        Assert.isTrue(
            salesType.equals(SalesTypeEnum.BY_NUM.getValue()) ||
            salesType.equals(SalesTypeEnum.BY_WEIGHT.getValue()),
            "销售方式不能为空");

        // 从form中获取工费计价方式参数（下划线格式）
        String workPriceTypeStr = form.getStr(OtherColumEnum.WORK_PRICE_TYPE.getSign());
        Assert.notBlank(workPriceTypeStr, "进工费计价方式不能为空");
        Integer workPriceType = Integer.valueOf(workPriceTypeStr);
        Assert.isTrue(
            workPriceType.equals(SalesTypeEnum.BY_NUM.getValue()) ||
            workPriceType.equals(SalesTypeEnum.BY_WEIGHT.getValue()),
            "进工费计价方式不能为空");

        String saleWorkPriceTypeStr = form.getStr(OtherColumEnum.SALE_WORK_PRICE_TYPE.getSign());
        Assert.notBlank(saleWorkPriceTypeStr, "销工费计价方式不能为空");
        Integer saleWorkPriceType = Integer.valueOf(saleWorkPriceTypeStr);
        Assert.isTrue(
            saleWorkPriceType.equals(SalesTypeEnum.BY_NUM.getValue()) ||
            saleWorkPriceType.equals(SalesTypeEnum.BY_WEIGHT.getValue()),
            "销工费计价方式不能为空");

        // 字段处理
        for (CustomColumnBO column : goodsDefaultColumn) {
            // 不能编辑该字段
            if (!column.getSecretLevel().equals(1) && !SecurityUtils.isMain() && !SecurityUtils.showSecret()) {
                continue;
            }
            if (column.getCategory().equals(2) && column.getType().equals(GoodsColumnTypeEnum.SELECT.getValue())) {
                form.set(column.getSign(), form.getStr(column.getSign() + "_id"));
            }
            // form没有传这个字段
            if (!form.containsKey(column.getSign())) {
                if (column.getType().equals(GoodsColumnTypeEnum.IMAGE.getValue())) {
                    form.set(column.getSign(), "[]");
                }else {
                    form.set(column.getSign(), "");
                }
            }
            // 自定义字段处理
            if (column.getCategory().equals(2)) {
                customerColumnHandle(column, form, goods, logString);
                continue;
            }
            // 基础字段处理
            String value = form.getStr(column.getSign());
            Field field = goodsFiledMap.get(column.getSign());
            Object oldValue = ReflectUtil.getFieldValue(goods, field);
            if (oldValue == null) {
                oldValue = "";
            }
            if (value == null || StringUtils.isBlank(value)) {
                if (JoinColumEnum.isJoinColumn(column.getSign())) {
                    value = "0";
                }else {
                    value = "";
                }
            }
            // image处理
            if (column.getSign().equals(OtherColumEnum.IMAGE.getSign())) {
                // 删除旧关联
                List<GoodsHasImagesEntity> oldImages = fillService.getGoodsImgByGoodsId(Set.of(goods.getId())).getOrDefault(goods.getId().toString(), Collections.emptyList());
                List<Long> oldMaterialIds = oldMaterialMapper.selectListByQueryAs(QueryWrapper.create()
                        .select(OLD_MATERIAL.ID)
                        .where(OLD_MATERIAL.GOODS_ID.eq(goods.getId()))
                        .where(OLD_MATERIAL.GOODS_ID.isNotNull())
                        .where(OLD_MATERIAL.GOODS_SN.isNotNull()), Long.class);
                Set<String> oldUrlSet = oldImages.stream().map(GoodsHasImagesEntity::getUrl).collect(Collectors.toSet());
                List<Long> oldIds = oldImages.stream().map(GoodsHasImagesEntity::getId).toList();
                List<Long> oldImgIds = oldImages.stream().map(GoodsHasImagesEntity::getImageId).toList();
                if (CollectionUtil.isNotEmpty(oldIds)) {
                    goodsHasImagesMapper.deleteBatchByIds(oldIds);
                    CommonUtils.batchUpdateFileStatus(oldImgIds, 0);
                }
                if (CollectionUtil.isNotEmpty(oldMaterialIds)) {
                    List<OldMaterialHasImagesEntity> entities = oldMaterialHasImagesMapper.selectListByQuery(QueryWrapper.create().where(OLD_MATERIAL_HAS_IMAGES.OLD_MATERIAL_ID.in(oldMaterialIds)));
                    if (CollectionUtil.isNotEmpty(entities)) {
                        oldMaterialHasImagesMapper.deleteBatchByIds(entities.stream().map(OldMaterialHasImagesEntity::getId).toList());
                        List<Long> oldMaterialImgIds = entities.stream().map(e -> e.getImageId().longValue()).toList();
                        CommonUtils.batchUpdateFileStatus(oldMaterialImgIds, 0);
                    }
                }
                // 处理新图片
                List<GoodsHasImagesEntity> newImages = form.getBeanList(OtherColumEnum.IMAGE.getSign(), GoodsHasImagesEntity.class);
                if (newImages == null) {
                    newImages = new ArrayList<>();
                }
                int sort = 1;
                for (GoodsHasImagesEntity newImage : newImages) {
                    if (newImage.getImageId() == null) {
                        newImage.setImageId(newImage.getId());
                    }
                    newImage.setId(null);
                    newImage.setGoodsId(goods.getId());
                    newImage.setCompanyId(companyId);
                    newImage.setSort(sort++);
                }
                if (CollectionUtil.isNotEmpty(newImages)) {
                    goodsHasImagesMapper.insertBatchSelective(newImages);
                    List<Long> newIds = newImages.stream().map(GoodsHasImagesEntity::getImageId).toList();
                    CommonUtils.batchUpdateFileStatus(newIds, 1);

                    List<OldMaterialHasImagesEntity> oldMaterialHasImages = new ArrayList<>();
                    newImages.forEach(img -> {
                        for (Long id : oldMaterialIds) {
                            OldMaterialHasImagesEntity entity = BeanUtil.copyProperties(img, OldMaterialHasImagesEntity.class);
                            entity.setOldMaterialId(id.intValue());
                            entity.setId(null);
                            oldMaterialHasImages.add(entity);
                        }
                    });
                    if (CollectionUtil.isNotEmpty(oldMaterialHasImages)) {
                        oldMaterialHasImagesMapper.insertBatchSelective(oldMaterialHasImages);
                    }
                }

                List<String> newUrls = newImages.stream().map(GoodsHasImagesEntity::getUrl).toList();
                boolean needLog = oldUrlSet.size() != newUrls.size();
                for (String newUrl : newUrls) {
                    if (!oldUrlSet.contains(newUrl)) {
                        needLog = true;
                        break;
                    }
                }
                // 记录日志
                if (needLog) {
                    logString.append("图片").append(COLON).append(String.join(",", oldUrlSet)).append(MODIFY_STRING).append(String.join(",", newUrls)).append(NEW_LINE);
                }
                continue;
            }

            // 如果是价格字段， 元 转 分
            if (PriceColumEnum.isPriceColumn(column.getSign())) {
                value = PriceUtil.yuan2fen(value).toPlainString();
            }

            // 如果新老值相同 不做修改
            try {
                // 尝试比较双方是否是数字类型
                BigDecimal old = new BigDecimal(oldValue.toString());
                BigDecimal newValue = new BigDecimal(value);
                if (old.compareTo(newValue) == 0) {
                    continue;
                }
            } catch (Exception ignore) {
            }
            // 尝试比较string是否相同
            if (oldValue.toString().equals(value)) {
                continue;
            }
            // 记录价格变更
            if (PriceColumEnum.isPriceColumn(column.getSign())) {
                if (priceChangeBO == null) {
                    priceChangeBO = new GoodsPriceChangeBO()
                            .setGoodsId(goods.getId())
                            .setComment("货品列表修改货品");
                }
                long priceChange = new BigDecimal(value).subtract(PriceUtil.formatTwoDecimal(oldValue.toString())).longValue();
                try {
                    Field priceField = GoodsPriceChangeBO.class.getDeclaredField(column.getSign());
                    priceField.setAccessible(true);
                    ReflectUtil.setFieldValue(priceChangeBO, priceField, priceChange);
                }catch (Exception ignore) {}
            }
            // 修改为新值
            // 获取旧值名称（如果是_id类型 则取到label值）
            String oldValueName = getValueNameBySign(column.getSign(), oldValue);
            try {
                if (StringUtils.isBlank(value)) {
                    logString.append(column.getName()).append(COLON).append(oldValueName).append(MODIFY_STRING).append(" ").append(NEW_LINE);
                    if (column.getType().equals(GoodsColumnTypeEnum.NUMBER.getValue()) || column.getType().equals(GoodsColumnTypeEnum.SELECT.getValue())) {
                        ReflectUtil.setFieldValue(goods, field, 0);
                    }else {
                        field.set(goods, null);
                    }
                } else {
                    String newName = getValueNameBySign(column.getSign(), value);
                    logString.append(column.getName()).append(COLON).append(oldValueName).append(MODIFY_STRING).append(newName).append(NEW_LINE);
                    ReflectUtil.setFieldValue(goods, field, value);
                }
            } catch (Exception e) {
                log.error("货品修改失败", e);
                throw new BusinessException("货品修改失败");
            }
        }
        // 修改前校验
        // 1：成本单价 = 金进金额 + 银进金额 + 进工费 + 证书费（根据工费计价方式计算）
        // 2：证书费不能大于成本单价
        // 计算各分项金额（每步都四舍五入，对齐前端计算方式）
        BigDecimal goldAmount = PriceUtil.fen2yuan(goods.getGoldPrice()).multiply(PriceUtil.formatThreeDecimal(goods.getNetGoldWeight())).setScale(2, RoundingMode.HALF_UP);
        BigDecimal silverAmount = PriceUtil.fen2yuan(goods.getSilverPrice()).multiply(PriceUtil.formatThreeDecimal(goods.getNetSilverWeight())).setScale(2, RoundingMode.HALF_UP);
        BigDecimal certAmount = PriceUtil.fen2yuan(goods.getCertPrice()).setScale(2, RoundingMode.HALF_UP);

        // 计算工费金额，根据工费计价方式
        BigDecimal workAmount = BigDecimal.ZERO;
        if (goods.getWorkPrice() != null && goods.getWorkPrice() > 0) {
            BigDecimal workPrice = PriceUtil.fen2yuan(goods.getWorkPrice());
            Integer goodsWorkPriceType = goods.getWorkPriceType() != null ? goods.getWorkPriceType() : SalesTypeEnum.BY_NUM.getValue();

            if (goodsWorkPriceType.equals(SalesTypeEnum.BY_WEIGHT.getValue())) {
                // 按重量：工费金额 = 工费单价 × (净金重 + 净银重)
                BigDecimal netGoldWeight = PriceUtil.formatThreeDecimal(goods.getNetGoldWeight());
                BigDecimal netSilverWeight = PriceUtil.formatThreeDecimal(goods.getNetSilverWeight());
                BigDecimal workWeight = netGoldWeight.add(netSilverWeight);
                workAmount = workPrice.multiply(workWeight).setScale(2, RoundingMode.HALF_UP);
            } else {
                // 按数量：直接取进工费单价
                workAmount = workPrice.setScale(2, RoundingMode.HALF_UP);
            }
        }

        // 计算总成本（各分项已四舍五入，直接相加）
        BigDecimal costYuan = goldAmount.add(silverAmount).add(workAmount).add(certAmount);
        if (!goods.getGoldPrice().equals(0L) || !goods.getSilverPrice().equals(0L) || !goods.getWorkPrice().equals(0L)) {
            // 使用BigDecimal.compareTo比较数值大小，忽略精度差异
            BigDecimal actualCostPrice = PriceUtil.fen2yuan(goods.getCostPrice());
            if (actualCostPrice.compareTo(costYuan) != 0) {
                CommonUtils.abort("成本单价与分项金额不匹配");
            }
        }
        Assert.isTrue(goods.getCertPrice() <= goods.getCostPrice(), "证书费不能大于成本单价");

        // 重量为0时处理: 金重 + 银重 + 主石重 * 主石数量 * 0.2 + 辅石重 * 辅石数 *  0.2
        if (Objects.isNull(goods.getWeight()) || goods.getWeight().compareTo(BigDecimal.ZERO) == 0) {
            BigDecimal weight = PriceUtil.formatThreeDecimal(goods.getNetGoldWeight())
                    .add(PriceUtil.formatThreeDecimal(goods.getNetSilverWeight()))
                    .add(PriceUtil.formatThreeDecimal(goods.getMainStoneWeight())
                            .multiply(new BigDecimal(goods.getMainStoneCount()))
                            .multiply(new BigDecimal("0.2")))
                    .add(PriceUtil.formatThreeDecimal(goods.getSubStoneWeight())
                            .multiply(new BigDecimal(goods.getSubStoneCount()))
                            .multiply(new BigDecimal("0.2")));
            goods.setWeight(PriceUtil.formatThreeDecimal(weight));
        }
        this.mapper.update(goods, false);
        updateOldMaterial(goods);
        if (priceChangeBO != null) {
            PriceUtil.logPriceChange(List.of(priceChangeBO));
        }
        if (!logString.isEmpty() && index == 0) {
            OpLogUtils.appendGoodsLog("货品列表-修改货品", logString.toString(), null, goods);
        }
    }

    private void customerColumnHandle(CustomColumnBO column, JSONObject form, GoodsEntity goods, StringBuilder logString) {
        // 没有则新增 有则更新
        GoodsHasColumnsEntity goodsHasColumns;
        if (column.getGoodsHasColumnId() == null) {
            goodsHasColumns = new GoodsHasColumnsEntity()
                    .setGoodsId(goods.getId())
                    .setColumnId(column.getId().intValue())
                    .setColumnSign(column.getSign())
                    .setValue("")
                    .setImageId(0)
                    .setCompanyId(SecurityUtils.getCompanyId());
        }else {
            goodsHasColumns = new GoodsHasColumnsEntity();
            goodsHasColumns.setId(column.getGoodsHasColumnId());
        }

        // 图片处理
        if (column.getType().equals(GoodsColumnTypeEnum.IMAGE.getValue())) {
            if (StringUtils.isBlank(form.getStr(column.getSign()))) {
                return;
            }
            List<GoodsHasImagesEntity> images = form.getBeanList(column.getSign(), GoodsHasImagesEntity.class);
            GoodsHasImagesEntity image = null;
            if (CollectionUtil.isNotEmpty(images)) {
                image = images.getFirst();
                if (image.getImageId() == null) {
                    image.setImageId(image.getId());
                }
            }
            // 更新
            if (image != null && image.getImageId() != null && !image.getImageId().equals(column.getImageId())) {
                CommonUtils.updateFileStatus(column.getImageId(), 0);
                CommonUtils.updateFileStatus(image.getImageId(), 1);
                goodsHasColumns.setImageId(image.getImageId().intValue());
                goodsHasColumns.setValue(image.getUrl());
                logString.append(column.getName()).append(COLON).append(column.getValue() == null ? " " : column.getValue())
                        .append(MODIFY_STRING).append(image.getUrl()).append(NEW_LINE);
            }
            // 删除
            if (image == null && column.getImageId() != null && column.getImageId() > 0) {
                CommonUtils.updateFileStatus(column.getImageId(), 0);
                goodsHasColumns.setImageId(0);
                goodsHasColumns.setValue("");
                logString.append(column.getName()).append(COLON).append(column.getValue()).append(MODIFY_STRING).append(" ").append(NEW_LINE);
            }
        }else {
            String newValue = form.getStr(column.getSign());
            if (column.getValue() != null && column.getValue().equals(newValue)) {
                return;
            }
            goodsHasColumns.setValue(newValue);
            // 下拉列表类型 取label记录日志
            if (column.getType().equals(GoodsColumnTypeEnum.SELECT.getValue())) {
                try {
                    Map<String, String> labelMap = JSONUtil.toList(column.getOptions(), Option.class).stream()
                            .collect(Collectors.toMap(item -> item.getValue().toString(), Option::getLabel, (v1, v2) -> v1));
                    boolean isMultiple = column.getIsMultiple().equals(1);
                    // 多选下拉处理
                    if (isMultiple) {
                        try {
                            List<String> oldValues = JSONUtil.toList(column.getValue(), String.class);
                            StringBuilder oldLabel = new StringBuilder();
                            for (String oldValue : oldValues) {
                                oldLabel.append(labelMap.get(oldValue)).append(",");
                            }
                            if (!oldLabel.isEmpty()) {
                                oldLabel.deleteCharAt(oldLabel.length() - 1);
                            }
                            List<String> newValueName = JSONUtil.toList(newValue, String.class);
                            StringBuilder newLabel = new StringBuilder();
                            for (String value : newValueName) {
                                newLabel.append(labelMap.get(value)).append(",");
                            }
                            if (!newLabel.isEmpty()) {
                                newLabel.deleteCharAt(newLabel.length() - 1);
                            }
                            logString.append(column.getName()).append(COLON).append(oldLabel).append(MODIFY_STRING).append(newLabel).append(NEW_LINE);
                        }catch (Exception ignore){

                        }
                    }else {
                        // 单选下拉处理
                        String oldValue = labelMap.get(column.getValue());
                        oldValue = oldValue == null ? " " : oldValue;
                        String newValueName = labelMap.get(newValue);
                        newValueName = newValueName == null ? " " : newValueName;
                        logString.append(column.getName()).append(COLON).append(oldValue).append(MODIFY_STRING).append(newValueName).append(NEW_LINE);
                    }
                }catch (Exception ignore){}
            }else {
                logString.append(column.getName()).append(COLON).append(column.getValue() != null ? column.getValue() : " ").append(MODIFY_STRING).append(newValue).append(NEW_LINE);
            }
        }
        goodsHasColumnsMapper.insertOrUpdate(goodsHasColumns,  true);
    }


    @Override
    public GoodsStatisticVo statistic(GoodsPageQuery query) {
        QueryWrapper wrapper = buildWrapper(query);
        wrapper.select(sum(GOODS.STOCK_NUM).as("totalStock"),
                sum(GOODS.NET_GOLD_WEIGHT.multiply(GOODS.STOCK_NUM)).as("totalNetGoldWeight"),
                sum(GOODS.NET_SILVER_WEIGHT.multiply(GOODS.STOCK_NUM)).as("totalNetSilverWeight"),
                sum(GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM)).as("totalCost"));
        GoodsStatisticVo vo = mapper.selectOneByQueryAs(wrapper, GoodsStatisticVo.class);
        if (Objects.isNull(vo)) {
            return new GoodsStatisticVo();
        }
        vo.setTotalCost(PriceUtil.fen2yuanString(vo.getTotalCost()));
        vo.setTotalNetGoldWeight(PriceUtil.formatThreeDecimal(vo.getTotalNetGoldWeight()));
        vo.setTotalNetSilverWeight(PriceUtil.formatThreeDecimal(vo.getTotalNetSilverWeight()));
        GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(PriceColumEnum.COST_PRICE.getSign());
        String cost = ColumnEncryptUtil.handleEncryptPrice(vo.getTotalCost(), column);
        vo.setTotalCost(cost);
        return vo;
    }

    @Override
    public Page<GoodsLogVo> log(String goodsSn, Integer pageNum, Integer pageSize) {
        QueryWrapper wrapper = QueryWrapper.create()
                .where(GoodsLogEntity::getGoodsSn).eq(goodsSn)
                .where(GoodsLogEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .where(GoodsLogEntity::getMerchantId).in(SecurityUtils.getMerchantIds(), !SecurityUtils.isMain())
                .orderBy(GoodsLogEntity::getCreatedAt, false);
        Page<GoodsLogVo> page = goodsLogMapper.paginateAs(pageNum, pageSize, wrapper, GoodsLogVo.class);
        List<GoodsLogVo> vos = page.getRecords();
        if (vos.isEmpty()) {
            return page;
        }
        ListFillUtil.of(vos)
                .build(fillService::getUserNameByUserId, vos.stream().map(GoodsLogVo::getUserId).collect(Collectors.toSet()), "userId", "creatorName")
                .handle();
        return page;
    }

    @Override
    public List<JSONObject> print(GoodsPrintQuery query) {
        List<GoodsPrintVo> vos = null;
        boolean isIncome = Objects.nonNull(query.getIncomeId());
        if (CollectionUtil.isNotEmpty(query.getGoodsIds())) {
            vos = this.mapper.selectListByQueryAs(QueryWrapper.create()
                    .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                    .where(GOODS.MERCHANT_ID.in(SecurityUtils.getMerchantIds()))
                    .where(GOODS.ID.in(query.getGoodsIds())), GoodsPrintVo.class);
        }

        if (Objects.nonNull(query.getIncomeId())) {
            QueryWrapper wrapper = QueryWrapper.create()
                    .where(GOODS_INCOME_DETAIL.RECEIVE_ID.eq(query.getIncomeId()))
                    .where(GOODS_INCOME_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                    .where(GOODS_INCOME_DETAIL.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
            if (CollectionUtil.isNotEmpty(query.getDetailIds())) {
                wrapper.where(GOODS_INCOME_DETAIL.ID.in(query.getDetailIds()));
            }
            vos = incomeDetailMapper.selectListByQueryAs(wrapper, GoodsPrintVo.class);
        }
        if (CollectionUtil.isEmpty(vos)) {
            return new ArrayList<>();
        }
        // 填充
        Set<Long> categoryIds  = new HashSet<>(List.of(0L));
        Set<Long> subclassIds  = new HashSet<>(List.of(0L));
        Set<Long> brandIds  = new HashSet<>(List.of(0L));
        Set<Long> styleIds  = new HashSet<>(List.of(0L));
        Set<Long> qualityIds  = new HashSet<>(List.of(0L));
        Set<Long> technologyIds  = new HashSet<>(List.of(0L));
        Set<Long> mainStoneIds  = new HashSet<>(List.of(0L));
        Set<Long> subStoneIds  = new HashSet<>(List.of(0L));
        Set<Long> counterIds  = new HashSet<>(List.of(0L));
        Set<Long> ids  = new HashSet<>();
        vos.forEach(vo -> {
            categoryIds.add(vo.getCategoryId());
            subclassIds.add(vo.getSubclassId());
            brandIds.add(vo.getBrandId());
            styleIds.add(vo.getStyleId());
            qualityIds.add(vo.getQualityId());
            technologyIds.add(vo.getTechnologyId());
            mainStoneIds.add(vo.getMainStoneId());
            subStoneIds.add(vo.getSubStoneId());
            counterIds.add(vo.getCounterId());
            ids.add(vo.getId());
        });
        ListFillUtil fillUtil = ListFillUtil.of(vos)
                .build(fillService::getCategoryNameById, categoryIds, "categoryId", "category")
                .build(fillService::getSubclassNameById, subclassIds, "subclassId", "subclass")
                .build(fillService::getBrandNameById, brandIds, "brandId", "brand")
                .build(fillService::getStyleNameById, styleIds, "styleId", "style")
                .build(fillService::getQualityNameById, qualityIds, "qualityId", "quality")
                .build(fillService::getQualityContentById, qualityIds, "qualityId", "qualityContent")
                .build(fillService::getTechnologyNameById, technologyIds, "technologyId", "technology")
                .build(fillService::getJewelryMapperNameById, mainStoneIds, "mainStoneId", "mainStone")
                .build(fillService::getJewelryMapperNameById, subStoneIds, "subStoneId", "subStone")
                .build(fillService::getCounterNameById, counterIds, "counterId", "counter")
                // .build(fillService::getBindRfidStatusByGoodsId, ids, "id", "bindRfid")
                // .build(fillService::getRfidByGoodsId, ids, "id", "rfid")
                .peek(obj -> {
                    GoodsPrintVo vo = (GoodsPrintVo) obj;
                    if (vo.getCustomerColumns() != null) {
                        vo.setCustomerColumns(vo.getCustomerColumns().stream().filter(column -> !column.getType().equals(GoodsColumnTypeEnum.IMAGE.getValue())).toList());
                    }

                    vo.setCostPrice(PriceUtil.fen2yuanString(vo.getCostPrice()));
                    vo.setCostPriceClear(PriceUtil.yuanClear(vo.getCostPrice()));
                    vo.setCostPricePreUnit(PriceUtil.yuanPreUnit(vo.getCostPrice()));
                    vo.setCostPricePreUnitClear(PriceUtil.yuanPreUnitClear(vo.getCostPrice()));
                    vo.setCostPriceLastUnit(PriceUtil.yuanLastUnit(vo.getCostPrice()));
                    vo.setCostPriceLastUnitClear(PriceUtil.yuanLastUnitClear(vo.getCostPrice()));

                    // 处理工费单价，根据销工费计价方式拼接单位
                    String workPriceStr = PriceUtil.fen2yuanString(vo.getSaleWorkPrice());
                    String workPriceUnit = "";
                    if (vo.getSaleWorkPriceType() != null) {
                        if (vo.getSaleWorkPriceType().equals(SalesTypeEnum.BY_WEIGHT.getValue())) {
                            workPriceUnit = "/克";
                        } else if (vo.getSaleWorkPriceType().equals(SalesTypeEnum.BY_NUM.getValue())) {
                            workPriceUnit = "/件";
                        }
                    }

                    vo.setWorkPrice(workPriceStr + workPriceUnit);
                    vo.setWorkPriceClear(PriceUtil.yuanClear(workPriceStr) + workPriceUnit);
                    vo.setWorkPricePreUnit(PriceUtil.yuanPreUnit(workPriceStr) + workPriceUnit);
                    vo.setWorkPricePreUnitClear(PriceUtil.yuanPreUnitClear(workPriceStr) + workPriceUnit);
                    vo.setWorkPriceLastUnit(PriceUtil.yuanLastUnit(workPriceStr) + workPriceUnit);
                    vo.setWorkPriceLastUnitClear(PriceUtil.yuanLastUnitClear(workPriceStr) + workPriceUnit);

                    vo.setTagPrice(PriceUtil.fen2yuanString(vo.getTagPrice()));
                    vo.setTagPriceClear(PriceUtil.yuanClear(vo.getTagPrice()));
                    vo.setTagPricePreUnit(PriceUtil.yuanPreUnit(vo.getTagPrice()));
                    vo.setTagPricePreUnitClear(PriceUtil.yuanPreUnitClear(vo.getTagPrice()));
                    vo.setTagPriceLastUnit(PriceUtil.yuanLastUnit(vo.getTagPrice()));
                    vo.setTagPriceLastUnitClear(PriceUtil.yuanLastUnitClear(vo.getTagPrice()));

                    vo.setCertPrice(PriceUtil.fen2yuanString(vo.getCertPrice()));
                    vo.setCertPriceClear(PriceUtil.yuanClear(vo.getCertPrice()));
                    vo.setCertPricePreUnit(PriceUtil.yuanPreUnit(vo.getCertPrice()));
                    vo.setCertPricePreUnitClear(PriceUtil.yuanPreUnitClear(vo.getCertPrice()));
                    vo.setCertPriceLastUnit(PriceUtil.yuanLastUnit(vo.getCertPrice()));
                    vo.setCertPriceLastUnitClear(PriceUtil.yuanLastUnitClear(vo.getCertPrice()));
                });
        if (isIncome) {
            fillUtil.build(fillService::getIncomeColumnVosById, ids, "id", "customerColumns");
        }else {
            fillUtil.build(fillService::getColumnVosById, ids, "id", "customerColumns");
        }
        fillUtil.handle();
        return ColumnEncryptUtil.encrypt(vos, GoodsPrintVo.class, "customerColumns");
    }

    @Override
    public List<GoodsPrintFieldVo> printFields() {
        // 基本字段
        Field[] fields = ReflectUtil.getFields(GoodsPrintVo.class);
        List<GoodsPrintFieldVo> base = Arrays.stream(fields)
                .filter(field -> field.getAnnotation(Schema.class) != null)
                .map(field -> new GoodsPrintFieldVo(field.getAnnotation(Schema.class).description(), field.getName(), 0))
                .toList();
        List<GoodsPrintFieldVo> list = new ArrayList<>(base);
        // 自定义字段
        List<GoodsPrintFieldVo> custom = CommonUtils.getGoodsColumns().stream()
                .filter(column -> column.getCategory().equals(2) && !column.getType().equals(GoodsColumnTypeEnum.IMAGE.getValue()))
                .map(column -> new GoodsPrintFieldVo(column.getName(), column.getSign(), 1))
                .toList();
        list.addAll(custom);
        return list;
    }

    /**
     * 根据大类id，查询当前用户可编辑的字段（包括基础字段和自定义字段）
     */
    private List<CustomColumnBO> getGoodsDefaultColumn(Long companyId, Long categoryId) {
        // 查询默认模板
        List<GoodsIncomeTemplateEntity> list = goodsIncomeTemplateMapper.selectListByQuery(
                QueryWrapper.create()
                        .where(GOODS_INCOME_TEMPLATE.CATEGORY_ID.eq(categoryId))
                        .and(GOODS_INCOME_TEMPLATE.STATUS.eq(1))
                        .orderBy(GOODS_INCOME_TEMPLATE.DEFAULT_FLAG, false)
                        .orderBy(GOODS_INCOME_TEMPLATE.ID, false)
        );
        Assert.notEmpty(list, "当前大类下无生效中默认入库模板");
        GoodsIncomeTemplateEntity template = list.getFirst();
        List<GoodsIncomeTemplateDetailEntity> templateDetails = goodsIncomeTemplateDetailMapper.selectListByQuery(QueryWrapper.create()
                .where(GOODS_INCOME_TEMPLATE_DETAIL.ENABLED.eq(1).and(GOODS_INCOME_TEMPLATE_DETAIL.TEMPLATE_ID.eq(template.getId())))
        );
        List<String> signs = templateDetails.stream().map(GoodsIncomeTemplateDetailEntity::getSign).toList();
        signs = signs.stream().filter(sign -> !sign.equals("goods_sn") && !sign.equals("counter_id") && !sign.equals("num")).toList();
        return goodsColumnMapper.selectListByQueryAs(QueryWrapper.create()
                .where(GOODS_COLUMN.SIGN.in(signs))
                .where(GOODS_COLUMN.COMPANY_ID.eq(companyId))
                .select(GOODS_COLUMN.ALL_COLUMNS), CustomColumnBO.class);
    }

    private void getCustomer(List<String> signs, Long goodsId, List<CustomColumnBO> columnBOS){
        Map<String, CustomColumnBO> map = columnBOS.stream().collect(Collectors.toMap(CustomColumnBO::getSign, columnBO -> columnBO));
        // 填充自定义字段
        List<GoodsHasColumnsEntity> goodsHasColumns = goodsHasColumnsMapper.selectListByQuery(QueryWrapper.create()
                .where(GOODS_HAS_COLUMNS.GOODS_ID.eq(goodsId))
                .where(GOODS_HAS_COLUMNS.COLUMN_SIGN.in(signs))
                .where(GOODS_HAS_COLUMNS.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        for (GoodsHasColumnsEntity goodsHasColumn : goodsHasColumns) {
            CustomColumnBO columnBO = map.get(goodsHasColumn.getColumnSign());
            columnBO.setValue(goodsHasColumn.getValue());
            Integer imageId = goodsHasColumn.getImageId();
            if (imageId != null) {
                columnBO.setImageId(imageId.longValue());
            }
            columnBO.setGoodsHasColumnId(goodsHasColumn.getId());
            columnBO.setGoodsId(goodsHasColumn.getGoodsId());
        }
    }

    private QueryWrapper buildWrapper(GoodsPageQuery query) {
        // 固定条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS.NUM.gt(0));
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        merchantIds.add(0L);
        // 非主账号限制门店
        queryWrapper.where(GOODS.MERCHANT_ID.in(merchantIds, !SecurityUtils.isMain()));
        // 筛选条件
        queryWrapper.where(GOODS.GOODS_SN.eq(query.getGoodsSn(), StringUtils.isNotBlank(query.getGoodsSn())));
        queryWrapper.where(GOODS.NAME.like(query.getName(), StringUtils.isNotBlank(query.getName())));
        if (StringUtils.isNotBlank(query.getMerchantIds())) {
            queryWrapper.where(GOODS.MERCHANT_ID.in(List.of(query.getMerchantIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getSupplierIds())) {
            queryWrapper.where(GOODS.SUPPLIER_ID.in(List.of(query.getSupplierIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getStyleIds())){
            queryWrapper.where(GOODS.STYLE_ID.in(List.of(query.getStyleIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getCategoryIds())){
            queryWrapper.where(GOODS.CATEGORY_ID.in(List.of(query.getCategoryIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getSubclassIds())){
            queryWrapper.where(GOODS.SUBCLASS_ID.in(List.of(query.getSubclassIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getCounterIds())){
            queryWrapper.where(GOODS.COUNTER_ID.in(List.of(query.getCounterIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getQualityIds())){
            queryWrapper.where(GOODS.QUALITY_ID.in(List.of(query.getQualityIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getBrandIds())){
            queryWrapper.where(GOODS.BRAND_ID.in(List.of(query.getBrandIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getMainStoneIds())){
            queryWrapper.where(GOODS.MAIN_STONE_ID.in(List.of(query.getMainStoneIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getSubStoneIds())){
            queryWrapper.where(GOODS.SUB_STONE_ID.in(List.of(query.getSubStoneIds().split(","))));
        }

        if (query.getStatus() != null) {
            String[] split = query.getStatus().split(",");
            for (String status : split) {
                if (status.equals("1")) {
                    queryWrapper.where(GOODS.STOCK_NUM.gt(0));
                }else if (status.equals("2")) {
                    queryWrapper.where(GOODS.SOLD_NUM.gt(0));
                }
            }
        }
        if (CollectionUtil.isNotEmpty(query.getIds())) {
            queryWrapper.where(GOODS.ID.in(query.getIds()));
        }

        // RFID筛选
        if (StringUtils.isNotBlank(query.getBindRfid())) {
            if ("1".equals(query.getBindRfid())) {
                // 已绑定RFID：存在关联记录
                queryWrapper.where(GOODS.ID.in(
                    QueryWrapper.create()
                        .select(GOODS_HAS_RFID.GOODS_ID)
                        .from(GOODS_HAS_RFID)
                        .where(GOODS_HAS_RFID.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                ));
            } else if ("0".equals(query.getBindRfid())) {
                // 未绑定RFID：不存在关联记录
                queryWrapper.where(GOODS.ID.notIn(
                    QueryWrapper.create()
                        .select(GOODS_HAS_RFID.GOODS_ID)
                        .from(GOODS_HAS_RFID)
                        .where(GOODS_HAS_RFID.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                ));
            }
        }

        // RFID值筛选
        if (StringUtils.isNotBlank(query.getRfid())) {
            queryWrapper.where(GOODS.ID.in(
                QueryWrapper.create()
                    .select(GOODS_HAS_RFID.GOODS_ID)
                    .from(GOODS_HAS_RFID)
                    .where(GOODS_HAS_RFID.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                    .where(GOODS_HAS_RFID.RFID.eq(query.getRfid()))
            ));
        }

        return queryWrapper;
    }

    private void fillList(List<GoodsPageVo> list) {
        ListFillUtilV2.of(list)
                .build(fillService::getMerchantNameById, GoodsPageVo::getMerchantId, GoodsPageVo::setMerchant)
                .build(fillService::getCounterNameById, GoodsPageVo::getCounterId, GoodsPageVo::setCounter)
                .build(fillService::getSupplierNameById, GoodsPageVo::getSupplierId, GoodsPageVo::setSupplier)
                .build(fillService::getCategoryNameById, GoodsPageVo::getCategoryId, GoodsPageVo::setCategory)
                .build(fillService::getSubclassNameById, GoodsPageVo::getSubclassId, GoodsPageVo::setSubclass)
                .build(fillService::getBrandNameById, GoodsPageVo::getBrandId, GoodsPageVo::setBrand)
                .build(fillService::getStyleNameById, GoodsPageVo::getStyleId, GoodsPageVo::setStyle)
                .build(fillService::getQualityNameById, GoodsPageVo::getQualityId, GoodsPageVo::setQuality)
                .build(fillService::getTechnologyNameById, GoodsPageVo::getTechnologyId, GoodsPageVo::setTechnology)
                .build(fillService::getJewelryMapperNameById, GoodsPageVo::getMainStoneId, GoodsPageVo::setMainStone)
                .build(fillService::getJewelryMapperNameById, GoodsPageVo::getSubStoneId, GoodsPageVo::setSubStone)
                .build(fillService::getColumnVosById, GoodsPageVo::getId, GoodsPageVo::setCustomerColumns)
                .build(fillService::getGoodsImgByGoodsId, GoodsPageVo::getId, GoodsPageVo::setImages)
                .build(fillService::getBindRfidStatusByGoodsId, GoodsPageVo::getId, GoodsPageVo::setBindRfid)
                .build(fillService::getRfidByGoodsId, GoodsPageVo::getId, GoodsPageVo::setRfid)
                .peek(vo -> {
                    // 价格字段format
                    vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
                    vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
                    vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
                    vo.setWorkPrice(PriceUtil.fen2yuan(vo.getWorkPrice()));
                    vo.setCertPrice(PriceUtil.fen2yuan(vo.getCertPrice()));
                    vo.setSaleWorkPrice(PriceUtil.fen2yuan(vo.getSaleWorkPrice()));
                    vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
                    if (vo.getImages() == null) {
                        vo.setImages(new ArrayList<>());
                    }
                    if (vo.getCustomerColumns() == null) {
                        vo.setCustomerColumns(new ArrayList<>());
                    }
                    vo.setSalesTypeName(OtherColumEnum.getSalesTypeText(vo.getSalesType()));
                    vo.setWorkPriceTypeName(OtherColumEnum.getWorkPriceTypeText(vo.getWorkPriceType()));
                    vo.setSaleWorkPriceTypeName(OtherColumEnum.getWorkPriceTypeText(vo.getSaleWorkPriceType()));
                })
                .handle();
    }

    private void doExport(QueryWrapper wrapper) {
        ExcelUtil.of(this.mapper, wrapper, GoodsPageVo.class, "goods", "货品列表")
                .getData((mapper, queryWrapper) -> {
                    if (mapper instanceof GoodsMapper goodsMapper) {
                        List<GoodsPageVo> list = goodsMapper.selectListByQueryAs(wrapper, GoodsPageVo.class);
                        this.fillList(list);
                        List<JSONObject> columns = ColumnEncryptUtil.encrypt(list, GoodsPageVo.class, "customerColumns");
                        ColumnEncryptUtil.handleJsonImageExport(columns);
                        columns.forEach(item -> {
                            String salesType = item.getStr(OtherColumEnum.SALES_TYPE.getSign());
                            if (StringUtils.isNotBlank(salesType)) {
                                item.set(OtherColumEnum.SALES_TYPE.getSign(), OtherColumEnum.getSalesTypeText(salesType));
                            }
                            String workPriceType = item.getStr(OtherColumEnum.WORK_PRICE_TYPE.getSign());
                            if (StringUtils.isNotBlank(workPriceType)) {
                                item.set(OtherColumEnum.WORK_PRICE_TYPE.getSign(), OtherColumEnum.getWorkPriceTypeText(workPriceType));
                            }
                            String saleWorkPriceType = item.getStr(OtherColumEnum.SALE_WORK_PRICE_TYPE.getSign());
                            if (StringUtils.isNotBlank(saleWorkPriceType)) {
                                item.set(OtherColumEnum.SALE_WORK_PRICE_TYPE.getSign(), OtherColumEnum.getWorkPriceTypeText(saleWorkPriceType));
                            }
                        });
                        return columns;
                    }
                    return new ArrayList<>();
                })
                .signHeadingConfig(columns -> {
                    List<HeadingColumns> newColumns = new ArrayList<>();
                    for (HeadingColumns column : columns) {
                        if (!column.getProp().equals(NumColumEnum.NUM_DETAIL.getField())) {
                            newColumns.add(column);
                            continue;
                        }
                        // 处理数量字段
                        newColumns.add(new HeadingColumns()
                                .setLabel(NumColumEnum.STOCK_NUM.getLabel())
                                .setProp(NumColumEnum.STOCK_NUM.getField())
                                .setIsImg(false)
                                .setShow(true));
                        newColumns.add(new HeadingColumns()
                                .setLabel(NumColumEnum.SOLD_NUM.getLabel())
                                .setProp(NumColumEnum.SOLD_NUM.getField())
                                .setIsImg(false)
                                .setShow(true));
                        newColumns.add(new HeadingColumns()
                                .setLabel(NumColumEnum.RETURN_NUM.getLabel())
                                .setProp(NumColumEnum.RETURN_NUM.getField())
                                .setIsImg(false)
                                .setShow(true));
                        newColumns.add(new HeadingColumns()
                                .setLabel(NumColumEnum.TRANSFER_NUM.getLabel())
                                .setProp(NumColumEnum.TRANSFER_NUM.getField())
                                .setIsImg(false)
                                .setShow(true));
                        newColumns.add(new HeadingColumns()
                                .setLabel(NumColumEnum.FROZEN_NUM.getLabel())
                                .setProp(NumColumEnum.FROZEN_NUM.getField())
                                .setIsImg(false)
                                .setShow(true));
                    }
                    return newColumns;
                })
                .doExport();
    }

    private void updateOldMaterial(GoodsEntity goods) {
        UpdateChain.of(OldMaterialEntity.class)
                .where(OldMaterialEntity::getGoodsSn).eq(goods.getGoodsSn())
                .set(OldMaterialEntity::getSupplierId, goods.getSupplierId())
                .set(OldMaterialEntity::getCategoryId, goods.getCategoryId())
                .set(OldMaterialEntity::getSubclassId, goods.getSubclassId())
                .set(OldMaterialEntity::getBrandId, goods.getBrandId())
                .set(OldMaterialEntity::getStyleId, goods.getStyleId())
                .set(OldMaterialEntity::getQualityId, goods.getQualityId())
                .set(OldMaterialEntity::getTechnologyId, goods.getTechnologyId())
                .set(OldMaterialEntity::getMainStoneId, goods.getMainStoneId())
                .set(OldMaterialEntity::getSubStoneId, goods.getSubStoneId())
                .set(OldMaterialEntity::getName, goods.getName())
                .set(OldMaterialEntity::getBatchNo, goods.getBatchNo())
                .set(OldMaterialEntity::getCertNo, goods.getCertNo())
                .set(OldMaterialEntity::getRemark, goods.getRemark())
                .set(OldMaterialEntity::getWeight, goods.getWeight())
                .set(OldMaterialEntity::getNetGoldWeight, goods.getNetGoldWeight())
                .set(OldMaterialEntity::getNetSilverWeight, goods.getNetSilverWeight())
                .set(OldMaterialEntity::getMainStoneCount, goods.getMainStoneCount())
                .set(OldMaterialEntity::getMainStoneWeight, goods.getMainStoneWeight())
                .set(OldMaterialEntity::getSubStoneCount, goods.getSubStoneCount())
                .set(OldMaterialEntity::getSubStoneWeight, goods.getSubStoneWeight())
                .set(OldMaterialEntity::getCircleSize, goods.getCircleSize())
                .set(OldMaterialEntity::getCostPrice, goods.getCostPrice())
                .set(OldMaterialEntity::getWorkPrice, goods.getWorkPrice())
                .set(OldMaterialEntity::getCertPrice, goods.getCertPrice())
                .set(OldMaterialEntity::getSaleWorkPrice, goods.getSaleWorkPrice())
                .set(OldMaterialEntity::getTagPrice, goods.getTagPrice())
                .update();
    }


    /**
     * 关联字段类型获取字段名称,用于日志记录
     */
    private String getValueNameBySign(String sign, Object value) {
        if (Objects.isNull(value)) {
            return "";
        }
        if (JoinColumEnum.COUNTER_ID.getSign().equals(sign)) {
            String name = fillService.getCounterNameById(Set.of(value.toString())).get(value.toString());
            return StringUtils.isNotBlank(name) ? name : "";
        }
        if (JoinColumEnum.SUPPLIER_ID.getSign().equals(sign)) {
            String name = fillService.getSupplierNameById(Set.of(value.toString())).get(value.toString());
            return StringUtils.isNotBlank(name) ? name : "";
        }
        if (JoinColumEnum.CATEGORY_ID.getSign().equals(sign)) {
            String name = fillService.getCategoryNameById(Set.of(value.toString())).get(value.toString());
            return StringUtils.isNotBlank(name) ? name : "";
        }
        if (JoinColumEnum.SUBCLASS_ID.getSign().equals(sign)) {
            String name = fillService.getSubclassNameById(Set.of(value.toString())).get(value.toString());
            return StringUtils.isNotBlank(name) ? name : "";
        }
        if (JoinColumEnum.BRAND_ID.getSign().equals(sign)) {
            String name = fillService.getBrandNameById(Set.of(value.toString())).get(value.toString());
            return StringUtils.isNotBlank(name) ? name : "";
        }
        if (JoinColumEnum.STYLE_ID.getSign().equals(sign)) {
            String name = fillService.getStyleNameById(Set.of(value.toString())).get(value.toString());
            return StringUtils.isNotBlank(name) ? name : "";
        }
        if (JoinColumEnum.QUALITY_ID.getSign().equals(sign)) {
            String name = fillService.getQualityNameById(Set.of(value.toString())).get(value.toString());
            return StringUtils.isNotBlank(name) ? name : "";
        }
        if (JoinColumEnum.TECHNOLOGY_ID.getSign().equals(sign)) {
            String name = fillService.getTechnologyNameById(Set.of(value.toString())).get(value.toString());
            return StringUtils.isNotBlank(name) ? name : "";
        }
        if (JoinColumEnum.MAIN_STONE_ID.getSign().equals(sign)) {
            String name = fillService.getJewelryMapperNameById(Set.of(value.toString())).get(value.toString());
            return StringUtils.isNotBlank(name) ? name : "";
        }
        if (JoinColumEnum.SUB_STONE_ID.getSign().equals(sign)) {
            String name = fillService.getJewelryMapperNameById(Set.of(value.toString())).get(value.toString());
            return StringUtils.isNotBlank(name) ? name : "";
        }
        if (PriceColumEnum.isPriceColumn(sign)) {
            return PriceUtil.fen2yuanString(value.toString());
        }
        if (OtherColumEnum.SALES_TYPE.getSign().equals(sign)) {
            return OtherColumEnum.getSalesTypeText(value.toString());
        }
        if (OtherColumEnum.WORK_PRICE_TYPE.getSign().equals(sign)) {
            return OtherColumEnum.getWorkPriceTypeText(value.toString());
        }
        if (OtherColumEnum.SALE_WORK_PRICE_TYPE.getSign().equals(sign)) {
            return OtherColumEnum.getWorkPriceTypeText(value.toString());
        }
        return value.toString();
    }


}
