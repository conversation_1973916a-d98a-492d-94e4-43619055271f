package com.xc.boot.modules.goods.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "调拨收货DTO")
public class GoodsTransferReceiptDTO {
    
    @Schema(description = "调拨单ID", required = true)
    @NotNull(message = "调拨单ID不能为空")
    private Long id;
    
    @Schema(description = "调入柜台ID", required = true)
    @NotNull(message = "调入柜台不能为空")
    private Integer counterId;
    
    @Schema(description = "收货备注")
    private String receiptRemark;
} 