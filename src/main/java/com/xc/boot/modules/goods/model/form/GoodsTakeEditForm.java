package com.xc.boot.modules.goods.model.form;

import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


@Data
@Schema(description = "确认盘点表单")
public class GoodsTakeEditForm {
    @Schema(description = "盘点货品id")
    @NotNull(message = "盘点货品id不能为空")
    private Long id;

    @Schema(description = "盘点单id")
    @NotNull(message = "盘点单id不能为空")
    private Long takeId;

    @Schema(description = "本次盘点数量(确认盘点传参)")
    @NotNull(message = "本次盘点数量不能为空", groups = Create.class)
    @Min(value = 0, message = "盘点数量不能小于0", groups = Create.class)
    private Integer num;

    @Schema(description = "已盘点数量(修改已盘点数传参)")
    @NotNull(message = "已盘点数量不能为空", groups = Update.class)
    @Min(value = 0, message = "已盘点数量不能小于0", groups = Update.class)
    private Integer takeNum;
}
