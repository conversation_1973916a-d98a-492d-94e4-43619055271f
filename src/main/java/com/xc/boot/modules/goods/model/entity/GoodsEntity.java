package com.xc.boot.modules.goods.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.annotation.GoodsColumn;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 货品实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods")
public class GoodsEntity extends BaseEntity {
    
    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    @GoodsColumn(value = "company_id")
    private Long companyId;

    /**
     * 所属门店ID
     */
    @Column(value = "merchant_id")
    @GoodsColumn(value = "merchant_id")
    private Long merchantId;

    /**
     * 所属柜台ID
     */
    @Column(value = "counter_id")
    @GoodsColumn(value = "counter_id")
    private Integer counterId;

    /**
     * 供应商ID
     */
    @Column(value = "supplier_id")
    @GoodsColumn(value = "supplier_id")
    private Integer supplierId;

    /**
     * 所属大类ID
     */
    @Column(value = "category_id")
    @GoodsColumn(value = "category_id")
    private Integer categoryId;

    /**
     * 所属小类ID
     */
    @Column(value = "subclass_id")
    @GoodsColumn(value = "subclass_id")
    private Integer subclassId;

    /**
     * 品牌ID
     */
    @Column(value = "brand_id")
    @GoodsColumn(value = "brand_id")
    private Integer brandId;

    /**
     * 款式ID
     */
    @Column(value = "style_id")
    @GoodsColumn(value = "style_id")
    private Integer styleId;

    /**
     * 成色ID
     */
    @Column(value = "quality_id")
    @GoodsColumn(value = "quality_id")
    private Integer qualityId;

    /**
     * 工艺ID
     */
    @Column(value = "technology_id")
    @GoodsColumn(value = "technology_id")
    private Integer technologyId;

    /**
     * 主石ID
     */
    @Column(value = "main_stone_id")
    @GoodsColumn(value = "main_stone_id")
    private Integer mainStoneId;

    /**
     * 辅石ID
     */
    @Column(value = "sub_stone_id")
    @GoodsColumn(value = "sub_stone_id")
    private Integer subStoneId;

    /**
     * 货品条码
     */
    @Column(value = "goods_sn")
    @GoodsColumn(value = "goods_sn")
    private String goodsSn;

    /**
     * 货品名称
     */
    @Column(value = "name")
    @GoodsColumn(value = "name")
    private String name;

    /**
     * 销售方式(1:按重量,2:按数量)
     */
    @Column(value = "sales_type")
    @GoodsColumn(value = "sales_type")
    private Integer salesType;

    /**
     * 批次号
     */
    @Column(value = "batch_no")
    @GoodsColumn(value = "batch_no")
    private String batchNo;

    /**
     * 证书号
     */
    @Column(value = "cert_no")
    @GoodsColumn(value = "cert_no")
    private String certNo;

    /**
     * 备注
     */
    @Column(value = "remark")
    @GoodsColumn(value = "remark")
    private String remark;

    /**
     * 重量(g)
     */
    @Column(value = "weight")
    @GoodsColumn(value = "weight")
    private BigDecimal weight;

    /**
     * 净金重(g)
     */
    @Column(value = "net_gold_weight")
    @GoodsColumn(value = "net_gold_weight")
    private BigDecimal netGoldWeight;

    /**
     * 净银重(g)
     */
    @Column(value = "net_silver_weight")
    @GoodsColumn(value = "net_silver_weight")
    private BigDecimal netSilverWeight;

    /**
     * 主石数
     */
    @Column(value = "main_stone_count")
    @GoodsColumn(value = "main_stone_count")
    private Integer mainStoneCount;

    /**
     * 主石重(ct)
     */
    @Column(value = "main_stone_weight")
    @GoodsColumn(value = "main_stone_weight")
    private BigDecimal mainStoneWeight;

    /**
     * 辅石数
     */
    @Column(value = "sub_stone_count")
    @GoodsColumn(value = "sub_stone_count")
    private Integer subStoneCount;

    /**
     * 辅石重(ct)
     */
    @Column(value = "sub_stone_weight")
    @GoodsColumn(value = "sub_stone_weight")
    private BigDecimal subStoneWeight;

    /**
     * 圈口
     */
    @Column(value = "circle_size")
    @GoodsColumn(value = "circle_size")
    private String circleSize;

    /**
     * 成本单价(分)
     */
    @Column(value = "cost_price")
    @GoodsColumn(value = "cost_price")
    private Long costPrice;

    /**
     * 金进单价(分)
     */
    @Column(value = "gold_price")
    @GoodsColumn(value = "gold_price")
    private Long goldPrice;

    /**
     * 银进单价(分)
     */
    @Column(value = "silver_price")
    @GoodsColumn(value = "silver_price")
    private Long silverPrice;

    /**
     * 进工费单价(分)
     */
    @Column(value = "work_price")
    @GoodsColumn(value = "work_price")
    private Long workPrice;

    /**
     * 进工费计价方式(1:按重量,2:按数量)
     */
    @Column(value = "work_price_type")
    @GoodsColumn(value = "work_price_type")
    private Integer workPriceType;

    /**
     * 证书费(分)
     */
    @Column(value = "cert_price")
    @GoodsColumn(value = "cert_price")
    private Long certPrice;

    /**
     * 工费单价(分)
     */
    @Column(value = "sale_work_price")
    @GoodsColumn(value = "sale_work_price")
    private Long saleWorkPrice;

    /**
     * 销工费计价方式(1:按重量,2:按数量)
     */
    @Column(value = "sale_work_price_type")
    @GoodsColumn(value = "sale_work_price_type")
    private Integer saleWorkPriceType;

    /**
     * 标签单价(分)
     */
    @Column(value = "tag_price")
    @GoodsColumn(value = "tag_price")
    private Long tagPrice;

    /**
     * 原始数量
     */
    @Column(value = "num")
    @GoodsColumn(value = "num")
    private Integer num;

    /**
     * 库存数量
     */
    @Column(value = "stock_num")
    @GoodsColumn(value = "stock_num")
    private Integer stockNum;

    /**
     * 采购退数量
     */
    @Column(value = "return_num")
    @GoodsColumn(value = "return_num")
    private Integer returnNum;

    /**
     * 售出数量
     */
    @Column(value = "sold_num")
    @GoodsColumn(value = "sold_num")
    private Integer soldNum;

    /**
     * 调拨中数量
     */
    @Column(value = "transfer_num")
    @GoodsColumn(value = "transfer_num")
    private Integer transferNum;

    /**
     * 冻结数量
     */
    @Column(value = "frozen_num")
    @GoodsColumn(value = "frozen_num")
    private Integer frozenNum;

    /**
     * 盘点中状态(0:否|1:盘点中)
     */
    @Column(value = "take_status")
    private Integer takeStatus;
} 