package com.xc.boot.modules.goods.controller;

import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.modules.goods.model.form.GoodsTakeEditForm;
import com.xc.boot.modules.goods.model.form.GoodsTakeForm;
import com.xc.boot.modules.goods.model.query.GoodsTakePageQuery;
import com.xc.boot.modules.goods.model.query.TakeGoodsListQuery;
import com.xc.boot.modules.goods.model.query.TakeGoodsPageQuery;
import com.xc.boot.modules.goods.model.vo.GoodsTakePageVo;
import com.xc.boot.modules.goods.model.vo.GoodsTakeStatisticVo;
import com.xc.boot.modules.goods.model.vo.TakeGoodsPageVo;
import com.xc.boot.modules.goods.model.vo.TakeGoodsVo;
import com.xc.boot.modules.goods.service.GoodsTakeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/goods/take")
@RequiredArgsConstructor
@Tag(name = "盘点单管理")
public class GoodsTakeController {
    private final GoodsTakeService goodsTakeService;

    @Operation(summary = "盘点单分页列表")
    @PostMapping("/page")
    public PageResult<GoodsTakePageVo> page(@Validated @RequestBody GoodsTakePageQuery query) {
        Page<GoodsTakePageVo> page = goodsTakeService.pageTake(query);
        return PageResult.success(page);
    }

    @Operation(summary = "盘点单详情查询")
    @GetMapping("/receiptDetail")
    public Result<GoodsTakePageVo> page(@RequestParam @Valid @NotNull(message = "盘点单ID不能为空") Long id) {
        GoodsTakePageVo vo = goodsTakeService.detailTake(id);
        return Result.success(vo);
    }

    @Operation(summary = "新增盘点单")
    @PostMapping("/add")
    public Result<?> addTake(@Validated(Create.class) @RequestBody GoodsTakeForm form) {
        try {
            Long takeId = goodsTakeService.addTake(form);
            return Result.success(takeId);
        }catch (Exception e) {
            SnUtils.syncTakeMaxSequence();
            throw e;
        }
    }

    @Operation(summary = "编辑盘点单")
    @PostMapping("/edit")
    public Result<?> edit(@Validated(Update.class) @RequestBody GoodsTakeForm form) {
        goodsTakeService.edit(form);
        return Result.success();
    }

    @Operation(summary = "查询盘点货品")
    @PostMapping("/getGoods")
    public PageResult<?> getGoods(@Validated @RequestBody TakeGoodsListQuery query) {
        Page<TakeGoodsVo> vo = goodsTakeService.goodsList(query);
        Page<JSONObject> encrypt = ColumnEncryptUtil.encrypt(vo, TakeGoodsVo.class, null);
        return PageResult.success(encrypt);
    }

    @Operation(summary = "确认盘点货品")
    @PostMapping("/confirm")
    public Result<?> confirm(@Validated(Create.class) @RequestBody GoodsTakeEditForm form) {
        goodsTakeService.confirm(form);
        return Result.success();
    }

    @Operation(summary = "修改已盘点数")
    @PostMapping("/editNum")
    public Result<?> editNum(@Validated(Update.class) @RequestBody GoodsTakeEditForm form) {
        goodsTakeService.editNum(form);
        return Result.success();
    }

    @Operation(summary = "分页查询盘点货品")
    @PostMapping("/detailPage")
    public PageResult<?> detailPage(@Validated @RequestBody TakeGoodsPageQuery query) {
        Page<TakeGoodsPageVo> vo = goodsTakeService.goodsPage(query);
        Page<JSONObject> encrypt = ColumnEncryptUtil.encrypt(vo, TakeGoodsPageVo.class, "customerColumns");
        return PageResult.success(encrypt);
    }

    @Operation(summary = "盘点货品详情统计")
    @PostMapping("/statistic")
    public Result<GoodsTakeStatisticVo> statistic(@Validated @RequestBody TakeGoodsPageQuery query) {
        GoodsTakeStatisticVo vo = goodsTakeService.statistic(query);
        return Result.success(vo);
    }

    @Operation(summary = "结束盘点")
    @GetMapping("/finish")
    public Result<?> finish(@Valid @NotNull(message = "盘点单id不能为空") @RequestParam Long takeId) {
        goodsTakeService.finish(takeId);
        return Result.success();
    }
} 