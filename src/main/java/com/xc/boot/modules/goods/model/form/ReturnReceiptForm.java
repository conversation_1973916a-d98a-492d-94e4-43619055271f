package com.xc.boot.modules.goods.model.form;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "退货单查询form")
public class ReturnReceiptForm extends BasePageQuery {

    @Schema(description = "门店id(英文逗号分隔)")
    private String merchantIds;

    @Schema(description = "供应商id(英文逗号分隔)")
    private String supplierIds;

    @Schema(description = "门店id(英文逗号分隔)")
    private String outcomeCode;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "审核人")
    private String auditBy;

    @Schema(description = "创建时间范围")
    private Date[] createdTimeRange;

    @Schema(description = "审核时间范围")
    private Date[] auditTimeRange;
}
