package com.xc.boot.modules.goods.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.goods.model.entity.GoodsOutcomeEntity;
import com.xc.boot.modules.goods.model.form.*;
import com.xc.boot.modules.goods.model.vo.ReturnDetailPageVo;
import com.xc.boot.modules.goods.model.vo.ReturnGoodsPageVo;
import com.xc.boot.modules.goods.model.vo.ReturnReceiptPageVo;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ReturnOutcomeService
 * @Date: 2025/6/14 15:41
 * @Description: 描述
 */

public interface ReturnOutcomeService extends IService<GoodsOutcomeEntity> {
    Page<ReturnGoodsPageVo> selectOutGoods(ReturnGoodsForm form);

    Page<ReturnReceiptPageVo> receiptPage(ReturnReceiptForm form);

    void delete(Long id);

    void audit(List<Long> ids);

    void create(ReturnCreateForm form);

    void edit(ReturnEditForm form);

    Page<ReturnDetailPageVo> detailPage(ReturnReceiptDetailForm form);

    void deleteDetail(Long id);

    ReturnReceiptPageVo receiptDetail(Long id);
}
