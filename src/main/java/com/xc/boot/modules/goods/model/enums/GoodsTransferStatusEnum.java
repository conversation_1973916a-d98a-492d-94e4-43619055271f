package com.xc.boot.modules.goods.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GoodsTransferStatusEnum implements IBaseEnum<Integer> {
    PENDING(0, "待审核"),
    TRANSFERRING(1, "调拨中"),
    COMPLETED(2, "已完成");

    private final Integer value;
    private final String label;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getLabel() {
        return label;
    }
} 