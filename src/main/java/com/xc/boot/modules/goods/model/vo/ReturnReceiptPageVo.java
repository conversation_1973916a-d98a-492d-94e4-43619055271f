package com.xc.boot.modules.goods.model.vo;

import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.excel.model.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "退货单vo")
public class ReturnReceiptPageVo {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "审核时间")
    private Date auditAt;

    @Schema(description = "所属门店ID")
    private Long merchantId;

    @Schema(description = "所属门店")
    private String merchant;

    @Schema(description = "出库单号")
    private String outcomeCode;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "供应商")
    private String supplier;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "总重量(g)")
    private BigDecimal totalWeight;

    @Schema(description = "总金重(g)")
    private BigDecimal totalNetGoldWeight;

    @Schema(description = "总银重(g)")
    private BigDecimal totalNetSilverWeight;

    @Schema(description = "总成本价(元)")
    private String totalCostPrice;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "状态(0:待审核,1:已审核)")
    @Excel(type = 3, key = {"0", "1"}, value = {"待审核", "已审核"} )
    private Integer status;

    @Schema(description = "审核人ID")
    private Integer auditBy;

    @Schema(description = "创建人ID")
    private Integer createdBy;

    @Schema(description = "审核人")
    private String auditByName;

    @Schema(description = "创建人")
    private String createdByName;

    public BigDecimal getTotalWeight() {
        return PriceUtil.formatThreeDecimal(totalWeight);
    }

    public BigDecimal getTotalNetGoldWeight() {
        return PriceUtil.formatThreeDecimal(totalNetGoldWeight);
    }

    public String getTotalCostPrice() {
        if (totalCostPrice == null || totalCostPrice.contains("*")) {
            return totalCostPrice;
        }
        return PriceUtil.formatTwoDecimal(totalCostPrice).toPlainString();
    }

    public BigDecimal getTotalNetSilverWeight() {
        return PriceUtil.formatThreeDecimal(totalNetSilverWeight);
    }
}
