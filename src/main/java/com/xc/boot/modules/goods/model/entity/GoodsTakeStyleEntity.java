package com.xc.boot.modules.goods.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 盘点款式关联实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_take_style")
public class GoodsTakeStyleEntity {
    /**
     * 盘点ID
     */
    @Column(value = "take_id")
    private Long takeId;

    /**
     * 款式ID
     */
    @Column(value = "style_id")
    private Long styleId;

    @Column(value = "company_id")
    private Long companyId;
}