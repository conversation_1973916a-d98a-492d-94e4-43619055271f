package com.xc.boot.modules.income.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xc.boot.common.annotation.GoodsColumn;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;

@Data
@Schema(description = "入库单详情VO")
public class GoodsIncomeInfoVO {
    
    @Schema(description = "入库单号")
    private String incomeCode;
    
    @Schema(description = "所属门店ID")
    @GoodsColumn(value = "merchant_id")
    private Integer merchantId;
    
    @Schema(description = "所属门店")
    @GoodsColumn(value = "merchant_id")
    private String storeName;
    
    @Schema(description = "供应商ID")
    @GoodsColumn(value = "supplier_id")
    private Integer supplierId;
    
    @Schema(description = "供应商")
    @GoodsColumn(value = "supplier_id")
    private String supplierName;
    
    @Schema(description = "状态")
    private Integer status;
    
    @Schema(description = "状态")
    private String statusLabel;
    
    @Schema(description = "总数量")
    @GoodsColumn(value = "num")
    private Integer num;
    
    @Schema(description = "总重量(g)")
    @GoodsColumn(value = "weight")
    private BigDecimal totalWeight;
    
    @Schema(description = "总金重(g)")
    @GoodsColumn(value = "net_gold_weight")
    private BigDecimal totalNetGoldWeight;
    
    @Schema(description = "总成本价")
    @GoodsColumn(value = "cost_price")
    private BigDecimal totalCostPrice;
    
    @Schema(description = "创建人ID")
    private Integer createdBy;
    
    @Schema(description = "创建人")
    private String creator;
    
    @Schema(description = "创建时间")
    private String createdAt;

    // getter
    public String getCreatedAt() {
        if (StrUtil.isBlank(createdAt)) {
            return "";
        }
        try {
            return createdAt;
        } catch (Exception e) {
            return createdAt;
        }
    }

    // setter to handle different types of input
    public void setCreatedAt(Object datetime) {
        if (datetime == null) {
            this.createdAt = "";
        } else if (datetime instanceof String) {
            this.createdAt = (String) datetime;
        } else if (datetime instanceof LocalDateTime) {
            this.createdAt = DateUtil.format((LocalDateTime) datetime, "yyyy-MM-dd HH:mm:ss");
        } else {
            this.createdAt = datetime.toString();
        }
    }
    
    @Schema(description = "审核人ID")
    private Integer auditBy;
    
    @Schema(description = "审核人")
    private String auditor;
    
    @Schema(description = "审核时间")
    private String auditAt;

    // getter
    public String getAuditAt() {
        if (StrUtil.isBlank(auditAt)) {
            return "";
        }
        try {
            return auditAt;
        } catch (Exception e) {
            return auditAt;
        }
    }

    // setter to handle different types of input
    public void setAuditAt(Object datetime) {
        if (datetime == null) {
            this.auditAt = "";
        } else if (datetime instanceof String) {
            this.auditAt = (String) datetime;
        } else if (datetime instanceof LocalDateTime) {
            this.auditAt = DateUtil.format((LocalDateTime) datetime, "yyyy-MM-dd HH:mm:ss");
        } else {
            this.auditAt = datetime.toString();
        }
    }
    
    @Schema(description = "入库模版ID")
    private Integer templateId;
    
    @Schema(description = "入库模版")
    private String templateName;
    
    @Schema(description = "备注")
    private String remark;
    
    @Schema(description = "总银重(g)")
    @GoodsColumn(value = "net_silver_weight")
    private BigDecimal totalNetSilverWeight;
} 