package com.xc.boot.modules.income.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 入库单明细实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_income_detail")
public class GoodsIncomeDetailEntity extends BaseEntity {
    
    /**
     * 入库单ID
     */
    @Column(value = "receive_id")
    private Integer receiveId;

    /**
     * 入库单号
     */
    @Column(value = "income_code")
    private String incomeCode;

    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 所属门店ID
     */
    @Column(value = "merchant_id")
    private Integer merchantId;

    /**
     * 货品ID
     */
    @Column(value = "goods_id")
    private Integer goodsId;

    /**
     * 所属柜台ID
     */
    @Column(value = "counter_id")
    private Integer counterId;

    /**
     * 供应商ID
     */
    @Column(value = "supplier_id")
    private Integer supplierId;

    /**
     * 所属大类ID
     */
    @Column(value = "category_id")
    private Integer categoryId;

    /**
     * 所属小类ID
     */
    @Column(value = "subclass_id")
    private Integer subclassId;

    /**
     * 品牌ID
     */
    @Column(value = "brand_id")
    private Integer brandId;

    /**
     * 款式ID
     */
    @Column(value = "style_id")
    private Integer styleId;

    /**
     * 成色ID
     */
    @Column(value = "quality_id")
    private Integer qualityId;

    /**
     * 工艺ID
     */
    @Column(value = "technology_id")
    private Integer technologyId;

    /**
     * 主石ID
     */
    @Column(value = "main_stone_id")
    private Integer mainStoneId;

    /**
     * 辅石ID
     */
    @Column(value = "sub_stone_id")
    private Integer subStoneId;

    /**
     * 货品条码
     */
    @Column(value = "goods_sn")
    private String goodsSn;

    /**
     * 货品名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 销售方式(1:按重量,2:按数量)
     */
    @Column(value = "sales_type")
    private Integer salesType;

    /**
     * 批次号
     */
    @Column(value = "batch_no")
    private String batchNo;

    /**
     * 证书号
     */
    @Column(value = "cert_no")
    private String certNo;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 重量(g)
     */
    @Column(value = "weight")
    private BigDecimal weight;

    /**
     * 净金重(g)
     */
    @Column(value = "net_gold_weight")
    private BigDecimal netGoldWeight;

    /**
     * 净银重(g)
     */
    @Column(value = "net_silver_weight")
    private BigDecimal netSilverWeight;

    /**
     * 主石数
     */
    @Column(value = "main_stone_count")
    private Integer mainStoneCount;

    /**
     * 主石重(ct)
     */
    @Column(value = "main_stone_weight")
    private BigDecimal mainStoneWeight;

    /**
     * 辅石数
     */
    @Column(value = "sub_stone_count")
    private Integer subStoneCount;

    /**
     * 辅石重(ct)
     */
    @Column(value = "sub_stone_weight")
    private BigDecimal subStoneWeight;

    /**
     * 圈口
     */
    @Column(value = "circle_size")
    private String circleSize;

    /**
     * 成本单价(分)
     */
    @Column(value = "cost_price")
    private Long costPrice;

    /**
     * 金进单价(分)
     */
    @Column(value = "gold_price")
    private Long goldPrice;

    /**
     * 银进单价(分)
     */
    @Column(value = "silver_price")
    private Long silverPrice;

    /**
     * 进工费单价(分)
     */
    @Column(value = "work_price")
    private Long workPrice;

    /**
     * 进工费计价方式(1:按重量,2:按数量)
     */
    @Column(value = "work_price_type")
    private Integer workPriceType;

    /**
     * 证书费(分)
     */
    @Column(value = "cert_price")
    private Long certPrice;

    /**
     * 工费单价(分)
     */
    @Column(value = "sale_work_price")
    private Long saleWorkPrice;

    /**
     * 销工费计价方式(1:按重量,2:按数量)
     */
    @Column(value = "sale_work_price_type")
    private Integer saleWorkPriceType;

    /**
     * 标签单价(分)
     */
    @Column(value = "tag_price")
    private Long tagPrice;

    /**
     * 原始数量
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 状态(0:待审核,1:已审核)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 审核人ID
     */
    @Column(value = "audit_by")
    private Integer auditBy;

    /**
     * 审核时间
     */
    @Column(value = "audit_at")
    private Date auditAt;
} 