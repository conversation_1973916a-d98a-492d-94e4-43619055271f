package com.xc.boot.modules.income.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "创建入库单")
public class GoodsIncomeCreateDTO {
    
    @Schema(description = "所属门店ID")
    @NotNull(message = "所属门店不能为空")
    private Integer merchantId;
    
    @Schema(description = "供应商ID")
    @NotNull(message = "供应商不能为空")
    private Integer supplierId;
    
    @Schema(description = "入库模板ID")
    private Long templateId;
    
    @Schema(description = "备注")
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;
    
    @Schema(description = "入库明细")
    @NotEmpty(message = "入库明细不能为空")
    @Valid
    private List<GoodsIncomeDetailCreateDTO> details;
} 