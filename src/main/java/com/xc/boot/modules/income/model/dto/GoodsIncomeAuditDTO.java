package com.xc.boot.modules.income.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 货品入库审核DTO
 */
@Data
@Schema(description = "货品入库审核请求对象")
public class GoodsIncomeAuditDTO {

    @Schema(description = "入库单ID列表，多个ID用逗号分隔", requiredMode = Schema.RequiredMode.REQUIRED, example = "1,2,3")
    @NotBlank(message = "入库单ID不能为空")
    @Pattern(regexp = "^\\d+(,\\d+)*$", message = "ID格式不正确，应为逗号分隔的数字")
    private String ids;
} 