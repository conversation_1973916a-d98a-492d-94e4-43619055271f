package com.xc.boot.modules.income.model.vo;

import com.xc.boot.common.annotation.GoodsColumn;
import com.xc.boot.modules.goods.model.entity.GoodsHasColumnsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品条码检查返回对象
 */
@Data
@Schema(description = "商品条码检查返回对象")
public class GoodsSnCheckVO {

    @Schema(description = "商品信息（价格已转换为元）")
    private GoodsWithYuanPriceVO goods;

    @Schema(description = "商品字段信息")
    private List<GoodsHasColumnsEntity> columns;

    @Schema(description = "商品字段信息-处理后")
    private List<Object> newColumns;

    @Schema(description = "商品图片信息")
    private List<GoodsHasImagesEntity> images;

    @Schema(description = "大类ID")
    private Integer categoryId;

    @Schema(description = "大类名称")
    @GoodsColumn(value = "category_id")
    private String categoryName;

    /**
     * 商品信息VO（价格已转换为元）
     */
    @Data
    @Schema(description = "商品信息（价格已转换为元）")
    public static class GoodsWithYuanPriceVO {
        @Schema(description = "商品ID")
        private Long id;
        
        @Schema(description = "公司ID")
        private Integer companyId;
        
        @Schema(description = "门店ID")
        @GoodsColumn(value = "merchant_id")
        private Integer merchantId;
        
        @Schema(description = "商品条码")
        @GoodsColumn(value = "goods_sn")
        private String goodsSn;
        
        @Schema(description = "商品名称")
        @GoodsColumn(value = "name")
        private String name;
        
        @Schema(description = "大类ID")
        @GoodsColumn(value = "category_id")
        private Integer categoryId;
        
        @Schema(description = "小类ID")
        @GoodsColumn(value = "subclass_id")
        private Integer subclassId;
        
        @Schema(description = "品牌ID")
        @GoodsColumn(value = "brand_id")
        private Integer brandId;
        
        @Schema(description = "款式ID")
        @GoodsColumn(value = "style_id")
        private Integer styleId;
        
        @Schema(description = "成色ID")
        @GoodsColumn(value = "quality_id")
        private Integer qualityId;
        
        @Schema(description = "工艺ID")
        @GoodsColumn(value = "technology_id")
        private Integer technologyId;
        
        @Schema(description = "主石ID")
        @GoodsColumn(value = "main_stone_id")
        private Integer mainStoneId;
        
        @Schema(description = "辅石ID")
        @GoodsColumn(value = "sub_stone_id")
        private Integer subStoneId;
        
        @Schema(description = "柜台ID")
        @GoodsColumn(value = "counter_id")
        private Integer counterId;
        
        @Schema(description = "供应商ID")
        @GoodsColumn(value = "supplier_id")
        private Integer supplierId;
        
        @Schema(description = "销售方式")
        @GoodsColumn(value = "sales_type")
        private String salesType;
        
        @Schema(description = "批次号")
        @GoodsColumn(value = "batch_no")
        private String batchNo;
        
        @Schema(description = "证书号")
        @GoodsColumn(value = "cert_no")
        private String certNo;
        
        @Schema(description = "备注")
        @GoodsColumn(value = "remark")
        private String remark;
        
        @Schema(description = "重量")
        @GoodsColumn(value = "weight")
        private BigDecimal weight;
        
        @Schema(description = "净金重")
        @GoodsColumn(value = "net_gold_weight")
        private BigDecimal netGoldWeight;
        
        @Schema(description = "净银重")
        @GoodsColumn(value = "net_silver_weight")
        private BigDecimal netSilverWeight;
        
        @Schema(description = "主石数")
        @GoodsColumn(value = "main_stone_count")
        private Integer mainStoneCount;
        
        @Schema(description = "主石重")
        @GoodsColumn(value = "main_stone_weight")
        private BigDecimal mainStoneWeight;
        
        @Schema(description = "辅石数")
        @GoodsColumn(value = "sub_stone_count")
        private Integer subStoneCount;
        
        @Schema(description = "辅石重")
        @GoodsColumn(value = "sub_stone_weight")
        private BigDecimal subStoneWeight;
        
        @Schema(description = "圈口")
        @GoodsColumn(value = "circle_size")
        private String circleSize;
        
        @Schema(description = "数量")
        @GoodsColumn(value = "num")
        private Integer num;
        
        @Schema(description = "库存数量")
        private Integer stockNum;
        
        @Schema(description = "已售数量")
        private Integer soldNum;
        
        @Schema(description = "退货数量")
        private Integer returnNum;
        
        @Schema(description = "调拨数量")
        private Integer transferNum;
        
        @Schema(description = "冻结数量")
        private Integer frozenNum;
        
        // 价格字段（已转换为元）
        @Schema(description = "成本单价（元）")
        @GoodsColumn(value = "cost_price")
        private BigDecimal costPrice;
        
        @Schema(description = "金进单价（元）")
        @GoodsColumn(value = "gold_price")
        private BigDecimal goldPrice;
        
        @Schema(description = "银进单价（元）")
        @GoodsColumn(value = "silver_price")
        private BigDecimal silverPrice;
        
        @Schema(description = "进工费单价（元）")
        @GoodsColumn(value = "work_price")
        private BigDecimal workPrice;
        
        @Schema(description = "证书费（元）")
        @GoodsColumn(value = "cert_price")
        private BigDecimal certPrice;
        
        @Schema(description = "销工费单价（元）")
        @GoodsColumn(value = "sale_work_price")
        private BigDecimal saleWorkPrice;
        
        @Schema(description = "标签单价（元）")
        @GoodsColumn(value = "tag_price")
        private BigDecimal tagPrice;
    }

    // @Schema(description = "合并后的商品字段信息，columnSign为key，value为值")
    // private Map<String, Object> columnMap;
} 