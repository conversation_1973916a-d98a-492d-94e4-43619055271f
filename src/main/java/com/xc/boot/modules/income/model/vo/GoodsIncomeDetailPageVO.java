package com.xc.boot.modules.income.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.util.List;
import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.common.annotation.Flatten;
import com.xc.boot.common.annotation.GoodsColumn;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库单明细分页VO")
public class GoodsIncomeDetailPageVO extends GoodsIncomeDetailVO {
    @Schema(description = "所属柜台")
    @GoodsColumn(value = "counter_id")
    private String counterName;
    
    @Schema(description = "所属大类")
    @GoodsColumn(value = "category_id")
    private String categoryName;
    
    @Schema(description = "所属小类")
    @GoodsColumn(value = "subclass_id")
    private String subclassName;
    
    @Schema(description = "品牌")
    @GoodsColumn(value = "brand_id")
    private String brandName;
    
    @Schema(description = "款式")
    @GoodsColumn(value = "style_id")
    private String styleName;
    
    @Schema(description = "成色")
    @GoodsColumn(value = "quality_id")
    private String qualityName;
    
    @Schema(description = "工艺")
    @GoodsColumn(value = "technology_id")
    private String technologyName;
    
    @Schema(description = "主石")
    @GoodsColumn(value = "main_stone_id")
    private String mainStoneName;
    
    @Schema(description = "辅石")
    @GoodsColumn(value = "sub_stone_id")
    private String subStoneName;
    
    @Schema(description = "金进金额(元)")
    @GoodsColumn(value = "gold_price")
    private BigDecimal goldAmount;
    
    @Schema(description = "银进金额(元)")
    @GoodsColumn(value = "silver_price")
    private BigDecimal silverAmount;
    
    @Schema(description = "进工费(元)")
    @GoodsColumn(value = "work_price")
    private BigDecimal workAmount;
    
    @Schema(description = "工费(元)")
    @GoodsColumn(value = "sale_work_price")
    private BigDecimal saleWorkAmount;
    
    @Schema(description = "自定义字段")
    @Flatten
    private List<CustomColumnItemDTO> customColumn;
} 