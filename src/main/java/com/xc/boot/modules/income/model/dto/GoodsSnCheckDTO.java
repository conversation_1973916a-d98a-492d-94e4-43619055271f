package com.xc.boot.modules.income.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 商品条码检查请求对象
 */
@Data
@Schema(description = "商品条码检查请求")
public class GoodsSnCheckDTO {

    @Schema(description = "商品条码")
    @NotBlank(message = "商品条码不能为空")
    private String goodsSn;

    @Schema(description = "入库单明细ID")
    private Long incomeDetailId;

    @Schema(description = "门店ID")
    private Integer merchantId;

    @Schema(description = "大类ID")
    private Integer categoryId;

    @Schema(description = "入库模板ID")
    private Integer templateId;
} 