package com.xc.boot.modules.income.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 入库单图片关联实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_income_has_images")
public class GoodsIncomeHasImagesEntity extends BaseEntity {
    
    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 入库单ID
     */
    @Column(value = "income_id")
    private Integer incomeId;

    /**
     * 入库单明细ID
     */
    @Column(value = "income_detail_id")
    private Integer incomeDetailId;

    /**
     * 图片ID
     */
    @Column(value = "image_id")
    private Integer imageId;

    /**
     * 图片URL
     */
    @Column(value = "url")
    private String url;

    /**
     * 排序号
     */
    @Column(value = "sort")
    private Integer sort;
} 