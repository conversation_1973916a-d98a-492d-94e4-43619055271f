package com.xc.boot.modules.income.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.util.ColumnEncryptUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.xc.boot.modules.income.model.dto.GoodsIncomeAuditDTO;
import com.xc.boot.modules.income.model.dto.GoodsIncomeCreateDTO;
import com.xc.boot.modules.income.model.dto.GoodsIncomeDetailUpdateDTO;
import com.xc.boot.modules.income.model.dto.GoodsIncomeTemplateExportDTO;
import com.xc.boot.modules.income.model.dto.GoodsSnCheckDTO;
import com.xc.boot.modules.income.model.query.GoodsIncomeDetailPageQuery;
import com.xc.boot.modules.income.model.query.GoodsIncomePageQuery;
import com.xc.boot.modules.income.model.vo.GoodsIncomeDetailPageVO;
import com.xc.boot.modules.income.model.vo.GoodsIncomeDetailVO;
import com.xc.boot.modules.income.model.vo.GoodsIncomePageVO;
import com.xc.boot.modules.income.model.vo.GoodsIncomeInfoVO;
import com.xc.boot.modules.income.model.vo.GoodsSnCheckVO;
import com.xc.boot.modules.income.service.GoodsIncomeAuditService;
import com.xc.boot.modules.income.service.GoodsIncomeService;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnTypeEnum;
import com.xc.boot.modules.order.model.enums.SalesTypeEnum;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.xc.boot.modules.income.service.GoodsIncomeParseTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 货品入库控制层
 */
@Tag(name = "货品管理-采购入库单")
@RestController
@RequestMapping("/api/income")
@RequiredArgsConstructor
public class GoodsIncomeController {
    
    private final GoodsIncomeService goodsIncomeService;
    private final GoodsIncomeParseTemplateService templateService;
    private final GoodsIncomeAuditService auditService;
    
    @Operation(summary = "创建入库单")
    @PostMapping("/create")
    public Result<Integer> create(@RequestBody @Valid GoodsIncomeCreateDTO dto) {
        Integer id = goodsIncomeService.create(dto);
        return Result.success(id);
    }

    @Operation(summary = "获取导入模板")
    @PostMapping("/template")
    public void exportTemplate(@RequestBody @Valid GoodsIncomeTemplateExportDTO dto) {
        templateService.exportTemplate(dto);
    }

    @Operation(summary = "解析模板")
    @PostMapping(value = "/template/parse", consumes = "multipart/form-data")
    public Result<List<Map<String, Object>>> parseTemplate(
        @RequestPart("file") MultipartFile file,
        @RequestParam("templateId") Long templateId,
        @RequestParam("merchantId") Integer merchantId
    ) {
        List<GoodsIncomeDetailVO> list = templateService.parseTemplate(file, templateId, merchantId);
        // 转换数据格式：xxxId字段转为字符串，销售类型转为数字值
        List<Map<String, Object>> result = convertToStringIds(list);
        return Result.success(result);
    }

    @Operation(summary = "入库单分页列表")
    @PostMapping("/page")
    public PageResult<?> getIncomePage(@RequestBody GoodsIncomePageQuery queryParams) {
        Page<GoodsIncomePageVO> page = goodsIncomeService.getIncomePage(queryParams);
        return PageResult.success((Page<?>)ColumnEncryptUtil.process(page));
    }

    @Operation(summary = "删除入库单")
    @PostMapping("/delete")
    public Result<Boolean> deleteIncome(@RequestBody @Valid DeleteRequest request) {
        boolean result = goodsIncomeService.deleteIncome(request.getId());
        return Result.success(result);
    }

    @Operation(summary = "审核入库单")
    @PostMapping("/audit")
    public Result<Boolean> auditIncome(@RequestBody @Valid GoodsIncomeAuditDTO dto) {
        boolean result = auditService.audit(dto.getIds());
        return Result.success(result);
    }

    @Operation(summary = "获取入库单详情")
    @PostMapping("/info")
    public Result<?> getIncomeInfo(@RequestBody @Valid DeleteRequest request) {
        GoodsIncomeInfoVO info = goodsIncomeService.getIncomeInfo(request.getId());
        List<JSONObject> list = ColumnEncryptUtil.encrypt(List.of(info), GoodsIncomeInfoVO.class, null);
        return Result.success(list.get(0));
    }

    @Operation(summary = "入库单明细分页列表")
    @PostMapping("/detail/page")
    public PageResult<?> getIncomeDetailPage(@RequestBody @Valid GoodsIncomeDetailPageQuery queryParams) {
        Page<GoodsIncomeDetailPageVO> page = goodsIncomeService.getIncomeDetailPage(queryParams);
        Page<?> result = (Page<?>) ColumnEncryptUtil.process(page);
        return PageResult.success(result);
    }

    @Operation(summary = "删除入库单明细")
    @PostMapping("/detail/delete")
    public Result<Boolean> deleteIncomeDetail(@RequestBody @Valid DeleteRequest request) {
        boolean result = goodsIncomeService.deleteIncomeDetail(request.getId());
        return Result.success(result);
    }

    @Operation(summary = "检查商品条码")
    @PostMapping("/goods/check")
    public Result<?> checkGoodsSn(@RequestBody @Valid GoodsSnCheckDTO dto) {
        GoodsSnCheckVO result = goodsIncomeService.checkGoodsSn(dto);
        Object obj = ColumnEncryptUtil.process(result);

        // JSONObject
        JSONObject json = JSONUtil.parseObj(obj);
        Object newColumns = json.get("newColumns");
        if(newColumns instanceof List){
            @SuppressWarnings("unchecked")
            List<JSONObject> list = (List<JSONObject>) newColumns;
            list.forEach(i -> {
                // 图片 json -> 数组
                if(i.getInt("imageId") != 0){
                    i.set("value", JSONUtil.parseArray(i.get("value")));
                }

                // 处理图片空数组
                if(i.getInt("type") == GoodsColumnTypeEnum.IMAGE.getValue() && i.get("value") instanceof String){
                    if(StrUtil.isNotBlank(i.getStr("value"))){
                        i.set("value", JSONUtil.parseArray(i.getStr("value")));
                    }
                }

                // 处理多选的 value
                if(i.getInt("isMultiple") == 1 && i.getInt("type") == GoodsColumnTypeEnum.SELECT.getValue() && i.get("value") instanceof String){
                    if(StrUtil.isNotBlank(i.getStr("value"))){
                        i.set("value", JSONUtil.parseArray(i.getStr("value")));
                    }else{
                        i.set("value", new ArrayList<>());
                    }
                }
            });
        }

        return Result.success(json);
    }

    @Operation(summary = "更新入库单明细")
    @PostMapping("/detail/update")
    public Result<Boolean> updateIncomeDetail(@RequestBody @Valid GoodsIncomeDetailUpdateDTO dto) {
        boolean result = goodsIncomeService.updateIncomeDetail(dto.getId(), dto);
        return Result.success(result);
    }

    /**
     * 转换数据格式：将xxxId字段转为字符串，销售类型转为数字值
     *
     * @param list 原始数据列表
     * @return 转换后的数据列表
     */
    private List<Map<String, Object>> convertToStringIds(List<GoodsIncomeDetailVO> list) {
        List<Map<String, Object>> result = new ArrayList<>();

        for (GoodsIncomeDetailVO vo : list) {
            Map<String, Object> map = new HashMap<>();

            // 将所有xxxId字段转换为字符串
            map.put("id", vo.getId() != null ? vo.getId().toString() : null);
            map.put("goodsId", vo.getGoodsId() != null ? vo.getGoodsId().toString() : null);
            map.put("counterId", vo.getCounterId() != null ? vo.getCounterId().toString() : null);
            map.put("categoryId", vo.getCategoryId() != null ? vo.getCategoryId().toString() : null);
            map.put("subclassId", vo.getSubclassId() != null ? vo.getSubclassId().toString() : null);
            map.put("brandId", vo.getBrandId() != null ? vo.getBrandId().toString() : null);
            map.put("styleId", vo.getStyleId() != null ? vo.getStyleId().toString() : null);
            map.put("qualityId", vo.getQualityId() != null ? vo.getQualityId().toString() : null);
            map.put("technologyId", vo.getTechnologyId() != null ? vo.getTechnologyId().toString() : null);
            map.put("mainStoneId", vo.getMainStoneId() != null ? vo.getMainStoneId().toString() : null);
            map.put("subStoneId", vo.getSubStoneId() != null ? vo.getSubStoneId().toString() : null);

            // 销售类型转换为数字值
            map.put("salesType", convertTypeToValue(vo.getSalesType()));

            // 进工费计价方式转换为数字值
            map.put("workPriceType", convertTypeToValue(vo.getWorkPriceType()));

            // 销工费计价方式转换为数字值
            map.put("saleWorkPriceType", convertTypeToValue(vo.getSaleWorkPriceType()));

            // 其他字段保持原样
            map.put("goodsSn", vo.getGoodsSn());
            map.put("name", vo.getName());
            map.put("salesTypeName", vo.getSalesTypeName());
            map.put("workPriceTypeName", vo.getWorkPriceTypeName());
            map.put("saleWorkPriceTypeName", vo.getSaleWorkPriceTypeName());
            map.put("batchNo", vo.getBatchNo());
            map.put("certNo", vo.getCertNo());
            map.put("remark", vo.getRemark());
            map.put("weight", vo.getWeight());
            map.put("netGoldWeight", vo.getNetGoldWeight());
            map.put("netSilverWeight", vo.getNetSilverWeight());
            map.put("mainStoneCount", vo.getMainStoneCount());
            map.put("mainStoneWeight", vo.getMainStoneWeight());
            map.put("subStoneCount", vo.getSubStoneCount());
            map.put("subStoneWeight", vo.getSubStoneWeight());
            map.put("circleSize", vo.getCircleSize());
            map.put("costPrice", vo.getCostPrice());
            map.put("goldPrice", vo.getGoldPrice());
            map.put("silverPrice", vo.getSilverPrice());
            map.put("workPrice", vo.getWorkPrice());
            map.put("certPrice", vo.getCertPrice());
            map.put("saleWorkPrice", vo.getSaleWorkPrice());
            map.put("tagPrice", vo.getTagPrice());
            map.put("num", vo.getNum());
            map.put("image", vo.getImage());

            map.put("customColumn", vo.getCustomColumn());
            if(vo.getCustomColumn() != null){
                for(CustomColumnItemDTO column : vo.getCustomColumn()){
                    map.put(column.getColumnSign(), column.getValue());
                }
            }

            result.add(map);
        }

        return result;
    }

    /**
     * 将类型文本转换为数字值
     * @param typeText 类型文本（如："按重量"、"按数量"或数字字符串）
     * @return 对应的数字字符串
     */
    private String convertTypeToValue(String typeText) {
        if (typeText == null) {
            return null;
        }

        // 检查是否为枚举标签
        for (SalesTypeEnum salesType : SalesTypeEnum.values()) {
            if (salesType.getLabel().equals(typeText)) {
                return salesType.getValue().toString();
            }
        }

        // 如果不是枚举标签，直接返回（可能已经是数字）
        return typeText;
    }
}