package com.xc.boot.modules.income.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 入库单货品字段关联实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_income_has_columns")
public class GoodsIncomeHasColumnsEntity extends BaseEntity {
    
    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 入库单ID
     */
    @Column(value = "income_id")
    private Integer incomeId;

    /**
     * 入库单明细ID
     */
    @Column(value = "income_detail_id")
    private Integer incomeDetailId;

    /**
     * 字段ID
     */
    @Column(value = "column_id")
    private Integer columnId;

    /**
     * 字段标识
     */
    @Column(value = "column_sign")
    private String columnSign;

    /**
     * 字段值
     */
    @Column(value = "value")
    private String value;

    /**
     * 图片ID
     */
    @Column(value = "image_id")
    private Integer imageId;
} 