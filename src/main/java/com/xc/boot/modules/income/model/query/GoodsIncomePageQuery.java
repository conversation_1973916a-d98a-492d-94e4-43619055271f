package com.xc.boot.modules.income.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 入库单分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库单分页查询对象")
public class GoodsIncomePageQuery extends BasePageQuery {

    @Schema(description = "所属门店ID(多个用逗号分隔)")
    private String merchantIds;

    @Schema(description = "入库单号")
    private String incomeCode;

    @Schema(description = "供应商ID(多个用逗号分隔)")
    private String supplierIds;

    @Schema(description = "状态(0:待审核,1:已审核)")
    private Integer status;

    @Schema(description = "创建人ID(多个用逗号分隔)")
    private String createdByIds;

    @Schema(description = "审核人ID(多个用逗号分隔)")
    private String auditByIds;

    @Schema(description = "创建时间范围")
    private List<String> createdAtRange;

    @Schema(description = "审核时间范围")
    private List<String> auditAtRange;

}