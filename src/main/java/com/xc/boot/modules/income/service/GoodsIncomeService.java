package com.xc.boot.modules.income.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.income.model.dto.GoodsIncomeCreateDTO;
import com.xc.boot.modules.income.model.dto.GoodsIncomeDetailCreateDTO;
import com.xc.boot.modules.income.model.dto.GoodsIncomeDetailUpdateDTO;
import com.xc.boot.modules.income.model.dto.GoodsSnCheckDTO;
import com.xc.boot.modules.income.model.entity.GoodsIncomeEntity;
import com.xc.boot.modules.income.model.query.GoodsIncomePageQuery;
import com.xc.boot.modules.income.model.query.GoodsIncomeDetailPageQuery;
import com.xc.boot.modules.income.model.vo.GoodsIncomePageVO;
import com.xc.boot.modules.income.model.vo.GoodsIncomeInfoVO;
import com.xc.boot.modules.income.model.vo.GoodsIncomeDetailPageVO;
import com.xc.boot.modules.income.model.vo.GoodsSnCheckVO;

/**
 * 货品入库服务接口
 */
public interface GoodsIncomeService extends IService<GoodsIncomeEntity> {
    
    /**
     * 创建入库单
     */
    Integer create(GoodsIncomeCreateDTO dto);

    /**
     * 获取入库单分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<GoodsIncomePageVO> getIncomePage(GoodsIncomePageQuery queryParams);

    /**
     * 删除入库单
     *
     * @param id 入库单ID
     * @return 是否删除成功
     */
    boolean deleteIncome(Long id);

    /**
     * 获取入库单详情
     *
     * @param id 入库单ID
     * @return 入库单详情
     */
    GoodsIncomeInfoVO getIncomeInfo(Long id);

    /**
     * 获取入库单明细分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<GoodsIncomeDetailPageVO> getIncomeDetailPage(GoodsIncomeDetailPageQuery queryParams);

    /**
     * 删除入库单明细
     *
     * @param id 明细ID
     * @return 是否成功
     */
    boolean deleteIncomeDetail(Long id);

    /**
     * 检查商品条码
     *
     * @param dto 检查参数
     * @return 商品信息
     */
    GoodsSnCheckVO checkGoodsSn(GoodsSnCheckDTO dto);

    /**
     * 更新入库单明细
     */
    boolean updateIncomeDetail(Long id, GoodsIncomeDetailUpdateDTO dto);
} 