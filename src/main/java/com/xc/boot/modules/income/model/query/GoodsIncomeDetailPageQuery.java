package com.xc.boot.modules.income.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "入库单明细分页查询")
public class GoodsIncomeDetailPageQuery extends BasePageQuery {
    
    @Schema(description = "入库单ID")
    @NotNull(message = "入库单ID不能为空")
    private Long id;
} 