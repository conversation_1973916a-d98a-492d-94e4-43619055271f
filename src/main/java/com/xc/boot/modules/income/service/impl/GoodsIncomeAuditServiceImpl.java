package com.xc.boot.modules.income.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;

import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsHasColumnsMapper;
import com.xc.boot.modules.goods.mapper.GoodsHasImagesMapper;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasColumnsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import com.xc.boot.modules.income.mapper.GoodsIncomeDetailMapper;
import com.xc.boot.modules.income.mapper.GoodsIncomeHasColumnsMapper;
import com.xc.boot.modules.income.mapper.GoodsIncomeHasImagesMapper;
import com.xc.boot.modules.income.mapper.GoodsIncomeMapper;
import com.xc.boot.modules.income.model.bo.GoodsCompareBO;
import com.xc.boot.modules.income.model.dto.GoodsIncomeDetailCreateDTO;
import com.xc.boot.modules.income.model.entity.GoodsIncomeDetailEntity;
import com.xc.boot.modules.income.model.entity.GoodsIncomeEntity;
import com.xc.boot.modules.income.model.entity.GoodsIncomeHasColumnsEntity;
import com.xc.boot.modules.income.model.entity.GoodsIncomeHasImagesEntity;
import com.xc.boot.modules.income.service.GoodsIncomeAuditService;
import com.xc.boot.system.mapper.CompanyMapper;
import com.xc.boot.system.model.entity.CompanyEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.common.util.StockUtils;
import com.xc.boot.modules.goods.model.bo.StockNumChangeBO;
import com.xc.boot.modules.goods.model.bo.GoodsPriceChangeBO;
import com.xc.boot.common.util.PriceUtil;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;

/**
 * 货品入库审核服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsIncomeAuditServiceImpl implements GoodsIncomeAuditService {

    private final GoodsIncomeMapper goodsIncomeMapper;
    private final GoodsIncomeDetailMapper goodsIncomeDetailMapper;
    private final GoodsIncomeHasColumnsMapper goodsIncomeHasColumnsMapper;
    private final GoodsIncomeHasImagesMapper goodsIncomeHasImagesMapper;
    private final GoodsMapper goodsMapper;
    private final GoodsHasColumnsMapper goodsHasColumnsMapper;
    private final GoodsHasImagesMapper goodsHasImagesMapper;
    private final CompanyMapper companyMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean audit(String ids) {
        log.info("开始审核入库单，IDs: {}", ids);

        try {
            // 1. 解析ids字符串，转换为ID列表
            List<Long> incomeIds = parseIds(ids);
            if (incomeIds.isEmpty()) {
                CommonUtils.abort("入库单ID不能为空");
            }

            // 2. 验证入库单是否存在且状态正确
            List<GoodsIncomeEntity> incomes = validateIncomes(incomeIds);

            // 3. 批量查询入库单明细
            List<GoodsIncomeDetailEntity> allDetails = getIncomeDetails(incomeIds);

            // 3.1 校验条码数量
            checkGoodsSnCount(allDetails);

            // 4. 按明细逐个处理，确保每个明细都有正确的goods_id
            processIncomeDetails(allDetails);

            // 5. 根据goods_id更新库存
            updateStockByGoodsId(allDetails);

            // 6. 更新入库单状态
            updateIncomeStatus(incomes);

            // 7. 记录操作日志
            recordAuditLog(incomes, allDetails, "审核入库单");

            log.info("入库单审核完成，IDs: {}", ids);
            return true;

        } catch (Exception e) {
            log.error("审核入库单失败，IDs: {}", ids, e);
            CommonUtils.abort("审核入库单失败：" + e.getMessage());
            return false;
        }
    }

    /**
     * 解析ids字符串，转换为ID列表
     */
    private List<Long> parseIds(String ids) {
        if (StrUtil.isBlank(ids)) {
            return new ArrayList<>();
        }
        return Arrays.stream(ids.split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    /**
     * 验证入库单是否存在且状态正确
     */
    private List<GoodsIncomeEntity> validateIncomes(List<Long> incomeIds) {
        List<GoodsIncomeEntity> incomes = goodsIncomeMapper.selectListByQuery(
                QueryWrapper.create()
                        .where(GoodsIncomeEntity::getId).in(incomeIds)
                        .and(GoodsIncomeEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));

        if (incomes.size() != incomeIds.size()) {
            CommonUtils.abort("部分入库单不存在或无权限操作");
        }

        // 检查状态，只有待审核的入库单才能审核
        for (GoodsIncomeEntity income : incomes) {
            if (income.getStatus() != 0) {
                CommonUtils.abort("入库单 [" + income.getIncomeCode() + "] 状态不正确，无法审核");
            }
        }

        return incomes;
    }

    /**
     * 批量查询入库单明细
     */
    private List<GoodsIncomeDetailEntity> getIncomeDetails(List<Long> incomeIds) {
        return goodsIncomeDetailMapper.selectListByQuery(
                QueryWrapper.create()
                        .where(GoodsIncomeDetailEntity::getReceiveId).in(incomeIds)
                        .and(GoodsIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));
    }

    /**
     * 检查货品条码数量
     * @param details 入库明细
     */
    private void checkGoodsSnCount(List<GoodsIncomeDetailEntity> details) {
        Long companyId = SecurityUtils.getCompanyId();
        Set<String> incomeSns = details.stream().map(GoodsIncomeDetailEntity::getGoodsSn).collect(Collectors.toSet());
        Long count = goodsMapper.selectOneByQueryAs(
                QueryWrapper.create()
                        .select(QueryMethods.count(QueryMethods.distinct(GOODS.GOODS_SN)).as("barcodeCount"))
                        .where(GOODS.COMPANY_ID.eq(companyId))
                        .where(GOODS.STOCK_NUM.gt(0).or(GOODS.FROZEN_NUM.gt(0)))
                        .where(GOODS.GOODS_SN.notIn(incomeSns)), Long.class
        );
        CompanyEntity company = companyMapper.selectOneById(companyId);
        Assert.notNull(company, "商家不存在");
        if (count == null) {
            count = 0L;
        }
        Assert.isTrue(count + incomeSns.size() <= company.getMaxSn(), "货品条码数量已达最大值，无法入库");
    }

    /**
     * 按明细逐个处理，确保每个明细都有正确的goods_id
     */
    private void processIncomeDetails(List<GoodsIncomeDetailEntity> details) {
        if (details.isEmpty()) {
            return;
        }

        // 收集所有条码
        Set<String> goodsSns = details.stream()
                .map(GoodsIncomeDetailEntity::getGoodsSn)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        // 一次性查询所有条码对应的货品记录
        // 使用 goods_sn + counter_id 作为唯一标识，避免同条码不同柜台的记录被覆盖
        Map<String, GoodsEntity> existingGoodsMap = new HashMap<>();
        if (!goodsSns.isEmpty()) {
            List<GoodsEntity> existingGoods = goodsMapper.selectListByQuery(
                    QueryWrapper.create()
                            .where(GoodsEntity::getGoodsSn).in(goodsSns)
                            .and(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));
            existingGoodsMap = existingGoods.stream()
                    .collect(Collectors.toMap(
                        goods -> goods.getGoodsSn() + "_" + goods.getCounterId(),
                        goods -> goods
                    ));
        }

        // 收集所有需要查询的ID
        Set<Long> detailIds = details.stream()
                .map(GoodsIncomeDetailEntity::getId)
                .collect(Collectors.toSet());
        Set<Long> goodsIds = existingGoodsMap.values().stream()
                .map(GoodsEntity::getId)
                .collect(Collectors.toSet());

        // 批量获取图片和自定义字段信息
        Map<String, List<GoodsIncomeHasImagesEntity>> detailImagesMap = new HashMap<>();
        Map<String, List<GoodsHasImagesEntity>> goodsImagesMap = new HashMap<>();
        Map<String, List<GoodsIncomeHasColumnsEntity>> detailColumnsMap = new HashMap<>();
        Map<String, List<GoodsHasColumnsEntity>> goodsColumnsMap = new HashMap<>();

        if (!detailIds.isEmpty()) {
            // 获取入库单明细的图片
            List<GoodsIncomeHasImagesEntity> detailImages = goodsIncomeHasImagesMapper.selectListByQuery(
                    QueryWrapper.create()
                            .where(GoodsIncomeHasImagesEntity::getIncomeDetailId).in(detailIds)
                            .and(GoodsIncomeHasImagesEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));
            detailImagesMap = detailImages.stream()
                    .collect(Collectors.groupingBy(image -> image.getIncomeDetailId().toString()));

            // 获取入库单明细的自定义字段
            List<GoodsIncomeHasColumnsEntity> detailColumns = goodsIncomeHasColumnsMapper.selectListByQuery(
                    QueryWrapper.create()
                            .where(GoodsIncomeHasColumnsEntity::getIncomeDetailId).in(detailIds)
                            .and(GoodsIncomeHasColumnsEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));
            detailColumnsMap = detailColumns.stream()
                    .collect(Collectors.groupingBy(column -> column.getIncomeDetailId().toString()));
        }

        if (!goodsIds.isEmpty()) {
            // 获取货品的图片
            List<GoodsHasImagesEntity> goodsImages = goodsHasImagesMapper.selectListByQuery(
                    QueryWrapper.create()
                            .where(GoodsHasImagesEntity::getGoodsId).in(goodsIds)
                            .and(GoodsHasImagesEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));
            goodsImagesMap = goodsImages.stream()
                    .collect(Collectors.groupingBy(image -> image.getGoodsId().toString()));

            // 获取货品的自定义字段
            List<GoodsHasColumnsEntity> goodsColumns = goodsHasColumnsMapper.selectListByQuery(
                    QueryWrapper.create()
                            .where(GoodsHasColumnsEntity::getGoodsId).in(goodsIds)
                            .and(GoodsHasColumnsEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));
            goodsColumnsMap = goodsColumns.stream()
                    .collect(Collectors.groupingBy(column -> column.getGoodsId().toString()));
        }

        List<GoodsEntity> goodsToCreate = new ArrayList<>();
        List<GoodsIncomeDetailEntity> detailsToUpdate = new ArrayList<>();

        for (GoodsIncomeDetailEntity detail : details) {
            // 检查条码和柜台组合是否已存在
            String goodsKey = detail.getGoodsSn() + "_" + detail.getCounterId();
            GoodsEntity existingGoods = existingGoodsMap.get(goodsKey);

            if (existingGoods != null) {
                // 条码存在，检查信息一致性
                String differences = isGoodsInfoConsistent(
                        detail,
                        existingGoods,
                        detailImagesMap.getOrDefault(detail.getId().toString(), Collections.emptyList()),
                        goodsImagesMap.getOrDefault(existingGoods.getId().toString(), Collections.emptyList()),
                        detailColumnsMap.getOrDefault(detail.getId().toString(), Collections.emptyList()),
                        goodsColumnsMap.getOrDefault(existingGoods.getId().toString(), Collections.emptyList()));
                if (differences.isEmpty()) {
                    // 信息一致，设置goods_id
                    detail.setGoodsId(existingGoods.getId().intValue());
                    detailsToUpdate.add(detail);
                } else {
                    CommonUtils.abort("货品条码 [" + detail.getGoodsSn() + "] 已存在，但信息不一致：\n" + differences);
                }
            } else {
                // 条码不存在，创建新货品记录
                GoodsEntity newGoods = createNewGoods(detail);
                goodsToCreate.add(newGoods);
            }
        }

        // 批量创建新货品记录
        if (!goodsToCreate.isEmpty()) {
            // 批量插入货品记录
            goodsMapper.insertBatchSelective(goodsToCreate);

            // 获取所有新插入货品的条码
            Set<String> insertedGoodsSns = goodsToCreate.stream()
                    .map(GoodsEntity::getGoodsSn)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 重新查询这些货品以获取ID
            List<GoodsEntity> insertedGoods = goodsMapper.selectListByQuery(
                    QueryWrapper.create()
                            .where(QueryMethods.column(GoodsEntity::getGoodsSn).in(insertedGoodsSns))
                            .where(QueryMethods.column(GoodsEntity::getCounterId).in(
                                    details.stream()
                                            .map(GoodsIncomeDetailEntity::getCounterId)
                                            .collect(Collectors.toSet())))
                            .where(QueryMethods.column(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())));

            // 将新创建的货品ID设置到对应的明细中
            Map<String, Long> goodsSnToIdMap = insertedGoods.stream()
                    .collect(Collectors.toMap(
                            goods -> goods.getGoodsSn() + "_" + goods.getCounterId(),
                            GoodsEntity::getId,
                            (existing, replacement) -> existing // 如果有重复的key，保留第一个
                    ));

            for (GoodsIncomeDetailEntity detail : details) {
                if (detail.getGoodsId() == null || detail.getGoodsId() == 0) {
                    if (StrUtil.isBlank(detail.getGoodsSn())) {
                        CommonUtils.abort("货品条码不能为空");
                    }
                    String key = detail.getGoodsSn() + "_" + detail.getCounterId();
                    Long goodsId = goodsSnToIdMap.get(key);
                    if (goodsId != null) {
                        detail.setGoodsId(goodsId.intValue());
                        detailsToUpdate.add(detail);
                    } else {
                        CommonUtils.abort(
                                "无法找到条码为 [" + detail.getGoodsSn() + "] 且柜台为 [" + detail.getCounterId() + "] 的货品记录");
                    }
                }
            }

            // 批量创建货品自定义字段和图片
            batchCreateGoodsRelatedData(detailIds, details);
        }

        // 批量更新入库单明细的goods_id
        if (!detailsToUpdate.isEmpty()) {
            for (GoodsIncomeDetailEntity detail : detailsToUpdate) {
                goodsIncomeDetailMapper.update(detail);
            }
        }
    }

    /**
     * 检查货品信息一致性
     * 
     * @return 如果信息一致返回空字符串，否则返回不一致的字段信息
     */
    private String isGoodsInfoConsistent(
            GoodsIncomeDetailEntity detail,
            GoodsEntity existingGoods,
            List<GoodsIncomeHasImagesEntity> detailImages,
            List<GoodsHasImagesEntity> existingImages,
            List<GoodsIncomeHasColumnsEntity> detailColumns,
            List<GoodsHasColumnsEntity> existingColumns) {
        // 转换为 GoodsCompareBO 对象
        GoodsCompareBO detailBO = convertToCompareBO(detail, detailImages, detailColumns);
        GoodsCompareBO existingBO = convertToCompareBO(existingGoods, existingImages, existingColumns);

        // 使用 compare 方法比较
        return detailBO.compare(existingBO);
    }

    /**
     * 将 GoodsIncomeDetailEntity 转换为 GoodsCompareBO
     */
    private GoodsCompareBO convertToCompareBO(
            GoodsIncomeDetailEntity detail,
            List<GoodsIncomeHasImagesEntity> images,
            List<GoodsIncomeHasColumnsEntity> columns) {
        GoodsCompareBO bo = new GoodsCompareBO();
        bo.setCompanyId(detail.getCompanyId().longValue());
        bo.setSupplierId(detail.getSupplierId().longValue());
        bo.setGoodsSn(detail.getGoodsSn());
        bo.setName(detail.getName());
        bo.setCategoryId(detail.getCategoryId().longValue());
        bo.setSubclassId(detail.getSubclassId().longValue());
        bo.setBrandId(detail.getBrandId().longValue());
        bo.setStyleId(detail.getStyleId().longValue());
        bo.setQualityId(detail.getQualityId().longValue());
        bo.setTechnologyId(detail.getTechnologyId().longValue());
        bo.setMainStoneId(detail.getMainStoneId().longValue());
        bo.setMainStoneCount(detail.getMainStoneCount());
        bo.setMainStoneWeight(detail.getMainStoneWeight());
        bo.setSubStoneId(detail.getSubStoneId().longValue());
        bo.setSubStoneCount(detail.getSubStoneCount());
        bo.setSubStoneWeight(detail.getSubStoneWeight());
        bo.setCertNo(detail.getCertNo());
        bo.setBatchNo(detail.getBatchNo());
        bo.setSalesType(detail.getSalesType());
        bo.setCostPrice(BigDecimal.valueOf(detail.getCostPrice()).divide(BigDecimal.valueOf(100)));
        bo.setGoldPrice(BigDecimal.valueOf(detail.getGoldPrice()).divide(BigDecimal.valueOf(100)));
        bo.setSilverPrice(BigDecimal.valueOf(detail.getSilverPrice()).divide(BigDecimal.valueOf(100)));
        bo.setWorkPrice(BigDecimal.valueOf(detail.getWorkPrice()).divide(BigDecimal.valueOf(100)));
        bo.setCertPrice(BigDecimal.valueOf(detail.getCertPrice()).divide(BigDecimal.valueOf(100)));
        bo.setSaleWorkPrice(BigDecimal.valueOf(detail.getSaleWorkPrice()).divide(BigDecimal.valueOf(100)));
        bo.setTagPrice(BigDecimal.valueOf(detail.getTagPrice()).divide(BigDecimal.valueOf(100)));
        bo.setWeight(detail.getWeight());
        bo.setNetGoldWeight(detail.getNetGoldWeight());
        bo.setNetSilverWeight(detail.getNetSilverWeight());
        bo.setCircleSize(detail.getCircleSize());
        bo.setRemark(detail.getRemark());

        // 设置图片列表
        if (images != null && !images.isEmpty()) {
            bo.setImages(images.stream()
                    .map(image -> {
                        GoodsCompareBO.Image boImage = new GoodsCompareBO.Image();
                        boImage.setFileId(image.getImageId().longValue());
                        return boImage;
                    })
                    .collect(Collectors.toList()));
        }

        // 设置自定义字段列表
        if (columns != null && !columns.isEmpty()) {
            // 获取字段名称映射
            Map<String, String> columnNameMap = CommonUtils.getGoodsColumns().stream()
                    .collect(Collectors.toMap(
                            GoodsColumnEntity::getSign,
                            GoodsColumnEntity::getName));

            bo.setColumns(columns.stream()
                    .map(column -> {
                        GoodsCompareBO.Column boColumn = new GoodsCompareBO.Column();
                        boColumn.setSign(column.getColumnSign());
                        boColumn.setValue(column.getValue());
                        boColumn.setName(columnNameMap.getOrDefault(column.getColumnSign(), column.getColumnSign()));
                        return boColumn;
                    })
                    .collect(Collectors.toList()));
        }

        return bo;
    }

    /**
     * 将 GoodsEntity 转换为 GoodsCompareBO
     */
    private GoodsCompareBO convertToCompareBO(
            GoodsEntity goods,
            List<GoodsHasImagesEntity> images,
            List<GoodsHasColumnsEntity> columns) {
        GoodsCompareBO bo = new GoodsCompareBO();
        bo.setCompanyId(goods.getCompanyId());
        bo.setSupplierId(goods.getSupplierId().longValue());
        bo.setGoodsSn(goods.getGoodsSn());
        bo.setName(goods.getName());
        bo.setCategoryId(goods.getCategoryId().longValue());
        bo.setSubclassId(goods.getSubclassId().longValue());
        bo.setBrandId(goods.getBrandId().longValue());
        bo.setStyleId(goods.getStyleId().longValue());
        bo.setQualityId(goods.getQualityId().longValue());
        bo.setTechnologyId(goods.getTechnologyId().longValue());
        bo.setMainStoneId(goods.getMainStoneId().longValue());
        bo.setMainStoneCount(goods.getMainStoneCount());
        bo.setMainStoneWeight(goods.getMainStoneWeight());
        bo.setSubStoneId(goods.getSubStoneId().longValue());
        bo.setSubStoneCount(goods.getSubStoneCount());
        bo.setSubStoneWeight(goods.getSubStoneWeight());
        bo.setCertNo(goods.getCertNo());
        bo.setBatchNo(goods.getBatchNo());
        bo.setSalesType(goods.getSalesType());
        bo.setCostPrice(BigDecimal.valueOf(goods.getCostPrice()).divide(BigDecimal.valueOf(100)));
        bo.setGoldPrice(BigDecimal.valueOf(goods.getGoldPrice()).divide(BigDecimal.valueOf(100)));
        bo.setSilverPrice(BigDecimal.valueOf(goods.getSilverPrice()).divide(BigDecimal.valueOf(100)));
        bo.setWorkPrice(BigDecimal.valueOf(goods.getWorkPrice()).divide(BigDecimal.valueOf(100)));
        bo.setCertPrice(BigDecimal.valueOf(goods.getCertPrice()).divide(BigDecimal.valueOf(100)));
        bo.setSaleWorkPrice(BigDecimal.valueOf(goods.getSaleWorkPrice()).divide(BigDecimal.valueOf(100)));
        bo.setTagPrice(BigDecimal.valueOf(goods.getTagPrice()).divide(BigDecimal.valueOf(100)));
        bo.setWeight(goods.getWeight());
        bo.setNetGoldWeight(goods.getNetGoldWeight());
        bo.setNetSilverWeight(goods.getNetSilverWeight());
        bo.setCircleSize(goods.getCircleSize());
        bo.setRemark(goods.getRemark());

        // 设置图片列表
        if (images != null && !images.isEmpty()) {
            bo.setImages(images.stream()
                    .map(image -> {
                        GoodsCompareBO.Image boImage = new GoodsCompareBO.Image();
                        boImage.setFileId(image.getImageId());
                        return boImage;
                    })
                    .collect(Collectors.toList()));
        }

        // 设置自定义字段列表
        if (columns != null && !columns.isEmpty()) {
            // 获取字段名称映射
            Map<String, String> columnNameMap = CommonUtils.getGoodsColumns().stream()
                    .collect(Collectors.toMap(
                            GoodsColumnEntity::getSign,
                            GoodsColumnEntity::getName));

            bo.setColumns(columns.stream()
                    .map(column -> {
                        GoodsCompareBO.Column boColumn = new GoodsCompareBO.Column();
                        boColumn.setSign(column.getColumnSign());
                        boColumn.setValue(column.getValue());
                        boColumn.setName(columnNameMap.getOrDefault(column.getColumnSign(), column.getColumnSign()));
                        return boColumn;
                    })
                    .collect(Collectors.toList()));
        }

        return bo;
    }

    /**
     * 创建新货品记录
     */
    private GoodsEntity createNewGoods(GoodsIncomeDetailEntity detail) {
        GoodsEntity goods = new GoodsEntity();
        goods.setCompanyId(SecurityUtils.getCompanyId());
        goods.setMerchantId(detail.getMerchantId().longValue());
        goods.setCounterId(detail.getCounterId());
        goods.setSupplierId(detail.getSupplierId());
        goods.setCategoryId(detail.getCategoryId());
        goods.setSubclassId(detail.getSubclassId());
        goods.setBrandId(detail.getBrandId());
        goods.setStyleId(detail.getStyleId());
        goods.setQualityId(detail.getQualityId());
        goods.setTechnologyId(detail.getTechnologyId());
        goods.setMainStoneId(detail.getMainStoneId());
        goods.setSubStoneId(detail.getSubStoneId());
        goods.setGoodsSn(detail.getGoodsSn());
        goods.setName(detail.getName());
        goods.setSalesType(detail.getSalesType());
        goods.setBatchNo(detail.getBatchNo());
        goods.setCertNo(detail.getCertNo());
        goods.setRemark(detail.getRemark());
        goods.setWeight(detail.getWeight());
        goods.setNetGoldWeight(detail.getNetGoldWeight());
        goods.setNetSilverWeight(detail.getNetSilverWeight());
        goods.setMainStoneCount(detail.getMainStoneCount());
        goods.setMainStoneWeight(detail.getMainStoneWeight());
        goods.setSubStoneCount(detail.getSubStoneCount());
        goods.setSubStoneWeight(detail.getSubStoneWeight());
        goods.setCircleSize(detail.getCircleSize());
        goods.setCostPrice(detail.getCostPrice());
        goods.setGoldPrice(detail.getGoldPrice());
        goods.setSilverPrice(detail.getSilverPrice());
        goods.setWorkPrice(detail.getWorkPrice());
        goods.setCertPrice(detail.getCertPrice());
        goods.setSaleWorkPrice(detail.getSaleWorkPrice());
        goods.setTagPrice(detail.getTagPrice());
        goods.setSaleWorkPriceType(detail.getSaleWorkPriceType());
        goods.setWorkPriceType(detail.getWorkPriceType());
        // 初始库存为0，后续统一更新
        goods.setNum(0);
        goods.setStockNum(0);
        goods.setReturnNum(0);
        goods.setSoldNum(0);
        goods.setTransferNum(0);
        goods.setFrozenNum(0);

        return goods;
    }

    /**
     * 批量创建货品相关的自定义字段和图片
     */
    private void batchCreateGoodsRelatedData(Set<Long> detailIds, List<GoodsIncomeDetailEntity> details) {
        if (detailIds.isEmpty()) {
            return;
        }

        // 批量查询入库单明细的自定义字段
        List<GoodsIncomeHasColumnsEntity> incomeColumns = goodsIncomeHasColumnsMapper.selectListByQuery(
                QueryWrapper.create()
                        .where(GoodsIncomeHasColumnsEntity::getIncomeDetailId).in(detailIds)
                        .and(GoodsIncomeHasColumnsEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));

        // 批量查询入库单明细的图片
        List<GoodsIncomeHasImagesEntity> incomeImages = goodsIncomeHasImagesMapper.selectListByQuery(
                QueryWrapper.create()
                        .where(GoodsIncomeHasImagesEntity::getIncomeDetailId).in(detailIds)
                        .and(GoodsIncomeHasImagesEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));

        // 创建入库单明细ID到货品ID的映射
        Map<Long, Long> detailToGoodsIdMap = details.stream()
                .filter(detail -> detail.getGoodsId() != null)
                .collect(Collectors.toMap(
                        GoodsIncomeDetailEntity::getId,
                        detail -> detail.getGoodsId().longValue()));

        // 批量创建货品自定义字段
        List<GoodsHasColumnsEntity> allColumns = new ArrayList<>();
        for (GoodsIncomeHasColumnsEntity incomeColumn : incomeColumns) {
            Long goodsId = detailToGoodsIdMap.get(incomeColumn.getIncomeDetailId().longValue());
            if (goodsId != null) {
                GoodsHasColumnsEntity column = new GoodsHasColumnsEntity();
                column.setCompanyId(SecurityUtils.getCompanyId());
                column.setGoodsId(goodsId);
                column.setColumnId(incomeColumn.getColumnId());
                column.setColumnSign(incomeColumn.getColumnSign());
                column.setValue(incomeColumn.getValue());
                column.setImageId(incomeColumn.getImageId());
                allColumns.add(column);
            }
        }

        // 批量创建货品图片
        List<GoodsHasImagesEntity> allImages = new ArrayList<>();
        for (GoodsIncomeHasImagesEntity incomeImage : incomeImages) {
            Long goodsId = detailToGoodsIdMap.get(incomeImage.getIncomeDetailId().longValue());
            if (goodsId != null) {
                GoodsHasImagesEntity image = new GoodsHasImagesEntity();
                image.setCompanyId(SecurityUtils.getCompanyId());
                image.setGoodsId(goodsId);
                image.setImageId(incomeImage.getImageId().longValue());
                image.setUrl(incomeImage.getUrl());
                image.setSort(incomeImage.getSort());
                allImages.add(image);
            }
        }

        // 批量插入自定义字段
        if (!allColumns.isEmpty()) {
            goodsHasColumnsMapper.insertBatchSelective(allColumns);
        }

        // 批量插入图片
        if (!allImages.isEmpty()) {
            goodsHasImagesMapper.insertBatchSelective(allImages);
        }
    }

    /**
     * 根据goods_id更新库存
     */
    private void updateStockByGoodsId(List<GoodsIncomeDetailEntity> details) {
        if (details.isEmpty()) {
            return;
        }

        // 转换为库存变更对象
        List<StockNumChangeBO> changes = details.stream()
                .filter(detail -> detail.getGoodsId() != null)
                .map(detail -> StockNumChangeBO.builder()
                        .goodsId(detail.getGoodsId().longValue())
                        .num(detail.getNum())
                        .stockNum(detail.getNum())
                        .comment("采购入库")
                        .build())
                .toList();

        // 使用StockUtils更新库存
        StockUtils.updateStocks(changes);

        // 记录价格变更日志
        List<GoodsPriceChangeBO> priceChanges = details.stream()
                .filter(detail -> detail.getGoodsId() != null)
                .map(detail -> GoodsPriceChangeBO.builder()
                        .goodsId(detail.getGoodsId().longValue())
                        .costPrice(detail.getCostPrice())
                        .goldPrice(detail.getGoldPrice())
                        .silverPrice(detail.getSilverPrice())
                        .workPrice(detail.getWorkPrice())
                        .certPrice(detail.getCertPrice())
                        .saleWorkPrice(detail.getSaleWorkPrice())
                        .tagPrice(detail.getTagPrice())
                        .comment("采购入库")
                        .build())
                .toList();

        // 记录价格变更
        if (!priceChanges.isEmpty()) {
            PriceUtil.logPriceChange(priceChanges);
        }
    }

    /**
     * 更新入库单状态
     */
    private void updateIncomeStatus(List<GoodsIncomeEntity> incomes) {
        if (incomes.isEmpty()) {
            return;
        }

        Date now = new Date();
        Long auditUserId = SecurityUtils.getUserId();

        // 构建更新实体
        GoodsIncomeEntity updateEntity = new GoodsIncomeEntity();
        updateEntity.setStatus(1);
        updateEntity.setAuditBy(auditUserId != null ? auditUserId.intValue() : null);
        updateEntity.setAuditAt(now);

        // 构建更新条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(GoodsIncomeEntity::getId).in(incomes.stream()
                        .map(GoodsIncomeEntity::getId)
                        .collect(Collectors.toList()))
                .and(GoodsIncomeEntity::getCompanyId).eq(SecurityUtils.getCompanyId());

        // 执行批量更新
        int updatedRows = goodsIncomeMapper.updateByQuery(updateEntity, queryWrapper);
        if (updatedRows != incomes.size()) {
            CommonUtils.abort("更新入库单状态失败，预期更新 " + incomes.size() + " 条记录，实际更新 " + updatedRows + " 条记录");
        }

        // 更新入库单明细状态
        GoodsIncomeDetailEntity detailUpdateEntity = new GoodsIncomeDetailEntity();
        detailUpdateEntity.setStatus(1);
        detailUpdateEntity.setAuditBy(auditUserId != null ? auditUserId.intValue() : null);
        detailUpdateEntity.setAuditAt(now);

        // 构建明细更新条件
        QueryWrapper detailQueryWrapper = QueryWrapper.create()
                .where(GoodsIncomeDetailEntity::getReceiveId).in(incomes.stream()
                        .map(GoodsIncomeEntity::getId)
                        .collect(Collectors.toList()))
                .and(GoodsIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId());

        // 执行明细批量更新
        int updatedDetailRows = goodsIncomeDetailMapper.updateByQuery(detailUpdateEntity, detailQueryWrapper);
        if (updatedDetailRows == 0) {
            CommonUtils.abort("更新入库单明细状态失败");
        }
    }

    /**
     * 记录审核日志
     */
    private void recordAuditLog(List<GoodsIncomeEntity> incomes, List<GoodsIncomeDetailEntity> details, String remark) {
        if (incomes.isEmpty()) {
            return;
        }

        Set<Integer> goodsIds = details.stream()
                .map(GoodsIncomeDetailEntity::getGoodsId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        List<GoodsEntity> goodsList = goodsMapper.selectListByIds(goodsIds);

        Map<Long, GoodsEntity> goodsMap = goodsList.stream().collect(Collectors.toMap(GoodsEntity::getId, g -> g));

        for (GoodsIncomeEntity income : incomes) {
            OpLogUtils.appendOpLog("入库单-审核", "入库单审核", income);
        }

        for (GoodsIncomeDetailEntity detail : details) {
            GoodsEntity goods = goodsMap.get(detail.getGoodsId().longValue());
            if (goods == null) {
                continue;
            }
            String logString = new StringBuilder()
                    .append("入库单：")
                    .append(detail.getIncomeCode())
                    .append("，货品：")
                    .append(goods.getGoodsSn())
                    .append("，数量：")
                    .append(detail.getNum())
                    .toString();
            OpLogUtils.appendGoodsLog("入库单-审核", logString, detail, goods);
        }
    }
}