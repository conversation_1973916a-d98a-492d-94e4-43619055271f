package com.xc.boot.modules.income.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 货品比较业务对象
 */
@Data
@Schema(description = "货品比较业务对象")
public class GoodsCompareBO {

    @Schema(description = "所属商户ID")
    private Long companyId;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "货品条码")
    private String goodsSn;

    @Schema(description = "货品名称")
    private String name;

    @Schema(description = "货品分类ID")
    private Long categoryId;

    @Schema(description = "所属小类ID")
    private Long subclassId;

    @Schema(description = "货品品牌ID")
    private Long brandId;

    @Schema(description = "货品款式ID")
    private Long styleId;

    @Schema(description = "货品材质ID")
    private Long jewelryId;

    @Schema(description = "货品成色ID")
    private Long qualityId;

    @Schema(description = "货品工艺ID")
    private Long technologyId;

    @Schema(description = "货品主石ID")
    private Long mainStoneId;

    @Schema(description = "货品主石数")
    private Integer mainStoneCount;

    @Schema(description = "货品主石重")
    private BigDecimal mainStoneWeight;

    @Schema(description = "货品辅石ID")
    private Long subStoneId;

    @Schema(description = "货品辅石数")
    private Integer subStoneCount;

    @Schema(description = "货品辅石重")
    private BigDecimal subStoneWeight;

    @Schema(description = "货品证书号")
    private String certNo;

    @Schema(description = "货品批次号")
    private String batchNo;

    @Schema(description = "销售方式(1:按重量,2:按数量)")
    private Integer salesType;

    @Schema(description = "货品成本价")
    private BigDecimal costPrice;

    @Schema(description = "金进单价")
    private BigDecimal goldPrice;

    @Schema(description = "银进单价")
    private BigDecimal silverPrice;

    @Schema(description = "进工费单价")
    private BigDecimal workPrice;

    @Schema(description = "证书费")
    private BigDecimal certPrice;

    @Schema(description = "工费单价")
    private BigDecimal saleWorkPrice;

    @Schema(description = "标签单价")
    private BigDecimal tagPrice;

    @Schema(description = "货品重量")
    private BigDecimal weight;

    @Schema(description = "净金重")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重")
    private BigDecimal netSilverWeight;

    @Schema(description = "圈口")
    private String circleSize;

    @Schema(description = "货品备注")
    private String remark;

    @Schema(description = "货品图片列表")
    private List<Image> images;

    @Schema(description = "自定义字段列表")
    private List<Column> columns;

    /**
     * 货品图片
     */
    @Data
    @Schema(description = "货品图片")
    public static class Image {
        @Schema(description = "文件ID")
        private Long fileId;

        @Schema(description = "文件链接")
        private String url;
    }

    /**
     * 自定义字段
     */
    @Data
    @Schema(description = "自定义字段")
    public static class Column {
        @Schema(description = "字段标识")
        private String sign;

        @Schema(description = "字段值")
        private String value;

        @Schema(description = "字段名称")
        private String name;
    }

    /**
     * 比较两个对象，返回不一致的字段信息
     * @param other 要比较的对象
     * @return 如果完全一致返回空字符串，否则返回不一致的字段信息
     */
    public String compare(GoodsCompareBO other) {
        if (this == other) return "";
        
        List<String> differences = new ArrayList<>();
        
        if (!Objects.equals(companyId, other.companyId)) {
            differences.add("所属商户");
        }
        if (!Objects.equals(supplierId, other.supplierId)) {
            differences.add("供应商");
        }
        if (!Objects.equals(goodsSn, other.goodsSn)) {
            differences.add("货品条码");
        }
        if (!Objects.equals(name, other.name)) {
            differences.add("货品名称");
        }
        if (!Objects.equals(categoryId, other.categoryId)) {
            differences.add("所属大类");
        }
        if (!Objects.equals(subclassId, other.subclassId)) {
            differences.add("所属小类");
        }
        if (!Objects.equals(brandId, other.brandId)) {
            differences.add("品牌");
        }
        if (!Objects.equals(styleId, other.styleId)) {
            differences.add("款式");
        }
        if (!Objects.equals(qualityId, other.qualityId)) {
            differences.add("成色");
        }
        if (!Objects.equals(technologyId, other.technologyId)) {
            differences.add("工艺");
        }
        if (!Objects.equals(mainStoneId, other.mainStoneId)) {
            differences.add("主石");
        }
        if (!Objects.equals(mainStoneCount, other.mainStoneCount)) {
            differences.add("主石数");
        }
        if (!Objects.equals(mainStoneWeight, other.mainStoneWeight)) {
            differences.add("主石重");
        }
        if (!Objects.equals(subStoneId, other.subStoneId)) {
            differences.add("辅石");
        }
        if (!Objects.equals(subStoneCount, other.subStoneCount)) {
            differences.add("辅石数");
        }
        if (!Objects.equals(subStoneWeight, other.subStoneWeight)) {
            differences.add("辅石重");
        }
        if (!Objects.equals(certNo, other.certNo)) {
            differences.add("证书号");
        }
        if (!Objects.equals(batchNo, other.batchNo)) {
            differences.add("批次号");
        }
        if (!Objects.equals(salesType, other.salesType)) {
            differences.add("销售方式");
        }
        if (!Objects.equals(costPrice, other.costPrice)) {
            differences.add("成本价");
        }
        if (!Objects.equals(goldPrice, other.goldPrice)) {
            differences.add("金进单价");
        }
        if (!Objects.equals(silverPrice, other.silverPrice)) {
            differences.add("银进单价");
        }
        if (!Objects.equals(workPrice, other.workPrice)) {
            differences.add("进工费单价");
        }
        if (!Objects.equals(certPrice, other.certPrice)) {
            differences.add("证书费");
        }
        if (!Objects.equals(saleWorkPrice, other.saleWorkPrice)) {
            differences.add("工费单价");
        }
        if (!Objects.equals(tagPrice, other.tagPrice)) {
            differences.add("标签单价");
        }
        if (!Objects.equals(weight, other.weight)) {
            differences.add("重量");
        }
        if (!Objects.equals(netGoldWeight, other.netGoldWeight)) {
            differences.add("净金重");
        }
        if (!Objects.equals(netSilverWeight, other.netSilverWeight)) {
            differences.add("净银重");
        }
        if (!Objects.equals(circleSize, other.circleSize)) {
            differences.add("圈口");
        }
        if (!Objects.equals(remark, other.remark)) {
            differences.add("备注");
        }
        
        // 比较图片列表
        if (!compareImages(other)) {
            differences.add("货品图片");
        }

        // 比较自定义字段
        List<String> columnDifferences = compareColumns(other);
        if (!columnDifferences.isEmpty()) {
            differences.addAll(columnDifferences);
        }
        
        return differences.isEmpty() ? "" : String.join("、", differences);
    }

    /**
     * 比较两个图片列表是否一致
     * @param other 要比较的对象
     * @return 如果图片列表一致返回true，否则返回false
     */
    private boolean compareImages(GoodsCompareBO other) {
        // 如果两个列表都为null，认为是一致的
        if (images == null && other.images == null) {
            return true;
        }
        
        // 如果只有一个列表为null，认为是不一致的
        if (images == null || other.images == null) {
            return false;
        }
        
        // 如果列表大小不同，认为是不一致的
        if (images.size() != other.images.size()) {
            return false;
        }
        
        // 获取两个列表的url集合
        Set<String> thisUrls = images.stream()
            .map(Image::getUrl)
            .collect(Collectors.toSet());
            
        Set<String> otherUrls = other.images.stream()
            .map(Image::getUrl)
            .collect(Collectors.toSet());
            
        // 比较两个集合是否相同
        return thisUrls.equals(otherUrls);
    }

    /**
     * 比较两个自定义字段列表
     * @param other 要比较的对象
     * @return 不一致的字段名称列表
     */
    private List<String> compareColumns(GoodsCompareBO other) {
        List<String> differences = new ArrayList<>();
        
        // 如果两个列表都为null，认为是一致的
        if (columns == null && other.columns == null) {
            return differences;
        }
        
        // 如果只有一个列表为null，认为是不一致的
        if (columns == null || other.columns == null) {
            differences.add("自定义字段");
            return differences;
        }
        
        // 将两个列表转换为Map，方便比较
        Map<String, Column> thisColumnsMap = columns.stream()
            .collect(Collectors.toMap(Column::getSign, column -> column));
            
        Map<String, Column> otherColumnsMap = other.columns.stream()
            .collect(Collectors.toMap(Column::getSign, column -> column));
            
        // 比较每个字段
        for (Map.Entry<String, Column> entry : thisColumnsMap.entrySet()) {
            String sign = entry.getKey();
            Column thisColumn = entry.getValue();
            Column otherColumn = otherColumnsMap.get(sign);
            
            // 如果另一个对象中没有这个字段，或者值不一致，则添加到差异列表
            if (otherColumn == null || !Objects.equals(thisColumn.getValue(), otherColumn.getValue())) {
                differences.add(thisColumn.getName());
            }
        }
        
        // 检查另一个对象中是否有额外的字段
        for (Map.Entry<String, Column> entry : otherColumnsMap.entrySet()) {
            String sign = entry.getKey();
            if (!thisColumnsMap.containsKey(sign)) {
                differences.add(entry.getValue().getName());
            }
        }
        
        return differences;
    }
} 