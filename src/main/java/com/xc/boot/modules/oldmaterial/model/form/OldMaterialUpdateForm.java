package com.xc.boot.modules.oldmaterial.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import com.xc.boot.common.base.FileItemDTO;

/**
 * 旧料编辑表单
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料编辑表单")
public class OldMaterialUpdateForm {

    @Schema(description = "主键ID")
    @NotNull(message = "ID不能为空")
    private Integer id;

    @Schema(description = "旧料编号")
    private String oldMaterialSn;

    @Schema(description = "旧料名称")
    private String name;

    @Schema(description = "货品小类ID")
    private Integer subclassId;

    @Schema(description = "款式ID")
    private Integer styleId;

    @Schema(description = "工艺ID")
    private Integer technologyId;

    @Schema(description = "品牌ID")
    private Integer brandId;

    @Schema(description = "证书号")
    private String certNo;

    @Schema(description = "圈口")
    private String circleSize;

    @Schema(description = "主石ID")
    private Integer mainStoneId;

    @Schema(description = "主石数")
    private Integer mainStoneCount;

    @Schema(description = "主石重")
    private BigDecimal mainStoneWeight;

    @Schema(description = "辅石ID")
    private Integer subStoneId;

    @Schema(description = "辅石数")
    private Integer subStoneCount;

    @Schema(description = "辅石重")
    private BigDecimal subStoneWeight;

    @Schema(description = "旧料图片列表")
    private List<FileItemDTO> images;
}
