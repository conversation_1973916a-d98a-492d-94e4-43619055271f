package com.xc.boot.modules.oldmaterial.model.vo;

import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialHasImagesEntity;
import com.xc.boot.common.annotation.GoodsColumn;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 旧料销售单明细分页VO
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料销售单明细分页VO")
public class OldMaterialOrderDetailPageVO {

    @Schema(description = "明细ID")
    private Integer id;

    @Schema(description = "销售单ID")
    private Integer orderId;

    @Schema(description = "所属商户ID")
    private Integer companyId;

    @Schema(description = "所属门店ID")
    private Integer merchantId;

    @Schema(description = "门店名称")
    private String merchantName;

    @Schema(description = "旧料ID")
    private Integer oldMaterialId;

    @Schema(description = "旧料编号")
    private String oldMaterialSn;

    @Schema(description = "旧料名称")
    private String oldMaterialName;

    @Schema(description = "旧料图片列表")
    @GoodsColumn(value = "images")
    private List<OldMaterialHasImagesEntity> images;

    @Schema(description = "旧料图片URL列表（逗号分隔）")
    private String imageUrls;

    // 分类信息
    @Schema(description = "所属大类ID")
    private Integer categoryId;

    @Schema(description = "所属大类名称")
    private String categoryName;

    @Schema(description = "货品小类ID")
    private Integer subclassId;

    @Schema(description = "货品小类名称")
    private String subclassName;

    // 销售明细信息
    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "最大可用数量（当前明细数量+旧料库存数量）")
    private Integer maxNum;

    @Schema(description = "重量(g)")
    private BigDecimal weight;

    @Schema(description = "净金重(g)")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")
    private BigDecimal netSilverWeight;

    @Schema(description = "回收单价(元)")
    private BigDecimal recyclePrice;

    @Schema(description = "销售单价(元)")
    private BigDecimal price;

    @Schema(description = "销售金额(元)")
    private BigDecimal totalAmount;

    // 关联信息
    @Schema(description = "供应商ID")
    private Integer supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "成色ID")
    private Integer qualityId;

    @Schema(description = "成色名称")
    private String qualityName;

    @Schema(description = "款式ID")
    private Integer styleId;

    @Schema(description = "款式名称")
    private String styleName;

    @Schema(description = "品牌ID")
    private Integer brandId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "圈口")
    private String circleSize;

    @Schema(description = "工艺ID")
    private Integer technologyId;

    @Schema(description = "工艺名称")
    private String technologyName;

    @Schema(description = "证书号")
    private String certNo;

    @Schema(description = "主石ID")
    private Integer mainStoneId;

    @Schema(description = "主石名称")
    private String mainStoneName;

    @Schema(description = "主石数")
    private Integer mainStoneCount;

    @Schema(description = "辅石ID")
    private Integer subStoneId;

    @Schema(description = "辅石名称")
    private String subStoneName;

    @Schema(description = "辅石数")
    private Integer subStoneCount;

    @Schema(description = "辅石重(g)")
    private BigDecimal subStoneWeight;

    // 销售明细字段
    @Schema(description = "计价方式(1:按重量,2:按数量)")
    private Object salesType;

    @Schema(description = "计价方式名称")
    private String salesTypeName;

    @Schema(description = "金单价(元)")
    private BigDecimal goldPrice;

    @Schema(description = "银单价(元)")
    private BigDecimal silverPrice;

    @Schema(description = "状态(0:待审核,1:已审核)")
    private Object status;

    @Schema(description = "状态名称")
    private String statusName;

    @Schema(description = "审核人ID")
    private Integer auditBy;

    @Schema(description = "审核人姓名")
    private String auditByName;

    @Schema(description = "审核时间")
    private LocalDateTime auditAt;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}