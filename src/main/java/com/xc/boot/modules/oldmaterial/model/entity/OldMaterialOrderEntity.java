package com.xc.boot.modules.oldmaterial.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 旧料销售单实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "old_material_order")
public class OldMaterialOrderEntity extends BaseEntity {
    
    /**
     * 销售单号
     */
    @Column(value = "order_sn")
    private String orderSn;

    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 所属门店ID
     */
    @Column(value = "merchant_id")
    private Integer merchantId;

    /**
     * 供应商ID
     */
    @Column(value = "supplier_id")
    private Integer supplierId;

    /**
     * 出库数量
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 总重量(g)
     */
    @Column(value = "total_weight")
    private BigDecimal totalWeight;

    /**
     * 总金重(g)
     */
    @Column(value = "total_net_gold_weight")
    private BigDecimal totalNetGoldWeight;

    /**
     * 总银重(g)
     */
    @Column(value = "total_net_silver_weight")
    private BigDecimal totalNetSilverWeight;

    /**
     * 销售金额(分)
     */
    @Column(value = "total_amount")
    private Long totalAmount;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 状态(0:待审核,1:已审核)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 审核人ID
     */
    @Column(value = "audit_by")
    private Integer auditBy;

    /**
     * 审核时间
     */
    @Column(value = "audit_at")
    private LocalDateTime auditAt;

    /**
     * 审核备注
     */
    @Column(value = "audit_remark")
    private String auditRemark;

    /**
     * 创建人ID
     */
    @Column(value = "created_by")
    private Integer createdBy;
}
