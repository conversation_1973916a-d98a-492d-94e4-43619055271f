package com.xc.boot.modules.oldmaterial.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.oldmaterial.model.dto.OldMaterialQueryDTO;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialOrderCreateForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialOrderUpdateForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialOrderDetailUpdateForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialOrderAuditForm;
import com.xc.boot.modules.oldmaterial.model.query.OldMaterialOrderQuery;
import com.xc.boot.modules.oldmaterial.model.query.OldMaterialOrderDetailPageQuery;
import com.xc.boot.modules.oldmaterial.model.vo.OldMaterialOrderVO;
import com.xc.boot.modules.oldmaterial.model.vo.OldMaterialOrderDetailPageVO;
import com.xc.boot.modules.oldmaterial.model.vo.OldMaterialQueryVO;
import com.xc.boot.modules.oldmaterial.service.OldMaterialOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 旧料销售单控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/old_material/orders")
@Tag(name = "旧料销售单管理", description = "旧料销售单相关接口")
public class OldMaterialOrderController {

    private final OldMaterialOrderService oldMaterialOrderService;

    /**
     * 旧料查询接口
     * 为旧料销售单提供旧料商品查询功能
     */
    @GetMapping("/query")
    @Operation(summary = "旧料查询", description = "为旧料销售单提供旧料商品查询功能，支持按条码精确查询和按名称模糊查询")
    public Result<List<OldMaterialQueryVO>> queryOldMaterials(@Valid OldMaterialQueryDTO dto) {
        List<OldMaterialQueryVO> results = oldMaterialOrderService.queryOldMaterials(dto);
        return Result.success(results);
    }

    /**
     * 分页查询旧料销售单列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询旧料销售单列表", description = "分页查询旧料销售单列表，支持多种查询条件")
    public PageResult<OldMaterialOrderVO> getOldMaterialOrderPage(@RequestBody OldMaterialOrderQuery query) {
        Page<OldMaterialOrderVO> page = oldMaterialOrderService.getOldMaterialOrderPage(query);
        return PageResult.success(page);
    }

    /**
     * 编辑旧料销售单
     */
    @PutMapping
    @Operation(summary = "编辑旧料销售单", description = "编辑待审核状态的旧料销售单备注")
    public Result<Void> updateOldMaterialOrder(@Valid @RequestBody OldMaterialOrderUpdateForm form) {
        oldMaterialOrderService.updateOldMaterialOrder(form);
        return Result.success();
    }

    /**
     * 创建旧料销售单
     */
    @PostMapping
    @Operation(summary = "创建旧料销售单", description = "创建新的旧料销售单")
    public Result<Long> createOldMaterialOrder(@Valid @RequestBody OldMaterialOrderCreateForm form) {
        Long orderId = oldMaterialOrderService.createOldMaterialOrder(form);
        return Result.success(orderId);
    }

    /**
     * 获取旧料销售单详情
     */
    @GetMapping("/info")
    @Operation(summary = "获取旧料销售单详情", description = "根据ID获取旧料销售单详细信息")
    public Result<OldMaterialOrderVO> getOldMaterialOrderInfo(@RequestParam @Valid @NotNull(message = "id不能为空") Long id) {
        OldMaterialOrderVO orderInfo = oldMaterialOrderService.getOldMaterialOrderDetail(id);
        return Result.success(orderInfo);
    }

    /**
     * 分页查询旧料销售单明细列表
     */
    @PostMapping("/detail/page")
    @Operation(summary = "分页查询旧料销售单明细列表", description = "分页查询指定销售单的明细列表，支持多种查询条件")
    public PageResult<OldMaterialOrderDetailPageVO> getOldMaterialOrderDetailPage(@Valid @RequestBody OldMaterialOrderDetailPageQuery query) {
        Page<OldMaterialOrderDetailPageVO> page = oldMaterialOrderService.getOldMaterialOrderDetailPage(query);
        return PageResult.success(page);
    }

    /**
     * 编辑旧料销售单明细
     */
    @PutMapping("/detail")
    @Operation(summary = "编辑旧料销售单明细", description = "编辑待审核状态的旧料销售单明细，支持修改数量、计价方式和价格")
    public Result<Void> updateOldMaterialOrderDetail(@Valid @RequestBody OldMaterialOrderDetailUpdateForm form) {
        oldMaterialOrderService.updateOldMaterialOrderDetail(form);
        return Result.success();
    }

    /**
     * 删除旧料销售单明细
     */
    @DeleteMapping("/detail")
    @Operation(summary = "删除旧料销售单明细", description = "删除待审核状态的旧料销售单明细，自动恢复库存")
    public Result<Void> deleteOldMaterialOrderDetail(@RequestParam @Valid @NotNull(message = "明细ID不能为空") Long id) {
        oldMaterialOrderService.deleteOldMaterialOrderDetail(id);
        return Result.success();
    }

    /**
     * 审核旧料销售单
     */
    @PostMapping("/audit")
    @Operation(summary = "审核旧料销售单", description = "审核旧料销售单，支持通过和驳回操作")
    public Result<Void> auditOldMaterialOrder(@Valid @RequestBody OldMaterialOrderAuditForm form) {
        oldMaterialOrderService.auditOldMaterialOrder(form);
        return Result.success();
    }
}
