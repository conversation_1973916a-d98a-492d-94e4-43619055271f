package com.xc.boot.modules.oldmaterial.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 旧料转货品表单
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料转货品表单")
public class OldMaterialConvertForm {
    @Schema(description = "旧料ID")
    @NotNull(message = "旧料ID不能为空")
    Long oldMaterialId;
    @Schema(description = "柜台ID")
    @NotNull(message = "柜台ID不能为空")
    Integer counterId;

}
