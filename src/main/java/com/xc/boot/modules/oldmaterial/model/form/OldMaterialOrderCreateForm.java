package com.xc.boot.modules.oldmaterial.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 旧料销售单创建表单
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料销售单创建表单")
public class OldMaterialOrderCreateForm {

    @Schema(description = "所属门店ID", required = true)
    @NotNull(message = "门店ID不能为空")
    private Integer merchantId;

    @Schema(description = "供应商ID", required = true)
    @NotNull(message = "供应商ID不能为空")
    private Integer supplierId;

    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500字符")
    private String remark;

    @Schema(description = "销售明细列表", required = true)
    @NotEmpty(message = "销售明细不能为空")
    @Valid
    private List<OldMaterialOrderDetailForm> details;

    /**
     * 旧料销售单明细表单
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    @Schema(description = "旧料销售单明细表单")
    public static class OldMaterialOrderDetailForm {

        @Schema(description = "旧料ID", required = true)
        @NotNull(message = "旧料ID不能为空")
        private Integer oldMaterialId;

        @Schema(description = "数量", required = true)
        @NotNull(message = "数量不能为空")
        private Integer num;

        @Schema(description = "计价方式(1:按重量,2:按数量)", required = true)
        @NotNull(message = "计价方式不能为空")
        private Integer salesType;

        @Schema(description = "金单价(元)")
        private BigDecimal goldPrice;

        @Schema(description = "银单价(元)")
        private BigDecimal silverPrice;

        @Schema(description = "销售单价(元)", required = true)
        @NotNull(message = "销售单价不能为空")
        private BigDecimal price;
    }
}
