package com.xc.boot.modules.oldmaterial.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 旧料销售单明细VO
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料销售单明细VO")
public class OldMaterialOrderDetailVO {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "所属商户ID")
    private Integer companyId;

    @Schema(description = "所属门店ID")
    private Integer merchantId;

    @Schema(description = "货品ID")
    private Integer goodsId;

    @Schema(description = "货品条码")
    private String goodsSn;

    @Schema(description = "旧料ID")
    private Integer oldMaterialId;

    @Schema(description = "旧料编号")
    private String oldMaterialSn;

    @Schema(description = "旧料名称")
    private String oldMaterialName;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "计价方式(1:按重量,2:按数量)")
    private Integer salesType;

    @Schema(description = "计价方式名称")
    private String salesTypeName;

    @Schema(description = "金单价(元)")
    private BigDecimal goldPrice;

    @Schema(description = "银单价(元)")
    private BigDecimal silverPrice;

    @Schema(description = "销售单价(元)")
    private BigDecimal price;

    @Schema(description = "状态(0:待审核,1:已审核)")
    private Integer status;

    @Schema(description = "状态名称")
    private String statusName;

    @Schema(description = "审核人ID")
    private Integer auditBy;

    @Schema(description = "审核人姓名")
    private String auditByName;

    @Schema(description = "审核时间")
    private LocalDateTime auditAt;

    @Schema(description = "数据快照")
    private String dataSnapshot;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
