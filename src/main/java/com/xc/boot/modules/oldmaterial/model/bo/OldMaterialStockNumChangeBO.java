package com.xc.boot.modules.oldmaterial.model.bo;

import lombok.Builder;
import lombok.Data;

/**
 * 旧料库存数量变更对象
 */
@Data
@Builder
public class OldMaterialStockNumChangeBO {
    /**
     * 旧料ID
     */
    private Long oldMaterialId;

    /**
     * 变更描述
     */
    private String comment;

    /**
     * 库存数量变化量
     */
    @Builder.Default
    private Integer num = 0;

    /**
     * 冻结数量变化量
     */
    @Builder.Default
    private Integer frozenNum = 0;
}
