package com.xc.boot.modules.oldmaterial.model.vo;

import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 旧料查询结果VO
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料查询结果VO")
public class OldMaterialQueryVO {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "旧料编号")
    private String oldMaterialSn;

    @Schema(description = "旧料名称")
    private String name;

    @Schema(description = "旧料图片")
    private List<OldMaterialHasImagesEntity> images;

    @Schema(description = "重量(g)")
    private BigDecimal weight;

    @Schema(description = "净金重(g)")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")
    private BigDecimal netSilverWeight;

    @Schema(description = "回收金价(元)")
    private BigDecimal goldPrice;

    @Schema(description = "回收银价(元)")
    private BigDecimal silverPrice;

    @Schema(description = "回收价(元)")
    private BigDecimal recyclePrice;

    // 前端交互字段（后端返回空值，前端填写）
    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "计价方式")
    private String salesType;

    @Schema(description = "金单价(元)")
    private BigDecimal saleGoldPrice;

    @Schema(description = "银单价(元)")
    private BigDecimal saleSilverPrice;

    // @Schema(description = "销售单价(元)")
    // private BigDecimal salePrice;

    @Schema(description = "销售金额(元，前端计算)")
    private BigDecimal totalAmount;

    // 关联信息字段
    @Schema(description = "所属大类名称")
    private String categoryName;

    @Schema(description = "货品小类名称")
    private String subclassName;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "成色名称")
    private String qualityName;

    @Schema(description = "款式名称")
    private String styleName;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "圈口")
    private String circleSize;

    @Schema(description = "工艺名称")
    private String technologyName;

    @Schema(description = "证书号")
    private String certNo;

    @Schema(description = "主石名称")
    private String mainStoneName;

    @Schema(description = "主石数")
    private Integer mainStoneCount;

    @Schema(description = "辅石名称")
    private String subStoneName;

    @Schema(description = "辅石数")
    private Integer subStoneCount;

    @Schema(description = "辅石重(ct)")
    private BigDecimal subStoneWeight;

    // 用于填充关联数据的ID字段
    @Schema(description = "所属大类ID")
    private Integer categoryId;

    @Schema(description = "所属小类ID")
    private Integer subclassId;

    @Schema(description = "供应商ID")
    private Integer supplierId;

    @Schema(description = "成色ID")
    private Integer qualityId;

    @Schema(description = "款式ID")
    private Integer styleId;

    @Schema(description = "品牌ID")
    private Integer brandId;

    @Schema(description = "工艺ID")
    private Integer technologyId;

    @Schema(description = "主石ID")
    private Integer mainStoneId;

    @Schema(description = "辅石ID")
    private Integer subStoneId;
}
