package com.xc.boot.modules.oldmaterial.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 旧料图片关联实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "old_material_has_images")
public class OldMaterialHasImagesEntity extends BaseEntity {
    
    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 旧料ID
     */
    @Column(value = "old_material_id")
    private Integer oldMaterialId;

    /**
     * 图片ID
     */
    @Column(value = "image_id")
    private Integer imageId;

    /**
     * 图片URL
     */
    @Column(value = "url")
    private String url;

    /**
     * 排序号
     */
    @Column(value = "sort")
    private Integer sort;
}
