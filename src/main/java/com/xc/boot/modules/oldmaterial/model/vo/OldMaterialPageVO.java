package com.xc.boot.modules.oldmaterial.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xc.boot.common.annotation.GoodsColumn;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialHasImagesEntity;

/**
 * 旧料分页查询VO
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料分页查询VO")
public class OldMaterialPageVO {

    @Schema(description = "主键ID")
    private Integer id;

    // ID字段（用于关联数据填充）
    @Schema(description = "所属门店ID")
    private Integer merchantId;

    @Schema(description = "所属大类ID")
    private Integer categoryId;

    @Schema(description = "所属小类ID")
    private Integer subclassId;

    @Schema(description = "成色ID")
    private Integer qualityId;

    @Schema(description = "供应商ID")
    private Integer supplierId;

    @Schema(description = "款式ID")
    private Integer styleId;

    @Schema(description = "品牌ID")
    private Integer brandId;

    @Schema(description = "工艺ID")
    private Integer technologyId;

    @Schema(description = "主石ID")
    private Integer mainStoneId;

    @Schema(description = "辅石ID")
    private Integer subStoneId;

    @Schema(description = "计价方式(1:按重量,2:按数量)")
    private Integer salesType;

    // 基础信息
    @Schema(description = "所属门店名称")
    private String merchantName;

    @Schema(description = "旧料图片URL列表（逗号分隔）")
    private String imageUrls;

    @Schema(description = "旧料图片列表")
    private List<OldMaterialHasImagesEntity> images;

    @Schema(description = "旧料编号")
    private String oldMaterialSn;

    @Schema(description = "旧料名称")
    private String name;

    @Schema(description = "所属大类名称")
    private String categoryName;

    @Schema(description = "货品小类名称")
    private String subclassName;

    // 数量重量
    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "重量(g)")
    private BigDecimal weight;

    @Schema(description = "净金重(g)")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")
    private BigDecimal netSilverWeight;

    @Schema(description = "计价方式名称")
    private String salesTypeName;

    // 价格信息
    @Schema(description = "回收金价")
    private BigDecimal goldPrice;

    @Schema(description = "回收银价")
    private BigDecimal silverPrice;

    @Schema(description = "回收单价")
    private BigDecimal recyclePrice;

    @Schema(description = "回收金额")
    private BigDecimal recycleAmount;

    @GoodsColumn(value = "cost_price")
    @Schema(description = "成本单价")
    private BigDecimal costPrice;

    @Schema(description = "标签单价")
    private BigDecimal tagPrice;

    @Schema(description = "证书费")
    private BigDecimal certPrice;

    // 货品信息
    @Schema(description = "本店货品标识")
    private Boolean isLocalGoods;

    @Schema(description = "本店货品标识名称")
    private String isLocalGoodsName;

    @Schema(description = "货品ID")
    private Integer goodsId;

    @Schema(description = "货品条码")
    private String goodsSn;

    @Schema(description = "成色名称")
    private String qualityName;

    @Schema(description = "供应商名称")
    private String supplierName;

    // 详细属性
    @Schema(description = "主石名称")
    private String mainStoneName;

    @Schema(description = "主石数")
    private Integer mainStoneCount;

    @Schema(description = "主石重")
    private BigDecimal mainStoneWeight;

    @Schema(description = "辅石名称")
    private String subStoneName;

    @Schema(description = "辅石数")
    private Integer subStoneCount;

    @Schema(description = "辅石重")
    private BigDecimal subStoneWeight;

    @Schema(description = "款式名称")
    private String styleName;

    @Schema(description = "圈口")
    private String circleSize;

    @Schema(description = "工艺名称")
    private String technologyName;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "证书号")
    private String certNo;

    // 时间信息
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    @Schema(description = "回收时间（创建时间）")
    private String recycleTime;

    // Getter方法 - 只对xxId字段特殊处理：值为0时返回null
    public Integer getId() {
        return id != null && id == 0 ? null : id;
    }

    public Integer getMerchantId() {
        return merchantId != null && merchantId == 0 ? null : merchantId;
    }

    public Integer getCategoryId() {
        return categoryId != null && categoryId == 0 ? null : categoryId;
    }

    public Integer getSubclassId() {
        return subclassId != null && subclassId == 0 ? null : subclassId;
    }

    public Integer getQualityId() {
        return qualityId != null && qualityId == 0 ? null : qualityId;
    }

    public Integer getSupplierId() {
        return supplierId != null && supplierId == 0 ? null : supplierId;
    }

    public Integer getStyleId() {
        return styleId != null && styleId == 0 ? null : styleId;
    }

    public Integer getBrandId() {
        return brandId != null && brandId == 0 ? null : brandId;
    }

    public Integer getTechnologyId() {
        return technologyId != null && technologyId == 0 ? null : technologyId;
    }

    public Integer getMainStoneId() {
        return mainStoneId != null && mainStoneId == 0 ? null : mainStoneId;
    }

    public Integer getSubStoneId() {
        return subStoneId != null && subStoneId == 0 ? null : subStoneId;
    }

    public Integer getGoodsId() {
        return goodsId != null && goodsId == 0 ? null : goodsId;
    }


}
