package com.xc.boot.modules.oldmaterial.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 旧料销售单编辑表单
 */
@Getter
@Setter
@Schema(description = "旧料销售单编辑表单")
public class OldMaterialOrderUpdateForm {

    @NotNull(message = "销售单ID不能为空")
    @Schema(description = "销售单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String remark;
}
