package com.xc.boot.modules.oldmaterial.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;

/**
 * 旧料查询DTO
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料查询DTO")
public class OldMaterialQueryDTO {

    @Schema(description = "所属门店ID", required = true)
    @NotNull(message = "门店ID不能为空")
    private Integer merchantId;

    @Schema(description = "供应商ID")
    private Integer supplierId;

    @Schema(description = "查询类型(1=按条码精确查询, 2=按名称模糊查询)", required = true)
    @NotNull(message = "查询类型不能为空")
    private Integer searchType;

    @Schema(description = "查询关键字", required = true)
    @NotNull(message = "查询关键字不能为空")
    private String keyword;
}
