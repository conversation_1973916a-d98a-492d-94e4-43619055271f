package com.xc.boot.modules.oldmaterial.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 旧料库存变化记录
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "old_material_num_logs")
public class OldMaterialNumLogsEntity extends BaseEntity {
    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 旧料ID
     */
    @Column(value = "old_material_id")
    private Long oldMaterialId;

    /**
     * 变更描述
     */
    @Column(value = "comment")
    private String comment;

    /**
     * 库存数量(变化量)
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 冻结数量(变化量)
     */
    @Column(value = "frozen_num")
    private Integer frozenNum;

    /**
     * 操作人
     */
    @Column(value = "created_by")
    private Long createdBy;
}
