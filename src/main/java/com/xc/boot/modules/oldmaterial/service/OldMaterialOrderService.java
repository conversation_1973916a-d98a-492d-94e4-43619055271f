package com.xc.boot.modules.oldmaterial.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.oldmaterial.model.dto.OldMaterialQueryDTO;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialOrderEntity;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialOrderCreateForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialOrderUpdateForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialOrderDetailUpdateForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialOrderAuditForm;
import com.xc.boot.modules.oldmaterial.model.query.OldMaterialOrderQuery;
import com.xc.boot.modules.oldmaterial.model.query.OldMaterialOrderDetailPageQuery;
import com.xc.boot.modules.oldmaterial.model.vo.OldMaterialOrderVO;
import com.xc.boot.modules.oldmaterial.model.vo.OldMaterialOrderDetailPageVO;
import com.xc.boot.modules.oldmaterial.model.vo.OldMaterialQueryVO;

import java.util.List;

/**
 * 旧料销售单服务接口
 */
public interface OldMaterialOrderService extends IService<OldMaterialOrderEntity> {

    /**
     * 查询旧料商品
     * 为旧料销售单提供旧料商品查询功能
     *
     * @param dto 查询条件
     * @return 旧料商品列表
     */
    List<OldMaterialQueryVO> queryOldMaterials(OldMaterialQueryDTO dto);

    /**
     * 分页查询旧料销售单列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    Page<OldMaterialOrderVO> getOldMaterialOrderPage(OldMaterialOrderQuery query);

    /**
     * 创建旧料销售单
     *
     * @param form 创建表单
     * @return 销售单ID
     */
    Long createOldMaterialOrder(OldMaterialOrderCreateForm form);

    /**
     * 编辑旧料销售单
     * 只能编辑待审核状态的销售单备注
     *
     * @param form 编辑表单
     */
    void updateOldMaterialOrder(OldMaterialOrderUpdateForm form);

    /**
     * 审核旧料销售单
     *
     * @param form 审核表单
     */
    void auditOldMaterialOrder(OldMaterialOrderAuditForm form);

    /**
     * 获取旧料销售单详情
     *
     * @param id 销售单ID
     * @return 销售单详情
     */
    OldMaterialOrderVO getOldMaterialOrderDetail(Long id);

    /**
     * 分页查询旧料销售单明细列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    Page<OldMaterialOrderDetailPageVO> getOldMaterialOrderDetailPage(OldMaterialOrderDetailPageQuery query);

    /**
     * 编辑旧料销售单明细
     * 只能编辑待审核状态的销售单明细
     *
     * @param form 编辑表单
     */
    void updateOldMaterialOrderDetail(OldMaterialOrderDetailUpdateForm form);

    /**
     * 删除旧料销售单明细
     * 只能删除待审核状态的销售单明细
     *
     * @param id 明细ID
     */
    void deleteOldMaterialOrderDetail(Long id);
}
