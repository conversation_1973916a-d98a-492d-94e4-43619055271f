package com.xc.boot.modules.oldmaterial.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.base.FileItemDTO;
import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.common.util.*;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtilV2;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsHasColumnsMapper;
import com.xc.boot.modules.goods.mapper.GoodsHasImagesMapper;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.bo.StockNumChangeBO;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasColumnsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import com.xc.boot.modules.merchant.mapper.*;
import com.xc.boot.modules.merchant.model.entity.*;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialHasImagesMapper;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialMapper;
import com.xc.boot.modules.oldmaterial.model.bo.OldMaterialStockNumChangeBO;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialEntity;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialHasImagesEntity;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialConvertForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialCreateForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialUpdateForm;
import com.xc.boot.modules.oldmaterial.model.query.OldMaterialQuery;
import com.xc.boot.modules.oldmaterial.model.vo.OldMaterialPageVO;
import com.xc.boot.modules.oldmaterial.service.OldMaterialService;
import com.xc.boot.modules.order.model.enums.SalesTypeEnum;
import com.xc.boot.system.model.vo.HeadingColumns;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;



/**
 * 旧料服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OldMaterialServiceImpl extends ServiceImpl<OldMaterialMapper, OldMaterialEntity> implements OldMaterialService {

    private final ListFillService listFillService;
    private final OldMaterialHasImagesMapper oldMaterialHasImagesMapper;

    // 验证用的 Mapper
    private final SubclassMapper subclassMapper;
    private final StyleMapper styleMapper;
    private final TechnologyMapper technologyMapper;
    private final BrandMapper brandMapper;
    private final SupplierMapper supplierMapper;
    private final CounterMapper counterMapper;
    private final QualityMapper qualityMapper;
    private final JewelryMapper jewelryMapper;
    private final GoodsMapper goodsMapper;
    private final GoodsHasImagesMapper goodsHasImagesMapper;
    private final GoodsHasColumnsMapper goodsHasColumnsMapper;

    @Override
    public Page<OldMaterialPageVO> getOldMaterialPage(OldMaterialQuery query) {
        QueryWrapper wrapper = buildWrapper(query);
        wrapper.orderBy("id", false);

        // 处理导出
        if (query.getExport() != null && query.getExport().equals(1)) {
            exportOldMaterials(wrapper, query);
            return new Page<>();
        }

        // 处理打印
        if (query.getPrint() != null && query.getPrint().equals(1)) {
            return printOldMaterials(wrapper, query);
        }

        // 执行分页查询
        Page<OldMaterialPageVO> page = this.mapper.paginateAs(query.getPageNum(), query.getPageSize(), wrapper, OldMaterialPageVO.class);
        List<OldMaterialPageVO> records = page.getRecords();
        if (records.isEmpty()) {
            return page;
        }

        fillList(records);
        return page;
    }

    @Override
    public void updateOldMaterial(OldMaterialUpdateForm form) {
        // 1. 验证旧料是否存在
        OldMaterialEntity entity = this.getById(form.getId());
        CommonUtils.abortIf(entity == null, "旧料不存在");

        // 2. 验证用户权限（merchantId在用户门店范围内）
        CommonUtils.abortIf(!entity.getCompanyId().equals(SecurityUtils.getCompanyId().intValue()),
            "无权操作其他商户的数据");

        // 验证门店权限
        if (!SecurityUtils.isMain()) {
            CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantId().longValue()),
                "无权操作其他门店的旧料");
        }

        // 3. 验证只能编辑goods_id为零的旧料
        CommonUtils.abortIf(entity.getGoodsId() != null && entity.getGoodsId() > 0,
            "已关联货品的旧料不能编辑");

        // 4. 验证关联数据有效性
        validateAssociations(form);

        // 5. 更新数据
        OldMaterialEntity updateEntity = new OldMaterialEntity();
        updateEntity.setId(form.getId().longValue());

        // 旧料名称
        if (StrUtil.isNotBlank(form.getName())) {
            updateEntity.setName(form.getName());
        }
        // 货品小类
        if (form.getSubclassId() != null) {
            updateEntity.setSubclassId(form.getSubclassId());
        }
        // 款式
        if (form.getStyleId() != null) {
            updateEntity.setStyleId(form.getStyleId());
        }
        // 工艺
        if (form.getTechnologyId() != null) {
            updateEntity.setTechnologyId(form.getTechnologyId());
        }
        // 品牌
        if (form.getBrandId() != null) {
            updateEntity.setBrandId(form.getBrandId());
        }
        // 证书号
        if (StrUtil.isNotBlank(form.getCertNo())) {
            updateEntity.setCertNo(form.getCertNo());
        }
        // 圈口
        if (StrUtil.isNotBlank(form.getCircleSize())) {
            updateEntity.setCircleSize(form.getCircleSize());
        }
        // 主石
        if (form.getMainStoneId() != null) {
            updateEntity.setMainStoneId(form.getMainStoneId());
        }
        // 主石数
        if (form.getMainStoneCount() != null) {
            updateEntity.setMainStoneCount(form.getMainStoneCount());
        }
        // 主石重
        if (form.getMainStoneWeight() != null) {
            updateEntity.setMainStoneWeight(form.getMainStoneWeight());
        }
        // 辅石
        if (form.getSubStoneId() != null) {
            updateEntity.setSubStoneId(form.getSubStoneId());
        }
        // 辅石数
        if (form.getSubStoneCount() != null) {
            updateEntity.setSubStoneCount(form.getSubStoneCount());
        }
        // 辅石重
        if (form.getSubStoneWeight() != null) {
            updateEntity.setSubStoneWeight(form.getSubStoneWeight());
        }

        // 重新计算重量：如果修改了主石或辅石相关字段，需要重新计算重量
        boolean needRecalculateWeight = form.getMainStoneCount() != null ||
                                       form.getMainStoneWeight() != null ||
                                       form.getSubStoneCount() != null ||
                                       form.getSubStoneWeight() != null;

        if (needRecalculateWeight) {
            // 获取当前实体的完整信息用于重量计算
            OldMaterialEntity currentEntity = this.getById(form.getId());

            // 使用更新后的值或现有值来计算重量
            BigDecimal netGoldWeight = currentEntity.getNetGoldWeight();
            BigDecimal netSilverWeight = currentEntity.getNetSilverWeight();
            BigDecimal mainStoneWeight = form.getMainStoneWeight() != null ? form.getMainStoneWeight() : currentEntity.getMainStoneWeight();
            Integer mainStoneCount = form.getMainStoneCount() != null ? form.getMainStoneCount() : currentEntity.getMainStoneCount();
            BigDecimal subStoneWeight = form.getSubStoneWeight() != null ? form.getSubStoneWeight() : currentEntity.getSubStoneWeight();
            Integer subStoneCount = form.getSubStoneCount() != null ? form.getSubStoneCount() : currentEntity.getSubStoneCount();

            // 计算新的重量
            BigDecimal calculatedWeight = calculateWeight(
                netGoldWeight,
                netSilverWeight,
                mainStoneWeight,
                mainStoneCount,
                subStoneWeight,
                subStoneCount
            );

            // 只有当前重量为0或null时才使用计算的重量，否则保持原重量
            if (currentEntity.getWeight() == null || currentEntity.getWeight().compareTo(BigDecimal.ZERO) == 0) {
                updateEntity.setWeight(calculatedWeight);
            }
        }

        this.updateById(updateEntity);

        // 6. 处理图片
        handleOldMaterialImagesFromFileItems(form.getId().longValue(), form.getImages());
    }

    @Override
    public Long createOldMaterial(OldMaterialCreateForm form) {
        // 1. 验证关联数据有效性
        validateCreateAssociations(form);

        // 2. 创建旧料实体
        OldMaterialEntity entity = new OldMaterialEntity();

        // 设置基础信息
        entity.setCompanyId(SecurityUtils.getCompanyId().intValue());
        // 设置当前用户的门店ID（取用户权限范围内的第一个门店）
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        merchantIds.remove(0L); // 移除默认的0值
        if (merchantIds.isEmpty()) {
            CommonUtils.abort("用户未分配门店权限");
        }
        entity.setMerchantId(merchantIds.iterator().next().intValue());
        entity.setCategoryId(form.getCategoryId());
        entity.setSubclassId(form.getSubclassId());
        entity.setQualityId(form.getQualityId());
        entity.setName(form.getName());
        entity.setSalesType(form.getSalesType());
        entity.setNum(0); // 初始化为0，通过库存变更系统设置实际数量
        entity.setFrozenNum(0); // 初始化冻结数量为0
        entity.setGoodsId(0); // 默认为0，表示未关联货品

        // 3. 自动生成旧料编号
        entity.setOldMaterialSn(SnUtils.generateOldMaterialSn());

        // 设置可选字段
        if (form.getRecyclePrice() != null) {
            entity.setRecyclePrice(form.getRecyclePrice().multiply(new BigDecimal("100")).longValue());
        }
        if (form.getNetGoldWeight() != null) {
            entity.setNetGoldWeight(form.getNetGoldWeight());
        }
        if (form.getNetSilverWeight() != null) {
            entity.setNetSilverWeight(form.getNetSilverWeight());
        }
        if (form.getSupplierId() != null) {
            entity.setSupplierId(form.getSupplierId());
        }
        if (form.getCounterId() != null) {
            entity.setCounterId(form.getCounterId());
        }
        if (form.getBrandId() != null) {
            entity.setBrandId(form.getBrandId());
        }
        if (form.getStyleId() != null) {
            entity.setStyleId(form.getStyleId());
        }
        if (form.getTechnologyId() != null) {
            entity.setTechnologyId(form.getTechnologyId());
        }
        if (form.getMainStoneId() != null) {
            entity.setMainStoneId(form.getMainStoneId());
        }
        if (form.getMainStoneCount() != null) {
            entity.setMainStoneCount(form.getMainStoneCount());
        }
        if (form.getMainStoneWeight() != null) {
            entity.setMainStoneWeight(form.getMainStoneWeight());
        }
        if (form.getSubStoneId() != null) {
            entity.setSubStoneId(form.getSubStoneId());
        }
        if (form.getSubStoneCount() != null) {
            entity.setSubStoneCount(form.getSubStoneCount());
        }
        if (form.getSubStoneWeight() != null) {
            entity.setSubStoneWeight(form.getSubStoneWeight());
        }
        if (StrUtil.isNotBlank(form.getCircleSize())) {
            entity.setCircleSize(form.getCircleSize());
        }

        // 重量计算逻辑：如果重量为0或null，则按公式计算
        BigDecimal calculatedWeight = calculateWeight(
            form.getNetGoldWeight(),
            form.getNetSilverWeight(),
            form.getMainStoneWeight(),
            form.getMainStoneCount(),
            form.getSubStoneWeight(),
            form.getSubStoneCount()
        );

        BigDecimal finalWeight = (form.getWeight() == null || form.getWeight().compareTo(BigDecimal.ZERO) == 0)
            ? calculatedWeight : form.getWeight();
        entity.setWeight(finalWeight);
        if (StrUtil.isNotBlank(form.getBatchNo())) {
            entity.setBatchNo(form.getBatchNo());
        }
        if (StrUtil.isNotBlank(form.getCertNo())) {
            entity.setCertNo(form.getCertNo());
        }
        if (StrUtil.isNotBlank(form.getRemark())) {
            entity.setRemark(form.getRemark());
        }

        // 设置价格字段（转换为分）
        if (form.getGoldPrice() != null) {
            entity.setGoldPrice(form.getGoldPrice().multiply(new BigDecimal("100")).longValue());
        }
        if (form.getSilverPrice() != null) {
            entity.setSilverPrice(form.getSilverPrice().multiply(new BigDecimal("100")).longValue());
        }
        if (form.getWorkPrice() != null) {
            entity.setWorkPrice(form.getWorkPrice().multiply(new BigDecimal("100")).longValue());
        }
        if (form.getCertPrice() != null) {
            entity.setCertPrice(form.getCertPrice().multiply(new BigDecimal("100")).longValue());
        }
        if (form.getSaleWorkPrice() != null) {
            entity.setSaleWorkPrice(form.getSaleWorkPrice().multiply(new BigDecimal("100")).longValue());
        }
        if (form.getTagPrice() != null) {
            entity.setTagPrice(form.getTagPrice().multiply(new BigDecimal("100")).longValue());
        }
        if (form.getCostPrice() != null) {
            entity.setCostPrice(form.getCostPrice().multiply(new BigDecimal("100")).longValue());
        }

        // 4. 保存到数据库
        this.save(entity);

        // 5. 处理图片
        if (CollUtil.isNotEmpty(form.getImages())) {
            handleOldMaterialImagesFromFileItems(entity.getId(), form.getImages());
        }

        return entity.getId();
    }

    /**
     * 更新旧料库存数量
     *
     * @param oldMaterialId 旧料ID
     * @param num 数量变化量
     * @param comment 变更描述
     */
    public void updateOldMaterialStock(Long oldMaterialId, Integer num, String comment) {
        if (oldMaterialId == null || num == null || num == 0) {
            return;
        }

        OldMaterialStockNumChangeBO stockChange = OldMaterialStockNumChangeBO.builder()
                .oldMaterialId(oldMaterialId)
                .num(num)
                .comment(comment)
                .build();
        StockUtils.updateOldMaterialStocks(List.of(stockChange));
    }

    /**
     * 批量更新旧料库存数量
     *
     * @param changes 库存变更列表
     */
    public void updateOldMaterialStocks(List<OldMaterialStockNumChangeBO> changes) {
        if (changes == null || changes.isEmpty()) {
            return;
        }
        StockUtils.updateOldMaterialStocks(changes);
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper buildWrapper(OldMaterialQuery query) {
        QueryWrapper wrapper = QueryWrapper.create()
                .from(OldMaterialEntity.class)
                .where(OldMaterialEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .where(OldMaterialEntity::getMerchantId).in(SecurityUtils.getMerchantIds());

        // 旧料ID列表筛选
        if (CollUtil.isNotEmpty(query.getIds())) {
            wrapper.and(OldMaterialEntity::getId).in(query.getIds());
        }

        // 门店ID列表
        if (StrUtil.isNotBlank(query.getMerchantIds())) {
            List<Integer> merchantIds = Arrays.stream(query.getMerchantIds().split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            wrapper.and(OldMaterialEntity::getMerchantId).in(merchantIds);
        }

        // 旧料编号模糊查询
        if (StrUtil.isNotBlank(query.getOldMaterialSn())) {
            wrapper.and(OldMaterialEntity::getOldMaterialSn).like(query.getOldMaterialSn());
        }

        // 旧料名称模糊查询
        if (StrUtil.isNotBlank(query.getName())) {
            wrapper.and(OldMaterialEntity::getName).like(query.getName());
        }

        // 大类ID列表
        if (StrUtil.isNotBlank(query.getCategoryIds())) {
            List<Integer> categoryIds = Arrays.stream(query.getCategoryIds().split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            wrapper.and(OldMaterialEntity::getCategoryId).in(categoryIds);
        }

        // 小类ID列表
        if (StrUtil.isNotBlank(query.getSubclassIds())) {
            List<Integer> subclassIds = Arrays.stream(query.getSubclassIds().split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            wrapper.and(OldMaterialEntity::getSubclassId).in(subclassIds);
        }

        // 计价方式
        if (query.getSalesType() != null) {
            wrapper.and(OldMaterialEntity::getSalesType).eq(query.getSalesType());
        }

        // 本店货品标识
        if (query.getIsLocalGoods() != null) {
            if (query.getIsLocalGoods()) {
                wrapper.and(OldMaterialEntity::getGoodsId).gt(0);
            } else {
                wrapper.and(OldMaterialEntity::getGoodsId).eq(0);
            }
        }

        // 成色ID列表
        if (StrUtil.isNotBlank(query.getQualityIds())) {
            List<Integer> qualityIds = Arrays.stream(query.getQualityIds().split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            wrapper.and(OldMaterialEntity::getQualityId).in(qualityIds);
        }

        // 供应商ID列表
        if (StrUtil.isNotBlank(query.getSupplierIds())) {
            List<Integer> supplierIds = Arrays.stream(query.getSupplierIds().split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            wrapper.and(OldMaterialEntity::getSupplierId).in(supplierIds);
        }

        // 回收时间范围
        if (CollUtil.isNotEmpty(query.getRecycleTimeRange()) && query.getRecycleTimeRange().size() == 2) {
            String startTime = query.getRecycleTimeRange().get(0);
            String endTime = query.getRecycleTimeRange().get(1);
            if (StrUtil.isNotBlank(startTime) && StrUtil.isNotBlank(endTime)) {
                wrapper.and(OldMaterialEntity::getCreatedAt).between(startTime, endTime);
            }
        }

        // 过滤零库存的旧料
        wrapper.and(OldMaterialEntity::getNum).gt(0);

        return wrapper;
    }

    /**
     * 填充关联数据
     */
    private void fillList(List<OldMaterialPageVO> records) {
        ListFillUtilV2.of(records)
                .build(listFillService::getMerchantNameById, OldMaterialPageVO::getMerchantId, OldMaterialPageVO::setMerchantName)
                .build(listFillService::getCategoryNameById, OldMaterialPageVO::getCategoryId, OldMaterialPageVO::setCategoryName)
                .build(listFillService::getSubclassNameById, OldMaterialPageVO::getSubclassId, OldMaterialPageVO::setSubclassName)
                .build(listFillService::getQualityNameById, OldMaterialPageVO::getQualityId, OldMaterialPageVO::setQualityName)
                .build(listFillService::getSupplierNameById, OldMaterialPageVO::getSupplierId, OldMaterialPageVO::setSupplierName)
                .build(listFillService::getStyleNameById, OldMaterialPageVO::getStyleId, OldMaterialPageVO::setStyleName)
                .build(listFillService::getBrandNameById, OldMaterialPageVO::getBrandId, OldMaterialPageVO::setBrandName)
                .build(listFillService::getTechnologyNameById, OldMaterialPageVO::getTechnologyId, OldMaterialPageVO::setTechnologyName)
                .build(listFillService::getJewelryMapperNameById, OldMaterialPageVO::getMainStoneId, OldMaterialPageVO::setMainStoneName)
                .build(listFillService::getJewelryMapperNameById, OldMaterialPageVO::getSubStoneId, OldMaterialPageVO::setSubStoneName)
                .build(listFillService::getOldMaterialImgStringByOldMaterialId, OldMaterialPageVO::getId, OldMaterialPageVO::setImageUrls)
                .build(listFillService::getOldMaterialImgByOldMaterialId, OldMaterialPageVO::getId, OldMaterialPageVO::setImages)
                .peek(record -> {
                    // 设置回收时间（使用创建时间）并格式化
                    if (record.getCreatedAt() != null) {
                        record.setRecycleTime(record.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }

                    // 设置本店货品标识（根据goods_id判断）
                    record.setIsLocalGoods(record.getGoodsId() != null && record.getGoodsId() > 0);

                    // 设置计价方式名称
                    if (record.getSalesType() != null) {
                        record.setSalesTypeName(record.getSalesType() == 1 ? "按重量" : "按数量");
                    }

                    // 价格转换（分转元）
                    // 注意：数据库中价格字段以分为单位存储，显示时需要转换为元
                    if (record.getRecyclePrice() != null) {
                        record.setRecycleAmount(PriceUtil.fen2yuan(record.getRecyclePrice().multiply(BigDecimal.valueOf(record.getNum()))));
                        record.setRecyclePrice(PriceUtil.fen2yuan(record.getRecyclePrice()));
                    }
                    if (record.getCostPrice() != null) {
                        record.setCostPrice(PriceUtil.fen2yuan(record.getCostPrice()));
                    }
                    if (record.getGoldPrice() != null) {
                        record.setGoldPrice(PriceUtil.fen2yuan(record.getGoldPrice()));
                    }
                    if (record.getSilverPrice() != null) {
                        record.setSilverPrice(PriceUtil.fen2yuan(record.getSilverPrice()));
                    }
                    if (record.getCertPrice() != null) {
                        record.setCertPrice(PriceUtil.fen2yuan(record.getCertPrice()));
                    }
                    if (record.getTagPrice() != null) {
                        record.setTagPrice(PriceUtil.fen2yuan(record.getTagPrice()));
                    }
                })
                .handle();
    }

    /**
     * 导出旧料列表
     */
    private void exportOldMaterials(QueryWrapper wrapper, OldMaterialQuery query) {
        // 检查导出数量限制
        long count = this.mapper.selectCountByQuery(wrapper);
        CommonUtils.abortIf(count > CommonUtils.getMaxExportSize(), "导出数量超过限制");

        // 使用ExcelUtil进行导出
        com.xc.boot.common.util.excel.ExcelUtil.of(this.mapper, wrapper, OldMaterialPageVO.class, "old_material", "旧料列表")
                .getData((mapper, queryWrapper) -> {
                    List<OldMaterialPageVO> allRecords = mapper.selectListByQueryAs(queryWrapper, OldMaterialPageVO.class);
                    fillList(allRecords);

                    // 处理导出字段转换
                    for (OldMaterialPageVO vo : allRecords) {
                        // 替换计价方式为文本，使用枚举
                        if (vo.getSalesType() != null) {
                            vo.setSalesTypeName(IBaseEnum.getLabelByValue(vo.getSalesType(), SalesTypeEnum.class));
                        }

                        // 替换是否本地货品为文本
                        if (vo.getIsLocalGoods() != null) {
                            vo.setIsLocalGoodsName(vo.getIsLocalGoods() ? "是" : "否");
                        }
                    }
                    List<JSONObject> result = (List<JSONObject>)ColumnEncryptUtil.process(allRecords);

                    ColumnEncryptUtil.handleJsonImageExport(result);

                    return result;
                })
                .signHeadingConfig(columns -> {
                    // 替换字段为对应的Name字段
                    for (HeadingColumns column : columns) {
                        if (column.getProp().equals("salesType")) {
                            column.setProp("salesTypeName");
                        } else if (column.getProp().equals("isLocalGoods")) {
                            column.setProp("isLocalGoodsName");
                        }
                    }
                    return columns;
                })
                .doExport();
    }

    /**
     * 打印旧料列表，返回填充后的列表数据（不分页）
     */
    private Page<OldMaterialPageVO> printOldMaterials(QueryWrapper wrapper, OldMaterialQuery query) {
        // 检查打印数量限制
        long count = this.mapper.selectCountByQuery(wrapper);
        CommonUtils.abortIf(count > CommonUtils.getMaxPrintSize(), "打印数量超过限制");

        List<OldMaterialPageVO> voList = this.mapper.selectListByQueryAs(wrapper, OldMaterialPageVO.class);
        fillList(voList);

        // 返回Page对象，pageNum=1, pageSize=voList.size(), total=count
        Page<OldMaterialPageVO> page = new Page<>(1, voList.size(), count);
        page.setRecords(voList);
        return page;
    }

    /**
     * 验证关联数据有效性
     */
    private void validateAssociations(OldMaterialUpdateForm form) {
        // 验证门店权限（非主账号需要验证门店权限）
        if (!SecurityUtils.isMain()) {
            // 非主账号只能操作自己权限范围内的门店数据
            OldMaterialEntity entity = this.getById(form.getId());
            if (entity != null && !SecurityUtils.getMerchantIds().contains(entity.getMerchantId().longValue())) {
                CommonUtils.abort("无权操作其他门店的旧料");
            }
        }

        // 验证小类是否存在
        if (form.getSubclassId() != null) {
            SubclassEntity subclass = subclassMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(SubclassEntity::getId).eq(form.getSubclassId())
                    .and(SubclassEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(SubclassEntity::getStatus).eq(1) // 启用状态
            );
            if (subclass == null) {
                CommonUtils.abort("小类不存在或已禁用");
            }
        }

        // 验证款式是否存在
        if (form.getStyleId() != null) {
            StyleEntity style = styleMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(StyleEntity::getId).eq(form.getStyleId())
                    .and(StyleEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(StyleEntity::getStatus).eq(1) // 启用状态
            );
            if (style == null) {
                CommonUtils.abort("款式不存在或已禁用");
            }
        }

        // 验证工艺是否存在
        if (form.getTechnologyId() != null) {
            TechnologyEntity technology = technologyMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(TechnologyEntity::getId).eq(form.getTechnologyId())
                    .and(TechnologyEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(TechnologyEntity::getStatus).eq(1) // 启用状态
            );
            if (technology == null) {
                CommonUtils.abort("工艺不存在或已禁用");
            }
        }

        // 验证品牌是否存在
        if (form.getBrandId() != null) {
            BrandEntity brand = brandMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(BrandEntity::getId).eq(form.getBrandId())
                    .and(BrandEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(BrandEntity::getStatus).eq(1) // 启用状态
            );
            if (brand == null) {
                CommonUtils.abort("品牌不存在或已禁用");
            }
        }

        // 验证主石是否存在
        if (form.getMainStoneId() != null) {
            JewelryEntity mainStone = jewelryMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(JewelryEntity::getId).eq(form.getMainStoneId())
                    .and(JewelryEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(JewelryEntity::getStatus).eq(1) // 启用状态
            );
            if (mainStone == null) {
                CommonUtils.abort("主石不存在或已禁用");
            }
        }

        // 验证辅石是否存在
        if (form.getSubStoneId() != null) {
            JewelryEntity subStone = jewelryMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(JewelryEntity::getId).eq(form.getSubStoneId())
                    .and(JewelryEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(JewelryEntity::getStatus).eq(1) // 启用状态
            );
            if (subStone == null) {
                CommonUtils.abort("辅石不存在或已禁用");
            }
        }
    }

    /**
     * 验证创建时的关联数据有效性
     */
    private void validateCreateAssociations(OldMaterialCreateForm form) {
        // 验证必填字段
        if (form.getCategoryId() == null) {
            CommonUtils.abort("所属大类ID不能为空");
        }
        if (StrUtil.isBlank(form.getName())) {
            CommonUtils.abort("旧料名称不能为空");
        }
        if (form.getSalesType() == null) {
            CommonUtils.abort("回收计价方式不能为空");
        }
        if (form.getNum() == null) {
            CommonUtils.abort("数量不能为空");
        }

        // 验证计价方式的有效性
        if (form.getSalesType() != 1 && form.getSalesType() != 2) {
            CommonUtils.abort("回收计价方式只能是1(按重量)或2(按数量)");
        }

        // 验证小类是否存在
        if (form.getSubclassId() != null) {
            SubclassEntity subclass = subclassMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(SubclassEntity::getId).eq(form.getSubclassId())
                    .and(SubclassEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(SubclassEntity::getStatus).eq(1) // 启用状态
            );
            if (subclass == null) {
                CommonUtils.abort("小类不存在或已禁用");
            }
        }

        // 验证款式是否存在
        if (form.getStyleId() != null) {
            StyleEntity style = styleMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(StyleEntity::getId).eq(form.getStyleId())
                    .and(StyleEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(StyleEntity::getStatus).eq(1) // 启用状态
            );
            if (style == null) {
                CommonUtils.abort("款式不存在或已禁用");
            }
        }

        // 验证工艺是否存在
        if (form.getTechnologyId() != null) {
            TechnologyEntity technology = technologyMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(TechnologyEntity::getId).eq(form.getTechnologyId())
                    .and(TechnologyEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(TechnologyEntity::getStatus).eq(1) // 启用状态
            );
            if (technology == null) {
                CommonUtils.abort("工艺不存在或已禁用");
            }
        }

        // 验证品牌是否存在
        if (form.getBrandId() != null) {
            BrandEntity brand = brandMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(BrandEntity::getId).eq(form.getBrandId())
                    .and(BrandEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(BrandEntity::getStatus).eq(1) // 启用状态
            );
            if (brand == null) {
                CommonUtils.abort("品牌不存在或已禁用");
            }
        }

        // 验证供应商是否存在
        if (form.getSupplierId() != null) {
            SupplierEntity supplier = supplierMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(SupplierEntity::getId).eq(form.getSupplierId())
                    .and(SupplierEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(SupplierEntity::getStatus).eq(1) // 启用状态
            );
            if (supplier == null) {
                CommonUtils.abort("供应商不存在或已禁用");
            }
        }

        // 验证柜台是否存在
        if (form.getCounterId() != null) {
            CounterEntity counter = counterMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(CounterEntity::getId).eq(form.getCounterId())
                    .and(CounterEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(CounterEntity::getStatus).eq(1) // 启用状态
            );
            if (counter == null) {
                CommonUtils.abort("柜台不存在或已禁用");
            }
        }

        // 验证成色是否存在
        if (form.getQualityId() != null) {
            QualityEntity quality = qualityMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(QualityEntity::getId).eq(form.getQualityId())
                    .and(QualityEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(QualityEntity::getStatus).eq(1) // 启用状态
            );
            if (quality == null) {
                CommonUtils.abort("成色不存在或已禁用");
            }
        }

        // 验证主石是否存在
        if (form.getMainStoneId() != null) {
            JewelryEntity mainStone = jewelryMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(JewelryEntity::getId).eq(form.getMainStoneId())
                    .and(JewelryEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(JewelryEntity::getStatus).eq(1) // 启用状态
            );
            if (mainStone == null) {
                CommonUtils.abort("主石不存在或已禁用");
            }
        }

        // 验证辅石是否存在
        if (form.getSubStoneId() != null) {
            JewelryEntity subStone = jewelryMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(JewelryEntity::getId).eq(form.getSubStoneId())
                    .and(JewelryEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(JewelryEntity::getStatus).eq(1) // 启用状态
            );
            if (subStone == null) {
                CommonUtils.abort("辅石不存在或已禁用");
            }
        }
    }






    /**
     * 处理旧料图片（通过FileItemDTO列表）
     */
    private void handleOldMaterialImagesFromFileItems(Long oldMaterialId, List<FileItemDTO> images) {
        if (oldMaterialId == null) {
            return;
        }

        // 删除旧的图片关联
        List<OldMaterialHasImagesEntity> oldImages = oldMaterialHasImagesMapper.selectListByQuery(
                QueryWrapper.create()
                        .where(OldMaterialHasImagesEntity::getOldMaterialId).eq(oldMaterialId.intValue())
                        .and(OldMaterialHasImagesEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));

        if (!oldImages.isEmpty()) {
            // 收集旧图片的ID，用于更新文件状态
            List<Long> oldImageIds = oldImages.stream()
                    .map(img -> img.getImageId().longValue())
                    .collect(Collectors.toList());

            // 删除旧的图片关联记录
            oldMaterialHasImagesMapper.deleteByQuery(
                    QueryWrapper.create()
                            .where(OldMaterialHasImagesEntity::getOldMaterialId).eq(oldMaterialId.intValue())
                            .and(OldMaterialHasImagesEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));

            // 将旧图片状态设为未使用
            CommonUtils.batchUpdateFileStatusSync(oldImageIds, 0);
        }

        // 添加新的图片关联
        if (CollUtil.isNotEmpty(images)) {
            List<OldMaterialHasImagesEntity> imageEntities = new ArrayList<>();
            List<Long> newImageIds = new ArrayList<>();
            int sort = 1;

            for (FileItemDTO image : images) {
                if (StrUtil.isBlank(image.getUrl())) {
                    continue;
                }

                Long imageId = image.getImageId() == null ? image.getId() : image.getImageId();

                OldMaterialHasImagesEntity imageEntity = new OldMaterialHasImagesEntity();
                imageEntity.setOldMaterialId(oldMaterialId.intValue());
                imageEntity.setCompanyId(SecurityUtils.getCompanyId().intValue());
                imageEntity.setImageId(imageId.intValue());
                imageEntity.setUrl(image.getUrl());
                imageEntity.setSort(sort++);
                imageEntities.add(imageEntity);
                newImageIds.add(imageId);
            }

            if (!imageEntities.isEmpty()) {
                // 批量插入新的图片关联
                oldMaterialHasImagesMapper.insertBatchSelective(imageEntities);

                // 将新图片状态设为已使用
                CommonUtils.batchUpdateFileStatusSync(newImageIds, 1);
            }
        }
    }

    /**
     * 计算重量
     * 重量 = 金重 + 银重 + 主石重 * 主石数量 * 0.2 + 辅石重 * 辅石数 * 0.2
     */
    private BigDecimal calculateWeight(BigDecimal netGoldWeight, BigDecimal netSilverWeight,
                                     BigDecimal mainStoneWeight, Integer mainStoneCount,
                                     BigDecimal subStoneWeight, Integer subStoneCount) {
        BigDecimal weight = BigDecimal.ZERO;

        // 金重
        if (netGoldWeight != null) {
            weight = weight.add(netGoldWeight);
        }

        // 银重
        if (netSilverWeight != null) {
            weight = weight.add(netSilverWeight);
        }

        // 主石重 * 主石数量 * 0.2
        if (mainStoneWeight != null && mainStoneCount != null && mainStoneCount > 0) {
            weight = weight.add(mainStoneWeight.multiply(new BigDecimal(mainStoneCount)).multiply(new BigDecimal("0.2")));
        }

        // 辅石重 * 辅石数 * 0.2
        if (subStoneWeight != null && subStoneCount != null && subStoneCount > 0) {
            weight = weight.add(subStoneWeight.multiply(new BigDecimal(subStoneCount)).multiply(new BigDecimal("0.2")));
        }

        return weight;
    }

    @Override
    @Transactional
    public void convertOldMaterial(OldMaterialConvertForm convertForm) {
        Long companyId = SecurityUtils.getCompanyId();
        OldMaterialEntity oldMaterial = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(OldMaterialEntity::getId).eq(convertForm.getOldMaterialId())
                .where(OldMaterialEntity::getCompanyId).eq(companyId)
                .where(OldMaterialEntity::getMerchantId).in(SecurityUtils.getMerchantIds()));
        Assert.notNull(oldMaterial, "未查询到该旧料");
        Assert.isTrue(oldMaterial.getNum() > 0, "该旧料库存为零");
        Assert.isTrue(StringUtils.isNotBlank(oldMaterial.getGoodsSn()), "只能转换退货旧料");

        List<GoodsEntity> goodsList = goodsMapper.selectListByQuery(QueryWrapper.create()
                .where(GoodsEntity::getGoodsSn).eq(oldMaterial.getGoodsSn())
                .where(GoodsEntity::getCompanyId).eq(companyId));
        Assert.isTrue(CollUtil.isNotEmpty(goodsList), "货品记录不存在");
        // 是否存在同门店同柜台货品
        GoodsEntity goodsEntity = goodsList.stream().filter(goods ->
                goods.getMerchantId().equals(oldMaterial.getMerchantId().longValue()) &&
                goods.getCounterId().equals(convertForm.getCounterId()))
                .findFirst().orElse(null);

        // 门店不同或者柜台不同则创建新货品
        if (Objects.isNull(goodsEntity)) {
            goodsEntity = goodsList.getFirst();
            List<GoodsHasImagesEntity> images = goodsHasImagesMapper.selectListByQuery(QueryWrapper.create()
                    .where(GoodsHasImagesEntity::getGoodsId).eq(goodsEntity.getId()));
            List<GoodsHasColumnsEntity> columns = goodsHasColumnsMapper.selectListByQuery(QueryWrapper.create()
                    .where(GoodsHasColumnsEntity::getGoodsId).eq(goodsEntity.getId()));

            goodsEntity.setId(null);
            goodsEntity.setMerchantId(oldMaterial.getMerchantId().longValue())
            .setNum(0).setStockNum(0).setReturnNum(0).setSoldNum(0)
            .setTransferNum(0).setFrozenNum(0).setTakeStatus(0)
            .setCounterId(convertForm.getCounterId());
            goodsEntity.setCreatedAt(null);
            goodsEntity.setUpdatedAt(null);
            goodsMapper.insertSelective(goodsEntity);
            if (CollectionUtil.isNotEmpty(images)) {
                for (GoodsHasImagesEntity image : images) {
                    image.setGoodsId(goodsEntity.getId())
                            .setId(null);
                }
                goodsHasImagesMapper.insertBatchSelective(images);
            }
            if (CollectionUtil.isNotEmpty(columns)) {
                for (GoodsHasColumnsEntity column : columns) {
                    column.setGoodsId(goodsEntity.getId())
                            .setId(null);
                }
                goodsHasColumnsMapper.insertBatchSelective(columns);
            }
        }else {
            // 检验是否盘点中
            Assert.isTrue(goodsEntity.getTakeStatus().equals(0), "该柜台货品正在盘点中");
        }
        // 改变货品数量
        StockNumChangeBO changeBO = StockNumChangeBO.builder()
                .goodsId(goodsEntity.getId())
                .comment("旧料转换货品")
                .num(oldMaterial.getNum())
                .stockNum(oldMaterial.getNum())
                .build();
        StockUtils.updateStocks(List.of(changeBO));
        // 改变旧料数量
        OldMaterialStockNumChangeBO oldMaterialChangeBO = OldMaterialStockNumChangeBO.builder()
                .oldMaterialId(oldMaterial.getId())
                .comment("旧料转换货品")
                .num(-oldMaterial.getNum())
                .build();
        StockUtils.updateOldMaterialStocks(List.of(oldMaterialChangeBO));
        // 记录日志
        String opUserName = Optional.ofNullable(listFillService.getUserNameByUserId(Set.of(SecurityUtils.getUserId())).get(SecurityUtils.getUserId().toString())).orElse("");
        String merchantName = Optional.ofNullable(listFillService.getMerchantNameById(Set.of(goodsEntity.getMerchantId())).get(goodsEntity.getMerchantId().toString())).orElse("");
        String counterName = Optional.ofNullable(listFillService.getCounterNameById(Set.of(goodsEntity.getCounterId())).get(goodsEntity.getCounterId().toString())).orElse("");
        String logContent = String.format("""
                旧料转货品
                入库门店：%s
                入库柜台：%s
                操作人：%s
                货品条码：%s
                货品数量：%s""", merchantName, counterName, opUserName, goodsEntity.getGoodsSn(), oldMaterial.getNum());
        OpLogUtils.appendOpLog("旧料列表-旧料转货品", logContent, null);
        OpLogUtils.appendGoodsLog("旧料列表-旧料转货品", logContent, null, goodsEntity);
    }
}
