package com.xc.boot.modules.oldmaterial.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 旧料销售单明细实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "old_material_order_detail")
public class OldMaterialOrderDetailEntity extends BaseEntity {

    /**
     * 销售单ID
     */
    @Column(value = "order_id")
    private Integer orderId;

    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 所属门店ID
     */
    @Column(value = "merchant_id")
    private Integer merchantId;

    /**
     * 货品ID
     */
    @Column(value = "goods_id")
    private Integer goodsId;

    /**
     * 货品条码
     */
    @Column(value = "goods_sn")
    private String goodsSn;

    /**
     * 旧料ID
     */
    @Column(value = "old_material_id")
    private Integer oldMaterialId;

    /**
     * 旧料编号
     */
    @Column(value = "old_material_sn")
    private String oldMaterialSn;

    /**
     * 数量
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 计价方式(1:按重量,2:按数量)
     */
    @Column(value = "sales_type")
    private Integer salesType;

    /**
     * 金单价(分)
     */
    @Column(value = "gold_price")
    private Long goldPrice;

    /**
     * 银单价(分)
     */
    @Column(value = "silver_price")
    private Long silverPrice;

    /**
     * 销售单价(分)
     */
    @Column(value = "price")
    private Long price;

    /**
     * 状态(0:待审核,1:已审核)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 审核人ID
     */
    @Column(value = "audit_by")
    private Integer auditBy;

    /**
     * 审核时间
     */
    @Column(value = "audit_at")
    private LocalDateTime auditAt;

    /**
     * 数据快照
     */
    @Column(value = "data_snapshot")
    private String dataSnapshot;
}
