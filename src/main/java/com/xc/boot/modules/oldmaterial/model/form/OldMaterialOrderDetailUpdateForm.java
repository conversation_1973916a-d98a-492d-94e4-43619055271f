package com.xc.boot.modules.oldmaterial.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 旧料销售单明细编辑表单
 */
@Getter
@Setter
@Schema(description = "旧料销售单明细编辑表单")
public class OldMaterialOrderDetailUpdateForm {

    @NotNull(message = "明细ID不能为空")
    @Schema(description = "明细ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @NotNull(message = "数量不能为空")
    @Positive(message = "数量必须大于0")
    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer num;

    @NotNull(message = "计价方式不能为空")
    @Schema(description = "计价方式(1:按重量,2:按数量)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer salesType;

    @Schema(description = "金单价(元)")
    private BigDecimal goldPrice;

    @Schema(description = "银单价(元)")
    private BigDecimal silverPrice;

    @NotNull(message = "销售单价不能为空")
    @Schema(description = "销售单价(元)", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal price;
}
