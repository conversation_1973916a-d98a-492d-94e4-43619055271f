# 旧料销售单模块

## 概述
本模块为旧料销售单管理提供完整的后端支持，包括旧料查询、销售单创建、审核等功能。

## 已创建的文件结构

### 1. Entity 实体类
- `OldMaterialOrderEntity.java` - 旧料销售单实体
- `OldMaterialOrderDetailEntity.java` - 旧料销售单明细实体

### 2. Mapper 接口
- `OldMaterialOrderMapper.java` - 旧料销售单数据访问接口
- `OldMaterialOrderDetailMapper.java` - 旧料销售单明细数据访问接口

### 3. Service 服务层
- `OldMaterialOrderService.java` - 旧料销售单服务接口
- `OldMaterialOrderServiceImpl.java` - 旧料销售单服务实现

### 4. Controller 控制器
- `OldMaterialOrderController.java` - 旧料销售单控制器

### 5. DTO 数据传输对象
- `OldMaterialQueryDTO.java` - 旧料查询请求DTO

### 6. VO 视图对象
- `OldMaterialQueryVO.java` - 旧料查询结果VO
- `OldMaterialOrderVO.java` - 旧料销售单VO
- `OldMaterialOrderDetailVO.java` - 旧料销售单明细VO

### 7. Query 查询条件
- `OldMaterialOrderQuery.java` - 旧料销售单查询条件

### 8. Form 表单对象
- `OldMaterialOrderCreateForm.java` - 旧料销售单创建表单

## 核心功能

### 1. 旧料查询接口 ✅ 已实现
**接口路径**: `GET /old_material/orders/query`

**功能描述**: 为旧料销售单提供旧料商品查询功能

**请求参数**:
- `merchantId` (Integer, 必填): 所属门店ID
- `supplierId` (Integer, 可选): 供应商ID，用于筛选
- `searchType` (Integer, 必填): 查询类型 (1=按条码精确查询, 2=按名称模糊查询)
- `keyword` (String, 必填): 查询关键字

**响应字段**:
- 基础信息: 旧料编号、名称、图片、重量、净金重、净银重、回收价格等
- 前端交互字段: 数量、计价方式、销售价格等（后端返回空值，前端填写）
- 关联信息: 大类名称、小类名称、供应商名称、成色名称等

**技术特点**:
- 使用MyBatis-Flex的TableDef进行类型安全的查询
- 支持按条码精确查询和按名称模糊查询
- 自动过滤库存为0的旧料
- 使用ListFillUtil进行关联数据填充
- 支持供应商筛选

### 2. 旧料销售单创建接口 ✅ 已实现
**接口路径**: `POST /old_material/orders`

**功能描述**: 创建新的旧料销售单

**请求参数**:
- `merchantId` (Integer, 必填): 所属门店ID
- `supplierId` (Integer, 必填): 供应商ID
- `remark` (String, 可选): 备注
- `details` (Array, 必填): 销售明细列表
  - `oldMaterialId` (Integer, 必填): 旧料ID
  - `num` (Integer, 必填): 数量
  - `salesType` (Integer, 必填): 计价方式(1:按重量,2:按数量)
  - `goldPrice` (Long, 可选): 金单价(分)
  - `silverPrice` (Long, 可选): 银单价(分)
  - `price` (Long, 必填): 销售单价(分)

**业务逻辑**:
- 验证用户权限（门店权限、商户权限）
- 验证旧料是否存在且库存充足
- 验证供应商是否有效
- 自动生成销售单号（OXS + YYYYMMDD + 5位顺序数字）
- 计算销售单汇总数据（总数量、总重量、总金重、总银重、总金额）
- 创建销售单主记录和明细记录
- 更新旧料库存（扣减数量增加冻结数量）
- 记录操作日志

**技术特点**:
- 使用@Transactional确保数据一致性
- 使用SnUtils生成唯一销售单号
- 使用StockUtils更新旧料库存
- 使用OpLogUtils记录操作日志
- 完整的参数校验和业务验证

### 3. 其他功能 🚧 待实现
- 分页查询旧料销售单列表
- 审核旧料销售单
- 获取旧料销售单详情

## 数据库表结构

### old_material_order (旧料销售单表)
- `id` - 主键ID
- `order_sn` - 销售单号
- `company_id` - 所属商户ID
- `merchant_id` - 所属门店ID
- `supplier_id` - 供应商ID
- `num` - 出库数量
- `total_weight` - 总重量(g)
- `total_net_gold_weight` - 总金重(g)
- `total_net_silver_weight` - 总银重(g)
- `total_amount` - 销售金额(分)
- `remark` - 备注
- `status` - 状态(0:待审核,1:已审核)
- `audit_by` - 审核人ID
- `audit_at` - 审核时间
- `created_by` - 创建人ID
- `created_at` - 创建时间
- `updated_at` - 更新时间

### old_material_order_detail (旧料销售单明细表)
- `id` - 主键ID
- `company_id` - 所属商户ID
- `merchant_id` - 所属门店ID
- `goods_id` - 货品ID
- `goods_sn` - 货品条码
- `old_material_id` - 旧料ID
- `old_material_sn` - 旧料编号
- `num` - 数量
- `sales_type` - 计价方式(1:按重量,2:按数量)
- `gold_price` - 金单价(分)
- `silver_price` - 银单价(分)
- `price` - 销售单价(分)
- `status` - 状态(0:待审核,1:已审核)
- `audit_by` - 审核人ID
- `audit_at` - 审核时间
- `data_snapshot` - 数据快照
- `created_at` - 创建时间
- `updated_at` - 更新时间

## 技术规范
- 遵循项目现有的代码风格和架构模式
- 使用MyBatis-Flex进行数据库操作
- 使用TableDef进行类型安全的查询构建
- 使用ListFillUtil进行关联数据填充
- 金额字段统一使用Long类型（以分为单位）
- 重量字段使用BigDecimal类型
- 时间字段使用LocalDateTime类型
- 使用@Transactional确保事务一致性
- 使用Builder模式创建BO对象
- 完整的参数校验和业务验证

## 新增的工具方法
### SnUtils 新增方法
- `generateOldMaterialOrderCode()`: 生成旧料销售单号（OXS + YYYYMMDD + 5位顺序数字）
- `syncOldMaterialOrderMaxSequence()`: 同步旧料销售单号最大序号

## 下一步开发计划
1. 实现分页查询旧料销售单列表功能
2. 实现审核旧料销售单功能
3. 实现销售单详情查询功能
4. 添加单元测试
5. 完善异常处理和参数校验
