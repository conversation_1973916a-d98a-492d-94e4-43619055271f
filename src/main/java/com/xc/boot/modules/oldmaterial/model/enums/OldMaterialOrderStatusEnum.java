package com.xc.boot.modules.oldmaterial.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 旧料销售单状态枚举
 */
@Getter
@AllArgsConstructor
public enum OldMaterialOrderStatusEnum implements IBaseEnum<Integer> {

    /**
     * 待审核
     */
    PENDING(0, "待审核"),

    /**
     * 已审核
     */
    APPROVED(1, "已审核"),

    /**
     * 已驳回
     */
    REJECTED(2, "已驳回");

    private final Integer value;
    private final String label;
}
