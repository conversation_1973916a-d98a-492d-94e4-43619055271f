package com.xc.boot.modules.oldmaterial.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 旧料销售单查询条件
 */
@Getter
@Setter
@Schema(description = "旧料销售单查询条件")
public class OldMaterialOrderQuery extends BasePageQuery {

    @Schema(description = "销售单号")
    private String orderSn;

    @Schema(description = "所属门店ID(支持多个ID，逗号分隔)")
    private String merchantId;

    @Schema(description = "门店ID列表(逗号分隔)")
    private String merchantIds;

    @Schema(description = "供应商ID(支持多个ID，逗号分隔)")
    private String supplierId;

    @Schema(description = "状态(0:待审核,1:已审核,2:已驳回，支持多个状态，逗号分隔)")
    private String status;

    @Schema(description = "创建时间范围[开始时间,结束时间]")
    private List<String> createdAtRange;

    @Schema(description = "审核时间范围[开始时间,结束时间]")
    private List<String> auditAtRange;

    @Schema(description = "创建人ID(支持多个ID，逗号分隔)")
    private String createdBy;

    @Schema(description = "审核人ID(支持多个ID，逗号分隔)")
    private String auditBy;
}