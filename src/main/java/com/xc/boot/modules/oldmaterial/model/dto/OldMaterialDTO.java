package com.xc.boot.modules.oldmaterial.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 旧料DTO
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料DTO")
public class OldMaterialDTO {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "所属商户ID")
    private Integer companyId;

    @Schema(description = "所属门店ID")
    private Integer merchantId;

    @Schema(description = "所属柜台ID")
    private Integer counterId;

    @Schema(description = "供应商ID")
    private Integer supplierId;

    @Schema(description = "所属大类ID")
    private Integer categoryId;

    @Schema(description = "所属小类ID")
    private Integer subclassId;

    @Schema(description = "品牌ID")
    private Integer brandId;

    @Schema(description = "款式ID")
    private Integer styleId;

    @Schema(description = "成色ID")
    private Integer qualityId;

    @Schema(description = "工艺ID")
    private Integer technologyId;

    @Schema(description = "主石ID")
    private Integer mainStoneId;

    @Schema(description = "辅石ID")
    private Integer subStoneId;

    @Schema(description = "货品ID")
    private Integer goodsId;

    @Schema(description = "货品条码")
    private String goodsSn;

    @Schema(description = "旧料编号")
    private String oldMaterialSn;

    @Schema(description = "旧料名称")
    private String name;

    @Schema(description = "计价方式(1:按重量,2:按数量)")
    private Integer salesType;

    @Schema(description = "批次号")
    private String batchNo;

    @Schema(description = "证书号")
    private String certNo;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "重量(g)")
    private BigDecimal weight;

    @Schema(description = "净金重(g)")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")
    private BigDecimal netSilverWeight;

    @Schema(description = "主石数")
    private Integer mainStoneCount;

    @Schema(description = "主石重(ct)")
    private BigDecimal mainStoneWeight;

    @Schema(description = "辅石数")
    private Integer subStoneCount;

    @Schema(description = "辅石重(ct)")
    private BigDecimal subStoneWeight;

    @Schema(description = "圈口")
    private String circleSize;

    @Schema(description = "成本单价(分)")
    private Long costPrice;

    @Schema(description = "回收金进(分)")
    private Long goldPrice;

    @Schema(description = "回收银进(分)")
    private Long silverPrice;

    @Schema(description = "回收单价(分)")
    private Long recyclePrice;

    @Schema(description = "进工费单价(分)")
    private Long workPrice;

    @Schema(description = "证书费(分)")
    private Long certPrice;

    @Schema(description = "工费单价(分)")
    private Long saleWorkPrice;

    @Schema(description = "标签单价(分)")
    private Long tagPrice;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "冻结数量")
    private Integer frozenNum;
}
