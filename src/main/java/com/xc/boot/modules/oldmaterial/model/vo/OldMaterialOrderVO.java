package com.xc.boot.modules.oldmaterial.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 旧料销售单VO
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料销售单VO")
public class OldMaterialOrderVO {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "销售单号")
    private String orderSn;

    @Schema(description = "所属商户ID")
    private Integer companyId;

    @Schema(description = "所属门店ID")
    private Integer merchantId;

    @Schema(description = "门店名称")
    private String merchantName;

    @Schema(description = "供应商ID")
    private Integer supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "出库数量")
    private Integer num;

    @Schema(description = "总重量(g)")
    private BigDecimal totalWeight;

    @Schema(description = "总金重(g)")
    private BigDecimal totalNetGoldWeight;

    @Schema(description = "总银重(g)")
    private BigDecimal totalNetSilverWeight;

    @Schema(description = "销售金额(元)")
    private BigDecimal totalAmount;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "状态(0:待审核,1:已审核,2:已驳回)")
    private Object status;

    @Schema(description = "状态名称")
    private String statusName;

    @Schema(description = "审核人ID")
    private Integer auditBy;

    @Schema(description = "审核人姓名")
    private String auditByName;

    @Schema(description = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditAt;

    @Schema(description = "审核备注")
    private String auditRemark;

    @Schema(description = "创建人ID")
    private Integer createdBy;

    @Schema(description = "创建人姓名")
    private String createdByName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;
}
