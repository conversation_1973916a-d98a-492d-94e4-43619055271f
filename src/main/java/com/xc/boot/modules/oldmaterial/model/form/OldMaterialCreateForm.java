package com.xc.boot.modules.oldmaterial.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import com.xc.boot.common.base.FileItemDTO;

/**
 * 旧料创建表单
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料创建表单")
public class OldMaterialCreateForm {

    @Schema(description = "所属大类ID")
    @NotNull(message = "所属大类ID不能为空")
    private Integer categoryId;

    @Schema(description = "货品小类ID")
    private Integer subclassId;

    @Schema(description = "成色ID")
    private Integer qualityId;

    @Schema(description = "旧料名称")
    @NotBlank(message = "旧料名称不能为空")
    private String name;

    @Schema(description = "回收计价方式(1:按重量,2:按数量)")
    @NotNull(message = "回收计价方式不能为空")
    private Integer salesType;

    @Schema(description = "数量")
    @NotNull(message = "数量不能为空")
    private Integer num;

    @Schema(description = "回收单价")
    private BigDecimal recyclePrice;

    @Schema(description = "净金重")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重")
    private BigDecimal netSilverWeight;

    @Schema(description = "回收金额")
    private BigDecimal recycleAmount;

    @Schema(description = "供应商ID")
    private Integer supplierId;

    @Schema(description = "柜台ID")
    private Integer counterId;

    @Schema(description = "品牌ID")
    private Integer brandId;

    @Schema(description = "款式ID")
    private Integer styleId;

    @Schema(description = "工艺ID")
    private Integer technologyId;

    @Schema(description = "主石ID")
    private Integer mainStoneId;

    @Schema(description = "主石数")
    private Integer mainStoneCount;

    @Schema(description = "主石重")
    private BigDecimal mainStoneWeight;

    @Schema(description = "辅石ID")
    private Integer subStoneId;

    @Schema(description = "辅石数")
    private Integer subStoneCount;

    @Schema(description = "辅石重")
    private BigDecimal subStoneWeight;

    @Schema(description = "圈口")
    private String circleSize;

    @Schema(description = "重量(g)")
    private BigDecimal weight;

    @Schema(description = "批次号")
    private String batchNo;

    @Schema(description = "证书号")
    private String certNo;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "回收金进")
    private BigDecimal goldPrice;

    @Schema(description = "回收银进")
    private BigDecimal silverPrice;

    @Schema(description = "进工费单价")
    private BigDecimal workPrice;

    @Schema(description = "证书费")
    private BigDecimal certPrice;

    @Schema(description = "工费单价")
    private BigDecimal saleWorkPrice;

    @Schema(description = "标签单价")
    private BigDecimal tagPrice;

    @Schema(description = "成本单价")
    private BigDecimal costPrice;

    @Schema(description = "旧料图片列表")
    private List<FileItemDTO> images;
}
