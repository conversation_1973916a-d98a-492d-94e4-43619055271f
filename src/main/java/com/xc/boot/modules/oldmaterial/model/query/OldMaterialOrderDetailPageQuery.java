package com.xc.boot.modules.oldmaterial.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;

/**
 * 旧料销售单明细分页查询条件
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料销售单明细分页查询条件")
public class OldMaterialOrderDetailPageQuery extends BasePageQuery {

    @Schema(description = "销售单ID", required = true)
    @NotNull(message = "销售单ID不能为空")
    private Long orderId;

    @Schema(description = "旧料编号")
    private String oldMaterialSn;

    @Schema(description = "旧料名称")
    private String oldMaterialName;

    @Schema(description = "所属大类ID(支持多个ID，逗号分隔)")
    private String categoryId;

    @Schema(description = "货品小类ID(支持多个ID，逗号分隔)")
    private String subclassId;

    @Schema(description = "供应商ID(支持多个ID，逗号分隔)")
    private String supplierId;

    @Schema(description = "成色ID(支持多个ID，逗号分隔)")
    private String qualityId;

    @Schema(description = "计价方式(1:按重量,2:按数量，支持多个值，逗号分隔)")
    private String salesType;
}
