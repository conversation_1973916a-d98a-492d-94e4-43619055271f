package com.xc.boot.modules.oldmaterial.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.oldmaterial.model.bo.OldMaterialStockNumChangeBO;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialEntity;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialConvertForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialCreateForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialUpdateForm;
import com.xc.boot.modules.oldmaterial.model.query.OldMaterialQuery;
import com.xc.boot.modules.oldmaterial.model.vo.OldMaterialPageVO;

import java.util.List;

/**
 * 旧料服务接口
 */
public interface OldMaterialService extends IService<OldMaterialEntity> {

    /**
     * 分页查询旧料列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    Page<OldMaterialPageVO> getOldMaterialPage(OldMaterialQuery query);

    /**
     * 编辑旧料
     *
     * @param form 编辑表单
     */
    void updateOldMaterial(OldMaterialUpdateForm form);

    /**
     * 创建旧料
     *
     * @param form 创建表单
     * @return 创建的旧料ID
     */
    Long createOldMaterial(OldMaterialCreateForm form);

    /**
     * 更新旧料库存数量
     *
     * @param oldMaterialId 旧料ID
     * @param num 数量变化量
     * @param comment 变更描述
     */
    void updateOldMaterialStock(Long oldMaterialId, Integer num, String comment);

    /**
     * 批量更新旧料库存数量
     *
     * @param changes 库存变更列表
     */
    void updateOldMaterialStocks(List<OldMaterialStockNumChangeBO> changes);

    /**
     * 旧料转货品
     */
    void convertOldMaterial(OldMaterialConvertForm form);
}
