package com.xc.boot.modules.oldmaterial.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 旧料销售单审核表单
 */
@Getter
@Setter
@Schema(description = "旧料销售单审核表单")
public class OldMaterialOrderAuditForm {

    @NotNull(message = "销售单ID不能为空")
    @Schema(description = "销售单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @NotNull(message = "审核结果不能为空")
    @Schema(description = "审核结果(1:通过,2:驳回)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer auditResult;

    @Schema(description = "审核备注(最多200字)")
    @Size(max = 200, message = "审核备注不能超过200字")
    private String auditRemark;
}
