package com.xc.boot.modules.oldmaterial.controller;

import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialConvertForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialUpdateForm;
import com.xc.boot.modules.oldmaterial.model.query.OldMaterialQuery;
import com.xc.boot.modules.oldmaterial.model.vo.OldMaterialPageVO;
import com.xc.boot.modules.oldmaterial.service.OldMaterialService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 旧料控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/old_material")
@Tag(name = "旧料管理", description = "旧料相关接口")
public class OldMaterialController {

    private final OldMaterialService oldMaterialService;

    /**
     * 分页查询旧料列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询旧料列表", description = "支持多种筛选条件的分页查询")
    public PageResult<?> getOldMaterialPage(@RequestBody OldMaterialQuery query) {
        Page<OldMaterialPageVO> page = oldMaterialService.getOldMaterialPage(query);

        // 进行加密处理
        @SuppressWarnings("unchecked")
        Page<JSONObject> processedPage = (Page<JSONObject>)ColumnEncryptUtil.process(page);

        // 处理xxId字段：值为0时返回null
        if (processedPage.getRecords() != null) {
            processedPage.getRecords().forEach(this::processIdFieldsForJson);
        }

        return PageResult.success(processedPage);
    }

    /**
     * 处理JSONObject中的ID字段：值为0时设置为null
     */
    private void processIdFieldsForJson(JSONObject json) {
        processIdField(json, "id");
        processIdField(json, "merchantId");
        processIdField(json, "categoryId");
        processIdField(json, "subclassId");
        processIdField(json, "qualityId");
        processIdField(json, "supplierId");
        processIdField(json, "styleId");
        processIdField(json, "brandId");
        processIdField(json, "technologyId");
        processIdField(json, "mainStoneId");
        processIdField(json, "subStoneId");
        processIdField(json, "goodsId");
    }

    /**
     * 处理单个ID字段：值为0时设置为null
     */
    private void processIdField(JSONObject json, String fieldName) {
        Object value = json.get(fieldName);
        if (value != null && value.equals(0)) {
            json.set(fieldName, "");
        }
    }

    /**
     * 编辑旧料
     */
    @PutMapping("/update")
    @Operation(summary = "编辑旧料", description = "只能编辑当前用户所属门店范围内并且goods_id为零的旧料")
    public Result<Void> updateOldMaterial(@Valid @RequestBody OldMaterialUpdateForm form) {
        oldMaterialService.updateOldMaterial(form);
        return Result.success();
    }

    /**
     * 旧料转货品
     */
    @PostMapping("/convert")
    @Operation(summary = "旧料转货品", description = "将旧料转成货品")
    public Result<Void> convertOldMaterial(@RequestBody @Validated OldMaterialConvertForm form) {
        oldMaterialService.convertOldMaterial(form);
        return Result.success();
    }
}
