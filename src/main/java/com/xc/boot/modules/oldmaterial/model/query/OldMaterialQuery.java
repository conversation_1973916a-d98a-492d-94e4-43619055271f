package com.xc.boot.modules.oldmaterial.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 旧料查询条件
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "旧料查询条件")
public class OldMaterialQuery extends BasePageQuery {

    @Schema(description = "所属商户ID")
    private Integer companyId;

    @Schema(description = "所属门店ID")
    private Integer merchantId;

    @Schema(description = "所属柜台ID")
    private Integer counterId;

    @Schema(description = "供应商ID")
    private Integer supplierId;

    @Schema(description = "所属大类ID")
    private Integer categoryId;

    @Schema(description = "所属小类ID")
    private Integer subclassId;

    @Schema(description = "品牌ID")
    private Integer brandId;

    @Schema(description = "款式ID")
    private Integer styleId;

    @Schema(description = "成色ID")
    private Integer qualityId;

    @Schema(description = "工艺ID")
    private Integer technologyId;

    @Schema(description = "主石ID")
    private Integer mainStoneId;

    @Schema(description = "辅石ID")
    private Integer subStoneId;

    @Schema(description = "货品ID")
    private Integer goodsId;

    @Schema(description = "货品条码")
    private String goodsSn;

    @Schema(description = "旧料编号")
    private String oldMaterialSn;

    @Schema(description = "旧料名称")
    private String name;

    @Schema(description = "计价方式(1:按重量,2:按数量)")
    private Integer salesType;

    @Schema(description = "批次号")
    private String batchNo;

    @Schema(description = "证书号")
    private String certNo;

    @Schema(description = "所属门店ID列表（多选，逗号分隔字符串，如\"1,2,3\"）")
    private String merchantIds;

    @Schema(description = "所属大类ID列表（多选，逗号分隔字符串）")
    private String categoryIds;

    @Schema(description = "货品小类ID列表（多选，逗号分隔字符串）")
    private String subclassIds;

    @Schema(description = "本店货品标识（Boolean，true/false）")
    private Boolean isLocalGoods;

    @Schema(description = "成色ID列表（多选，逗号分隔字符串）")
    private String qualityIds;

    @Schema(description = "供应商ID列表（多选，逗号分隔字符串）")
    private String supplierIds;

    @Schema(description = "回收时间范围（格式：[\"2024-01-01\", \"2024-01-31\"]）")
    private List<String> recycleTimeRange;
}
