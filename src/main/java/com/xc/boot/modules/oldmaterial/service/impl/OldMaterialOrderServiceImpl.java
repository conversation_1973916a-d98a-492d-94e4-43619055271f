package com.xc.boot.modules.oldmaterial.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;

import static com.mybatisflex.core.query.QueryMethods.column;
import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.QueryUtils;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.common.util.StockUtils;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.merchant.mapper.GoldPriceMapper;
import com.xc.boot.modules.merchant.model.vo.QualityGoldPriceVo;
import com.xc.boot.common.enums.CategoryEnum;

import static com.xc.boot.modules.merchant.model.entity.table.GoldPriceTableDef.GOLD_PRICE;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialOrderMapper;
import com.xc.boot.modules.oldmaterial.model.dto.OldMaterialQueryDTO;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialEntity;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialHasImagesEntity;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialOrderEntity;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialOrderDetailEntity;
import com.xc.boot.modules.oldmaterial.model.enums.OldMaterialOrderStatusEnum;
import com.xc.boot.modules.order.model.enums.SalesTypeEnum;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialOrderCreateForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialOrderUpdateForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialOrderDetailUpdateForm;
import com.xc.boot.modules.oldmaterial.model.form.OldMaterialOrderAuditForm;
import com.xc.boot.modules.oldmaterial.model.query.OldMaterialOrderQuery;
import com.xc.boot.modules.oldmaterial.model.query.OldMaterialOrderDetailPageQuery;
import com.xc.boot.modules.oldmaterial.model.vo.OldMaterialOrderVO;
import com.xc.boot.modules.oldmaterial.model.vo.OldMaterialOrderDetailPageVO;
import com.xc.boot.modules.oldmaterial.model.vo.OldMaterialQueryVO;
import com.xc.boot.modules.oldmaterial.model.bo.OldMaterialStockNumChangeBO;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialOrderDetailMapper;
import com.xc.boot.modules.merchant.mapper.SupplierMapper;
import com.xc.boot.modules.merchant.model.entity.SupplierEntity;
import com.xc.boot.modules.oldmaterial.service.OldMaterialOrderService;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialMapper;
import com.xc.boot.system.model.vo.CompanySettingsVO;
import com.xc.boot.system.service.CompanySettingsService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.util.Map;

import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.date.DateUtil;
import java.util.*;


/**
 * 旧料销售单服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OldMaterialOrderServiceImpl extends ServiceImpl<OldMaterialOrderMapper, OldMaterialOrderEntity> implements OldMaterialOrderService {

    private final ListFillService listFillService;
    private final OldMaterialMapper oldMaterialMapper;
    private final OldMaterialOrderDetailMapper oldMaterialOrderDetailMapper;
    private final SupplierMapper supplierMapper;
    private final GoldPriceMapper goldPriceMapper;
    private final CompanySettingsService companySettingsService;

    @Override
    public List<OldMaterialQueryVO> queryOldMaterials(OldMaterialQueryDTO dto) {
        // 1. 参数验证
        CommonUtils.abortIf(dto.getSearchType() == null || (dto.getSearchType() != 1 && dto.getSearchType() != 2), 
            "查询类型必须为1(按条码查询)或2(按名称查询)");
        CommonUtils.abortIf(StrUtil.isBlank(dto.getKeyword()), "查询关键字不能为空");

        // 2. 构建查询条件
        QueryWrapper wrapper = QueryWrapper.create()
                .where(column(OldMaterialEntity::getCompanyId).eq(SecurityUtils.getCompanyId()))
                .and(column(OldMaterialEntity::getMerchantId).eq(dto.getMerchantId()))
                .and(column(OldMaterialEntity::getNum).gt(0)); // 过滤掉库存为0的旧料

        // 供应商筛选
        if (dto.getSupplierId() != null) {
            wrapper.and(column(OldMaterialEntity::getSupplierId).eq(dto.getSupplierId()));
        }

        // 根据查询类型添加条件
        if (dto.getSearchType() == 1) {
            // 按条码精确查询
            wrapper.and(column(OldMaterialEntity::getOldMaterialSn).eq(dto.getKeyword()));
        } else {
            // 按名称模糊查询
            wrapper.and(column(OldMaterialEntity::getName).like(dto.getKeyword()));
        }

        // 3. 执行查询
        List<OldMaterialQueryVO> results = oldMaterialMapper.selectListByQueryAs(wrapper, OldMaterialQueryVO.class);
        
        if (results.isEmpty()) {
            return Collections.emptyList();
        }

        // 4. 填充关联数据
        fillAssociatedData(results);

        return results;
    }

    /**
     * 填充关联数据
     */
    private void fillAssociatedData(List<OldMaterialQueryVO> results) {
        // 收集需要填充的ID集合
        Set<Integer> categoryIds = results.stream().map(OldMaterialQueryVO::getCategoryId).collect(Collectors.toSet());
        Set<Integer> subclassIds = results.stream().map(OldMaterialQueryVO::getSubclassId).collect(Collectors.toSet());
        Set<Integer> supplierIds = results.stream().map(OldMaterialQueryVO::getSupplierId).collect(Collectors.toSet());
        Set<Integer> qualityIds = results.stream().map(OldMaterialQueryVO::getQualityId).collect(Collectors.toSet());
        Set<Integer> styleIds = results.stream().map(OldMaterialQueryVO::getStyleId).collect(Collectors.toSet());
        Set<Integer> brandIds = results.stream().map(OldMaterialQueryVO::getBrandId).collect(Collectors.toSet());
        Set<Integer> technologyIds = results.stream().map(OldMaterialQueryVO::getTechnologyId).collect(Collectors.toSet());
        Set<Integer> mainStoneIds = results.stream().map(OldMaterialQueryVO::getMainStoneId).collect(Collectors.toSet());
        Set<Integer> subStoneIds = results.stream().map(OldMaterialQueryVO::getSubStoneId).collect(Collectors.toSet());
        Set<Integer> oldMaterialIds = results.stream().map(OldMaterialQueryVO::getId).collect(Collectors.toSet());

        // 获取金价银价Map
        Map<String, BigDecimal[]> goldSilverPriceMap = getGoldSilverPriceMap();

        // 使用ListFillUtil填充关联数据
        ListFillUtil.of(results)
                .build(listFillService::getCategoryNameById, categoryIds, "categoryId", "categoryName")
                .build(listFillService::getSubclassNameById, subclassIds, "subclassId", "subclassName")
                .build(listFillService::getSupplierNameById, supplierIds, "supplierId", "supplierName")
                .build(listFillService::getQualityNameById, qualityIds, "qualityId", "qualityName")
                .build(listFillService::getStyleNameById, styleIds, "styleId", "styleName")
                .build(listFillService::getBrandNameById, brandIds, "brandId", "brandName")
                .build(listFillService::getTechnologyNameById, technologyIds, "technologyId", "technologyName")
                .build(listFillService::getJewelryMapperNameById, mainStoneIds, "mainStoneId", "mainStoneName")
                .build(listFillService::getJewelryMapperNameById, subStoneIds, "subStoneId", "subStoneName")
                .build(listFillService::getOldMaterialImgByOldMaterialId, oldMaterialIds, "id", "images")
                .peek(obj -> {
                    OldMaterialQueryVO vo = (OldMaterialQueryVO) obj;
                    // 价格字段分转元
                    vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
                    vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
                    vo.setRecyclePrice(PriceUtil.fen2yuan(vo.getRecyclePrice()));

                    // 填充销售金价和销售银价
                    Long categoryId = vo.getCategoryId() != null ? vo.getCategoryId().longValue() : null;
                    Long qualityId = vo.getQualityId() != null ? vo.getQualityId().longValue() : null;
                    BigDecimal[] prices = getGoldSilverPrice(categoryId, qualityId, goldSilverPriceMap);
                    vo.setSaleGoldPrice(prices[0]);
                    vo.setSaleSilverPrice(prices[1]);

                    // 根据计价方式设置销售单价
                    // if ("1".equals(vo.getSalesType())) {
                    //     // 按重量计价：使用金价或银价
                    //     if (CategoryEnum.SILVER.getValue().equals(categoryId)) {
                    //         vo.setSalePrice(prices[1]); // 银饰使用银价
                    //     } else {
                    //         vo.setSalePrice(prices[0]); // 其他使用金价
                    //     }
                    // } else if ("2".equals(vo.getSalesType())) {
                    //     // 按数量计价：使用回收价作为销售价
                    //     vo.setSalePrice(vo.getRecyclePrice());
                    // }
                })
                .handle();
    }

    /**
     * 获取金价银价Map，支持循环调用
     *
     * @return 金价银价Map，key为"categoryId_qualityId"或"categoryId"，value为[销售金价, 销售银价, 回收金价, 回收银价]
     */
    private Map<String, BigDecimal[]> getGoldSilverPriceMap() {
        Map<String, BigDecimal[]> priceMap = new HashMap<>();

        try {
            Long companyId = SecurityUtils.getCompanyId();

            // 一次查询获取所有生效的金价数据
            List<QualityGoldPriceVo> allPrices = goldPriceMapper.selectListByQueryAs(
                QueryWrapper.create()
                    .where(GOLD_PRICE.COMPANY_ID.eq(companyId))
                    .where(GOLD_PRICE.STATUS.eq(1))
                    .select(
                        GOLD_PRICE.CATEGORY_ID,
                        GOLD_PRICE.QUALITY_ID,
                        GOLD_PRICE.SALE_PRICE.divide(100).as("goldSalePrice"),
                        GOLD_PRICE.RECYCLE_PRICE.divide(100).as("goldRecyclePrice"),
                        GOLD_PRICE.ACTIVE_TIME
                    )
                    .orderBy(GOLD_PRICE.ACTIVE_TIME, false),
                QualityGoldPriceVo.class
            );

            if (allPrices.isEmpty()) {
                return priceMap;
            }

            // 用于金包银大类的金价银价
            BigDecimal goldSalePrice = null;
            BigDecimal silverSalePrice = null;
            BigDecimal goldRecyclePrice = null;
            BigDecimal silverRecyclePrice = null;

            // 查找第一个金价和第一个银价
            for (QualityGoldPriceVo price : allPrices) {
                if (goldSalePrice == null && CategoryEnum.GOLD.getValue().equals(price.getCategoryId())) {
                    goldSalePrice = price.getGoldSalePrice();
                    goldRecyclePrice = price.getGoldRecyclePrice();
                }
                if (silverSalePrice == null && CategoryEnum.SILVER.getValue().equals(price.getCategoryId())) {
                    silverSalePrice = price.getGoldSalePrice(); // 银饰的价格存储在goldSalePrice字段中
                    silverRecyclePrice = price.getGoldRecyclePrice();
                }
                if (goldSalePrice != null && silverSalePrice != null) {
                    break;
                }
            }

            // 处理每个价格记录
            for (QualityGoldPriceVo price : allPrices) {
                BigDecimal saleGoldPrice = null;
                BigDecimal saleSilverPrice = null;
                BigDecimal recycleGoldPrice = null;
                BigDecimal recycleSilverPrice = null;

                if (CategoryEnum.SILVER.getValue().equals(price.getCategoryId())) {
                    // 银饰：只有银价，金价为null（使用当前记录的价格）
                    saleGoldPrice = null;
                    saleSilverPrice = price.getGoldSalePrice(); // 银饰的价格存储在goldSalePrice字段中
                    recycleGoldPrice = null;
                    recycleSilverPrice = price.getGoldRecyclePrice(); // 银饰的回收价格存储在goldRecyclePrice字段中
                } else {
                    // 其他大类：只有金价，银价为null（使用当前记录的价格）
                    saleGoldPrice = price.getGoldSalePrice();
                    saleSilverPrice = null;
                    recycleGoldPrice = price.getGoldRecyclePrice();
                    recycleSilverPrice = null;
                }

                BigDecimal[] prices = new BigDecimal[]{saleGoldPrice, saleSilverPrice, recycleGoldPrice, recycleSilverPrice};

                // 如果有成色ID，加入大类+成色组合Map
                if (price.getQualityId() != null) {
                    String qualityKey = price.getCategoryId() + "_" + price.getQualityId();
                    if (!priceMap.containsKey(qualityKey)) {
                        priceMap.put(qualityKey, prices);
                    }
                }

                // 加入大类Map（作为fallback）
                String categoryKey = String.valueOf(price.getCategoryId());
                if (!priceMap.containsKey(categoryKey)) {
                    priceMap.put(categoryKey, prices);
                }
            }

            // 单独处理金包银大类：使用第一个金价和第一个银价
            if (goldSalePrice != null || silverSalePrice != null) {
                BigDecimal[] goldSilverPrices = new BigDecimal[]{goldSalePrice, silverSalePrice, goldRecyclePrice, silverRecyclePrice};
                String goldSilverKey = String.valueOf(CategoryEnum.GOLD_SILVER.getValue());
                priceMap.put(goldSilverKey, goldSilverPrices);
            }

        } catch (Exception e) {
            log.error("批量查询金价银价失败", e);
        }

        return priceMap;
    }

    /**
     * 根据大类和成色匹配金价银价
     * 优先用大类+成色匹配，如果没有则使用大类匹配，否则为零
     *
     * @param categoryId 大类ID
     * @param qualityId 成色ID
     * @param priceMap 金价银价Map
     * @return [销售金价, 销售银价]
     */
    private BigDecimal[] getGoldSilverPrice(Long categoryId, Long qualityId, Map<String, BigDecimal[]> priceMap) {
        BigDecimal goldPrice = BigDecimal.ZERO;
        BigDecimal silverPrice = BigDecimal.ZERO;

        if (categoryId == null) {
            return new BigDecimal[]{goldPrice, silverPrice};
        }

        BigDecimal[] prices = null;

        // 1. 优先尝试大类+成色匹配
        if (qualityId != null) {
            String key = categoryId + "_" + qualityId;
            prices = priceMap.get(key);
        }

        // 2. 如果没有找到，尝试大类匹配
        if (prices == null) {
            String key = String.valueOf(categoryId);
            prices = priceMap.get(key);
        }

        // 3. 提取金价银价
        if (prices != null) {
            if (prices[0] != null) {
                goldPrice = prices[0];
            }
            if (prices[1] != null) {
                silverPrice = prices[1];
            }
        }

        return new BigDecimal[]{goldPrice, silverPrice};
    }



    /**
     * 销售单汇总数据
     */
    private static class OrderSummary {
        private Integer totalNum = 0;
        private BigDecimal totalWeight = BigDecimal.ZERO;
        private BigDecimal totalNetGoldWeight = BigDecimal.ZERO;
        private BigDecimal totalNetSilverWeight = BigDecimal.ZERO;
        private Long totalAmount = 0L;

        // Getters and setters
        public Integer getTotalNum() { return totalNum; }
        public void setTotalNum(Integer totalNum) { this.totalNum = totalNum; }
        public BigDecimal getTotalWeight() { return totalWeight; }
        public void setTotalWeight(BigDecimal totalWeight) { this.totalWeight = totalWeight; }
        public BigDecimal getTotalNetGoldWeight() { return totalNetGoldWeight; }
        public void setTotalNetGoldWeight(BigDecimal totalNetGoldWeight) { this.totalNetGoldWeight = totalNetGoldWeight; }
        public BigDecimal getTotalNetSilverWeight() { return totalNetSilverWeight; }
        public void setTotalNetSilverWeight(BigDecimal totalNetSilverWeight) { this.totalNetSilverWeight = totalNetSilverWeight; }
        public Long getTotalAmount() { return totalAmount; }
        public void setTotalAmount(Long totalAmount) { this.totalAmount = totalAmount; }
    }

    @Override
    public Page<OldMaterialOrderVO> getOldMaterialOrderPage(OldMaterialOrderQuery query) {
        QueryWrapper wrapper = buildOrderQueryWrapper(query);
        wrapper.orderBy(column(OldMaterialOrderEntity::getCreatedAt), false);

        // 处理导出
        if (query.getExport() != null && query.getExport().equals(1)) {
            exportOldMaterialOrders(wrapper, query);
            return new Page<>();
        }

        // 处理打印
        if (query.getPrint() != null && query.getPrint().equals(1)) {
            return printOldMaterialOrders(wrapper, query);
        }

        // 执行分页查询
        Page<OldMaterialOrderVO> page = this.mapper.paginateAs(query.getPageNum(), query.getPageSize(), wrapper, OldMaterialOrderVO.class);
        List<OldMaterialOrderVO> records = page.getRecords();
        if (records.isEmpty()) {
            return page;
        }

        fillOrderList(records);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createOldMaterialOrder(OldMaterialOrderCreateForm form) {
        // 1. 验证用户权限
        validateUserPermissions(form);

        // 2. 验证供应商是否有效
        validateSupplier(form.getSupplierId());

        // 3. 验证旧料并获取详细信息
        List<OldMaterialEntity> oldMaterials = validateAndGetOldMaterials(form);

        // 4. 计算销售单汇总数据
        OrderSummary summary = calculateOrderSummary(form, oldMaterials);

        // 5. 获取审核开关设置
        CompanySettingsVO settings = companySettingsService.getSettings();
        boolean auditEnabled = settings != null && Boolean.TRUE.equals(settings.getOldMaterialOutcomeAuditEnabled());

        // 6. 创建销售单主记录
        OldMaterialOrderEntity order = createOrderEntity(form, summary, auditEnabled);
        this.save(order);

        // 7. 创建销售单明细记录
        createOrderDetails(order.getId(), form, oldMaterials, auditEnabled);

        // 8. 更新旧料库存
        if (auditEnabled) {
            // 开启审核：扣减数量增加冻结数量
            updateOldMaterialStocks(form);
        } else {
            // 关闭审核：直接处理出库，将冻结数量转为实际出库
            handleDirectStockOutcome(form);
        }

        // 9. 记录操作日志
        String auditStatus = auditEnabled ? "待审核" : "已通过";
        OpLogUtils.appendOpLog("旧料销售单-创建销售单",
            "创建旧料销售单: " + order.getOrderSn(),
            Map.of("销售单ID", order.getId(), "销售单号", order.getOrderSn(), "明细数量", form.getDetails().size(), "审核状态", auditStatus));

        return order.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOldMaterialOrder(OldMaterialOrderUpdateForm form) {
        // 1. 验证销售单是否存在
        OldMaterialOrderEntity order = this.getById(form.getId());
        CommonUtils.abortIf(order == null, "销售单不存在");

        // 2. 验证用户权限
        validateOrderPermissions(order);

        // 3. 验证销售单状态（只能编辑待审核的销售单）
        CommonUtils.abortIf(!order.getStatus().equals(0), "只能编辑待审核状态的销售单");

        // 4. 更新备注
        order.setRemark(form.getRemark());
        this.updateById(order);

        // 5. 记录操作日志
        OpLogUtils.appendOpLog("旧料销售单-编辑销售单",
            "编辑旧料销售单备注: " + order.getOrderSn(),
            Map.of("销售单ID", order.getId(), "销售单号", order.getOrderSn(), "新备注", form.getRemark()));
    }

    /**
     * 验证销售单权限
     */
    private void validateOrderPermissions(OldMaterialOrderEntity order) {
        // 验证商户权限
        Long companyId = SecurityUtils.getCompanyId();
        CommonUtils.abortIf(!order.getCompanyId().equals(companyId.intValue()), "无权操作其他商户的销售单");

        // 验证门店权限（非主账号需要验证门店权限）
        if (!SecurityUtils.isMain()) {
            Set<Long> merchantIds = SecurityUtils.getMerchantIds();
            CommonUtils.abortIf(!merchantIds.contains(order.getMerchantId().longValue()),
                "无权操作该门店的销售单");
        }
    }

    /**
     * 验证用户权限
     */
    private void validateUserPermissions(OldMaterialOrderCreateForm form) {
        // 验证商户权限
        Long companyId = SecurityUtils.getCompanyId();
        CommonUtils.abortIf(companyId == null, "获取商户信息失败");

        // 验证门店权限（非主账号需要验证门店权限）
        if (!SecurityUtils.isMain()) {
            Set<Long> merchantIds = SecurityUtils.getMerchantIds();
            CommonUtils.abortIf(!merchantIds.contains(form.getMerchantId().longValue()),
                "无权操作该门店的数据");
        }
    }

    /**
     * 验证供应商是否有效
     */
    private void validateSupplier(Integer supplierId) {
        SupplierEntity supplier = supplierMapper.selectOneByQuery(
            QueryWrapper.create()
                .where(SupplierEntity::getId).eq(supplierId)
                .and(SupplierEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(SupplierEntity::getStatus).eq(1) // 启用状态
        );
        CommonUtils.abortIf(supplier == null, "供应商不存在或已禁用");
    }

    /**
     * 验证旧料并获取详细信息
     */
    private List<OldMaterialEntity> validateAndGetOldMaterials(OldMaterialOrderCreateForm form) {
        // 收集所有旧料ID
        Set<Integer> oldMaterialIds = form.getDetails().stream()
            .map(OldMaterialOrderCreateForm.OldMaterialOrderDetailForm::getOldMaterialId)
            .collect(Collectors.toSet());

        // 查询旧料信息
        List<OldMaterialEntity> oldMaterials = oldMaterialMapper.selectListByQuery(
            QueryWrapper.create()
                .where(column(OldMaterialEntity::getId).in(oldMaterialIds))
                .and(column(OldMaterialEntity::getCompanyId).eq(SecurityUtils.getCompanyId()))
                .and(column(OldMaterialEntity::getMerchantId).eq(form.getMerchantId()))
        );

        // 验证旧料是否都存在
        CommonUtils.abortIf(oldMaterials.size() != oldMaterialIds.size(),
            "部分旧料不存在或不属于指定门店");

        // 验证库存是否充足
        Map<Integer, Integer> requestNumMap = form.getDetails().stream()
            .collect(Collectors.toMap(
                OldMaterialOrderCreateForm.OldMaterialOrderDetailForm::getOldMaterialId,
                OldMaterialOrderCreateForm.OldMaterialOrderDetailForm::getNum
            ));

        for (OldMaterialEntity oldMaterial : oldMaterials) {
            Integer requestNum = requestNumMap.get(oldMaterial.getId().intValue());
            CommonUtils.abortIf(oldMaterial.getNum() < requestNum,
                String.format("旧料[%s]库存不足，当前库存：%d，申请数量：%d",
                    oldMaterial.getName(), oldMaterial.getNum(), requestNum));
        }

        return oldMaterials;
    }

    /**
     * 计算销售单汇总数据
     */
    private OrderSummary calculateOrderSummary(OldMaterialOrderCreateForm form, List<OldMaterialEntity> oldMaterials) {
        OrderSummary summary = new OrderSummary();

        // 创建旧料ID到实体的映射
        Map<Long, OldMaterialEntity> oldMaterialMap = oldMaterials.stream()
            .collect(Collectors.toMap(OldMaterialEntity::getId, entity -> entity));

        for (OldMaterialOrderCreateForm.OldMaterialOrderDetailForm detail : form.getDetails()) {
            OldMaterialEntity oldMaterial = oldMaterialMap.get(detail.getOldMaterialId().longValue());

            // 累计数量
            summary.totalNum += detail.getNum();

            // 累计重量（按数量计算）
            if (oldMaterial.getWeight() != null) {
                summary.totalWeight = summary.totalWeight.add(
                    oldMaterial.getWeight().multiply(BigDecimal.valueOf(detail.getNum()))
                );
            }

            // 累计金重（按数量计算）
            if (oldMaterial.getNetGoldWeight() != null) {
                summary.totalNetGoldWeight = summary.totalNetGoldWeight.add(
                    oldMaterial.getNetGoldWeight().multiply(BigDecimal.valueOf(detail.getNum()))
                );
            }

            // 累计银重（按数量计算）
            if (oldMaterial.getNetSilverWeight() != null) {
                summary.totalNetSilverWeight = summary.totalNetSilverWeight.add(
                    oldMaterial.getNetSilverWeight().multiply(BigDecimal.valueOf(detail.getNum()))
                );
            }

            // 累计金额（将元转换为分）
            if (detail.getPrice() != null) {
                Long priceInFen = PriceUtil.yuan2fenLong(detail.getPrice());
                summary.totalAmount += priceInFen * detail.getNum();
            }
        }

        return summary;
    }

    /**
     * 创建销售单主记录
     */
    private OldMaterialOrderEntity createOrderEntity(OldMaterialOrderCreateForm form, OrderSummary summary, boolean auditEnabled) {
        OldMaterialOrderEntity order = new OldMaterialOrderEntity();

        // 生成销售单号
        order.setOrderSn(SnUtils.generateOldMaterialOrderCode());
        order.setCompanyId(SecurityUtils.getCompanyId().intValue());
        order.setMerchantId(form.getMerchantId());
        order.setSupplierId(form.getSupplierId());
        order.setNum(summary.getTotalNum());
        order.setTotalWeight(summary.getTotalWeight());
        order.setTotalNetGoldWeight(summary.getTotalNetGoldWeight());
        order.setTotalNetSilverWeight(summary.getTotalNetSilverWeight());
        order.setTotalAmount(summary.getTotalAmount());
        order.setRemark(form.getRemark());

        // 根据审核开关设置状态
        if (auditEnabled) {
            order.setStatus(OldMaterialOrderStatusEnum.PENDING.getValue()); // 待审核
        } else {
            order.setStatus(OldMaterialOrderStatusEnum.APPROVED.getValue()); // 已通过
            order.setAuditBy(SecurityUtils.getUserId().intValue());
            order.setAuditAt(java.time.LocalDateTime.now());
        }

        order.setCreatedBy(SecurityUtils.getUserId().intValue());

        // 同步销售单号最大序号
        SnUtils.syncOldMaterialOrderMaxSequence();

        return order;
    }

    /**
     * 创建销售单明细记录
     */
    private void createOrderDetails(Long orderId, OldMaterialOrderCreateForm form, List<OldMaterialEntity> oldMaterials, boolean auditEnabled) {
        // 创建旧料ID到实体的映射
        Map<Long, OldMaterialEntity> oldMaterialMap = oldMaterials.stream()
            .collect(Collectors.toMap(OldMaterialEntity::getId, entity -> entity));

        List<OldMaterialOrderDetailEntity> details = new ArrayList<>();

        for (OldMaterialOrderCreateForm.OldMaterialOrderDetailForm detailForm : form.getDetails()) {
            OldMaterialEntity oldMaterial = oldMaterialMap.get(detailForm.getOldMaterialId().longValue());

            OldMaterialOrderDetailEntity detail = new OldMaterialOrderDetailEntity();
            detail.setOrderId(orderId.intValue());
            detail.setCompanyId(SecurityUtils.getCompanyId().intValue());
            detail.setMerchantId(form.getMerchantId());
            detail.setGoodsId(0); // 旧料没有对应的货品ID
            detail.setGoodsSn(""); // 旧料没有对应的货品条码
            detail.setOldMaterialId(detailForm.getOldMaterialId());
            detail.setOldMaterialSn(oldMaterial.getOldMaterialSn());
            detail.setNum(detailForm.getNum());
            detail.setSalesType(detailForm.getSalesType());
            detail.setGoldPrice(detailForm.getGoldPrice() != null ? PriceUtil.yuan2fenLong(detailForm.getGoldPrice()) : null);
            detail.setSilverPrice(detailForm.getSilverPrice() != null ? PriceUtil.yuan2fenLong(detailForm.getSilverPrice()) : null);
            detail.setPrice(PriceUtil.yuan2fenLong(detailForm.getPrice()));

            // 根据审核开关设置状态
            if (auditEnabled) {
                detail.setStatus(OldMaterialOrderStatusEnum.PENDING.getValue()); // 待审核
                detail.setAuditBy(0); // 待审核状态下设置为0，审核时会更新为实际审核人ID
            } else {
                detail.setStatus(OldMaterialOrderStatusEnum.APPROVED.getValue()); // 已通过
                detail.setAuditBy(SecurityUtils.getUserId().intValue());
                detail.setAuditAt(java.time.LocalDateTime.now());
            }

            detail.setDataSnapshot(JSONUtil.toJsonStr(detail));

            details.add(detail);
        }

        // 批量保存明细
        oldMaterialOrderDetailMapper.insertBatchSelective(details);
    }

    /**
     * 更新旧料库存（审核开启时使用）
     */
    private void updateOldMaterialStocks(OldMaterialOrderCreateForm form) {
        List<OldMaterialStockNumChangeBO> stockChanges = new ArrayList<>();

        for (OldMaterialOrderCreateForm.OldMaterialOrderDetailForm detail : form.getDetails()) {
            OldMaterialStockNumChangeBO stockChange = OldMaterialStockNumChangeBO.builder()
                .oldMaterialId(detail.getOldMaterialId().longValue())
                .comment("创建旧料销售单")
                .num(-detail.getNum()) // 扣减库存
                .frozenNum(detail.getNum()) // 增加冻结数量
                .build();

            stockChanges.add(stockChange);
        }

        // 更新库存
        StockUtils.updateOldMaterialStocks(stockChanges);
    }

    /**
     * 直接处理旧料出库（审核关闭时使用）
     */
    private void handleDirectStockOutcome(OldMaterialOrderCreateForm form) {
        List<OldMaterialStockNumChangeBO> stockChanges = new ArrayList<>();

        for (OldMaterialOrderCreateForm.OldMaterialOrderDetailForm detail : form.getDetails()) {
            OldMaterialStockNumChangeBO stockChange = OldMaterialStockNumChangeBO.builder()
                .oldMaterialId(detail.getOldMaterialId().longValue())
                .comment("创建旧料销售单")
                .num(-detail.getNum()) // 直接扣减库存，不增加冻结数量
                .frozenNum(0) // 不增加冻结数量
                .build();

            stockChanges.add(stockChange);
        }

        // 更新库存
        StockUtils.updateOldMaterialStocks(stockChanges);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditOldMaterialOrder(OldMaterialOrderAuditForm form) {
        // 1. 验证销售单是否存在
        OldMaterialOrderEntity order = this.getById(form.getId());
        CommonUtils.abortIf(order == null, "销售单不存在");

        // 2. 验证用户权限
        validateOrderPermissions(order);

        // 3. 验证销售单状态（只能审核待审核的销售单）
        CommonUtils.abortIf(!order.getStatus().equals(OldMaterialOrderStatusEnum.PENDING.getValue()), "只能审核待审核状态的销售单");

        // 4. 验证审核结果参数
        CommonUtils.abortIf(form.getAuditResult() == null ||
            (!form.getAuditResult().equals(OldMaterialOrderStatusEnum.APPROVED.getValue()) &&
             !form.getAuditResult().equals(OldMaterialOrderStatusEnum.REJECTED.getValue())),
            "审核结果参数错误");

        // 5. 更新销售单状态
        order.setStatus(form.getAuditResult());
        order.setAuditBy(SecurityUtils.getUserId().intValue());
        order.setAuditAt(java.time.LocalDateTime.now());
        order.setAuditRemark(form.getAuditRemark());
        this.updateById(order);

        // 6. 更新明细状态
        updateOrderDetailsStatus(order.getId(), form.getAuditResult());

        // 7. 处理库存变更
        if (form.getAuditResult().equals(OldMaterialOrderStatusEnum.APPROVED.getValue())) {
            // 审核通过：将冻结数量转为实际出库
            handleApprovedStockChange(order.getId());
        } else if (form.getAuditResult().equals(OldMaterialOrderStatusEnum.REJECTED.getValue())) {
            // 审核驳回：恢复库存，清除冻结数量
            handleRejectedStockChange(order.getId());
        }

        // 8. 记录操作日志
        String statusText = form.getAuditResult().equals(OldMaterialOrderStatusEnum.APPROVED.getValue()) ? "通过" : "驳回";
        OpLogUtils.appendOpLog("旧料销售单-审核销售单",
            "审核旧料销售单" + statusText + ": " + order.getOrderSn(),
            Map.of("销售单ID", order.getId(), "销售单号", order.getOrderSn(),
                   "审核结果", statusText, "审核备注", form.getAuditRemark()));
    }

    /**
     * 更新销售单明细状态
     */
    private void updateOrderDetailsStatus(Long orderId, Integer status) {
        List<OldMaterialOrderDetailEntity> details = oldMaterialOrderDetailMapper.selectListByQuery(
            QueryWrapper.create().where(column("order_id").eq(orderId)));

        if (!details.isEmpty()) {
            for (OldMaterialOrderDetailEntity detail : details) {
                detail.setStatus(status);
                detail.setAuditBy(SecurityUtils.getUserId().intValue());
                detail.setAuditAt(java.time.LocalDateTime.now());
                oldMaterialOrderDetailMapper.update(detail);
            }
        }
    }

    /**
     * 处理审核通过的库存变更
     */
    private void handleApprovedStockChange(Long orderId) {
        List<OldMaterialOrderDetailEntity> details = oldMaterialOrderDetailMapper.selectListByQuery(
            QueryWrapper.create().where(column("order_id").eq(orderId)));

        List<OldMaterialStockNumChangeBO> stockChanges = new ArrayList<>();
        for (OldMaterialOrderDetailEntity detail : details) {
            OldMaterialStockNumChangeBO stockChange = OldMaterialStockNumChangeBO.builder()
                .oldMaterialId(detail.getOldMaterialId().longValue())
                .comment("旧料销售单审核通过")
                .num(0) // 不变更库存数量
                .frozenNum(-detail.getNum()) // 减少冻结数量
                .build();
            stockChanges.add(stockChange);
        }

        if (!stockChanges.isEmpty()) {
            StockUtils.updateOldMaterialStocks(stockChanges);
        }
    }

    /**
     * 处理审核驳回的库存变更
     */
    private void handleRejectedStockChange(Long orderId) {
        List<OldMaterialOrderDetailEntity> details = oldMaterialOrderDetailMapper.selectListByQuery(
            QueryWrapper.create().where(column("order_id").eq(orderId)));

        List<OldMaterialStockNumChangeBO> stockChanges = new ArrayList<>();
        for (OldMaterialOrderDetailEntity detail : details) {
            OldMaterialStockNumChangeBO stockChange = OldMaterialStockNumChangeBO.builder()
                .oldMaterialId(detail.getOldMaterialId().longValue())
                .comment("旧料销售单审核驳回")
                .num(detail.getNum()) // 恢复库存数量
                .frozenNum(-detail.getNum()) // 减少冻结数量
                .build();
            stockChanges.add(stockChange);
        }

        if (!stockChanges.isEmpty()) {
            StockUtils.updateOldMaterialStocks(stockChanges);
        }
    }

    @Override
    public OldMaterialOrderVO getOldMaterialOrderDetail(Long id) {
        // 1. 查询销售单基础信息
        OldMaterialOrderVO vo = this.mapper.selectOneByQueryAs(
            QueryWrapper.create()
                .where(column(OldMaterialOrderEntity::getId).eq(id))
                .and(column(OldMaterialOrderEntity::getCompanyId).eq(SecurityUtils.getCompanyId())),
            OldMaterialOrderVO.class
        );

        CommonUtils.abortIf(vo == null, "旧料销售单不存在");

        // 2. 权限验证：非主账号只能查看有权限的门店数据
        if (!SecurityUtils.isMain()) {
            Set<Long> merchantIds = SecurityUtils.getMerchantIds();
            CommonUtils.abortIf(merchantIds == null || !merchantIds.contains(vo.getMerchantId().longValue()),
                "无权查看该门店数据");
        }

        // 3. 填充关联数据
        fillOrderList(List.of(vo));

        return vo;
    }

    @Override
    public Page<OldMaterialOrderDetailPageVO> getOldMaterialOrderDetailPage(OldMaterialOrderDetailPageQuery query) {
        // 1. 验证销售单是否存在及权限
        validateOrderAccess(query.getOrderId());

        // 2. 构建查询条件
        QueryWrapper wrapper = buildDetailQueryWrapper(query);

        // 3. 处理导出
        if (query.getExport() != null && query.getExport().equals(1)) {
            exportOrderDetails(wrapper, query);
            return new Page<>();
        }

        // 4. 处理打印
        if (query.getPrint() != null && query.getPrint().equals(1)) {
            return printOrderDetails(wrapper, query);
        }

        // 5. 执行分页查询
        Page<OldMaterialOrderDetailPageVO> page = oldMaterialOrderDetailMapper.paginateAs(
                query.getPageNum(),
                query.getPageSize(),
                wrapper,
                OldMaterialOrderDetailPageVO.class
        );

        // 6. 填充关联数据
        if (!page.getRecords().isEmpty()) {
            fillOrderDetailPageVOs(page.getRecords());
        }

        return page;
    }

    /**
     * 构建旧料销售单查询条件
     */
    private QueryWrapper buildOrderQueryWrapper(OldMaterialOrderQuery query) {
        QueryWrapper wrapper = QueryWrapper.create()
                .where(column(OldMaterialOrderEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));

        // 权限控制：非主账号只能查看有权限的门店数据
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        if (!SecurityUtils.isMain() && merchantIds != null && !merchantIds.isEmpty()) {
            wrapper.and(column(OldMaterialOrderEntity::getMerchantId).in(merchantIds));
        }

        // 销售单号模糊查询
        if (StrUtil.isNotBlank(query.getOrderSn())) {
            wrapper.and(column(OldMaterialOrderEntity::getOrderSn).like(query.getOrderSn()));
        }

        // 门店ID查询（支持多个ID，逗号分隔）
        List<Long> merchantIdList = QueryUtils.parseIds(query.getMerchantId());
        if (!merchantIdList.isEmpty()) {
            wrapper.and(column(OldMaterialOrderEntity::getMerchantId).in(merchantIdList));
        }

        // 门店ID列表查询（兼容旧参数）
        List<Long> merchantIdsList = QueryUtils.parseIds(query.getMerchantIds());
        if (!merchantIdsList.isEmpty()) {
            wrapper.and(column(OldMaterialOrderEntity::getMerchantId).in(merchantIdsList));
        }

        // 供应商ID查询（支持多个ID，逗号分隔）
        List<Integer> supplierIdList = QueryUtils.parseIntegerIds(query.getSupplierId());
        if (!supplierIdList.isEmpty()) {
            wrapper.and(column(OldMaterialOrderEntity::getSupplierId).in(supplierIdList));
        }

        // 状态查询（支持多个状态，逗号分隔）
        List<Integer> statusList = QueryUtils.parseIntegerIds(query.getStatus());
        if (!statusList.isEmpty()) {
            wrapper.and(column(OldMaterialOrderEntity::getStatus).in(statusList));
        }

        // 创建人ID查询（支持多个ID，逗号分隔）
        List<Integer> createdByList = QueryUtils.parseIntegerIds(query.getCreatedBy());
        if (!createdByList.isEmpty()) {
            wrapper.and(column(OldMaterialOrderEntity::getCreatedBy).in(createdByList));
        }

        // 审核人ID查询（支持多个ID，逗号分隔）
        List<Integer> auditByList = QueryUtils.parseIntegerIds(query.getAuditBy());
        if (!auditByList.isEmpty()) {
            wrapper.and(column(OldMaterialOrderEntity::getAuditBy).in(auditByList));
        }

        // 创建时间范围查询
        if (query.getCreatedAtRange() != null && !query.getCreatedAtRange().isEmpty()) {
            if (query.getCreatedAtRange().size() >= 2) {
                Date startTime = DateUtil.parseDateTime(query.getCreatedAtRange().get(0));
                Date endTime = DateUtil.parseDateTime(query.getCreatedAtRange().get(1));
                wrapper.and(column(OldMaterialOrderEntity::getCreatedAt).between(startTime, endTime));
            }
        }

        // 审核时间范围查询
        if (query.getAuditAtRange() != null && !query.getAuditAtRange().isEmpty()) {
            if (query.getAuditAtRange().size() >= 2) {
                Date startTime = DateUtil.parseDateTime(query.getAuditAtRange().get(0));
                Date endTime = DateUtil.parseDateTime(query.getAuditAtRange().get(1));
                wrapper.and(column(OldMaterialOrderEntity::getAuditAt).between(startTime, endTime));
            }
        }

        // 处理ids参数（用于打印导出指定记录）
        if (query.getIds() != null && !query.getIds().isEmpty()) {
            wrapper.and(column(OldMaterialOrderEntity::getId).in(query.getIds()));
        }

        return wrapper;
    }

    /**
     * 填充旧料销售单列表关联数据
     */
    private void fillOrderList(List<OldMaterialOrderVO> records) {
        if (records.isEmpty()) {
            return;
        }

        // 收集需要填充的ID
        Set<Integer> merchantIds = records.stream()
                .map(OldMaterialOrderVO::getMerchantId)
                .collect(Collectors.toSet());
        Set<Integer> supplierIds = records.stream()
                .map(OldMaterialOrderVO::getSupplierId)
                .collect(Collectors.toSet());
        Set<Integer> createdByIds = records.stream()
                .map(OldMaterialOrderVO::getCreatedBy)
                .collect(Collectors.toSet());
        Set<Integer> auditByIds = records.stream()
                .map(OldMaterialOrderVO::getAuditBy)
                .filter(id -> id != null)
                .collect(Collectors.toSet());

        // 使用ListFillUtil填充关联数据
        ListFillUtil.of(records)
                .build(listFillService::getMerchantNameById, merchantIds, "merchantId", "merchantName")
                .build(listFillService::getSupplierNameById, supplierIds, "supplierId", "supplierName")
                .build(listFillService::getUserNameByUserId, createdByIds, "createdBy", "createdByName")
                .build(listFillService::getUserNameByUserId, auditByIds, "auditBy", "auditByName")
                .peek(obj -> {
                    OldMaterialOrderVO vo = (OldMaterialOrderVO) obj;
                    // 设置状态名称
                    vo.setStatusName(IBaseEnum.getLabelByValue(vo.getStatus(), OldMaterialOrderStatusEnum.class));
                    // 价格字段分转元
                    vo.setTotalAmount(PriceUtil.fen2yuan(vo.getTotalAmount()));
                })
                .handle();
    }

    /**
     * 导出旧料销售单列表
     */
    private void exportOldMaterialOrders(QueryWrapper wrapper, OldMaterialOrderQuery query) {
        // 检查导出数量限制
        long count = this.mapper.selectCountByQuery(wrapper);
        CommonUtils.abortIf(count > CommonUtils.getMaxExportSize(), "导出数量超过限制");

        ExcelUtil.of(this.mapper, wrapper, OldMaterialOrderVO.class, "old_material_order", "旧料销售单列表")
                .getData((mapper, queryWrapper) -> {
                    List<OldMaterialOrderVO> voList = mapper.selectListByQueryAs(wrapper, OldMaterialOrderVO.class);
                    fillOrderList(voList);

                    // 处理导出字段转换
                    for (OldMaterialOrderVO vo : voList) {
                        // 替换状态为文本
                        if (vo.getStatus() instanceof Integer) {
                            Integer statusValue = (Integer) vo.getStatus();
                            vo.setStatus(IBaseEnum.getLabelByValue(statusValue, OldMaterialOrderStatusEnum.class));
                        }
                    }

                    // 处理加密字段和图片导出
                    List<JSONObject> processedList = (List<JSONObject>) ColumnEncryptUtil.process(voList);
                    // 处理图片字段，确保最终的value值保留一张图片
                    ColumnEncryptUtil.handleJsonImageExport(processedList);

                    return processedList;
                })
                .modifyConfig(config -> {
                    config.setTitle("旧料销售单列表");
                    config.setWidth(15);
                })
                .doExport();
    }

    /**
     * 打印旧料销售单列表
     */
    private Page<OldMaterialOrderVO> printOldMaterialOrders(QueryWrapper wrapper, OldMaterialOrderQuery query) {
        // 检查打印数量限制
        long count = this.mapper.selectCountByQuery(wrapper);
        CommonUtils.abortIf(count > CommonUtils.getMaxPrintSize(), "打印数量超过限制");

        List<OldMaterialOrderVO> voList = this.mapper.selectListByQueryAs(wrapper, OldMaterialOrderVO.class);
        fillOrderList(voList);

        // 处理打印字段转换
        for (OldMaterialOrderVO vo : voList) {
            // 替换状态为文本
            if (vo.getStatus() instanceof Integer) {
                Integer statusValue = (Integer) vo.getStatus();
                vo.setStatus(IBaseEnum.getLabelByValue(statusValue, OldMaterialOrderStatusEnum.class));
            }
        }

        // 返回Page对象，pageNum=1, pageSize=voList.size(), total=count
        Page<OldMaterialOrderVO> page = new Page<>(1, voList.size(), count);
        page.setRecords(voList);
        return page;
    }

    /**
     * 验证销售单访问权限
     */
    private void validateOrderAccess(Long orderId) {
        OldMaterialOrderEntity order = this.getById(orderId);
        CommonUtils.abortIf(order == null, "旧料销售单不存在");
        CommonUtils.abortIf(!order.getCompanyId().equals(SecurityUtils.getCompanyId().intValue()),
            "无权访问该销售单");

        // 权限验证：非主账号只能查看有权限的门店数据
        if (!SecurityUtils.isMain()) {
            Set<Long> merchantIds = SecurityUtils.getMerchantIds();
            CommonUtils.abortIf(merchantIds == null || !merchantIds.contains(order.getMerchantId().longValue()),
                "无权查看该门店数据");
        }
    }

    /**
     * 构建明细查询条件
     */
    private QueryWrapper buildDetailQueryWrapper(OldMaterialOrderDetailPageQuery query) {
        // 简化查询，只查询明细表，然后通过填充服务获取关联数据
        QueryWrapper wrapper = QueryWrapper.create()
            .where(column(OldMaterialOrderDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId()))
            .and(column(OldMaterialOrderDetailEntity::getOrderId).eq(query.getOrderId()));

        // 处理ids参数（用于打印导出指定记录）
        if (query.getIds() != null && !query.getIds().isEmpty()) {
            wrapper.and(column(OldMaterialOrderDetailEntity::getId).in(query.getIds()));
        }

        // 旧料编号模糊查询
        if (StrUtil.isNotBlank(query.getOldMaterialSn())) {
            // 需要通过子查询关联旧料表
            wrapper.and(column(OldMaterialOrderDetailEntity::getOldMaterialId).in(
                QueryWrapper.create()
                    .select(column(OldMaterialEntity::getId))
                    .from(OldMaterialEntity.class)
                    .where(column(OldMaterialEntity::getOldMaterialSn).like(query.getOldMaterialSn()))
            ));
        }

        // 旧料名称模糊查询
        if (StrUtil.isNotBlank(query.getOldMaterialName())) {
            // 需要通过子查询关联旧料表
            wrapper.and(column(OldMaterialOrderDetailEntity::getOldMaterialId).in(
                QueryWrapper.create()
                    .select(column(OldMaterialEntity::getId))
                    .from(OldMaterialEntity.class)
                    .where(column(OldMaterialEntity::getName).like(query.getOldMaterialName()))
            ));
        }

        // 所属大类ID查询（支持多个ID，逗号分隔）
        List<Integer> categoryIdList = QueryUtils.parseIntegerIds(query.getCategoryId());
        if (!categoryIdList.isEmpty()) {
            // 通过子查询关联旧料表
            wrapper.and(column(OldMaterialOrderDetailEntity::getOldMaterialId).in(
                QueryWrapper.create()
                    .select(column(OldMaterialEntity::getId))
                    .from(OldMaterialEntity.class)
                    .where(column(OldMaterialEntity::getCategoryId).in(categoryIdList))
            ));
        }

        // 货品小类ID查询（支持多个ID，逗号分隔）
        List<Integer> subclassIdList = QueryUtils.parseIntegerIds(query.getSubclassId());
        if (!subclassIdList.isEmpty()) {
            // 通过子查询关联旧料表
            wrapper.and(column(OldMaterialOrderDetailEntity::getOldMaterialId).in(
                QueryWrapper.create()
                    .select(column(OldMaterialEntity::getId))
                    .from(OldMaterialEntity.class)
                    .where(column(OldMaterialEntity::getSubclassId).in(subclassIdList))
            ));
        }

        // 供应商ID查询（支持多个ID，逗号分隔）
        List<Integer> supplierIdList = QueryUtils.parseIntegerIds(query.getSupplierId());
        if (!supplierIdList.isEmpty()) {
            // 通过子查询关联旧料表
            wrapper.and(column(OldMaterialOrderDetailEntity::getOldMaterialId).in(
                QueryWrapper.create()
                    .select(column(OldMaterialEntity::getId))
                    .from(OldMaterialEntity.class)
                    .where(column(OldMaterialEntity::getSupplierId).in(supplierIdList))
            ));
        }

        // 成色ID查询（支持多个ID，逗号分隔）
        List<Integer> qualityIdList = QueryUtils.parseIntegerIds(query.getQualityId());
        if (!qualityIdList.isEmpty()) {
            // 通过子查询关联旧料表
            wrapper.and(column(OldMaterialOrderDetailEntity::getOldMaterialId).in(
                QueryWrapper.create()
                    .select(column(OldMaterialEntity::getId))
                    .from(OldMaterialEntity.class)
                    .where(column(OldMaterialEntity::getQualityId).in(qualityIdList))
            ));
        }

        // 计价方式查询（支持多个值，逗号分隔）
        List<Integer> salesTypeList = QueryUtils.parseIntegerIds(query.getSalesType());
        if (!salesTypeList.isEmpty()) {
            wrapper.and(column(OldMaterialOrderDetailEntity::getSalesType).in(salesTypeList));
        }

        wrapper.orderBy(column(OldMaterialOrderDetailEntity::getCreatedAt), false);

        return wrapper;
    }

    /**
     * 填充明细分页VO数据
     */
    private void fillOrderDetailPageVOs(List<OldMaterialOrderDetailPageVO> records) {
        if (records.isEmpty()) {
            return;
        }

        // 一次循环收集所有需要的ID，并填充旧料信息
        Set<Integer> oldMaterialIds = new HashSet<>();
        Set<Integer> merchantIds = new HashSet<>();
        Set<Integer> supplierIds = new HashSet<>();
        Set<Integer> categoryIds = new HashSet<>();
        Set<Integer> subclassIds = new HashSet<>();
        Set<Integer> qualityIds = new HashSet<>();
        Set<Integer> styleIds = new HashSet<>();
        Set<Integer> brandIds = new HashSet<>();
        Set<Integer> technologyIds = new HashSet<>();
        Set<Integer> mainStoneIds = new HashSet<>();
        Set<Integer> subStoneIds = new HashSet<>();
        Set<Integer> auditByIds = new HashSet<>();

        // 一次循环收集所有ID
        for (OldMaterialOrderDetailPageVO vo : records) {
            if (vo.getOldMaterialId() != null) oldMaterialIds.add(vo.getOldMaterialId());
            if (vo.getMerchantId() != null) merchantIds.add(vo.getMerchantId());
            if (vo.getSupplierId() != null) supplierIds.add(vo.getSupplierId());
            if (vo.getCategoryId() != null) categoryIds.add(vo.getCategoryId());
            if (vo.getSubclassId() != null) subclassIds.add(vo.getSubclassId());
            if (vo.getQualityId() != null) qualityIds.add(vo.getQualityId());
            if (vo.getStyleId() != null) styleIds.add(vo.getStyleId());
            if (vo.getBrandId() != null) brandIds.add(vo.getBrandId());
            if (vo.getTechnologyId() != null) technologyIds.add(vo.getTechnologyId());
            if (vo.getMainStoneId() != null) mainStoneIds.add(vo.getMainStoneId());
            if (vo.getSubStoneId() != null) subStoneIds.add(vo.getSubStoneId());
            if (vo.getAuditBy() != null) auditByIds.add(vo.getAuditBy());
        }

        // 查询旧料信息并创建映射
        Map<Integer, OldMaterialEntity> oldMaterialMap = new HashMap<>();
        if (!oldMaterialIds.isEmpty()) {
            List<OldMaterialEntity> oldMaterials = oldMaterialMapper.selectListByIds(oldMaterialIds);
            oldMaterialMap = oldMaterials.stream()
                    .collect(Collectors.toMap(entity -> entity.getId().intValue(), entity -> entity));
        }

        // 再次循环填充旧料信息到VO（这次循环是必要的，因为需要设置VO的属性）
        for (OldMaterialOrderDetailPageVO vo : records) {
            OldMaterialEntity oldMaterial = oldMaterialMap.get(vo.getOldMaterialId());
            if (oldMaterial != null) {
                vo.setOldMaterialSn(oldMaterial.getOldMaterialSn());
                vo.setOldMaterialName(oldMaterial.getName());
                vo.setWeight(oldMaterial.getWeight());
                vo.setNetGoldWeight(oldMaterial.getNetGoldWeight());
                vo.setNetSilverWeight(oldMaterial.getNetSilverWeight());
                vo.setRecyclePrice(BigDecimal.valueOf(oldMaterial.getRecyclePrice()));
                vo.setCategoryId(oldMaterial.getCategoryId());
                vo.setSubclassId(oldMaterial.getSubclassId());
                vo.setSupplierId(oldMaterial.getSupplierId());
                vo.setQualityId(oldMaterial.getQualityId());
                vo.setStyleId(oldMaterial.getStyleId());
                vo.setBrandId(oldMaterial.getBrandId());
                vo.setTechnologyId(oldMaterial.getTechnologyId());
                vo.setMainStoneId(oldMaterial.getMainStoneId());
                vo.setSubStoneId(oldMaterial.getSubStoneId());
                vo.setMainStoneCount(oldMaterial.getMainStoneCount());
                vo.setSubStoneCount(oldMaterial.getSubStoneCount());
                vo.setSubStoneWeight(oldMaterial.getSubStoneWeight());
                vo.setCircleSize(oldMaterial.getCircleSize());
                vo.setCertNo(oldMaterial.getCertNo());

                // 计算最大可用数量（当前明细数量 + 旧料库存数量）
                Integer currentNum = vo.getNum() != null ? vo.getNum() : 0;
                Integer stockNum = oldMaterial.getNum() != null ? oldMaterial.getNum() : 0;
                vo.setMaxNum(currentNum + stockNum);

                // 更新收集的ID（因为旧料信息可能覆盖了原有的ID）
                if (oldMaterial.getSupplierId() != null) supplierIds.add(oldMaterial.getSupplierId());
                if (oldMaterial.getCategoryId() != null) categoryIds.add(oldMaterial.getCategoryId());
                if (oldMaterial.getSubclassId() != null) subclassIds.add(oldMaterial.getSubclassId());
                if (oldMaterial.getQualityId() != null) qualityIds.add(oldMaterial.getQualityId());
                if (oldMaterial.getStyleId() != null) styleIds.add(oldMaterial.getStyleId());
                if (oldMaterial.getBrandId() != null) brandIds.add(oldMaterial.getBrandId());
                if (oldMaterial.getTechnologyId() != null) technologyIds.add(oldMaterial.getTechnologyId());
                if (oldMaterial.getMainStoneId() != null) mainStoneIds.add(oldMaterial.getMainStoneId());
                if (oldMaterial.getSubStoneId() != null) subStoneIds.add(oldMaterial.getSubStoneId());
            }
        }

        // 使用ListFillUtil填充关联数据
        ListFillUtil.of(records)
                .build(listFillService::getMerchantNameById, merchantIds, "merchantId", "merchantName")
                .build(listFillService::getSupplierNameById, supplierIds, "supplierId", "supplierName")
                .build(listFillService::getCategoryNameById, categoryIds, "categoryId", "categoryName")
                .build(listFillService::getSubclassNameById, subclassIds, "subclassId", "subclassName")
                .build(listFillService::getQualityNameById, qualityIds, "qualityId", "qualityName")
                .build(listFillService::getStyleNameById, styleIds, "styleId", "styleName")
                .build(listFillService::getBrandNameById, brandIds, "brandId", "brandName")
                .build(listFillService::getTechnologyNameById, technologyIds, "technologyId", "technologyName")
                .build(listFillService::getJewelryMapperNameById, mainStoneIds, "mainStoneId", "mainStoneName")
                .build(listFillService::getJewelryMapperNameById, subStoneIds, "subStoneId", "subStoneName")
                .build(listFillService::getUserNameByUserId, auditByIds, "auditBy", "auditByName")
                .build(listFillService::getOldMaterialImgByOldMaterialId, oldMaterialIds, "oldMaterialId", "images")
                .peek(obj -> {
                    OldMaterialOrderDetailPageVO vo = (OldMaterialOrderDetailPageVO) obj;
                    // 设置状态名称
                    vo.setStatusName(IBaseEnum.getLabelByValue(vo.getStatus(), OldMaterialOrderStatusEnum.class));
                    // 设置计价方式名称
                    vo.setSalesTypeName(vo.getSalesType() != null && vo.getSalesType().equals(1) ? "按重量" : "按数量");

                    // 价格转换（分转元）
                    if (vo.getGoldPrice() != null) {
                        vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
                    }
                    if (vo.getSilverPrice() != null) {
                        vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
                    }
                    if (vo.getPrice() != null) {
                        vo.setPrice(PriceUtil.fen2yuan(vo.getPrice()));
                    }
                    if (vo.getRecyclePrice() != null) {
                        vo.setRecyclePrice(PriceUtil.fen2yuan(vo.getRecyclePrice()));
                    }

                    // 计算销售金额（使用转换后的价格）
                    if (vo.getPrice() != null && vo.getNum() != null) {
                        vo.setTotalAmount(vo.getPrice().multiply(new BigDecimal(vo.getNum())));
                    }
                })
                .handle();
    }

    /**
     * 导出明细列表
     */
    private void exportOrderDetails(QueryWrapper wrapper, OldMaterialOrderDetailPageQuery query) {
        // 检查导出数量限制
        long count = oldMaterialOrderDetailMapper.selectCountByQuery(wrapper);
        CommonUtils.abortIf(count > CommonUtils.getMaxExportSize(), "导出数量超过限制");

        ExcelUtil.of(oldMaterialOrderDetailMapper, wrapper, OldMaterialOrderDetailPageVO.class, "old_material_order_detail", "旧料销售单明细")
                .getData((mapper, queryWrapper) -> {
                    List<OldMaterialOrderDetailPageVO> voList = oldMaterialOrderDetailMapper.selectListByQueryAs(wrapper, OldMaterialOrderDetailPageVO.class);
                    fillOrderDetailPageVOs(voList);

                    // 处理导出字段转换
                    for (OldMaterialOrderDetailPageVO vo : voList) {
                        // 替换状态为文本
                        if (vo.getStatus() instanceof Integer) {
                            Integer statusValue = (Integer) vo.getStatus();
                            vo.setStatus(IBaseEnum.getLabelByValue(statusValue, OldMaterialOrderStatusEnum.class));
                        }

                        // 替换计价方式为文本，直接覆盖原字段
                        if (vo.getSalesType() instanceof Integer) {
                            Integer salesTypeValue = (Integer) vo.getSalesType();
                            String salesTypeText = IBaseEnum.getLabelByValue(salesTypeValue, SalesTypeEnum.class);
                            vo.setSalesType(salesTypeText != null ? salesTypeText : vo.getSalesType());
                        }
                    }

                    // 处理图片字段，使用ColumnEncryptUtil.process进行加密处理，然后处理图片导出
                    List<JSONObject> processedList = (List<JSONObject>) ColumnEncryptUtil.process(voList);
                    // 处理图片字段，确保最终的value值保留一张图片
                    ColumnEncryptUtil.handleJsonImageExport(processedList);

                    return processedList;
                })
                .modifyConfig(config -> {
                    config.setTitle("旧料销售单明细");
                    config.setWidth(15);
                })
                .doExport();
    }

    /**
     * 打印明细列表
     */
    private Page<OldMaterialOrderDetailPageVO> printOrderDetails(QueryWrapper wrapper, OldMaterialOrderDetailPageQuery query) {
        // 检查打印数量限制
        long count = oldMaterialOrderDetailMapper.selectCountByQuery(wrapper);
        CommonUtils.abortIf(count > CommonUtils.getMaxPrintSize(), "打印数量超过限制");

        List<OldMaterialOrderDetailPageVO> voList = oldMaterialOrderDetailMapper.selectListByQueryAs(wrapper, OldMaterialOrderDetailPageVO.class);
        fillOrderDetailPageVOs(voList);

        // 处理打印字段转换
        for (OldMaterialOrderDetailPageVO vo : voList) {
            // 替换状态为文本
            if (vo.getStatus() instanceof Integer) {
                Integer statusValue = (Integer) vo.getStatus();
                vo.setStatus(IBaseEnum.getLabelByValue(statusValue, OldMaterialOrderStatusEnum.class));
            }

            // 替换计价方式为文本，直接覆盖原字段
            if (vo.getSalesType() instanceof Integer) {
                Integer salesTypeValue = (Integer) vo.getSalesType();
                String salesTypeText = IBaseEnum.getLabelByValue(salesTypeValue, SalesTypeEnum.class);
                vo.setSalesType(salesTypeText != null ? salesTypeText : vo.getSalesType());
            }
        }

        // 返回Page对象，pageNum=1, pageSize=voList.size(), total=count
        Page<OldMaterialOrderDetailPageVO> page = new Page<>(1, voList.size(), count);
        page.setRecords(voList);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOldMaterialOrderDetail(OldMaterialOrderDetailUpdateForm form) {
        // 1. 验证明细是否存在
        OldMaterialOrderDetailEntity detail = oldMaterialOrderDetailMapper.selectOneById(form.getId());
        CommonUtils.abortIf(detail == null, "销售单明细不存在");

        // 2. 验证用户权限
        validateDetailPermissions(detail);

        // 3. 验证销售单状态（只能编辑待审核的销售单明细）
        OldMaterialOrderEntity order = this.getById(detail.getOrderId());
        CommonUtils.abortIf(order == null, "销售单不存在");
        CommonUtils.abortIf(!order.getStatus().equals(0), "只能编辑待审核状态的销售单明细");

        // 4. 验证旧料库存是否充足
        validateStockForDetailUpdate(detail, form);

        // 5. 计算库存变化量
        Integer oldNum = detail.getNum();
        Integer newNum = form.getNum();
        Integer numChange = newNum - oldNum;

        // 6. 更新明细信息（价格从元转换为分）
        detail.setNum(form.getNum());
        detail.setSalesType(form.getSalesType());
        detail.setGoldPrice(form.getGoldPrice() != null ? PriceUtil.yuan2fenLong(form.getGoldPrice()) : null);
        detail.setSilverPrice(form.getSilverPrice() != null ? PriceUtil.yuan2fenLong(form.getSilverPrice()) : null);
        detail.setPrice(PriceUtil.yuan2fenLong(form.getPrice()));
        oldMaterialOrderDetailMapper.update(detail);

        // 7. 更新旧料库存（如果数量有变化）
        if (numChange != 0) {
            updateStockForDetailChange(detail.getOldMaterialId().longValue(), numChange);
        }

        // 8. 重新计算销售单汇总数据
        recalculateOrderSummary(order.getId());

        // 9. 记录操作日志
        OpLogUtils.appendOpLog("旧料销售单-编辑明细",
            "编辑旧料销售单明细: " + order.getOrderSn(),
            Map.of("销售单ID", order.getId(), "明细ID", detail.getId(), "旧数量", oldNum, "新数量", newNum));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOldMaterialOrderDetail(Long id) {
        // 1. 验证明细是否存在
        OldMaterialOrderDetailEntity detail = oldMaterialOrderDetailMapper.selectOneById(id);
        CommonUtils.abortIf(detail == null, "销售单明细不存在");

        // 2. 验证用户权限
        validateDetailPermissions(detail);

        // 3. 验证销售单状态（只能删除待审核的销售单明细）
        OldMaterialOrderEntity order = this.getById(detail.getOrderId());
        CommonUtils.abortIf(order == null, "销售单不存在");
        CommonUtils.abortIf(!order.getStatus().equals(0), "只能删除待审核状态的销售单明细");

        // 4. 检查是否是最后一条明细
        Long detailCount = oldMaterialOrderDetailMapper.selectCountByQuery(
            QueryWrapper.create()
                .where(column(OldMaterialOrderDetailEntity::getOrderId).eq(detail.getOrderId()))
                .and(column(OldMaterialOrderDetailEntity::getCompanyId).eq(detail.getCompanyId()))
        );
        CommonUtils.abortIf(detailCount <= 1, "该销售单下已无其他明细，无法删除");

        // 5. 恢复旧料库存（增加库存数量，减少冻结数量）
        restoreStockForDetailDelete(detail);

        // 6. 删除明细记录
        oldMaterialOrderDetailMapper.deleteById(id);

        // 7. 重新计算销售单汇总数据
        recalculateOrderSummary(order.getId());

        // 8. 记录操作日志
        OpLogUtils.appendOpLog("旧料销售单-删除明细",
            "删除旧料销售单明细: " + order.getOrderSn(),
            Map.of("销售单ID", order.getId(), "明细ID", detail.getId(), "旧料ID", detail.getOldMaterialId(), "数量", detail.getNum()));
    }

    /**
     * 验证明细权限
     */
    private void validateDetailPermissions(OldMaterialOrderDetailEntity detail) {
        // 验证商户权限
        CommonUtils.abortIf(!detail.getCompanyId().equals(SecurityUtils.getCompanyId().intValue()),
            "无权操作其他商户的数据");

        // 验证门店权限（非主账号需要验证门店权限）
        if (!SecurityUtils.isMain()) {
            Set<Long> merchantIds = SecurityUtils.getMerchantIds();
            CommonUtils.abortIf(merchantIds == null || !merchantIds.contains(detail.getMerchantId().longValue()),
                "无权操作其他门店的销售单明细");
        }
    }

    /**
     * 验证明细更新时的库存
     */
    private void validateStockForDetailUpdate(OldMaterialOrderDetailEntity detail, OldMaterialOrderDetailUpdateForm form) {
        // 查询旧料当前库存
        OldMaterialEntity oldMaterial = oldMaterialMapper.selectOneById(detail.getOldMaterialId());
        CommonUtils.abortIf(oldMaterial == null, "旧料不存在");

        // 计算数量变化
        Integer oldNum = detail.getNum();
        Integer newNum = form.getNum();
        Integer numChange = newNum - oldNum;

        // 如果增加数量，需要验证库存是否充足
        if (numChange > 0) {
            CommonUtils.abortIf(oldMaterial.getNum() < numChange,
                String.format("旧料[%s]库存不足，当前库存：%d，需要增加：%d",
                    oldMaterial.getName(), oldMaterial.getNum(), numChange));
        }
    }

    /**
     * 更新明细变化时的库存
     */
    private void updateStockForDetailChange(Long oldMaterialId, Integer numChange) {
        OldMaterialStockNumChangeBO stockChange = OldMaterialStockNumChangeBO.builder()
            .oldMaterialId(oldMaterialId)
            .comment("编辑旧料销售单明细")
            .num(-numChange) // 库存变化与明细数量变化相反
            .frozenNum(numChange) // 冻结数量变化与明细数量变化相同
            .build();

        StockUtils.updateOldMaterialStocks(List.of(stockChange));
    }

    /**
     * 恢复明细删除时的库存
     */
    private void restoreStockForDetailDelete(OldMaterialOrderDetailEntity detail) {
        OldMaterialStockNumChangeBO stockChange = OldMaterialStockNumChangeBO.builder()
            .oldMaterialId(detail.getOldMaterialId().longValue())
            .comment("删除旧料销售单明细")
            .num(detail.getNum()) // 恢复库存数量
            .frozenNum(-detail.getNum()) // 减少冻结数量
            .build();

        StockUtils.updateOldMaterialStocks(List.of(stockChange));
    }

    /**
     * 重新计算销售单汇总数据
     */
    private void recalculateOrderSummary(Long orderId) {
        // 查询所有明细
        List<OldMaterialOrderDetailEntity> details = oldMaterialOrderDetailMapper.selectListByQuery(
            QueryWrapper.create()
                .where(column(OldMaterialOrderDetailEntity::getOrderId).eq(orderId))
        );

        if (details.isEmpty()) {
            return;
        }

        // 查询相关旧料信息
        Set<Integer> oldMaterialIds = details.stream()
            .map(OldMaterialOrderDetailEntity::getOldMaterialId)
            .collect(Collectors.toSet());

        List<OldMaterialEntity> oldMaterials = oldMaterialMapper.selectListByQuery(
            QueryWrapper.create()
                .where(column(OldMaterialEntity::getId).in(oldMaterialIds))
        );

        Map<Integer, OldMaterialEntity> oldMaterialMap = oldMaterials.stream()
            .collect(Collectors.toMap(entity -> entity.getId().intValue(), entity -> entity));

        // 计算汇总数据
        OrderSummary summary = new OrderSummary();
        for (OldMaterialOrderDetailEntity detail : details) {
            OldMaterialEntity oldMaterial = oldMaterialMap.get(detail.getOldMaterialId());
            if (oldMaterial != null) {
                summary.totalNum += detail.getNum();
                if (oldMaterial.getWeight() != null) {
                    summary.totalWeight = summary.totalWeight.add(oldMaterial.getWeight().multiply(new BigDecimal(detail.getNum())));
                }
                if (oldMaterial.getNetGoldWeight() != null) {
                    summary.totalNetGoldWeight = summary.totalNetGoldWeight.add(oldMaterial.getNetGoldWeight().multiply(new BigDecimal(detail.getNum())));
                }
                if (oldMaterial.getNetSilverWeight() != null) {
                    summary.totalNetSilverWeight = summary.totalNetSilverWeight.add(oldMaterial.getNetSilverWeight().multiply(new BigDecimal(detail.getNum())));
                }
                if (detail.getPrice() != null) {
                    summary.totalAmount += detail.getPrice() * detail.getNum();
                }
            }
        }

        // 更新销售单汇总数据
        OldMaterialOrderEntity order = new OldMaterialOrderEntity();
        order.setId(orderId);
        order.setNum(summary.totalNum);
        order.setTotalWeight(summary.totalWeight);
        order.setTotalNetGoldWeight(summary.totalNetGoldWeight);
        order.setTotalNetSilverWeight(summary.totalNetSilverWeight);
        order.setTotalAmount(summary.totalAmount);
        this.updateById(order);
    }
}
