package com.xc.boot.modules.order.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.order.model.entity.MaterialRecycleEntity;
import com.xc.boot.modules.order.model.form.MaterialRecycleForm;
import com.xc.boot.modules.order.model.form.MaterialRecycleUpdateForm;
import com.xc.boot.modules.order.model.query.MaterialRecycleQuery;
import com.xc.boot.modules.order.model.vo.MaterialRecycleDetailVO;
import com.xc.boot.modules.order.model.vo.MaterialRecyclePageVo;

import java.util.List;

/**
 * 回收单服务接口
 */
public interface MaterialRecycleService extends IService<MaterialRecycleEntity> {

    /**
     * 回收单分页列表
     */
    Page<MaterialRecyclePageVo> page(MaterialRecycleQuery query);

    /**
     * 获取回收单详情
     */
    MaterialRecyclePageVo getRecycleDetail(Integer id);

    /**
     * 获取回收单明细列表
     */
    List<MaterialRecycleDetailVO> getRecycleDetailList(Integer recycleId);

    /**
     * 创建回收单
     */
    void createRecycle(MaterialRecycleForm form);

    /**
     * 更新回收单
     */
    void updateRecycle(MaterialRecycleUpdateForm form);
} 