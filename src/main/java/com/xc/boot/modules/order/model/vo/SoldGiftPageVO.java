package com.xc.boot.modules.order.model.vo;

import com.xc.boot.modules.gift.model.entity.GiftHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 销售开单-赠品查询分页VO
 */
@Data
@Schema(description = "销售开单-赠品查询分页VO")
public class SoldGiftPageVO {

    @Schema(description = "赠品ID")
    private Long id;

    @Schema(description = "所属门店ID")
    private Integer merchantId;

    @Schema(description = "门店名称")
    private String merchantName;

    @Schema(description = "供应商ID")
    private Integer supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "赠品编号")
    private String giftSn;

    @Schema(description = "赠品名称")
    private String name;

    @Schema(description = "原始数量")
    private Integer num;

    @Schema(description = "库存数量")
    private Integer stockNum;

    @Schema(description = "售出数量")
    private Integer soldNum;

    @Schema(description = "调拨中数量")
    private Integer transferNum;

    @Schema(description = "冻结数量")
    private Integer frozenNum;

    @Schema(description = "重量(g)")
    private BigDecimal weight;

    @Schema(description = "成本单价(元)")
    private BigDecimal costPrice;

    @Schema(description = "标签单价(元)")
    private BigDecimal tagPrice;

    @Schema(description = "赠品图片列表")
    private List<GiftHasImagesEntity> images = new ArrayList<>();
}
