package com.xc.boot.modules.order.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售单创建请求DTO
 */
@Data
@Accessors(chain = true)
@Schema(description = "销售单创建请求")
public class SoldReceiptCreateDTO {

    @Schema(description = "销售单ID(取单时必填)", example = "1")
    private Long soldReceiptId;

    @Schema(description = "门店ID", required = true, example = "1")
    @NotNull(message = "门店ID不能为空")
    private Long merchantId;

    @Schema(description = "会员ID", example = "1")
    private Long memberId;

    @Schema(description = "收银员ID", required = true, example = "1")
    @NotNull(message = "收银员ID不能为空")
    private Long cashierId;

    @Schema(description = "主销导购ID", required = true, example = "1")
    @NotNull(message = "主销导购ID不能为空")
    private Long mainSellerId;

    @Schema(description = "辅销导购ID", example = "2")
    private Long supportSellerId;

    @Schema(description = "销售时间", example = "2023-01-01 12:00:00")
    private String datetime;

    @Schema(description = "抹零金额(元)", example = "0.50")
    private BigDecimal adjustAmount;

    @Schema(description = "备注", example = "销售备注")
    private String remark;

    @Schema(description = "货品列表", required = true)
    @NotEmpty(message = "货品列表不能为空")
    @Valid
    private List<SoldGoodsItemDTO> goodsList;

    @Schema(description = "旧料列表")
    @Valid
    private List<SoldOldMaterialItemDTO> oldMaterialList;

    @Schema(description = "赠品列表")
    @Valid
    private List<SoldGiftItemDTO> giftList;

    @Schema(description = "支付信息列表", required = true)
    @NotEmpty(message = "支付信息列表不能为空")
    @Valid
    private List<SoldPaymentItemDTO> paymentList;

    /**
     * 货品明细DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "货品明细")
    public static class SoldGoodsItemDTO {

        @Schema(description = "货品明细ID(取单时存在则更新，不存在则新增)", example = "1")
        private Long goodsDetailId;

        @Schema(description = "货品ID", required = true, example = "1")
        @NotNull(message = "货品ID不能为空")
        private Long goodsId;

        @Schema(description = "数量", required = true, example = "1")
        @NotNull(message = "数量不能为空")
        private Integer num;

        @Schema(description = "金价(元)", example = "500.00")
        private BigDecimal goldPrice;

        @Schema(description = "银价(元)", example = "10.00")
        private BigDecimal silverPrice;

        @Schema(description = "标签单价(元)", required = true, example = "1000.00")
        @NotNull(message = "标签单价不能为空")
        private BigDecimal tagPrice;

        @Schema(description = "工费单价(元)", example = "100.00")
        private BigDecimal saleWorkPrice;

        @Schema(description = "销工费计价方式(1:按重量,2:按数量)", example = "2")
        private Integer saleWorkPriceType;

        @Schema(description = "员工折扣(百分比)", example = "95")
        private BigDecimal staffDiscount;

        @Schema(description = "工费折扣(百分比)", example = "90")
        private BigDecimal workPriceDiscount;
    }

    /**
     * 旧料明细DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "旧料明细")
    public static class SoldOldMaterialItemDTO {

        @Schema(description = "旧料明细ID(取单时存在则更新，不存在则新增)", example = "1")
        private Long oldMaterialDetailId;

        @Schema(description = "旧料ID(取单时必填)", example = "1")
        private Long oldMaterialId;

        @Schema(description = "货品ID(可以为0)", example = "0")
        private Long goodsId;

        @Schema(description = "所属大类", required = true, example = "1")
        @NotNull(message = "所属大类不能为空")
        private Integer categoryId;

        @Schema(description = "货品小类", required = true, example = "1")
        @NotNull(message = "货品小类不能为空")
        private Integer subclassId;

        @Schema(description = "成色", required = true, example = "1")
        private Integer qualityId;

        @Schema(description = "旧料名称", required = true, example = "黄金戒指")
        @NotNull(message = "旧料名称不能为空")
        private String name;

        @Schema(description = "计价方式(1:按重量,2:按数量)", required = true, example = "1")
        @NotNull(message = "计价方式不能为空")
        private Integer salesType;

        @Schema(description = "数量", required = true, example = "1")
        @NotNull(message = "数量不能为空")
        private Integer num;

        @Schema(description = "回收金价(元)", example = "450.00")
        private BigDecimal goldPrice;

        @Schema(description = "回收银价(元)", example = "8.00")
        private BigDecimal silverPrice;

        @Schema(description = "回收单价(元)", required = true, example = "500.00")
        @NotNull(message = "回收单价不能为空")
        private BigDecimal recyclePrice;

        @Schema(description = "净金重(g)", example = "10.5")
        private BigDecimal netGoldWeight;

        @Schema(description = "净银重(g)", example = "2.3")
        private BigDecimal netSilverWeight;

        @Schema(description = "工费单价(元)", example = "50.00")
        private BigDecimal saleWorkPrice;

        @Schema(description = "旧料销售单号")
        private String soldReceiptSn;
    }

    /**
     * 赠品明细DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "赠品明细")
    public static class SoldGiftItemDTO {

        @Schema(description = "赠品明细ID(取单时存在则更新，不存在则新增)", example = "1")
        private Long giftDetailId;

        @Schema(description = "赠品ID", required = true, example = "1")
        @NotNull(message = "赠品ID不能为空")
        private Long giftId;

        @Schema(description = "数量", required = true, example = "1")
        @NotNull(message = "数量不能为空")
        private Integer num;

        @Schema(description = "销售单价(元)", required = true, example = "50.00")
        @NotNull(message = "销售单价不能为空")
        private BigDecimal soldPrice;
    }

    /**
     * 支付信息DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "支付信息")
    public static class SoldPaymentItemDTO {

        @Schema(description = "支付信息ID(取单时存在则更新，不存在则新增)", example = "1")
        private Long paymentId;

        @Schema(description = "支付类型(0:现金,1:支付宝,2:微信,3:刷卡)", required = true, example = "0")
        @NotNull(message = "支付类型不能为空")
        private Integer type;

        @Schema(description = "金额(元)", required = true, example = "1000.00")
        @NotNull(message = "金额不能为空")
        private BigDecimal amount;
    }
}
