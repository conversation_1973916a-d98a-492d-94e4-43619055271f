package com.xc.boot.modules.order.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 回收单查询条件
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "回收单查询条件")
public class MaterialRecycleQuery extends BasePageQuery {

    @Schema(description = "所属门店ID(英文逗号分割)")
    private String merchantIds;

    @Schema(description = "回收单号")
    private String recycleCode;

    @Schema(description = "主导购ID(英文逗号分割)")
    private String mainAdviserIds;

    @Schema(description = "辅购ID(英文逗号分割)")
    private String subAdviserIds;

    @Schema(description = "收银员ID(英文逗号分割)")
    private String cashierIds;

    @Schema(description = "会员ID(英文逗号分割)")
    private String memberIds;

    @Schema(description = "创建人ID(英文逗号分割)")
    private String createdByIds;

    @Schema(description = "创建时间范围")
    private Date[] timeRange;

    @Schema(description = "最小回收金额(元)")
    private BigDecimal minTotalPrice;

    @Schema(description = "最大回收金额(元)")
    private BigDecimal maxTotalPrice;

    @Schema(description = "支付方式(0:现金,1:支付宝,2:微信,3:刷卡)")
    private Integer payType;
} 