package com.xc.boot.modules.order.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 销售单挂单分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "销售单挂单分页查询对象")
public class SoldReceiptPendingPageQuery extends BasePageQuery {
    
    @Schema(description = "所属门店ID(英文逗号分割)")
    private String merchantIds;
    
    @Schema(description = "订单号")
    private String receiptSn;
    
    @Schema(description = "会员ID(英文逗号分割)")
    private String memberIds;
    
    @Schema(description = "主销导购ID(英文逗号分割)")
    private String mainSellerIds;
    
    @Schema(description = "辅销导购ID(英文逗号分割)")
    private String supportSellerIds;
    
    @Schema(description = "挂单时间范围")
    private Date[] pendingTimeRange;
}
