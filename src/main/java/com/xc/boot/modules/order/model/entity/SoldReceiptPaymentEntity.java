package com.xc.boot.modules.order.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 销售单支付明细
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Table(value = "sold_receipt_payment", comment = "销售单支付明细")
public class SoldReceiptPaymentEntity extends BaseEntity {

    /**
     * 销售单ID
     */
    @Column("sold_receipt_id")
    private Long soldReceiptId;

    /**
     * 支付方式(0:现金,1:支付宝,2:微信,3:刷卡)
     */
    @Column("type")
    private Integer type;

    /**
     * 金额(分)
     */
    @Column("amount")
    private Long amount;

}
