package com.xc.boot.modules.order.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * 销售单挂单分页VO
 */
@Data
@Schema(description = "销售单挂单分页VO")
public class SoldReceiptPendingPageVO {
    
    @Schema(description = "销售单ID")
    private Long id;
    
    @Schema(description = "所属门店ID")
    private Long merchantId;
    
    @Schema(description = "所属门店名称")
    private String merchantName;
    
    @Schema(description = "订单号")
    private String receiptSn;
    
    @Schema(description = "会员ID")
    private Long memberId;
    
    @Schema(description = "会员名称")
    private String memberName;
    
    @Schema(description = "主销导购ID")
    private Long mainSellerId;
    
    @Schema(description = "主销导购名称")
    private String mainSellerName;
    
    @Schema(description = "辅销导购ID")
    private Long supportSellerId;
    
    @Schema(description = "辅销导购名称")
    private String supportSellerName;
    
    @Schema(description = "货品数量")
    private Integer num;
    
    @Schema(description = "订单金额(元)")
    private BigDecimal amount;
    
    @Schema(description = "已收订金(元)")
    private BigDecimal receivedAmount;
    
    @Schema(description = "挂单时间")
    private Date pendingAt;
    
    @Schema(description = "备注")
    private String remark;

    /**
     * 挂单天数
     * 使用挂单时间与当前时间计算
     */
    @Schema(description = "挂单天数")
    public Long getPendingDays() {
        if (pendingAt == null) {
            return 0L;
        }

        LocalDateTime pendingTime = pendingAt.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        LocalDateTime now = LocalDateTime.now();

        return ChronoUnit.DAYS.between(pendingTime, now);
    }
}
