package com.xc.boot.modules.order.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 销售单明细类型枚举
 */
@Getter
@AllArgsConstructor
public enum SoldReceiptDetailType implements IBaseEnum<Integer> {

    /**
     * 货品
     */
    GOODS(1, "货品"),

    /**
     * 旧料
     */
    OLD_MATERIAL(2, "旧料"),

    /**
     * 赠品
     */
    GIFT(3, "赠品");

    /**
     * 类型值
     */
    private final Integer value;

    /**
     * 类型描述
     */
    private final String label;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
