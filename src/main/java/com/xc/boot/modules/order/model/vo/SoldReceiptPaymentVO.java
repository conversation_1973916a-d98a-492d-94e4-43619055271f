package com.xc.boot.modules.order.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 销售单支付记录VO
 */
@Data
@Schema(description = "销售单支付记录VO")
public class SoldReceiptPaymentVO {

    @Schema(description = "支付记录ID")
    private Long paymentId;

    @Schema(description = "销售单ID")
    private Long soldReceiptId;

    @Schema(description = "支付方式(0:现金,1:支付宝,2:微信,3:刷卡)")
    private Integer type;
    
    @Schema(description = "支付方式名称")
    private String typeName;

    @Schema(description = "金额(元)")
    private BigDecimal amount;
}
