package com.xc.boot.modules.order.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 销售单完整详情VO
 */
@Data
@Schema(description = "销售单完整详情VO")
public class SoldReceiptFullDetailVO {

    // ========== 销售单基本信息 ==========
    @Schema(description = "销售单ID")
    private Long id;
    
    @Schema(description = "订单号")
    private String receiptSn;
    
    @Schema(description = "所属门店ID")
    private Long merchantId;
    
    @Schema(description = "所属门店名称")
    private String merchantName;
    
    @Schema(description = "门店电话")
    private String merchantPhone;
    
    @Schema(description = "门店联系地址")
    private String merchantAddress;
    
    @Schema(description = "会员ID")
    private Long memberId;
    
    @Schema(description = "会员姓名")
    private String memberName;
    
    @Schema(description = "会员手机号")
    private String memberPhone;
    
    @Schema(description = "订单状态")
    private Integer status;
    
    @Schema(description = "订单状态名称")
    private String statusName;
    
    @Schema(description = "主销导购ID")
    private Long mainSellerId;
    
    @Schema(description = "主销导购姓名")
    private String mainSellerName;
    
    @Schema(description = "辅销导购ID")
    private Long supportSellerId;
    
    @Schema(description = "辅销导购姓名")
    private String supportSellerName;
    
    @Schema(description = "收银员ID")
    private Long cashierId;
    
    @Schema(description = "收银员姓名")
    private String cashierName;
    
    @Schema(description = "商品数量")
    private Integer num;
    
    @Schema(description = "总重量(g)")
    private BigDecimal weight;
    
    @Schema(description = "净金重(g)")
    private BigDecimal goldWeight;
    
    @Schema(description = "净银重(g)")
    private BigDecimal silverWeight;
    
    @Schema(description = "订单金额(元)")
    private BigDecimal amount;
    
    @Schema(description = "支付方式")
    private String paymentMethods;
    
    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "销售时间")
    private Date soldAt;
    
    @Schema(description = "备注")
    private String remark;
    
    @Schema(description = "货品金额(元)")
    private BigDecimal goodsAmount;
    
    @Schema(description = "优惠金额(元)")
    private BigDecimal discountAmount;
    
    @Schema(description = "旧料抵扣(元)")
    private BigDecimal deductionAmount;
    
    @Schema(description = "赠品金额(元)")
    private BigDecimal giftAmount;
    
    @Schema(description = "抹零金额(元)")
    private BigDecimal adjustAmount;
    
    @Schema(description = "实付总额(元)")
    private BigDecimal paidAmount;

    // ========== 销售明细 ==========
    @Schema(description = "货品列表")
    private List<SoldReceiptGoodsDetailVO> goodsList = new ArrayList<>();
    
    @Schema(description = "旧料列表")
    private List<SoldReceiptOldMaterialDetailVO> oldMaterialList = new ArrayList<>();
    
    @Schema(description = "赠品列表")
    private List<SoldReceiptGiftDetailVO> giftList = new ArrayList<>();
    
    @Schema(description = "支付记录")
    private List<SoldReceiptPaymentDetailVO> paymentList = new ArrayList<>();

    /**
     * 货品明细VO
     */
    @Data
    @Schema(description = "货品明细VO")
    public static class SoldReceiptGoodsDetailVO {

        @Schema(description = "货品明细ID")
        private Long goodsDetailId;

        @Schema(description = "货品ID")
        private Long goodsId;

        @Schema(description = "货品条码")
        private String goodsSn;
        
        @Schema(description = "货品名称")
        private String goodsName;
        
        @Schema(description = "货品图片列表")
        private List<com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity> images = new ArrayList<>();
        
        @Schema(description = "所属大类")
        private String categoryName;
        
        @Schema(description = "货品小类")
        private String subclassName;
        
        @Schema(description = "成色")
        private String qualityName;

        @Schema(description = "圈口")
        private String circleSize;

        @Schema(description = "工艺ID")
        private Long technologyId;

        @Schema(description = "工艺名称")
        private String technologyName;

        @Schema(description = "大类ID")
        private Long categoryId;

        @Schema(description = "小类ID")
        private Long subclassId;

        @Schema(description = "成色ID")
        private Long qualityId;

        @Schema(description = "净金重(g)")
        private BigDecimal goldWeight;
        
        @Schema(description = "净银重(g)")
        private BigDecimal silverWeight;

        @Schema(description = "金重(g) 净金重 * 数量")
        private BigDecimal totalGoldWeight;

        @Schema(description = "银重(g) 净银重 * 数量")
        private BigDecimal totalSilverWeight;

        @Schema(description = "总金价(元) 金重 * 金价")
        private BigDecimal totalGoldPrice;

        @Schema(description = "总银价(元) 银重 * 银价")
        private BigDecimal totalSilverPrice;

        @Schema(description = "总重量(g) 重量 * 数量")
        private BigDecimal weight;
        
        @Schema(description = "销售方式")
        private String saleTypeName;
        
        @Schema(description = "数量")
        private Integer num;
        
        @Schema(description = "金价(元)")
        private BigDecimal goldPrice;
        
        @Schema(description = "银价(元)")
        private BigDecimal silverPrice;

        @Schema(description = "销售金价(元)")
        private BigDecimal saleGoldPrice;

        @Schema(description = "销售银价(元)")
        private BigDecimal saleSilverPrice;

        @Schema(description = "销售单价(元)")
        private BigDecimal soldPrice;

        @Schema(description = "标签单价(元)")
        private BigDecimal tagPrice;

        @Schema(description = "工费单价(元)")
        private BigDecimal saleWorkPrice;

        @Schema(description = "销工费计价方式(1:按重量,2:按数量)")
        private Integer saleWorkPriceType;

        @Schema(description = "净金重(g)")
        private BigDecimal netGoldWeight;

        @Schema(description = "净银重(g)")
        private BigDecimal netSilverWeight;

        @Schema(description = "工费(元)")
        private BigDecimal workPrice;
        
        @Schema(description = "销售价(元)")
        private BigDecimal totalPrice;

        @Schema(description = "应收金额(元)")
        private BigDecimal realAmount;
        
        @Schema(description = "员工折扣(%)")
        private BigDecimal staffDiscount;

        @Schema(description = "员工折扣优惠金额(元)")
        private BigDecimal discountAmount;

        @Schema(description = "工费折扣(%)")
        private BigDecimal workPriceDiscount;

        @Schema(description = "工费折扣优惠金额(元)")
        private BigDecimal workPriceDiscountAmount;

        @Schema(description = "总优惠金额(元)")
        private BigDecimal totalDiscountAmount;
    }

    /**
     * 旧料明细VO
     */
    @Data
    @Schema(description = "旧料明细VO")
    public static class SoldReceiptOldMaterialDetailVO {

        @Schema(description = "旧料明细ID")
        private Long oldMaterialDetailId;

        @Schema(description = "旧料ID")
        private Long oldMaterialId;

        @Schema(description = "旧料编号")
        private String oldMaterialSn;
        
        @Schema(description = "旧料名称")
        private String oldMaterialName;
        
        @Schema(description = "旧料图片列表")
        private List<com.xc.boot.modules.oldmaterial.model.entity.OldMaterialHasImagesEntity> images = new ArrayList<>();

        @Schema(description = "分类ID")
        private Long categoryId;

        @Schema(description = "小类ID")
        private Long subclassId;

        @Schema(description = "成色ID")
        private Long qualityId;

        @Schema(description = "所属大类")
        private String categoryName;

        @Schema(description = "货品小类")
        private String subclassName;

        @Schema(description = "成色")
        private String qualityName;
        
        @Schema(description = "数量")
        private Integer num;

        @Schema(description = "总重量 重量 * 数量")
        private BigDecimal weight;

        @Schema(description = "净金重(g)")
        private BigDecimal netGoldWeight;
        
        @Schema(description = "净银重(g)")
        private BigDecimal netSilverWeight;

        @Schema(description = "计价方式(1:按重量,2:按数量)")
        private Integer salesType;

        @Schema(description = "计价方式")
        private String pricingMethodName;
        
        @Schema(description = "回收金价(元)")
        private BigDecimal goldPrice;
        
        @Schema(description = "回收银价(元)")
        private BigDecimal silverPrice;
        
        @Schema(description = "回收单价(元)")
        private BigDecimal recyclePrice;
        
        @Schema(description = "回收金额(元)")
        private BigDecimal totalAmount;

        @Schema(description = "工费单价(元)")
        private BigDecimal saleWorkPrice;

        @Schema(description = "销售单sn")
        private String soldReceiptSn;
    }

    /**
     * 赠品明细VO
     */
    @Data
    @Schema(description = "赠品明细VO")
    public static class SoldReceiptGiftDetailVO {

        @Schema(description = "赠品明细ID")
        private Long giftDetailId;

        @Schema(description = "赠品ID")
        private Long giftId;

        @Schema(description = "赠品编号")
        private String giftSn;
        
        @Schema(description = "赠品名称")
        private String giftName;
        
        @Schema(description = "赠品图片列表")
        private List<com.xc.boot.modules.gift.model.entity.GiftHasImagesEntity> images = new ArrayList<>();
        
        @Schema(description = "数量")
        private Integer num;
        
        @Schema(description = "赠品价值(元)")
        private BigDecimal tagPrice;
        
        @Schema(description = "销售单价(元)")
        private BigDecimal soldPrice;
        
        @Schema(description = "销售金额(元)")
        private BigDecimal totalAmount;
    }

    /**
     * 支付记录VO
     */
    @Data
    @Schema(description = "支付记录VO")
    public static class SoldReceiptPaymentDetailVO {
        
        @Schema(description = "支付方式")
        private String paymentTypeName;
        
        @Schema(description = "支付金额(元)")
        private BigDecimal amount;
    }
}
