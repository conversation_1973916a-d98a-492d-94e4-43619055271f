package com.xc.boot.modules.order.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 销售单查询分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "销售单查询分页查询对象")
public class SoldReceiptPageQuery extends BasePageQuery {
    
    @Schema(description = "销售单号")
    private String receiptSn;

    @Schema(description = "货品条码")
    private String goodsSn;

    @Schema(description = "会员ID(英文逗号分割)")
    private String memberIds;

    @Schema(description = "关键词搜索(支持销售单号或货品条码)")
    private String keywords;
}
