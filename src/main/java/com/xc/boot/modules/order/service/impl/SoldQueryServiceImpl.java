package com.xc.boot.modules.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.listFill.ListFillService;

import com.xc.boot.common.util.listFill.ListFillUtilV2;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.gift.mapper.GiftMapper;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptGoodsMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptGiftMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptOldMaterialMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptPaymentMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptMapper;
import com.xc.boot.modules.order.model.query.SoldGiftPageQuery;
import com.xc.boot.modules.order.model.query.SoldGoodsPageQuery;

import com.xc.boot.modules.order.model.query.SoldReceiptPageQuery;
import com.xc.boot.modules.order.model.vo.SoldGiftPageVO;
import com.xc.boot.modules.order.model.vo.SoldGoodsPageVO;
import com.xc.boot.modules.order.model.vo.SoldReceiptDetailVO;
import com.xc.boot.modules.order.model.vo.SoldReceiptFullDetailVO;
import com.xc.boot.modules.order.model.vo.SoldReceiptGoodsVO;
import com.xc.boot.modules.order.model.vo.SoldReceiptGiftVO;

import com.xc.boot.modules.order.model.vo.SoldReceiptOldMaterialVO;
import com.xc.boot.modules.order.model.vo.SoldReceiptPageVO;
import com.xc.boot.modules.order.model.vo.SoldReceiptPaymentVO;
import com.xc.boot.modules.order.model.enums.SalesTypeEnum;
import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.modules.order.service.SoldQueryService;
import com.xc.boot.modules.order.model.enums.SoldReceiptStatusEnum;
import com.xc.boot.modules.order.model.enums.PayTypeEnum;

import com.xc.boot.modules.merchant.mapper.GoldPriceMapper;
import com.xc.boot.modules.merchant.model.vo.QualityGoldPriceVo;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.common.util.CommonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;

import java.util.HashMap;
import java.util.HashSet;


import java.util.List;
import java.util.Map;

import java.util.Set;
import java.util.stream.Collectors;



import static com.xc.boot.modules.gift.model.entity.table.GiftTableDef.GIFT;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptTableDef.SOLD_RECEIPT;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptGoodsTableDef.SOLD_RECEIPT_GOODS;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptGiftTableDef.SOLD_RECEIPT_GIFT;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptOldMaterialTableDef.SOLD_RECEIPT_OLD_MATERIAL;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptPaymentTableDef.SOLD_RECEIPT_PAYMENT;
import static com.xc.boot.modules.oldmaterial.model.entity.table.OldMaterialTableDef.OLD_MATERIAL;


import static com.xc.boot.modules.merchant.model.entity.table.GoldPriceTableDef.GOLD_PRICE;

/**
 * 销售开单查询服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SoldQueryServiceImpl implements SoldQueryService {

    private final GoodsMapper goodsMapper;
    private final GiftMapper giftMapper;
    private final SoldReceiptMapper soldReceiptMapper;
    private final SoldReceiptGoodsMapper soldReceiptGoodsMapper;
    private final SoldReceiptGiftMapper soldReceiptGiftMapper;
    private final SoldReceiptOldMaterialMapper soldReceiptOldMaterialMapper;
    private final SoldReceiptPaymentMapper soldReceiptPaymentMapper;
    private final ListFillService listFillService;
    private final GoldPriceMapper goldPriceMapper;
    
    @Override
    public Page<SoldGoodsPageVO> goodsPage(SoldGoodsPageQuery query) {
        QueryWrapper wrapper = buildWrapper(query);
        wrapper.orderBy(GOODS.ID, false);

        Page<SoldGoodsPageVO> page = goodsMapper.paginateAs(
            query.getPageNum(),
            query.getPageSize(),
            wrapper,
            SoldGoodsPageVO.class
        );

        List<SoldGoodsPageVO> records = page.getRecords();
        if (records.isEmpty()) {
            return page;
        }

        // 填充关联数据和自定义字段
        fillList(records);

        return page;
    }
    
    /**
     * 构建查询条件
     */
    private QueryWrapper buildWrapper(SoldGoodsPageQuery query) {
        // 固定条件：只查询在库货品
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS.NUM.gt(0))
                .where(GOODS.MERCHANT_ID.eq(query.getMerchantId()))
                .where(GOODS.STOCK_NUM.gt(0)); // 只查询有库存的货品
        
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        merchantIds.add(0L);
        // 非主账号限制门店
        queryWrapper.where(GOODS.MERCHANT_ID.in(merchantIds, !SecurityUtils.isMain()));
        
        // 查询条件
        if (StringUtils.isNotBlank(query.getGoodsSn())) {
            queryWrapper.where(GOODS.GOODS_SN.eq(query.getGoodsSn()));
        }
        
        if (StringUtils.isNotBlank(query.getName())) {
            queryWrapper.where(GOODS.NAME.like(query.getName()));
        }
        
        if (StringUtils.isNotBlank(query.getCounterIds())) {
            queryWrapper.where(GOODS.COUNTER_ID.in(List.of(query.getCounterIds().split(","))));
        }
        
        if (StringUtils.isNotBlank(query.getCategoryIds())) {
            queryWrapper.where(GOODS.CATEGORY_ID.in(List.of(query.getCategoryIds().split(","))));
        }
        
        if (StringUtils.isNotBlank(query.getSubclassIds())) {
            queryWrapper.where(GOODS.SUBCLASS_ID.in(List.of(query.getSubclassIds().split(","))));
        }
        
        if (StringUtils.isNotBlank(query.getQualityIds())) {
            queryWrapper.where(GOODS.QUALITY_ID.in(List.of(query.getQualityIds().split(","))));
        }
        
        if (StringUtils.isNotBlank(query.getStyleIds())) {
            queryWrapper.where(GOODS.STYLE_ID.in(List.of(query.getStyleIds().split(","))));
        }
        
        return queryWrapper;
    }
    
    /**
     * 填充列表数据
     */
    private void fillList(List<SoldGoodsPageVO> list) {
        // 获取金价银价Map
        Map<String, BigDecimal[]> goldSilverPriceMap = getGoldSilverPriceMap();
        
        ListFillUtilV2.of(list)
                .build(listFillService::getMerchantNameById, SoldGoodsPageVO::getMerchantId, SoldGoodsPageVO::setMerchant)
                .build(listFillService::getCounterNameById, SoldGoodsPageVO::getCounterId, SoldGoodsPageVO::setCounter)
                .build(listFillService::getSupplierNameById, SoldGoodsPageVO::getSupplierId, SoldGoodsPageVO::setSupplier)
                .build(listFillService::getCategoryNameById, SoldGoodsPageVO::getCategoryId, SoldGoodsPageVO::setCategory)
                .build(listFillService::getSubclassNameById, SoldGoodsPageVO::getSubclassId, SoldGoodsPageVO::setSubclass)
                .build(listFillService::getQualityNameById, SoldGoodsPageVO::getQualityId, SoldGoodsPageVO::setQuality)
                .build(listFillService::getBrandNameById, SoldGoodsPageVO::getBrandId, SoldGoodsPageVO::setBrand)
                .build(listFillService::getStyleNameById, SoldGoodsPageVO::getStyleId, SoldGoodsPageVO::setStyle)
                .build(listFillService::getTechnologyNameById, SoldGoodsPageVO::getTechnologyId, SoldGoodsPageVO::setTechnology)
                .build(listFillService::getJewelryMapperNameById, SoldGoodsPageVO::getMainStoneId, SoldGoodsPageVO::setMainStone)
                .build(listFillService::getJewelryMapperNameById, SoldGoodsPageVO::getSubStoneId, SoldGoodsPageVO::setSubStone)
                .build(listFillService::getColumnVosById, SoldGoodsPageVO::getId, SoldGoodsPageVO::setCustomerColumns)
                .build(listFillService::getGoodsImgByGoodsId, SoldGoodsPageVO::getId, SoldGoodsPageVO::setImages)
                .peek(obj -> {
                    SoldGoodsPageVO vo = (SoldGoodsPageVO) obj;
                    // 价格字段分转元
                    vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
                    vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
                    vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
                    vo.setWorkPrice(PriceUtil.fen2yuan(vo.getWorkPrice()));
                    vo.setCertPrice(PriceUtil.fen2yuan(vo.getCertPrice()));
                    vo.setSaleWorkPrice(PriceUtil.fen2yuan(vo.getSaleWorkPrice()));
                    vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));

                    // 设置销工费计价方式名称
                    if (vo.getSaleWorkPriceType() != null) {
                        String saleWorkPriceTypeName = IBaseEnum.getLabelByValue(vo.getSaleWorkPriceType(), SalesTypeEnum.class);
                        vo.setSaleWorkPriceTypeName(saleWorkPriceTypeName != null ? saleWorkPriceTypeName : "未知");
                    }

                    // 填充销售金价和销售银价
                    Long categoryId = vo.getCategoryId() != null ? vo.getCategoryId().longValue() : null;
                    Long qualityId = vo.getQualityId() != null ? vo.getQualityId().longValue() : null;
                    BigDecimal[] prices = getGoldSilverPrice(categoryId, qualityId, goldSilverPriceMap);
                    vo.setSaleGoldPrice(prices[0]);
                    vo.setSaleSilverPrice(prices[1]);
                })
                .handle();
    }

    @Override
    public Page<SoldReceiptPageVO> receiptPage(SoldReceiptPageQuery query) {
        // 检查查询条件是否全部为空，如果是则返回空列表
        if (isReceiptQueryEmpty(query)) {
            return new Page<>(query.getPageNum(), query.getPageSize(), 0);
        }

        QueryWrapper wrapper = buildReceiptWrapper(query);
        wrapper.orderBy(SOLD_RECEIPT.ID, false);

        Page<SoldReceiptPageVO> page = soldReceiptMapper.paginateAs(
            query.getPageNum(),
            query.getPageSize(),
            wrapper,
            SoldReceiptPageVO.class
        );

        List<SoldReceiptPageVO> records = page.getRecords();
        if (records.isEmpty()) {
            return page;
        }

        // 填充关联数据和货品列表
        fillReceiptList(records);

        return page;
    }

    /**
     * 构建销售单查询条件
     */
    private QueryWrapper buildReceiptWrapper(SoldReceiptPageQuery query) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(SOLD_RECEIPT.STATUS.notIn(List.of(
                        SoldReceiptStatusEnum.PENDING.getValue(),  // 排除挂单中
                        SoldReceiptStatusEnum.CANCELLED.getValue() // 排除已作废
                )));

        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        merchantIds.add(0L);
        // 非主账号限制门店
        queryWrapper.where(SOLD_RECEIPT.MERCHANT_ID.in(merchantIds, !SecurityUtils.isMain()));

        // 查询条件
        if (StringUtils.isNotBlank(query.getReceiptSn())) {
            queryWrapper.where(SOLD_RECEIPT.RECEIPT_SN.eq(query.getReceiptSn()));
        }

        // 会员筛选
        if (StringUtils.isNotBlank(query.getMemberIds())) {
            queryWrapper.where(SOLD_RECEIPT.MEMBER_ID.in(List.of(query.getMemberIds().split(","))));
        }

        // 根据货品条码查询
        if (StringUtils.isNotBlank(query.getGoodsSn())) {
            // 子查询：查找包含指定货品条码的销售单ID
            QueryWrapper subQuery = QueryWrapper.create()
                    .select(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID)
                    .from(SOLD_RECEIPT_GOODS)
                    .where(SOLD_RECEIPT_GOODS.GOODS_SN.eq(query.getGoodsSn()));

            queryWrapper.where(SOLD_RECEIPT.ID.in(subQuery));
        }

        // 关键词搜索（支持销售单号或货品条码）
        if (StringUtils.isNotBlank(query.getKeywords())) {
            String keywords = query.getKeywords().trim();

            // 子查询：查找包含指定货品条码的销售单ID
            QueryWrapper goodsSubQuery = QueryWrapper.create()
                    .select(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID)
                    .from(SOLD_RECEIPT_GOODS)
                    .where(SOLD_RECEIPT_GOODS.GOODS_SN.eq(keywords));

            // 使用OR条件：销售单号匹配 或 包含匹配的货品条码
            queryWrapper.where(
                SOLD_RECEIPT.RECEIPT_SN.eq(keywords)
                .or(SOLD_RECEIPT.ID.in(goodsSubQuery))
            );
        }

        return queryWrapper;
    }

    /**
     * 填充销售单列表数据
     */
    private void fillReceiptList(List<SoldReceiptPageVO> list) {
        Set<Long> receiptIds = list.stream().map(SoldReceiptPageVO::getId).collect(Collectors.toSet());

        // 查询货品列表，关联货品表获取基本信息
        List<SoldReceiptGoodsVO> goodsList = soldReceiptGoodsMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(
                                SOLD_RECEIPT_GOODS.ALL_COLUMNS,
                                GOODS.NAME.as("goodsName"),
                                GOODS.WEIGHT,
                                GOODS.NET_GOLD_WEIGHT.as("netGoldWeight"),
                                GOODS.NET_SILVER_WEIGHT.as("netSilverWeight"),
                                GOODS.CATEGORY_ID,
                                GOODS.SUBCLASS_ID,
                                GOODS.QUALITY_ID,
                                GOODS.BRAND_ID,
                                GOODS.STYLE_ID,
                                GOODS.TECHNOLOGY_ID,
                                GOODS.STOCK_NUM.as("stockNum")
                        )
                        .from(SOLD_RECEIPT_GOODS)
                        .leftJoin(GOODS).on(SOLD_RECEIPT_GOODS.GOODS_ID.eq(GOODS.ID))
                        .where(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.in(receiptIds)),
                SoldReceiptGoodsVO.class
        );

        // 填充货品关联数据
        fillGoodsList(goodsList);

        // 按销售单ID分组货品
        Map<Long, List<SoldReceiptGoodsVO>> goodsMap = goodsList.stream()
                .collect(Collectors.groupingBy(SoldReceiptGoodsVO::getSoldReceiptId));

        ListFillUtilV2.of(list)
                .build(listFillService::getMerchantNameById, SoldReceiptPageVO::getMerchantId, SoldReceiptPageVO::setMerchantName)
                .build(listFillService::getMemberNameById, SoldReceiptPageVO::getMemberId, SoldReceiptPageVO::setMemberName)
                .build(listFillService::getUserNameByUserId, SoldReceiptPageVO::getCashierId, SoldReceiptPageVO::setCashierName)
                .build(listFillService::getUserNameByUserId, SoldReceiptPageVO::getMainSellerId, SoldReceiptPageVO::setMainSellerName)
                .build(listFillService::getUserNameByUserId, SoldReceiptPageVO::getSupportSellerId, SoldReceiptPageVO::setSupportSellerName)
                .peek(obj -> {
                    SoldReceiptPageVO vo = (SoldReceiptPageVO) obj;
                    // 价格字段分转元
                    vo.setAmount(PriceUtil.fen2yuan(vo.getAmount()));
                    vo.setGoodsAmount(PriceUtil.fen2yuan(vo.getGoodsAmount()));
                    vo.setDiscountAmount(PriceUtil.fen2yuan(vo.getDiscountAmount()));
                    vo.setDeductionAmount(PriceUtil.fen2yuan(vo.getDeductionAmount()));
                    vo.setGiftAmount(PriceUtil.fen2yuan(vo.getGiftAmount()));
                    vo.setAdjustAmount(PriceUtil.fen2yuan(vo.getAdjustAmount()));
                    vo.setReceivedAmount(PriceUtil.fen2yuan(vo.getReceivedAmount()));
                    vo.setPaidAmount(PriceUtil.fen2yuan(vo.getPaidAmount()));
                    vo.setRefundAmount(PriceUtil.fen2yuan(vo.getRefundAmount()));

                    // 设置状态名称
                    vo.setStatusName(getStatusName(vo.getStatus()));

                    // 设置货品列表
                    vo.setGoodsList(goodsMap.getOrDefault(vo.getId(), List.of()));

                    // 设置销售单号
                    String receiptSn = vo.getReceiptSn();
                    if (CollectionUtil.isNotEmpty(vo.getGoodsList())) {
                        vo.getGoodsList().forEach(e -> e.setSoldReceiptSn(receiptSn));
                    }
                })
                .handle();
    }

    /**
     * 填充货品列表关联数据
     */
    private void fillGoodsList(List<SoldReceiptGoodsVO> goodsList) {
        if (goodsList.isEmpty()) {
            return;
        }

        // 获取金价银价Map
        Map<String, BigDecimal[]> goldSilverPriceMap = getGoldSilverPriceMap();

        ListFillUtilV2.of(goodsList)
                .build(listFillService::getCategoryNameById, SoldReceiptGoodsVO::getCategoryId, SoldReceiptGoodsVO::setCategoryName)
                .build(listFillService::getSubclassNameById, SoldReceiptGoodsVO::getSubclassId, SoldReceiptGoodsVO::setSubclassName)
                .build(listFillService::getQualityNameById, SoldReceiptGoodsVO::getQualityId, SoldReceiptGoodsVO::setQualityName)
                .build(listFillService::getBrandNameById, SoldReceiptGoodsVO::getBrandId, SoldReceiptGoodsVO::setBrandName)
                .build(listFillService::getStyleNameById, SoldReceiptGoodsVO::getStyleId, SoldReceiptGoodsVO::setStyleName)
                .build(listFillService::getTechnologyNameById, SoldReceiptGoodsVO::getTechnologyId, SoldReceiptGoodsVO::setTechnologyName)
                .build(listFillService::getGoodsImgByGoodsId, SoldReceiptGoodsVO::getGoodsId, SoldReceiptGoodsVO::setImages)
                .peek(obj -> {
                    SoldReceiptGoodsVO goods = (SoldReceiptGoodsVO) obj;
                    // 价格字段分转元
                    goods.setTagPrice(PriceUtil.fen2yuan(goods.getTagPrice()));
                    goods.setSaleWorkPrice(PriceUtil.fen2yuan(goods.getSaleWorkPrice()));
                    goods.setGoldPrice(PriceUtil.fen2yuan(goods.getGoldPrice()));
                    goods.setSilverPrice(PriceUtil.fen2yuan(goods.getSilverPrice()));
                    goods.setSoldPrice(PriceUtil.fen2yuan(goods.getSoldPrice()));
                    goods.setDiscountAmount(PriceUtil.fen2yuan(goods.getDiscountAmount()));
                    goods.setWorkPriceDiscountAmount(PriceUtil.fen2yuan(goods.getWorkPriceDiscountAmount()));
                    goods.setRealAmount(PriceUtil.fen2yuan(goods.getRealAmount()));
                    goods.setGoldPriceNow(PriceUtil.fen2yuan(goods.getGoldPriceNow()));
                    goods.setSilverPriceNow(PriceUtil.fen2yuan(goods.getSilverPriceNow()));
                    goods.setPlatinumPriceNow(PriceUtil.fen2yuan(goods.getPlatinumPriceNow()));

                    // 填充回收金价和回收银价
                    Long categoryId = goods.getCategoryId() != null ? goods.getCategoryId().longValue() : null;
                    Long qualityId = goods.getQualityId() != null ? goods.getQualityId().longValue() : null;
                    BigDecimal[] recyclePrices = getRecycleGoldSilverPrice(categoryId, qualityId, goldSilverPriceMap);
                    goods.setRecycleGoldPrice(recyclePrices[0]);
                    goods.setRecycleSilverPrice(recyclePrices[1]);

                    // 重量字段格式化
                    goods.setWeight(PriceUtil.formatThreeDecimal(goods.getWeight()));
                    goods.setNetGoldWeight(PriceUtil.formatThreeDecimal(goods.getNetGoldWeight()));
                    goods.setNetSilverWeight(PriceUtil.formatThreeDecimal(goods.getNetSilverWeight()));
                })
                .handle();
    }

    @Override
    public Page<SoldGiftPageVO> giftPage(SoldGiftPageQuery query) {
        QueryWrapper wrapper = buildGiftWrapper(query);
        wrapper.orderBy(GIFT.ID, false);

        Page<SoldGiftPageVO> page = giftMapper.paginateAs(
            query.getPageNum(),
            query.getPageSize(),
            wrapper,
            SoldGiftPageVO.class
        );

        List<SoldGiftPageVO> records = page.getRecords();
        if (records.isEmpty()) {
            return page;
        }

        // 填充关联数据
        fillGiftList(records);

        return page;
    }

    /**
     * 构建赠品查询条件
     */
    private QueryWrapper buildGiftWrapper(SoldGiftPageQuery query) {
        // 固定条件：只查询在库赠品
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(GIFT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GIFT.NUM.gt(0))
                .where(GIFT.STOCK_NUM.gt(0)); // 只查询有库存的赠品

        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        merchantIds.add(0L);
        // 非主账号限制门店
        queryWrapper.where(GIFT.MERCHANT_ID.in(merchantIds, !SecurityUtils.isMain()));

        // 查询条件
        if (StringUtils.isNotBlank(query.getMerchantId())) {
            queryWrapper.where(GIFT.MERCHANT_ID.eq(query.getMerchantId()));
        }

        if (StringUtils.isNotBlank(query.getGiftSn())) {
            queryWrapper.where(GIFT.GIFT_SN.eq(query.getGiftSn()));
        }

        if (StringUtils.isNotBlank(query.getName())) {
            queryWrapper.where(GIFT.NAME.like(query.getName()));
        }

        return queryWrapper;
    }

    /**
     * 填充赠品列表关联数据
     */
    private void fillGiftList(List<SoldGiftPageVO> list) {
        ListFillUtilV2.of(list)
                .build(listFillService::getMerchantNameById, SoldGiftPageVO::getMerchantId, SoldGiftPageVO::setMerchantName)
                .build(listFillService::getSupplierNameById, SoldGiftPageVO::getSupplierId, SoldGiftPageVO::setSupplierName)
                .build(listFillService::getGiftImgByGiftId, SoldGiftPageVO::getId, SoldGiftPageVO::setImages)
                .peek(obj -> {
                    SoldGiftPageVO vo = (SoldGiftPageVO) obj;
                    // 价格字段分转元
                    vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
                    vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
                    // 重量字段格式化
                    vo.setWeight(PriceUtil.formatThreeDecimal(vo.getWeight()));
                })
                .handle();
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return "未知";
        }
        return switch (status) {
            case -1 -> "已作废";
            case 0 -> "挂单中";
            case 1 -> "已完成";
            case 2 -> "部分退货";
            case 3 -> "全部退货";
            default -> "未知";
        };
    }

    @Override
    public SoldReceiptDetailVO getPendingReceiptDetail(Long soldReceiptId) {
        // 1. 验证销售单是否存在且状态为挂单中
        SoldReceiptDetailVO detailVO = soldReceiptMapper.selectOneByQueryAs(
                QueryWrapper.create()
                        .where(SOLD_RECEIPT.ID.eq(soldReceiptId))
                        .and(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId())),
                SoldReceiptDetailVO.class
        );

        if (detailVO == null) {
            CommonUtils.abort("销售单不存在");
            return null; // 这行不会执行，但避免编译器警告
        }

        // 2. 验证销售单状态
        if (!SoldReceiptStatusEnum.PENDING.getValue().equals(detailVO.getStatus())) {
            CommonUtils.abort("只能查询挂单中的销售单");
        }

        // 3. 验证权限
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        if (!SecurityUtils.isMain() && !merchantIds.contains(detailVO.getMerchantId())) {
            CommonUtils.abort("无权限查看此销售单");
        }

        // 4. 填充关联数据
        fillReceiptDetail(detailVO);

        return detailVO;
    }

    /**
     * 填充销售单详情关联数据
     */
    private void fillReceiptDetail(SoldReceiptDetailVO detailVO) {
        Long soldReceiptId = detailVO.getId();

        // 填充基本信息
        fillReceiptBasicInfo(List.of(detailVO));

        // 填充货品列表
        detailVO.setGoodsList(getReceiptGoodsList(soldReceiptId));

        // 填充旧料列表
        detailVO.setOldMaterialList(getReceiptOldMaterialList(soldReceiptId));

        // 填充赠品列表
        detailVO.setGiftList(getReceiptGiftList(soldReceiptId));

        // 填充支付记录列表
        detailVO.setPaymentList(getReceiptPaymentList(soldReceiptId));

        // 价格字段分转元
        detailVO.setAmount(PriceUtil.fen2yuan(detailVO.getAmount()));
        detailVO.setGoodsAmount(PriceUtil.fen2yuan(detailVO.getGoodsAmount()));
        detailVO.setDiscountAmount(PriceUtil.fen2yuan(detailVO.getDiscountAmount()));
        detailVO.setDeductionAmount(PriceUtil.fen2yuan(detailVO.getDeductionAmount()));
        detailVO.setGiftAmount(PriceUtil.fen2yuan(detailVO.getGiftAmount()));
        detailVO.setAdjustAmount(PriceUtil.fen2yuan(detailVO.getAdjustAmount()));
        detailVO.setReceivedAmount(PriceUtil.fen2yuan(detailVO.getReceivedAmount()));
        detailVO.setPaidAmount(PriceUtil.fen2yuan(detailVO.getPaidAmount()));
        detailVO.setRefundAmount(PriceUtil.fen2yuan(detailVO.getRefundAmount()));

        // 设置状态名称
        detailVO.setStatusName(getStatusName(detailVO.getStatus()));
    }

    /**
     * 填充销售单基本信息
     */
    private void fillReceiptBasicInfo(List<SoldReceiptDetailVO> list) {
        if (list.isEmpty()) {
            return;
        }

        ListFillUtilV2.of(list)
                .build(listFillService::getMerchantNameById, SoldReceiptDetailVO::getMerchantId, SoldReceiptDetailVO::setMerchantName)
                .build(listFillService::getMemberNameById, SoldReceiptDetailVO::getMemberId, SoldReceiptDetailVO::setMemberName)
                .build(listFillService::getUserNameByUserId, SoldReceiptDetailVO::getCashierId, SoldReceiptDetailVO::setCashierName)
                .build(listFillService::getUserNameByUserId, SoldReceiptDetailVO::getMainSellerId, SoldReceiptDetailVO::setMainSellerName)
                .build(listFillService::getUserNameByUserId, SoldReceiptDetailVO::getSupportSellerId, SoldReceiptDetailVO::setSupportSellerName)
                .handle();
    }

    /**
     * 获取销售单货品列表
     */
    private List<SoldReceiptGoodsVO> getReceiptGoodsList(Long soldReceiptId) {
        List<SoldReceiptGoodsVO> goodsList = soldReceiptGoodsMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(
                                SOLD_RECEIPT_GOODS.ID.as("goodsDetailId"),
                                SOLD_RECEIPT_GOODS.ALL_COLUMNS,
                                GOODS.NAME.as("goodsName"),
                                GOODS.WEIGHT,
                                GOODS.NET_GOLD_WEIGHT.as("netGoldWeight"),
                                GOODS.NET_SILVER_WEIGHT.as("netSilverWeight"),
                                GOODS.CATEGORY_ID,
                                GOODS.SUBCLASS_ID,
                                GOODS.QUALITY_ID,
                                GOODS.BRAND_ID,
                                GOODS.STYLE_ID,
                                GOODS.TECHNOLOGY_ID,
                                GOODS.STOCK_NUM.as("stockNum")
                        )
                        .from(SOLD_RECEIPT_GOODS)
                        .leftJoin(GOODS).on(SOLD_RECEIPT_GOODS.GOODS_ID.eq(GOODS.ID))
                        .where(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.eq(soldReceiptId)),
                SoldReceiptGoodsVO.class
        );

        if (!goodsList.isEmpty()) {
            fillGoodsList(goodsList);

            // 计算库存数量：stockNum = 原始stockNum + 已有明细的num
            goodsList.forEach(goods -> {
                if (goods.getStockNum() != null && goods.getNum() != null) {
                    goods.setStockNum(goods.getStockNum() + goods.getNum());
                }
            });
        }

        return goodsList;
    }

    /**
     * 获取销售单旧料列表
     */
    private List<SoldReceiptOldMaterialVO> getReceiptOldMaterialList(Long soldReceiptId) {
        List<SoldReceiptOldMaterialVO> oldMaterialList = soldReceiptOldMaterialMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(
                                SOLD_RECEIPT_OLD_MATERIAL.ID.as("oldMaterialDetailId"),
                                SOLD_RECEIPT_OLD_MATERIAL.ALL_COLUMNS,
                                OLD_MATERIAL.NAME.as("oldMaterialName"),
                                OLD_MATERIAL.GOODS_ID,
                                OLD_MATERIAL.GOODS_SN.as("goodsSn"),
                                OLD_MATERIAL.CATEGORY_ID,
                                OLD_MATERIAL.SUBCLASS_ID,
                                OLD_MATERIAL.QUALITY_ID,
                                OLD_MATERIAL.SALES_TYPE,
                                OLD_MATERIAL.SOLD_RECEIPT_SN.as("soldReceiptSn")
                        )
                        .from(SOLD_RECEIPT_OLD_MATERIAL)
                        .leftJoin(OLD_MATERIAL).on(SOLD_RECEIPT_OLD_MATERIAL.OLD_MATERIAL_ID.eq(OLD_MATERIAL.ID))
                        .where(SOLD_RECEIPT_OLD_MATERIAL.SOLD_RECEIPT_ID.eq(soldReceiptId)),
                SoldReceiptOldMaterialVO.class
        );

        if (!oldMaterialList.isEmpty()) {
            // 填充分类信息
            fillOldMaterialList(oldMaterialList);
        }

        // 价格字段分转元和计算回收金额
        oldMaterialList.forEach(vo -> {
            vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
            vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
            vo.setRecyclePrice(PriceUtil.fen2yuan(vo.getRecyclePrice()));
            vo.setSaleWorkPrice(PriceUtil.fen2yuan(vo.getSaleWorkPrice()));

            // 设置计价方式名称
            if (vo.getSalesType() != null) {
                String salesTypeName = IBaseEnum.getLabelByValue(vo.getSalesType(), SalesTypeEnum.class);
                vo.setSalesTypeName(salesTypeName != null ? salesTypeName : "未知");
            }

            // 计算回收金额 = 回收单价 * 数量
            if (vo.getRecyclePrice() != null && vo.getNum() != null) {
                vo.setRecycleAmount(vo.getRecyclePrice().multiply(BigDecimal.valueOf(vo.getNum())));
            }
        });

        return oldMaterialList;
    }

    /**
     * 获取销售单赠品列表
     */
    private List<SoldReceiptGiftVO> getReceiptGiftList(Long soldReceiptId) {
        List<SoldReceiptGiftVO> giftList = soldReceiptGiftMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(
                                SOLD_RECEIPT_GIFT.ID.as("giftDetailId"),
                                SOLD_RECEIPT_GIFT.ALL_COLUMNS,
                                GIFT.NAME.as("giftName"),
                                GIFT.STOCK_NUM.as("stockNum")
                        )
                        .from(SOLD_RECEIPT_GIFT)
                        .leftJoin(GIFT).on(SOLD_RECEIPT_GIFT.GIFT_ID.eq(GIFT.ID))
                        .where(SOLD_RECEIPT_GIFT.SOLD_RECEIPT_ID.eq(soldReceiptId)),
                SoldReceiptGiftVO.class
        );

        // 价格字段分转元
        giftList.forEach(vo -> {
            vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
            vo.setSoldPrice(PriceUtil.fen2yuan(vo.getSoldPrice()));

            // 计算库存数量：stockNum = 原始stockNum + 已有明细的num
            if (vo.getStockNum() != null && vo.getNum() != null) {
                vo.setStockNum(vo.getStockNum() + vo.getNum());
            }
        });

        return giftList;
    }

    /**
     * 获取销售单支付记录列表
     */
    private List<SoldReceiptPaymentVO> getReceiptPaymentList(Long soldReceiptId) {
        List<SoldReceiptPaymentVO> paymentList = soldReceiptPaymentMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(
                                SOLD_RECEIPT_PAYMENT.ID.as("paymentId"),
                                SOLD_RECEIPT_PAYMENT.SOLD_RECEIPT_ID,
                                SOLD_RECEIPT_PAYMENT.TYPE,
                                SOLD_RECEIPT_PAYMENT.AMOUNT,
                                SOLD_RECEIPT_PAYMENT.CREATED_AT,
                                SOLD_RECEIPT_PAYMENT.UPDATED_AT
                        )
                        .where(SOLD_RECEIPT_PAYMENT.SOLD_RECEIPT_ID.eq(soldReceiptId)),
                SoldReceiptPaymentVO.class
        );

        // 价格字段分转元和设置支付方式名称
        paymentList.forEach(vo -> {
            vo.setAmount(PriceUtil.fen2yuan(vo.getAmount()));
            vo.setTypeName(getPaymentTypeName(vo.getType()));
        });

        return paymentList;
    }



    /**
     * 填充旧料列表分类信息
     */
    private void fillOldMaterialList(List<SoldReceiptOldMaterialVO> list) {
        if (list.isEmpty()) {
            return;
        }

        ListFillUtilV2.of(list)
                .build(listFillService::getCategoryNameById, SoldReceiptOldMaterialVO::getCategoryId, SoldReceiptOldMaterialVO::setCategoryName)
                .build(listFillService::getSubclassNameById, SoldReceiptOldMaterialVO::getSubclassId, SoldReceiptOldMaterialVO::setSubclassName)
                .build(listFillService::getQualityNameById, SoldReceiptOldMaterialVO::getQualityId, SoldReceiptOldMaterialVO::setQualityName)
                .handle();
    }

    @Override
    public SoldReceiptFullDetailVO getSoldReceiptDetail(Long soldReceiptId) {
        // 1. 验证销售单是否存在
        SoldReceiptFullDetailVO detailVO = soldReceiptMapper.selectOneByQueryAs(
                QueryWrapper.create()
                        .where(SOLD_RECEIPT.ID.eq(soldReceiptId))
                        .and(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId())),
                SoldReceiptFullDetailVO.class
        );

        if (detailVO == null) {
            CommonUtils.abort("销售单不存在");
            return null;
        }

        // 2. 验证权限
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        if (!SecurityUtils.isMain() && !merchantIds.contains(detailVO.getMerchantId())) {
            CommonUtils.abort("无权限查看此销售单");
        }

        // 3. 填充关联数据
        fillSoldReceiptFullDetail(detailVO);

        return detailVO;
    }

    /**
     * 填充销售单完整详情关联数据
     */
    private void fillSoldReceiptFullDetail(SoldReceiptFullDetailVO detailVO) {
        Long soldReceiptId = detailVO.getId();

        // 填充基本信息
        fillSoldReceiptBasicInfo(detailVO);

        // 填充货品列表
        detailVO.setGoodsList(getSoldReceiptGoodsDetailList(soldReceiptId));

        // 填充旧料列表
        detailVO.setOldMaterialList(getSoldReceiptOldMaterialDetailList(soldReceiptId));

        // 填充赠品列表
        detailVO.setGiftList(getSoldReceiptGiftDetailList(soldReceiptId));

        // 填充支付记录列表
        detailVO.setPaymentList(getSoldReceiptPaymentDetailList(soldReceiptId));

        // 设置状态名称
        String statusName = IBaseEnum.getLabelByValue(detailVO.getStatus(), SoldReceiptStatusEnum.class);
        detailVO.setStatusName(statusName != null ? statusName : "未知");

        // 价格字段分转元
        detailVO.setAmount(PriceUtil.fen2yuan(detailVO.getAmount()));
        detailVO.setWeight(PriceUtil.formatThreeDecimal(detailVO.getWeight()));
        detailVO.setGoldWeight(PriceUtil.formatThreeDecimal(detailVO.getGoldWeight()));
        detailVO.setSilverWeight(PriceUtil.formatThreeDecimal(detailVO.getSilverWeight()));
        detailVO.setGoodsAmount(PriceUtil.fen2yuan(detailVO.getGoodsAmount()));
        detailVO.setDiscountAmount(PriceUtil.fen2yuan(detailVO.getDiscountAmount()));
        detailVO.setDeductionAmount(PriceUtil.fen2yuan(detailVO.getDeductionAmount()));
        detailVO.setGiftAmount(PriceUtil.fen2yuan(detailVO.getGiftAmount()));
        detailVO.setAdjustAmount(PriceUtil.fen2yuan(detailVO.getAdjustAmount()));
        detailVO.setPaidAmount(PriceUtil.fen2yuan(detailVO.getPaidAmount()));
    }

    /**
     * 获取支付方式映射
     */
    @SuppressWarnings("rawtypes")
    private Map<Long, String> getPaymentMethodsMap(Set<Long> soldReceiptIds) {
        if (soldReceiptIds.isEmpty()) {
            return new HashMap<>();
        }

        List<Map> paymentData = soldReceiptPaymentMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(SOLD_RECEIPT_PAYMENT.SOLD_RECEIPT_ID.as("soldReceiptId"), SOLD_RECEIPT_PAYMENT.TYPE.as("type"))
                        .where(SOLD_RECEIPT_PAYMENT.SOLD_RECEIPT_ID.in(soldReceiptIds)),
                Map.class
        );

        Map<Long, Set<String>> paymentTypesMap = new HashMap<>();
        for (Map payment : paymentData) {
            Object soldReceiptIdObj = payment.get("soldReceiptId");
            Integer type = (Integer) payment.get("type");
            String typeName = getPaymentTypeName(type);

            if (soldReceiptIdObj != null) {
                Long soldReceiptId = soldReceiptIdObj instanceof Integer ?
                    ((Integer) soldReceiptIdObj).longValue() : (Long) soldReceiptIdObj;
                paymentTypesMap.computeIfAbsent(soldReceiptId, k -> new HashSet<>()).add(typeName);
            }
        }

        // 将支付方式集合转换为逗号分隔的字符串（自动去重）
        Map<Long, String> result = new HashMap<>();
        for (Map.Entry<Long, Set<String>> entry : paymentTypesMap.entrySet()) {
            result.put(entry.getKey(), String.join(",", entry.getValue()));
        }

        return result;
    }

    /**
     * 获取支付方式名称
     */
    private String getPaymentTypeName(Integer type) {
        if (type == null) {
            return "未知";
        }

        String typeName = IBaseEnum.getLabelByValue(type, PayTypeEnum.class);
        return typeName != null ? typeName : "未知";
    }

    /**
     * 填充销售单基本信息
     */
    private void fillSoldReceiptBasicInfo(SoldReceiptFullDetailVO detailVO) {
        List<SoldReceiptFullDetailVO> list = List.of(detailVO);

        // 获取支付方式信息
        Map<Long, String> paymentMethodsMap = getPaymentMethodsMap(Set.of(detailVO.getId()));

        ListFillUtilV2.of(list)
                .build(listFillService::getMerchantNameById, SoldReceiptFullDetailVO::getMerchantId, SoldReceiptFullDetailVO::setMerchantName)
                .build(listFillService::getMerchantPhoneById, SoldReceiptFullDetailVO::getMerchantId, SoldReceiptFullDetailVO::setMerchantPhone)
                .build(listFillService::getMerchantAddressById, SoldReceiptFullDetailVO::getMerchantId, SoldReceiptFullDetailVO::setMerchantAddress)
                .build(listFillService::getMemberNameById, SoldReceiptFullDetailVO::getMemberId, SoldReceiptFullDetailVO::setMemberName)
                .build(listFillService::getMemberPhoneById, SoldReceiptFullDetailVO::getMemberId, SoldReceiptFullDetailVO::setMemberPhone)
                .build(listFillService::getUserNameByUserId, SoldReceiptFullDetailVO::getCashierId, SoldReceiptFullDetailVO::setCashierName)
                .build(listFillService::getUserNameByUserId, SoldReceiptFullDetailVO::getMainSellerId, SoldReceiptFullDetailVO::setMainSellerName)
                .build(listFillService::getUserNameByUserId, SoldReceiptFullDetailVO::getSupportSellerId, SoldReceiptFullDetailVO::setSupportSellerName)
                .peek(vo -> {
                    // 设置支付方式
                    vo.setPaymentMethods(paymentMethodsMap.getOrDefault(vo.getId(), ""));
                })
                .handle();
    }

    /**
     * 获取销售单货品详情列表
     */
    private List<SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO> getSoldReceiptGoodsDetailList(Long soldReceiptId) {
        List<SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO> goodsList = soldReceiptGoodsMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(
                                SOLD_RECEIPT_GOODS.ID.as("goodsDetailId"),
                                SOLD_RECEIPT_GOODS.GOODS_ID.as("goodsId"),
                                SOLD_RECEIPT_GOODS.GOODS_SN,
                                SOLD_RECEIPT_GOODS.NUM,
                                SOLD_RECEIPT_GOODS.GOLD_WEIGHT,
                                SOLD_RECEIPT_GOODS.SILVER_WEIGHT,
                                SOLD_RECEIPT_GOODS.SALE_TYPE.as("saleTypeName"),
                                SOLD_RECEIPT_GOODS.GOLD_PRICE,
                                SOLD_RECEIPT_GOODS.SILVER_PRICE,
                                SOLD_RECEIPT_GOODS.SOLD_PRICE,
                                SOLD_RECEIPT_GOODS.TAG_PRICE,
                                SOLD_RECEIPT_GOODS.SALE_WORK_PRICE,
                                SOLD_RECEIPT_GOODS.SALE_WORK_PRICE_TYPE,
                                SOLD_RECEIPT_GOODS.DISCOUNT_AMOUNT,
                                SOLD_RECEIPT_GOODS.REAL_AMOUNT,
                                SOLD_RECEIPT_GOODS.STAFF_DISCOUNT,
                                SOLD_RECEIPT_GOODS.WORK_PRICE_DISCOUNT_AMOUNT,
                                SOLD_RECEIPT_GOODS.WORK_PRICE_DISCOUNT,
                                GOODS.NAME.as("goodsName"),
                                GOODS.CATEGORY_ID,
                                GOODS.SUBCLASS_ID,
                                GOODS.QUALITY_ID,
                                GOODS.TECHNOLOGY_ID,
                                GOODS.CIRCLE_SIZE,
                                GOODS.NET_GOLD_WEIGHT,
                                GOODS.NET_SILVER_WEIGHT,
                                GOODS.WEIGHT.multiply(SOLD_RECEIPT_GOODS.NUM).as("weight")
                        )
                        .from(SOLD_RECEIPT_GOODS)
                        .leftJoin(GOODS).on(SOLD_RECEIPT_GOODS.GOODS_ID.eq(GOODS.ID))
                        .where(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.eq(soldReceiptId)),
                SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO.class
        );

        // 价格字段分转元和格式化
        goodsList.forEach(vo -> {
            vo.setGoldWeight(PriceUtil.formatThreeDecimal(vo.getGoldWeight()));
            vo.setSilverWeight(PriceUtil.formatThreeDecimal(vo.getSilverWeight()));
            vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
            vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
            vo.setSoldPrice(PriceUtil.fen2yuan(vo.getSoldPrice()));
            vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
            // 计算工费金额（根据工费计价方式）
            BigDecimal workAmount = calculateWorkAmount(
                PriceUtil.fen2yuan(vo.getSaleWorkPrice()),
                vo.getSaleWorkPriceType(),
                vo.getCategoryId() != null ? vo.getCategoryId().intValue() : null,
                vo.getNetGoldWeight(),
                vo.getNetSilverWeight()
            );
            // 工费总额 = 工费金额 * 数量
            vo.setWorkPrice(workAmount.multiply(BigDecimal.valueOf(vo.getNum())));
            vo.setSaleWorkPrice(PriceUtil.fen2yuan(vo.getSaleWorkPrice()));
            vo.setDiscountAmount(PriceUtil.fen2yuan(vo.getDiscountAmount()));
            vo.setWorkPriceDiscountAmount(PriceUtil.fen2yuan(vo.getWorkPriceDiscountAmount()));
            vo.setRealAmount(PriceUtil.fen2yuan(vo.getRealAmount()));

            // 计算总优惠金额 = 工费折扣优惠金额 + 员工折扣优惠金额
            BigDecimal totalDiscountAmount = BigDecimal.ZERO;
            if (vo.getWorkPriceDiscountAmount() != null) {
                totalDiscountAmount = totalDiscountAmount.add(vo.getWorkPriceDiscountAmount());
            }
            if (vo.getDiscountAmount() != null) {
                totalDiscountAmount = totalDiscountAmount.add(vo.getDiscountAmount());
            }
            vo.setTotalDiscountAmount(totalDiscountAmount);
            vo.setTotalGoldWeight(PriceUtil.formatThreeDecimal(vo.getGoldWeight().multiply(BigDecimal.valueOf(vo.getNum()))));
            vo.setTotalSilverWeight(PriceUtil.formatThreeDecimal(vo.getSilverWeight().multiply(BigDecimal.valueOf(vo.getNum()))));
            vo.setTotalGoldPrice(PriceUtil.formatTwoDecimal(vo.getGoldPrice().multiply(vo.getTotalGoldWeight())));
            vo.setTotalSilverPrice(PriceUtil.formatTwoDecimal(vo.getSilverPrice().multiply(vo.getTotalSilverWeight())));
            vo.setWeight(PriceUtil.formatThreeDecimal(vo.getWeight()));
            // 计算销售价 = 销售单价 * 数量
            if (vo.getSoldPrice() != null && vo.getNum() != null) {
                vo.setTotalPrice(vo.getSoldPrice().multiply(BigDecimal.valueOf(vo.getNum())));
            }

            // 设置销售方式名称
            if (vo.getSaleTypeName() != null) {
                try {
                    Integer saleType = Integer.valueOf(vo.getSaleTypeName());
                    String typeName = IBaseEnum.getLabelByValue(saleType, SalesTypeEnum.class);
                    vo.setSaleTypeName(typeName != null ? typeName : "未知");
                } catch (NumberFormatException e) {
                    vo.setSaleTypeName("未知");
                }
            }
        });

        // 填充图片和分类信息
        fillGoodsDetailInfo(goodsList);

        return goodsList;
    }

    /**
     * 获取销售单旧料详情列表
     */
    private List<SoldReceiptFullDetailVO.SoldReceiptOldMaterialDetailVO> getSoldReceiptOldMaterialDetailList(Long soldReceiptId) {
        List<SoldReceiptFullDetailVO.SoldReceiptOldMaterialDetailVO> oldMaterialList = soldReceiptOldMaterialMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(
                                SOLD_RECEIPT_OLD_MATERIAL.ID.as("oldMaterialDetailId"),
                                SOLD_RECEIPT_OLD_MATERIAL.OLD_MATERIAL_ID.as("oldMaterialId"),
                                SOLD_RECEIPT_OLD_MATERIAL.OLD_MATERIAL_SN,
                                SOLD_RECEIPT_OLD_MATERIAL.NUM,
                                SOLD_RECEIPT_OLD_MATERIAL.NET_GOLD_WEIGHT,
                                SOLD_RECEIPT_OLD_MATERIAL.NET_SILVER_WEIGHT,
                                SOLD_RECEIPT_OLD_MATERIAL.GOLD_PRICE,
                                SOLD_RECEIPT_OLD_MATERIAL.SILVER_PRICE,
                                SOLD_RECEIPT_OLD_MATERIAL.RECYCLE_PRICE,
                                OLD_MATERIAL.NAME.as("oldMaterialName"),
                                OLD_MATERIAL.SALES_TYPE,
                                OLD_MATERIAL.CATEGORY_ID,
                                OLD_MATERIAL.SUBCLASS_ID,
                                OLD_MATERIAL.QUALITY_ID,
                                OLD_MATERIAL.SOLD_RECEIPT_SN,
                                OLD_MATERIAL.WEIGHT
                        )
                        .from(SOLD_RECEIPT_OLD_MATERIAL)
                        .leftJoin(OLD_MATERIAL).on(SOLD_RECEIPT_OLD_MATERIAL.OLD_MATERIAL_ID.eq(OLD_MATERIAL.ID))
                        .where(SOLD_RECEIPT_OLD_MATERIAL.SOLD_RECEIPT_ID.eq(soldReceiptId)),
                SoldReceiptFullDetailVO.SoldReceiptOldMaterialDetailVO.class
        );

        // 使用ListFillUtilV2填充图片信息和分类信息
        ListFillUtilV2.of(oldMaterialList)
                .build(listFillService::getOldMaterialImgByOldMaterialId, SoldReceiptFullDetailVO.SoldReceiptOldMaterialDetailVO::getOldMaterialId, SoldReceiptFullDetailVO.SoldReceiptOldMaterialDetailVO::setImages)
                .build(listFillService::getCategoryNameById, SoldReceiptFullDetailVO.SoldReceiptOldMaterialDetailVO::getCategoryId, SoldReceiptFullDetailVO.SoldReceiptOldMaterialDetailVO::setCategoryName)
                .build(listFillService::getSubclassNameById, SoldReceiptFullDetailVO.SoldReceiptOldMaterialDetailVO::getSubclassId, SoldReceiptFullDetailVO.SoldReceiptOldMaterialDetailVO::setSubclassName)
                .build(listFillService::getQualityNameById, SoldReceiptFullDetailVO.SoldReceiptOldMaterialDetailVO::getQualityId, SoldReceiptFullDetailVO.SoldReceiptOldMaterialDetailVO::setQualityName)
                .peek(vo -> {
                    // 价格字段分转元和格式化
                    vo.setNetGoldWeight(PriceUtil.formatThreeDecimal(vo.getNetGoldWeight()));
                    vo.setNetSilverWeight(PriceUtil.formatThreeDecimal(vo.getNetSilverWeight()));
                    vo.setWeight(PriceUtil.formatThreeDecimal(vo.getWeight().multiply(BigDecimal.valueOf(vo.getNum()))));
                    vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
                    vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
                    vo.setRecyclePrice(PriceUtil.fen2yuan(vo.getRecyclePrice()));
                    vo.setSaleWorkPrice(PriceUtil.fen2yuan(vo.getSaleWorkPrice()));

                    // 设置计价方式名称
                    if (vo.getSalesType() != null) {
                        String pricingMethodName = IBaseEnum.getLabelByValue(vo.getSalesType(), SalesTypeEnum.class);
                        vo.setPricingMethodName(pricingMethodName != null ? pricingMethodName : "未知");
                    }

                    // 计算回收金额 = 回收单价 * 数量
                    if (vo.getRecyclePrice() != null && vo.getNum() != null) {
                        vo.setTotalAmount(vo.getRecyclePrice().multiply(BigDecimal.valueOf(vo.getNum())));
                    }
                })
                .handle();

        return oldMaterialList;
    }

    /**
     * 获取销售单赠品详情列表
     */
    private List<SoldReceiptFullDetailVO.SoldReceiptGiftDetailVO> getSoldReceiptGiftDetailList(Long soldReceiptId) {
        List<SoldReceiptFullDetailVO.SoldReceiptGiftDetailVO> giftList = soldReceiptGiftMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(
                                SOLD_RECEIPT_GIFT.ID.as("giftDetailId"),
                                SOLD_RECEIPT_GIFT.GIFT_ID.as("giftId"),
                                SOLD_RECEIPT_GIFT.GIFT_SN,
                                SOLD_RECEIPT_GIFT.NUM,
                                SOLD_RECEIPT_GIFT.TAG_PRICE,
                                SOLD_RECEIPT_GIFT.SOLD_PRICE,
                                GIFT.NAME.as("giftName")
                        )
                        .from(SOLD_RECEIPT_GIFT)
                        .leftJoin(GIFT).on(SOLD_RECEIPT_GIFT.GIFT_ID.eq(GIFT.ID))
                        .where(SOLD_RECEIPT_GIFT.SOLD_RECEIPT_ID.eq(soldReceiptId)),
                SoldReceiptFullDetailVO.SoldReceiptGiftDetailVO.class
        );

        // 价格字段分转元
        giftList.forEach(vo -> {
            vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
            vo.setSoldPrice(PriceUtil.fen2yuan(vo.getSoldPrice()));

            // 计算销售金额 = 销售单价 * 数量
            if (vo.getSoldPrice() != null && vo.getNum() != null) {
                vo.setTotalAmount(vo.getSoldPrice().multiply(BigDecimal.valueOf(vo.getNum())));
            }
        });

        // 填充图片信息
        fillGiftDetailInfo(giftList);

        return giftList;
    }

    /**
     * 获取销售单支付详情列表
     */
    private List<SoldReceiptFullDetailVO.SoldReceiptPaymentDetailVO> getSoldReceiptPaymentDetailList(Long soldReceiptId) {
        List<SoldReceiptFullDetailVO.SoldReceiptPaymentDetailVO> paymentList = soldReceiptPaymentMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(
                                SOLD_RECEIPT_PAYMENT.TYPE.as("paymentTypeName"),
                                SOLD_RECEIPT_PAYMENT.AMOUNT
                        )
                        .from(SOLD_RECEIPT_PAYMENT)
                        .where(SOLD_RECEIPT_PAYMENT.SOLD_RECEIPT_ID.eq(soldReceiptId)),
                SoldReceiptFullDetailVO.SoldReceiptPaymentDetailVO.class
        );

        // 价格字段分转元和设置支付方式名称
        paymentList.forEach(vo -> {
            vo.setAmount(PriceUtil.fen2yuan(vo.getAmount()));
            // 根据type设置支付方式名称
            if (vo.getPaymentTypeName() != null) {
                try {
                    Integer type = Integer.valueOf(vo.getPaymentTypeName());
                    String typeName = IBaseEnum.getLabelByValue(type, PayTypeEnum.class);
                    vo.setPaymentTypeName(typeName != null ? typeName : "未知");
                } catch (NumberFormatException e) {
                    vo.setPaymentTypeName("未知");
                }
            }
        });

        return paymentList;
    }

    /**
     * 填充货品详情信息（图片、分类等）
     */
    private void fillGoodsDetailInfo(List<SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO> goodsList) {
        if (goodsList.isEmpty()) {
            return;
        }

        // 获取金价银价Map
        Map<String, BigDecimal[]> goldSilverPriceMap = getGoldSilverPriceMap();

        // 使用ListFillUtilV2填充图片信息和分类信息
        ListFillUtilV2.of(goodsList)
                .build(listFillService::getGoodsImgByGoodsId, SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO::getGoodsId, SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO::setImages)
                .build(listFillService::getCategoryNameById, SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO::getCategoryId, SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO::setCategoryName)
                .build(listFillService::getSubclassNameById, SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO::getSubclassId, SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO::setSubclassName)
                .build(listFillService::getQualityNameById, SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO::getQualityId, SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO::setQualityName)
                .build(listFillService::getTechnologyNameById, SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO::getTechnologyId, SoldReceiptFullDetailVO.SoldReceiptGoodsDetailVO::setTechnologyName)
                .peek(vo -> {
                    // 填充销售金价和销售银价
                    BigDecimal[] prices = getGoldSilverPrice(vo.getCategoryId(), vo.getQualityId(), goldSilverPriceMap);
                    vo.setSaleGoldPrice(prices[0]);
                    vo.setSaleSilverPrice(prices[1]);
                })
                .handle();
    }

    /**
     * 填充赠品详情信息（图片等）
     */
    private void fillGiftDetailInfo(List<SoldReceiptFullDetailVO.SoldReceiptGiftDetailVO> giftList) {
        if (giftList.isEmpty()) {
            return;
        }

        // 使用ListFillUtilV2填充图片信息
        ListFillUtilV2.of(giftList)
                .build(listFillService::getGiftImgByGiftId, SoldReceiptFullDetailVO.SoldReceiptGiftDetailVO::getGiftId, SoldReceiptFullDetailVO.SoldReceiptGiftDetailVO::setImages)
                .handle();
    }

    /**
     * 获取金价银价Map，支持循环调用
     *
     * @return 金价银价Map，key为"categoryId_qualityId"或"categoryId"，value为[销售金价, 销售银价, 回收金价, 回收银价]
     */
    private Map<String, BigDecimal[]> getGoldSilverPriceMap() {
        Map<String, BigDecimal[]> priceMap = new HashMap<>();

        try {
            Long companyId = SecurityUtils.getCompanyId();

            // 一次查询获取所有生效的金价数据
            List<QualityGoldPriceVo> allPrices = goldPriceMapper.selectListByQueryAs(
                QueryWrapper.create()
                    .where(GOLD_PRICE.COMPANY_ID.eq(companyId))
                    .where(GOLD_PRICE.STATUS.eq(1))
                    .select(
                        GOLD_PRICE.CATEGORY_ID,
                        GOLD_PRICE.QUALITY_ID,
                        GOLD_PRICE.SALE_PRICE.divide(100).as("goldSalePrice"),
                        GOLD_PRICE.RECYCLE_PRICE.divide(100).as("goldRecyclePrice"),
                        GOLD_PRICE.ACTIVE_TIME
                    )
                    .orderBy(GOLD_PRICE.ACTIVE_TIME, false),
                QualityGoldPriceVo.class
            );

            // 用于存储黄金和白银大类的销售价格和回收价格（用于金包银大类）
            BigDecimal goldSalePrice = null;
            BigDecimal silverSalePrice = null;
            BigDecimal goldRecyclePrice = null;
            BigDecimal silverRecyclePrice = null;

            // 先找到黄金和白银大类的销售价格和回收价格（仅用于金包银大类）
            for (QualityGoldPriceVo price : allPrices) {
                if (CategoryEnum.GOLD.getValue().equals(price.getCategoryId()) && goldSalePrice == null) {
                    goldSalePrice = price.getGoldSalePrice();
                    goldRecyclePrice = price.getGoldRecyclePrice();
                }
                if (CategoryEnum.SILVER.getValue().equals(price.getCategoryId()) && silverSalePrice == null) {
                    silverSalePrice = price.getGoldSalePrice(); // 银饰的价格存储在goldSalePrice字段中
                    silverRecyclePrice = price.getGoldRecyclePrice(); // 银饰的回收价格存储在goldRecyclePrice字段中
                }
            }

            // 处理每个价格记录
            for (QualityGoldPriceVo price : allPrices) {
                BigDecimal saleGoldPrice = null;
                BigDecimal saleSilverPrice = null;
                BigDecimal recycleGoldPrice = null;
                BigDecimal recycleSilverPrice = null;

                if (CategoryEnum.SILVER.getValue().equals(price.getCategoryId())) {
                    // 银饰：只有银价，金价为null（使用当前记录的价格）
                    saleGoldPrice = null;
                    saleSilverPrice = price.getGoldSalePrice(); // 银饰的价格存储在goldSalePrice字段中
                    recycleGoldPrice = null;
                    recycleSilverPrice = price.getGoldRecyclePrice(); // 银饰的回收价格存储在goldRecyclePrice字段中
                } else {
                    // 其他大类：只有金价，银价为null（使用当前记录的价格）
                    saleGoldPrice = price.getGoldSalePrice();
                    saleSilverPrice = null;
                    recycleGoldPrice = price.getGoldRecyclePrice();
                    recycleSilverPrice = null;
                }

                BigDecimal[] prices = new BigDecimal[]{saleGoldPrice, saleSilverPrice, recycleGoldPrice, recycleSilverPrice};

                // 如果有成色ID，加入大类+成色组合Map
                if (price.getQualityId() != null) {
                    String qualityKey = price.getCategoryId() + "_" + price.getQualityId();
                    if (!priceMap.containsKey(qualityKey)) {
                        priceMap.put(qualityKey, prices);
                    }
                }

                // 加入大类Map（作为fallback）
                String categoryKey = String.valueOf(price.getCategoryId());
                if (!priceMap.containsKey(categoryKey)) {
                    priceMap.put(categoryKey, prices);
                }
            }

            // 单独处理金包银大类：使用第一个金价和第一个银价
            if (goldSalePrice != null || silverSalePrice != null) {
                BigDecimal[] goldSilverPrices = new BigDecimal[]{goldSalePrice, silverSalePrice, goldRecyclePrice, silverRecyclePrice};
                String goldSilverKey = String.valueOf(CategoryEnum.GOLD_SILVER.getValue());
                priceMap.put(goldSilverKey, goldSilverPrices);
            }

        } catch (Exception e) {
            log.error("批量查询金价银价失败", e);
        }

        return priceMap;
    }

    /**
     * 根据大类和成色匹配金价银价
     * 优先用大类+成色匹配，如果没有则使用大类匹配，否则为零
     *
     * @param categoryId 大类ID
     * @param qualityId 成色ID
     * @param priceMap 金价银价Map
     * @return [销售金价, 销售银价]
     */
    private BigDecimal[] getGoldSilverPrice(Long categoryId, Long qualityId, Map<String, BigDecimal[]> priceMap) {
        BigDecimal goldPrice = BigDecimal.ZERO;
        BigDecimal silverPrice = BigDecimal.ZERO;

        if (categoryId == null) {
            return new BigDecimal[]{goldPrice, silverPrice};
        }

        BigDecimal[] prices = null;

        // 1. 优先尝试大类+成色匹配
        if (qualityId != null) {
            String key = categoryId + "_" + qualityId;
            prices = priceMap.get(key);
        }

        // 2. 如果没有找到，尝试大类匹配
        if (prices == null) {
            String key = String.valueOf(categoryId);
            prices = priceMap.get(key);
        }

        // 3. 提取金价银价
        if (prices != null) {
            if (prices[0] != null) {
                goldPrice = prices[0];
            }
            if (prices[1] != null) {
                silverPrice = prices[1];
            }
        }

        return new BigDecimal[]{goldPrice, silverPrice};
    }

    /**
     * 根据大类和成色匹配回收金价银价
     * 优先用大类+成色匹配，如果没有则使用大类匹配，否则为零
     *
     * @param categoryId 大类ID
     * @param qualityId 成色ID
     * @param priceMap 金价银价Map
     * @return [回收金价, 回收银价]
     */
    private BigDecimal[] getRecycleGoldSilverPrice(Long categoryId, Long qualityId, Map<String, BigDecimal[]> priceMap) {
        BigDecimal recycleGoldPrice = BigDecimal.ZERO;
        BigDecimal recycleSilverPrice = BigDecimal.ZERO;

        if (categoryId == null) {
            return new BigDecimal[]{recycleGoldPrice, recycleSilverPrice};
        }

        BigDecimal[] prices = null;

        // 1. 优先尝试大类+成色匹配
        if (qualityId != null) {
            String key = categoryId + "_" + qualityId;
            prices = priceMap.get(key);
        }

        // 2. 如果没有找到，尝试大类匹配
        if (prices == null) {
            String key = String.valueOf(categoryId);
            prices = priceMap.get(key);
        }

        // 3. 提取回收金价银价（从四元素数组的第3、4个元素）
        if (prices != null && prices.length >= 4) {
            if (prices[2] != null) {
                recycleGoldPrice = prices[2];
            }
            if (prices[3] != null) {
                recycleSilverPrice = prices[3];
            }
        }

        return new BigDecimal[]{recycleGoldPrice, recycleSilverPrice};
    }

    /**
     * 检查销售单查询条件是否全部为空（除了分页参数）
     */
    private boolean isReceiptQueryEmpty(SoldReceiptPageQuery query) {
        return StringUtils.isBlank(query.getReceiptSn()) &&
               StringUtils.isBlank(query.getGoodsSn()) &&
               StringUtils.isBlank(query.getMemberIds()) &&
               StringUtils.isBlank(query.getKeywords());
    }

    /**
     * 计算工费金额（根据工费计价方式）
     */
    private BigDecimal calculateWorkAmount(BigDecimal saleWorkPrice, Integer saleWorkPriceType,
                                         Integer categoryId, BigDecimal netGoldWeight, BigDecimal netSilverWeight) {
        if (saleWorkPrice == null || saleWorkPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        // 获取工费计价方式，默认按数量
        Integer workPriceType = saleWorkPriceType != null ? saleWorkPriceType : SalesTypeEnum.BY_NUM.getValue();

        if (workPriceType.equals(SalesTypeEnum.BY_WEIGHT.getValue())) {
            // 按重量：根据商品类别计算工费金额
            BigDecimal workWeight = calculateWorkWeight(categoryId, netGoldWeight, netSilverWeight);
            return saleWorkPrice.multiply(workWeight).setScale(2, java.math.RoundingMode.HALF_UP);
        }

        // 按数量：工费金额 = 工费单价
        return saleWorkPrice.setScale(2, java.math.RoundingMode.HALF_UP);
    }

    /**
     * 根据商品类别计算工费重量
     * a. 金包银：工费金额 = 工费单价 × (净金重 + 净银重)
     * b. 银饰：工费金额 = 工费单价 × 净银重
     * c. 其他：工费金额 = 工费单价 × 净金重
     */
    private BigDecimal calculateWorkWeight(Integer categoryId, BigDecimal netGoldWeight, BigDecimal netSilverWeight) {
        if (categoryId == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal safeNetGoldWeight = netGoldWeight != null ? netGoldWeight : BigDecimal.ZERO;
        BigDecimal safeNetSilverWeight = netSilverWeight != null ? netSilverWeight : BigDecimal.ZERO;

        if (categoryId.equals(CategoryEnum.GOLD_SILVER.getValue().intValue())) {
            // 金包银：净金重 + 净银重
            return safeNetGoldWeight.add(safeNetSilverWeight);
        } else if (categoryId.equals(CategoryEnum.SILVER.getValue().intValue())) {
            // 银饰：只用净银重
            return safeNetSilverWeight;
        } else {
            // 其他：只用净金重
            return safeNetGoldWeight;
        }
    }
}
