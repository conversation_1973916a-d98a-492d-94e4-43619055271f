package com.xc.boot.modules.order.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 计价方式枚举
 */
@Getter
public enum SalesTypeEnum implements IBaseEnum<Integer> {
    
    BY_WEIGHT(1, "按重量"),
    BY_NUM(2, "按数量");

    private final Integer value;
    private final String label;

    SalesTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
} 