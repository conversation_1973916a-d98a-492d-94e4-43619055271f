package com.xc.boot.modules.order.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改销售单导购请求DTO
 */
@Data
@Schema(description = "修改销售单导购请求DTO")
public class UpdateSoldReceiptSellerDTO {

    @Schema(description = "销售单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "销售单ID不能为空")
    private Long soldReceiptId;

    @Schema(description = "主销导购ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主销导购ID不能为空")
    private Long mainSellerId;

    @Schema(description = "辅销导购ID")
    private Long supportSellerId;

    @Schema(description = "收银员ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "收银员ID不能为空")
    private Long cashierId;
}
