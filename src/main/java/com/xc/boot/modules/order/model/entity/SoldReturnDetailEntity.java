package com.xc.boot.modules.order.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 销售退货单详情
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Table(value = "sold_return_detail", comment = "销售退货单详情")
public class SoldReturnDetailEntity extends BaseEntity {

    /**
     * 所属商户ID
     */
    @Column("company_id")
    private Long companyId;

    /**
     * 所属门店ID
     */
    @Column("merchant_id")
    private Long merchantId;

    /**
     * 退货单号
     */
    @Column("return_code")
    private String returnCode;

    /**
     * 退货单ID
     */
    @Column("return_id")
    private Long returnId;

    /**
     * 货品ID
     */
    @Column("goods_id")
    private Long goodsId;

    /**
     * 货品编号
     */
    @Column("goods_sn")
    private String goodsSn;

    /**
     * 旧料ID
     */
    @Column("old_material_id")
    private Long oldMaterialId;

    /**
     * 销售方式(1:按重量,2:按数量)
     */
    @Column("sales_type")
    private Integer salesType;

    /**
     * 原购数量
     */
    @Column("origin_num")
    private Integer originNum;

    /**
     * 退货数量
     */
    @Column("num")
    private Integer num;

    /**
     * 净金重(g)
     */
    @Column("net_gold_weight")
    private BigDecimal netGoldWeight;

    /**
     * 净银重(g)
     */
    @Column("net_silver_weight")
    private BigDecimal netSilverWeight;

    /**
     * 成本价(分)
     */
    @Column("cost_price")
    private Long costPrice;

    /**
     * 标签价(分)
     */
    @Column("tag_price")
    private Long tagPrice;

    /**
     * 实收金额(分)
     */
    @Column("revenue_price")
    private Long revenuePrice;

    /**
     * 退货金额(分)
     */
    @Column("return_price")
    private Long returnPrice;

    /**
     * 折旧费(分)
     */
    @Column("depreciation_price")
    private Long depreciationPrice;

    /**
     * 应退金额(分)
     */
    @Column("pay_price")
    private Long payPrice;
} 