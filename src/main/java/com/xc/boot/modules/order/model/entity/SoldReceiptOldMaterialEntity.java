package com.xc.boot.modules.order.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 销售单旧料明细
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Table(value = "sold_receipt_old_material", comment = "销售单旧料明细")
public class SoldReceiptOldMaterialEntity extends BaseEntity {

    /**
     * 销售单ID
     */
    @Column("sold_receipt_id")
    private Long soldReceiptId;

    /**
     * 旧料ID
     */
    @Column("old_material_id")
    private Long oldMaterialId;

    /**
     * 旧料编号
     */
    @Column("old_material_sn")
    private String oldMaterialSn;

    /**
     * 数量
     */
    @Column("num")
    private Integer num;

    /**
     * 净金重(g)
     */
    @Column("net_gold_weight")
    private BigDecimal netGoldWeight;

    /**
     * 净银重(g)
     */
    @Column("net_silver_weight")
    private BigDecimal netSilverWeight;

    /**
     * 回收金价(分)
     */
    @Column("gold_price")
    private Long goldPrice;

    /**
     * 回收银价(分)
     */
    @Column("silver_price")
    private Long silverPrice;

    /**
     * 回收单价(分)
     */
    @Column("recycle_price")
    private Long recyclePrice;

    /**
     * 工费单价(分)
     */
    @Column("sale_work_price")
    private Long saleWorkPrice;

    /**
     * 旧料快照
     */
    @Column("data_snapshot")
    private String dataSnapshot;

}
