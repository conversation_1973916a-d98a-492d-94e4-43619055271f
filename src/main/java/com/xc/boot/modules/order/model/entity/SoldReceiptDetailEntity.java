package com.xc.boot.modules.order.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 销售单明细
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Table(value = "sold_receipt_details", comment = "销售单明细")
public class SoldReceiptDetailEntity extends BaseEntity {

    /**
     * 销售单ID
     */
    @Column("sold_receipt_id")
    private Long soldReceiptId;

    /**
     * 明细类型(1-货品 2-旧料 3-赠品)
     */
    @Column("type")
    private Integer type;

    /**
     * 明细ID
     */
    @Column("detail_id")
    private Long detailId;

}
