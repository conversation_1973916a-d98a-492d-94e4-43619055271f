package com.xc.boot.modules.order.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 销售单旧料明细VO
 */
@Data
@Schema(description = "销售单旧料明细VO")
public class SoldReceiptOldMaterialVO {

    @Schema(description = "旧料明细ID")
    private Long oldMaterialDetailId;

    @Schema(description = "销售单ID")
    private Long soldReceiptId;

    @Schema(description = "销售单SN")
    private String soldReceiptSn;

    @Schema(description = "旧料ID")
    private Long oldMaterialId;
    
    @Schema(description = "货品ID")
    private Long goodsId;

    @Schema(description = "货品条码")
    private String goodsSn;

    @Schema(description = "旧料编号")
    private String oldMaterialSn;

    @Schema(description = "旧料名称")
    private String oldMaterialName;

    @Schema(description = "所属大类")
    private String categoryName;

    @Schema(description = "货品小类")
    private String subclassName;

    @Schema(description = "成色")
    private String qualityName;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "小类ID")
    private Long subclassId;

    @Schema(description = "成色ID")
    private Long qualityId;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "净金重(g)")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")
    private BigDecimal netSilverWeight;

    @Schema(description = "回收金价(元)")
    private BigDecimal goldPrice;

    @Schema(description = "回收银价(元)")
    private BigDecimal silverPrice;

    @Schema(description = "回收单价(元)")
    private BigDecimal recyclePrice;

    @Schema(description = "回收金额(元)")
    private BigDecimal recycleAmount;

    @Schema(description = "工费单价(元)")
    private BigDecimal saleWorkPrice;

    @Schema(description = "计价方式(1:按重量,2:按数量)")
    private Integer salesType;

    @Schema(description = "计价方式名称")
    private String salesTypeName;
}
