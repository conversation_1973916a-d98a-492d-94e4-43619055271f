package com.xc.boot.modules.order.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

/**
 * 回收单表单
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "回收单表单")
public class MaterialRecycleForm {

    @Schema(description = "所属门店ID")
    @NotNull(message = "所属门店不能为空")
    private Integer merchantId;

    @Schema(description = "会员ID")
    private Long memberId;

    @Schema(description = "收银员ID")
    @NotNull(message = "收银员不能为空")
    private Long cashierId;

    @Schema(description = "主导购ID")
    @NotNull(message = "主导购不能为空")
    private Integer mainAdviserId;

    @Schema(description = "辅购ID")
    private Integer subAdviserId;

    @Schema(description = "支付方式(0:现金,1:支付宝,2:微信,3:刷卡)")
    @NotNull(message = "支付方式不能为空")
    private Integer payType;

    @Schema(description = "回收日期")
    @NotNull(message = "回收日期不能为空")
    private Date recycleDate;

    @Schema(description = "备注")
    @Length(max = 500, message = "备注长度不能超过500")
    private String remark;

    @Valid
    @Schema(description = "旧料列表")
    @NotNull(message = "旧料列表不能为空")
    @NotEmpty(message = "旧料列表不能为空")
    private List<MaterialRecycleItemForm> items;
} 