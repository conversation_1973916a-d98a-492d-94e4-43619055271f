package com.xc.boot.modules.order.model.vo;

import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 回收单明细VO
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "回收单明细VO")
public class MaterialRecycleDetailVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "物料ID")
    private Long materialId;

    @Schema(description = "旧料编号")
    private String oldMaterialSn;

    @Schema(description = "旧料名称")
    private String name;

    @Schema(description = "旧料图片")
    private List<OldMaterialHasImagesEntity> image;

    @Schema(description = "所属大类")
    private String categoryName;

    @Schema(description = "货品大类")
    private Long categoryId;

    @Schema(description = "货品小类")
    private String subclassName;

    @Schema(description = "货品小类")
    private Long subclassId;

    @Schema(description = "成色")
    private String qualityName;

    @Schema(description = "成色")
    private Long qualityId;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "净金重")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重")
    private BigDecimal netSilverWeight;

    @Schema(description = "计价方式")
    private Integer salesType;

    @Schema(description = "计价方式文本")
    private String salesTypeText;

    @Schema(description = "回收金价")
    private BigDecimal goldPrice;

    @Schema(description = "回收银价")
    private BigDecimal silverPrice;

    @Schema(description = "回收单价")
    private BigDecimal recyclePrice;

    @Schema(description = "回收金额")
    private BigDecimal recycleAmount;

    public BigDecimal getNetGoldWeight() {
        return PriceUtil.formatThreeDecimal(netGoldWeight);
    }

    public BigDecimal getNetSilverWeight() {
        return PriceUtil.formatThreeDecimal(netSilverWeight);
    }

    public BigDecimal getGoldPrice() {
        return PriceUtil.formatTwoDecimal(goldPrice);
    }

    public BigDecimal getSilverPrice() {
        return PriceUtil.formatTwoDecimal(silverPrice);
    }

    public BigDecimal getRecyclePrice() {
        return PriceUtil.formatTwoDecimal(recyclePrice);
    }

    @SuppressWarnings("unused")
    public BigDecimal getRecycleAmount() {
        return PriceUtil.formatTwoDecimal(recycleAmount);
    }
}