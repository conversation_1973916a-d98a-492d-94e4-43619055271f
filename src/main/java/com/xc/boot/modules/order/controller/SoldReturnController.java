package com.xc.boot.modules.order.controller;

import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.modules.order.model.form.SoldReturnForm;
import com.xc.boot.modules.order.model.form.UpdateSoldReturnMemberForm;
import com.xc.boot.modules.order.model.form.UpdateSoldReturnTransactorForm;
import com.xc.boot.modules.order.model.query.SoldReturnQuery;
import com.xc.boot.modules.order.model.vo.SoldReturnInfoVO;
import com.xc.boot.modules.order.model.vo.SoldReturnGoodsDetailVO;
import com.xc.boot.modules.order.model.vo.SoldReturnPageVO;
import com.xc.boot.modules.order.service.SoldReturnService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售退货
 */
@Tag(name = "销售管理-销售退货")
@RestController
@RequestMapping("/api/sold/return")
@RequiredArgsConstructor
public class SoldReturnController {

    private final SoldReturnService soldReturnService;

    @Operation(summary = "销售退货单分页列表")
    @PostMapping("/page")
    public PageResult<SoldReturnPageVO> page(@Validated @RequestBody SoldReturnQuery query) {
        return PageResult.success(soldReturnService.getPage(query));
    }

    @Operation(summary = "查看单个退货单详情")
    @GetMapping("/detail")
    public Result<SoldReturnPageVO> getDetail(@RequestParam @Validated @NotNull(message = "id不能为空") Long id) {
        return Result.success(soldReturnService.getDetail(id));
    }

    @Operation(summary = "查看退货单明细列表")
    @GetMapping("/detailList")
    public Result<List<SoldReturnGoodsDetailVO>> getDetailList(@RequestParam @Validated @NotNull(message = "Id不能为空") Long id) {
        return Result.success(soldReturnService.getDetailList(id));
    }

    @Operation(summary = "修改退货单经办人")
    @PostMapping("/updateTransactor")
    public Result<?> updateTransactor(@RequestBody @Validated UpdateSoldReturnTransactorForm form) {
        soldReturnService.updateTransactor(form);
        return Result.success();
    }

    @Operation(summary = "修改退货单会员")
    @PostMapping("/updateMember")
    public Result<?> updateMember(@RequestBody @Validated UpdateSoldReturnMemberForm form) {
        soldReturnService.updateMember(form);
        return Result.success();
    }

    @Operation(summary = "根据货品条码或者销售单号查询销售单")
    @GetMapping("/getSaleInfo")
    public Result<List<SoldReturnInfoVO>> getSaleInfo(@RequestParam String keyword,
                                                      @RequestParam(required = false) Long merchantId) {
        return Result.success(soldReturnService.getSaleInfo(keyword, merchantId));
    }

    @Operation(summary = "销售退货开单")
    @PostMapping("/create")
    public Result<?> create(@RequestBody @Validated SoldReturnForm form) {
        try {
            soldReturnService.create(form);
        }catch (Exception e) {
            SnUtils.syncReturnMaxSequence();
            SnUtils.syncOldMaterialMaxSequence();
            throw e;
        }
        return Result.success();
    }

} 