package com.xc.boot.modules.order.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 支付方式枚举
 */
@Getter
public enum PayTypeEnum implements IBaseEnum<Integer> {
    
    CASH(0, "现金"),
    ALIPAY(1, "支付宝"),
    WECHAT(2, "微信"),
    CARD(3, "刷卡");

    private final Integer value;
    private final String label;

    PayTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
} 