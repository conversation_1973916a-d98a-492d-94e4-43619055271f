package com.xc.boot.modules.order.model.vo;

import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 销售单货品明细VO
 */
@Data
@Schema(description = "销售单货品明细VO")
public class SoldReceiptGoodsVO {

    @Schema(description = "货品明细ID")
    private Long goodsDetailId;

    @Schema(description = "销售单ID")
    private Long soldReceiptId;

    @Schema(description = "货品ID")
    private Long goodsId;
    
    @Schema(description = "货品条码")
    private String goodsSn;
    
    @Schema(description = "货品名称")
    private String goodsName;

    @Schema(description = "所属大类ID")
    private Integer categoryId;

    @Schema(description = "所属大类名称")
    private String categoryName;

    @Schema(description = "货品小类ID")
    private Integer subclassId;

    @Schema(description = "货品小类名称")
    private String subclassName;

    @Schema(description = "成色ID")
    private Integer qualityId;

    @Schema(description = "成色名称")
    private String qualityName;

    @Schema(description = "品牌ID")
    private Integer brandId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "款式ID")
    private Integer styleId;

    @Schema(description = "款式名称")
    private String styleName;

    @Schema(description = "工艺ID")
    private Integer technologyId;

    @Schema(description = "工艺名称")
    private String technologyName;

    @Schema(description = "重量(g)")
    private BigDecimal weight;

    @Schema(description = "净金重(g)")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")
    private BigDecimal netSilverWeight;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "库存数量")
    private Integer stockNum;
    
    @Schema(description = "标签单价(元)")
    private BigDecimal tagPrice;
    
    @Schema(description = "工费单价(元)")
    private BigDecimal saleWorkPrice;

    @Schema(description = "销工费计价方式(1:按重量,2:按数量)")
    private Integer saleWorkPriceType;

    @Schema(description = "计价方式(1:按重量,2:按数量)")
    private Integer saleType;

    // 兼容
    public Integer getSalesType() {
        return saleType;
    }
    
    @Schema(description = "金价(元)")
    private BigDecimal goldPrice;
    
    @Schema(description = "银价(元)")
    private BigDecimal silverPrice;

    @Schema(description = "回收金价(元)")
    private BigDecimal recycleGoldPrice;

    @Schema(description = "回收银价(元)")
    private BigDecimal recycleSilverPrice;

    @Schema(description = "销售单价(元)")
    private BigDecimal soldPrice;
    
    @Schema(description = "优惠金额(元)")
    private BigDecimal discountAmount;
    
    @Schema(description = "应收金额(元)")
    private BigDecimal realAmount;
    
    @Schema(description = "员工折扣(百分比)")
    private BigDecimal staffDiscount;

    @Schema(description = "工费折扣优惠金额(元)")
    private BigDecimal workPriceDiscountAmount;

    @Schema(description = "工费折扣(百分比)")
    private BigDecimal workPriceDiscount;

    @Schema(description = "退货数量")
    private Integer refundNum;
    
    @Schema(description = "实时黄金价格(元)")
    private BigDecimal goldPriceNow;
    
    @Schema(description = "实时白银价格(元)")
    private BigDecimal silverPriceNow;
    
    @Schema(description = "实时铂金价格(元)")
    private BigDecimal platinumPriceNow;

    @Schema(description = "销售单sn")
    private String soldReceiptSn;

    @Schema(description = "货品图片列表")
    private List<GoodsHasImagesEntity> images = new ArrayList<>();
}
