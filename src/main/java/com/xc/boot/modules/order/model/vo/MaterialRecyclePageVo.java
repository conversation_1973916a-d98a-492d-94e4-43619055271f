package com.xc.boot.modules.order.model.vo;

import com.xc.boot.common.util.PriceUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "回收单分页列表VO")
public class MaterialRecyclePageVo {
    
    @Schema(description = "id")
    private Long id;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;

    @Schema(description = "回收单号")
    private String recycleCode;

    @Schema(description = "所属门店ID")
    private Integer merchantId;

    @Schema(description = "所属门店")
    private String merchantName;

    @Schema(description = "主导购ID")
    private Integer mainAdviserId;

    @Schema(description = "主导购")
    private String mainAdviserName;

    @Schema(description = "辅购ID")
    private Integer subAdviserId;

    @Schema(description = "辅购")
    private String subAdviserName;

    @Schema(description = "会员ID")
    private Integer memberId;

    @Schema(description = "会员姓名")
    private String memberName;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "总金重")
    private BigDecimal totalNetGoldWeight;

    @Schema(description = "总银重")
    private BigDecimal totalNetSilverWeight;

    @Schema(description = "回收金额")
    private BigDecimal totalPrice;

    @Schema(description = "付款方式")
    private Integer payType;

    @Schema(description = "付款方式文本")
    private String payTypeText;

    @Schema(description = "收银员ID")
    private Integer cashierId;

    @Schema(description = "收银员")
    private String cashierName;

    @Schema(description = "创建人ID")
    private Integer createdBy;

    @Schema(description = "创建人姓名")
    private String createdByName;

    @Schema(description = "备注")
    private String remark;


    public BigDecimal getTotalNetGoldWeight() {
        return PriceUtil.formatThreeDecimal(totalNetGoldWeight);
    }

    public BigDecimal getTotalNetSilverWeight() {
        return PriceUtil.formatThreeDecimal(totalNetSilverWeight);
    }

    public BigDecimal getTotalPrice() {
        return PriceUtil.formatTwoDecimal(totalPrice);
    }
}