package com.xc.boot.modules.order.service;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.modules.order.model.query.SoldGiftPageQuery;
import com.xc.boot.modules.order.model.query.SoldGoodsPageQuery;
import com.xc.boot.modules.order.model.query.SoldReceiptPageQuery;
import com.xc.boot.modules.order.model.vo.SoldGiftPageVO;
import com.xc.boot.modules.order.model.vo.SoldGoodsPageVO;
import com.xc.boot.modules.order.model.vo.SoldReceiptDetailVO;
import com.xc.boot.modules.order.model.vo.SoldReceiptFullDetailVO;
import com.xc.boot.modules.order.model.vo.SoldReceiptPageVO;

/**
 * 销售开单查询服务接口
 */
public interface SoldQueryService {

    /**
     * 货品查询分页列表
     *
     * @param query 查询参数
     * @return 分页列表
     */
    Page<SoldGoodsPageVO> goodsPage(SoldGoodsPageQuery query);

    /**
     * 销售单查询分页列表
     *
     * @param query 查询参数
     * @return 分页列表
     */
    Page<SoldReceiptPageVO> receiptPage(SoldReceiptPageQuery query);

    /**
     * 赠品查询分页列表
     *
     * @param query 查询参数
     * @return 分页列表
     */
    Page<SoldGiftPageVO> giftPage(SoldGiftPageQuery query);

    /**
     * 取单 - 获取挂单中销售单详情
     *
     * @param soldReceiptId 销售单ID
     * @return 销售单详情
     */
    SoldReceiptDetailVO getPendingReceiptDetail(Long soldReceiptId);

    /**
     * 获取销售单完整详情
     *
     * @param soldReceiptId 销售单ID
     * @return 销售单完整详情
     */
    SoldReceiptFullDetailVO getSoldReceiptDetail(Long soldReceiptId);
}
