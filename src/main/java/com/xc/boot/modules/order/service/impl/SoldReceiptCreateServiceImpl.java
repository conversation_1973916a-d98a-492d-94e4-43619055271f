package com.xc.boot.modules.order.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.common.util.StockUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.gift.mapper.GiftMapper;
import com.xc.boot.modules.gift.model.bo.GiftStockNumChangeBO;
import com.xc.boot.modules.gift.model.entity.GiftEntity;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.bo.StockNumChangeBO;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.oldmaterial.model.bo.OldMaterialStockNumChangeBO;
import com.xc.boot.modules.merchant.mapper.ActualGoldPriceMapper;
import com.xc.boot.modules.merchant.model.entity.ActualGoldPriceEntity;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialHasImagesMapper;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialMapper;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialEntity;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialHasImagesEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import com.xc.boot.modules.goods.mapper.GoodsHasImagesMapper;
import com.xc.boot.modules.order.mapper.*;
import com.xc.boot.modules.order.model.dto.SoldReceiptCreateDTO;
import com.xc.boot.modules.order.model.entity.*;
import com.xc.boot.modules.order.model.enums.SalesTypeEnum;
import com.xc.boot.modules.order.model.enums.SoldReceiptDetailType;
import com.xc.boot.modules.order.model.enums.SoldReceiptStatusEnum;
import com.xc.boot.modules.order.service.SoldReceiptCreateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.xc.boot.modules.merchant.model.entity.table.ActualGoldPriceTableDef.ACTUAL_GOLD_PRICE;

/**
 * 销售单创建服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SoldReceiptCreateServiceImpl implements SoldReceiptCreateService {

    private final SoldReceiptMapper soldReceiptMapper;
    private final SoldReceiptGoodsMapper soldReceiptGoodsMapper;
    private final SoldReceiptOldMaterialMapper soldReceiptOldMaterialMapper;
    private final SoldReceiptGiftMapper soldReceiptGiftMapper;
    private final SoldReceiptDetailMapper soldReceiptDetailMapper;
    private final SoldReceiptPaymentMapper soldReceiptPaymentMapper;
    private final GoodsMapper goodsMapper;
    private final GoodsHasImagesMapper goodsHasImagesMapper;
    private final OldMaterialMapper oldMaterialMapper;
    private final OldMaterialHasImagesMapper oldMaterialHasImagesMapper;
    private final GiftMapper giftMapper;
    private final ActualGoldPriceMapper actualGoldPriceMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSoldReceipt(SoldReceiptCreateDTO dto) {
        // 判断是否为取单操作
        if (dto.getSoldReceiptId() != null) {
            return updateSoldReceipt(dto);
        } else {
            return createNewSoldReceipt(dto);
        }
    }

    /**
     * 创建新销售单
     */
    private Long createNewSoldReceipt(SoldReceiptCreateDTO dto) {
        // 1. 数据验证
        validateCreateData(dto);

        // 2. 获取实时价格
        ActualGoldPriceEntity actualPrice = getLatestActualPrice();

        // 3. 查询并验证货品信息
        Map<Long, GoodsEntity> goodsMap = validateAndGetGoods(dto.getGoodsList());

        // 4. 查询并验证赠品信息
        Map<Long, GiftEntity> giftMap = validateAndGetGifts(dto.getGiftList());

        // 5. 创建旧料记录
        List<OldMaterialEntity> oldMaterials = createOldMaterials(dto.getOldMaterialList(), dto.getMerchantId());

        // 6. 计算销售单统计数据
        SoldReceiptSummary summary = calculateSummary(dto, goodsMap, giftMap, oldMaterials);

        // 7. 创建销售单主记录
        SoldReceiptEntity soldReceipt = createSoldReceiptEntity(dto, summary);
        soldReceiptMapper.insertSelective(soldReceipt);

        // 8. 创建货品明细
        createGoodsDetails(soldReceipt.getId(), dto.getGoodsList(), goodsMap, actualPrice);

        // 9. 创建旧料明细
        createOldMaterialDetails(soldReceipt.getId(), dto.getOldMaterialList(), oldMaterials);

        // 10. 创建赠品明细
        createGiftDetails(soldReceipt.getId(), dto.getGiftList(), giftMap);

        // 11. 创建支付明细
        createPaymentDetails(soldReceipt.getId(), dto.getPaymentList());

        // 12. 创建销售单明细记录
        createSoldReceiptDetails(soldReceipt.getId(), dto);

        // 13. 更新库存
        updateStocks(dto, oldMaterials, soldReceipt.getStatus(), soldReceipt.getReceiptSn());

        // 14. 记录操作日志
        OpLogUtils.appendOpLog("销售管理-创建销售单", "创建销售单", "销售单号: " + soldReceipt.getReceiptSn());

        return soldReceipt.getId();
    }

    /**
     * 取单逻辑 - 更新现有销售单
     */
    private Long updateSoldReceipt(SoldReceiptCreateDTO dto) {
        // 1. 验证销售单是否存在且状态为挂单中
        SoldReceiptEntity existingSoldReceipt = validateExistingSoldReceipt(dto.getSoldReceiptId());
        Integer originalStatus = existingSoldReceipt.getStatus();

        // 2. 验证新订单金额不能小于已收金额
        validateOrderAmountForUpdate(dto, existingSoldReceipt);

        // 3. 获取实时价格
        ActualGoldPriceEntity actualPrice = getLatestActualPrice();

        // 4. 处理货品明细
        updateGoodsDetails(dto.getSoldReceiptId(), dto.getGoodsList(), actualPrice, originalStatus);

        // 5. 处理旧料明细
        updateOldMaterialDetails(dto.getSoldReceiptId(), dto.getOldMaterialList(), originalStatus, existingSoldReceipt.getMerchantId());

        // 6. 处理赠品明细
        updateGiftDetails(dto.getSoldReceiptId(), dto.getGiftList(), originalStatus);

        // 7. 处理支付明细
        updatePaymentDetails(dto.getSoldReceiptId(), dto.getPaymentList());

        // 8. 重新计算并更新销售单统计数据
        updateSoldReceiptSummary(dto, existingSoldReceipt);

        // 9. 检查状态变化，如果从挂单变为已完成，需要调整库存
        if (originalStatus == 0 && existingSoldReceipt.getStatus() == 1) {
            adjustStockForStatusChange(dto.getSoldReceiptId());
        }

        // 10. 更新销售单明细记录
        updateSoldReceiptDetails(dto.getSoldReceiptId(), dto);

        // 11. 记录操作日志
        OpLogUtils.appendOpLog("销售管理-取单", "取单", "销售单号: " + existingSoldReceipt.getReceiptSn());

        return dto.getSoldReceiptId();
    }

    /**
     * 验证现有销售单
     */
    private SoldReceiptEntity validateExistingSoldReceipt(Long soldReceiptId) {
        SoldReceiptEntity soldReceipt = soldReceiptMapper.selectOneByQuery(
                QueryWrapper.create()
                        .where(SoldReceiptEntity::getId).eq(soldReceiptId)
                        .and(SoldReceiptEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );

        if (soldReceipt == null) {
            throw new BusinessException("销售单不存在");
        }

        if (soldReceipt.getStatus() != 0) {
            throw new BusinessException("只能取单状态为挂单中的销售单");
        }

        return soldReceipt;
    }

    /**
     * 验证订单金额不能小于已收金额
     */
    private void validateOrderAmountForUpdate(SoldReceiptCreateDTO dto, SoldReceiptEntity existingSoldReceipt) {
        // 计算新订单总金额
        BigDecimal newOrderAmount = calculateNewOrderAmount(dto);

        // 获取已收金额
        BigDecimal receivedAmount = PriceUtil.fen2yuan(existingSoldReceipt.getReceivedAmount());

        if (newOrderAmount.compareTo(receivedAmount) < 0) {
            throw new BusinessException("订单金额不能小于已收金额: " + receivedAmount + "元");
        }
    }

    /**
     * 计算新订单总金额
     */
    private BigDecimal calculateNewOrderAmount(SoldReceiptCreateDTO dto) {
        BigDecimal totalAmount = BigDecimal.ZERO;

        // 货品金额
        if (dto.getGoodsList() != null) {
            for (SoldReceiptCreateDTO.SoldGoodsItemDTO goodsItem : dto.getGoodsList()) {
                // 1. 计算工费金额（根据工费计价方式）
                BigDecimal workAmount = calculateWorkAmount(goodsItem);

                // 2. 计算工费总额 = 工费金额 * 数量
                BigDecimal totalWorkAmount = workAmount.multiply(new BigDecimal(goodsItem.getNum()));

                // 3. 计算工费折扣金额 = 工费总额 * (100 - 工费折扣) / 100
                // 工费折扣90%表示打9折，优惠10%
                BigDecimal workPriceDiscountAmount = BigDecimal.ZERO;
                if (goodsItem.getWorkPriceDiscount() != null &&
                    goodsItem.getWorkPriceDiscount().compareTo(BigDecimal.ZERO) > 0 &&
                    goodsItem.getWorkPriceDiscount().compareTo(BigDecimal.valueOf(100)) < 0) {
                    // 优惠金额 = 工费总额 * (100 - 折扣) / 100
                    BigDecimal discountRate = BigDecimal.valueOf(100).subtract(goodsItem.getWorkPriceDiscount())
                            .divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
                    workPriceDiscountAmount = totalWorkAmount.multiply(discountRate).setScale(2, RoundingMode.HALF_UP);
                }

                // 4. 计算销售价 = 标签价 * 数量 + (工费总额 - 工费折扣金额)
                BigDecimal salesPrice = goodsItem.getTagPrice().multiply(new BigDecimal(goodsItem.getNum()))
                        .add(totalWorkAmount.subtract(workPriceDiscountAmount));

                // 5. 计算员工折扣金额 = 销售价 * (100 - 员工折扣) / 100
                // 员工折扣50%表示打5折，优惠50%
                BigDecimal staffDiscountAmount = BigDecimal.ZERO;
                if (goodsItem.getStaffDiscount() != null &&
                    goodsItem.getStaffDiscount().compareTo(BigDecimal.ZERO) > 0 &&
                    goodsItem.getStaffDiscount().compareTo(BigDecimal.valueOf(100)) < 0) {
                    // 优惠金额 = 销售价 * (100 - 折扣) / 100
                    BigDecimal discountRate = BigDecimal.valueOf(100).subtract(goodsItem.getStaffDiscount())
                            .divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
                    staffDiscountAmount = salesPrice.multiply(discountRate).setScale(2, RoundingMode.HALF_UP);
                }

                // 6. 计算货品金额 = (标签价 + 工费金额) * 数量
                BigDecimal goodsAmount = goodsItem.getTagPrice().add(workAmount)
                        .multiply(new BigDecimal(goodsItem.getNum()));

                // 7. 计算总优惠金额 = 工费折扣金额 + 员工折扣金额
                BigDecimal totalDiscountAmount = workPriceDiscountAmount.add(staffDiscountAmount);

                // 9. 计算应收金额 = 货品金额 - 总优惠金额
                BigDecimal realAmount = goodsAmount.subtract(totalDiscountAmount);

                totalAmount = totalAmount.add(realAmount);
            }
        }

        // 赠品金额
        if (dto.getGiftList() != null) {
            for (SoldReceiptCreateDTO.SoldGiftItemDTO giftItem : dto.getGiftList()) {
                BigDecimal giftAmount = giftItem.getSoldPrice().multiply(new BigDecimal(giftItem.getNum()));
                totalAmount = totalAmount.add(giftAmount);
            }
        }

        // 减去旧料抵扣金额
        if (dto.getOldMaterialList() != null) {
            for (SoldReceiptCreateDTO.SoldOldMaterialItemDTO oldMaterialItem : dto.getOldMaterialList()) {
                BigDecimal deductionAmount = oldMaterialItem.getRecyclePrice().multiply(new BigDecimal(oldMaterialItem.getNum()));
                totalAmount = totalAmount.subtract(deductionAmount);
            }
        }

        // 减去抹零金额
        if (dto.getAdjustAmount() != null) {
            totalAmount = totalAmount.subtract(dto.getAdjustAmount());
        }

        return totalAmount;
    }

    /**
     * 计算工费金额（根据工费计价方式）
     */
    private BigDecimal calculateWorkAmount(SoldReceiptCreateDTO.SoldGoodsItemDTO goodsItem) {
        if (goodsItem.getSaleWorkPrice() == null || goodsItem.getSaleWorkPrice().compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        // 获取工费计价方式，默认按数量
        Integer workPriceType = goodsItem.getSaleWorkPriceType() != null ?
                goodsItem.getSaleWorkPriceType() : SalesTypeEnum.BY_NUM.getValue();

        if (workPriceType.equals(SalesTypeEnum.BY_WEIGHT.getValue())) {
            // 按重量：根据商品类别计算工费金额
            // 需要从货品信息中获取净金重和净银重
            GoodsEntity goods = goodsMapper.selectOneById(goodsItem.getGoodsId());
            if (goods != null) {
                BigDecimal netGoldWeight = goods.getNetGoldWeight() != null ? goods.getNetGoldWeight() : BigDecimal.ZERO;
                BigDecimal netSilverWeight = goods.getNetSilverWeight() != null ? goods.getNetSilverWeight() : BigDecimal.ZERO;

                // 根据商品类别计算工费重量
                BigDecimal workWeight = calculateWorkWeight(goods.getCategoryId(), netGoldWeight, netSilverWeight);
                return goodsItem.getSaleWorkPrice().multiply(workWeight).setScale(2, RoundingMode.HALF_UP);
            }
        }

        // 按数量：工费金额 = 工费单价
        return goodsItem.getSaleWorkPrice().setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 根据商品类别计算工费重量
     * a. 金包银：工费金额 = 工费单价 × (净金重 + 净银重)
     * b. 银饰：工费金额 = 工费单价 × 净银重
     * c. 其他：工费金额 = 工费单价 × 净金重
     */
    private BigDecimal calculateWorkWeight(Integer categoryId, BigDecimal netGoldWeight, BigDecimal netSilverWeight) {
        if (categoryId == null) {
            return BigDecimal.ZERO;
        }

        if (categoryId.equals(CategoryEnum.GOLD_SILVER.getValue().intValue())) {
            // 金包银：净金重 + 净银重
            return netGoldWeight.add(netSilverWeight);
        } else if (categoryId.equals(CategoryEnum.SILVER.getValue().intValue())) {
            // 银饰：只用净银重
            return netSilverWeight;
        } else {
            // 其他：只用净金重
            return netGoldWeight;
        }
    }

    /**
     * 更新货品明细
     */
    private void updateGoodsDetails(Long soldReceiptId, List<SoldReceiptCreateDTO.SoldGoodsItemDTO> goodsList,
                                   ActualGoldPriceEntity actualPrice, Integer soldReceiptStatus) {
        if (goodsList == null) {
            goodsList = List.of();
        }

        // 获取现有货品明细
        List<SoldReceiptGoodsEntity> existingGoodsDetails = soldReceiptGoodsMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptGoodsEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        // 获取现有货品ID集合
        Set<Long> existingGoodsDetailIds = existingGoodsDetails.stream()
                .map(SoldReceiptGoodsEntity::getId)
                .collect(Collectors.toSet());

        // 获取请求中的货品明细ID集合
        Set<Long> requestGoodsDetailIds = goodsList.stream()
                .map(SoldReceiptCreateDTO.SoldGoodsItemDTO::getGoodsDetailId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 需要删除的货品明细ID
        Set<Long> toDeleteGoodsDetailIds = existingGoodsDetailIds.stream()
                .filter(id -> !requestGoodsDetailIds.contains(id))
                .collect(Collectors.toSet());

        // 批量删除不在请求中的货品明细并恢复库存
        if (!toDeleteGoodsDetailIds.isEmpty()) {
            deleteGoodsDetailsAndRestoreStock(toDeleteGoodsDetailIds, soldReceiptStatus);
        }

        // 验证并获取货品信息
        Map<Long, GoodsEntity> goodsMap = validateAndGetGoods(goodsList);

        // 分类处理：更新和新增
        List<SoldReceiptGoodsEntity> toUpdateGoods = new ArrayList<>();
        List<SoldReceiptGoodsEntity> toInsertGoods = new ArrayList<>();
        List<StockNumChangeBO> stockChanges = new ArrayList<>();

        for (SoldReceiptCreateDTO.SoldGoodsItemDTO goodsItem : goodsList) {
            GoodsEntity goods = goodsMap.get(goodsItem.getGoodsId());

            if (goodsItem.getGoodsDetailId() != null && existingGoodsDetailIds.contains(goodsItem.getGoodsDetailId())) {
                // 更新现有货品明细
                SoldReceiptGoodsEntity existingDetail = existingGoodsDetails.stream()
                        .filter(detail -> detail.getId().equals(goodsItem.getGoodsDetailId()))
                        .findFirst()
                        .orElseThrow(() -> new BusinessException("货品明细不存在"));

                // 计算数量变化
                int quantityChange = goodsItem.getNum() - existingDetail.getNum();

                // 验证库存是否足够
                if (quantityChange > 0 && goods.getStockNum() < quantityChange) {
                    throw new BusinessException("货品 " + goods.getName() + " 库存不足，最大可出库数: " +
                            (existingDetail.getNum() + goods.getStockNum()));
                }

                // 更新货品明细实体
                updateGoodsDetailEntity(existingDetail, goodsItem, goods, actualPrice);
                toUpdateGoods.add(existingDetail);

                // 收集库存变更
                if (quantityChange != 0) {
                    stockChanges.add(createGoodsStockChange(goods.getId(), quantityChange, soldReceiptStatus));
                }
            } else {
                // 创建新货品明细
                if (goods.getStockNum() < goodsItem.getNum()) {
                    throw new BusinessException("货品 " + goods.getName() + " 库存不足");
                }

                SoldReceiptGoodsEntity goodsDetail = new SoldReceiptGoodsEntity();
                goodsDetail.setSoldReceiptId(soldReceiptId);
                updateGoodsDetailEntity(goodsDetail, goodsItem, goods, actualPrice);
                toInsertGoods.add(goodsDetail);

                // 收集库存变更
                stockChanges.add(createGoodsStockChange(goods.getId(), goodsItem.getNum(), soldReceiptStatus));
            }
        }

        // 批量更新货品明细
        if (!toUpdateGoods.isEmpty()) {
            for (SoldReceiptGoodsEntity goodsDetail : toUpdateGoods) {
                soldReceiptGoodsMapper.update(goodsDetail);
            }
        }

        // 批量插入新货品明细
        if (!toInsertGoods.isEmpty()) {
            soldReceiptGoodsMapper.insertBatchSelective(toInsertGoods);
        }

        // 批量更新库存
        if (!stockChanges.isEmpty()) {
            StockUtils.updateStocks(stockChanges);

            // 记录货品操作日志
            SoldReceiptEntity soldReceipt = soldReceiptMapper.selectOneById(soldReceiptId);
            String receiptSn = soldReceipt != null ? soldReceipt.getReceiptSn() : "未知";

            for (StockNumChangeBO stockChange : stockChanges) {
                GoodsEntity goods = goodsMap.get(stockChange.getGoodsId());
                if (goods != null) {
                    String logContent = buildGoodsLogContent(receiptSn, stockChange);
                    OpLogUtils.appendGoodsLog("销售管理-取单", logContent, stockChange, goods);
                }
            }
        }
    }

    /**
     * 删除货品明细并恢复库存
     */
    private void deleteGoodsDetailsAndRestoreStock(Set<Long> goodsDetailIds, Integer soldReceiptStatus) {
        List<SoldReceiptGoodsEntity> goodsDetails = soldReceiptGoodsMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptGoodsEntity::getId).in(goodsDetailIds)
        );

        // 恢复库存
        List<StockNumChangeBO> stockChanges = new ArrayList<>();
        for (SoldReceiptGoodsEntity goodsDetail : goodsDetails) {
            StockNumChangeBO.StockNumChangeBOBuilder builder = StockNumChangeBO.builder()
                    .goodsId(goodsDetail.getGoodsId())
                    .stockNum(goodsDetail.getNum()); // 增加库存

            if (soldReceiptStatus != null && soldReceiptStatus == 1) {
                // 已完成状态：减少销售数量
                builder.soldNum(-goodsDetail.getNum()).comment("取单移除货品-恢复库存");
            } else {
                // 挂单状态：减少冻结数量
                builder.frozenNum(-goodsDetail.getNum()).comment("取单移除货品-恢复库存");
            }

            stockChanges.add(builder.build());
        }

        if (!stockChanges.isEmpty()) {
            StockUtils.updateStocks(stockChanges);

            // 记录货品操作日志
            if (!goodsDetails.isEmpty()) {
                Long soldReceiptId = goodsDetails.get(0).getSoldReceiptId();
                SoldReceiptEntity soldReceipt = soldReceiptMapper.selectOneById(soldReceiptId);
                String receiptSn = soldReceipt != null ? soldReceipt.getReceiptSn() : "未知";

                // 批量查询货品信息
                Set<Long> goodsIds = stockChanges.stream().map(StockNumChangeBO::getGoodsId).collect(Collectors.toSet());
                Map<Long, GoodsEntity> goodsMap = goodsIds.isEmpty() ? Collections.emptyMap() :
                    goodsMapper.selectListByIds(goodsIds).stream().collect(Collectors.toMap(GoodsEntity::getId, g -> g));

                for (StockNumChangeBO stockChange : stockChanges) {
                    GoodsEntity goods = goodsMap.get(stockChange.getGoodsId());
                    if (goods != null) {
                        String logContent = buildGoodsLogContent(receiptSn, stockChange);
                        OpLogUtils.appendGoodsLog("销售管理-取单", logContent, stockChange, goods);
                    }
                }
            }
        }

        // 删除货品明细记录
        soldReceiptGoodsMapper.deleteBatchByIds(goodsDetailIds);

        // 删除对应的销售单明细记录
        soldReceiptDetailMapper.deleteByQuery(
                QueryWrapper.create()
                        .where(SoldReceiptDetailEntity::getDetailId).in(goodsDetailIds)
                        .and(SoldReceiptDetailEntity::getType).eq(SoldReceiptDetailType.GOODS.getValue())
        );
    }



    /**
     * 更新货品明细实体
     */
    private void updateGoodsDetailEntity(SoldReceiptGoodsEntity goodsDetail, SoldReceiptCreateDTO.SoldGoodsItemDTO goodsItem,
                                       GoodsEntity goods, ActualGoldPriceEntity actualPrice) {
        goodsDetail.setGoodsId(goodsItem.getGoodsId());
        goodsDetail.setGoodsSn(goods.getGoodsSn());
        goodsDetail.setNum(goodsItem.getNum());
        goodsDetail.setTagPrice(PriceUtil.yuan2fen(goodsItem.getTagPrice()).longValue());
        goodsDetail.setSaleWorkPrice(goodsItem.getSaleWorkPrice() != null ?
                PriceUtil.yuan2fen(goodsItem.getSaleWorkPrice()).longValue() : 0L);
        goodsDetail.setSaleWorkPriceType(goodsItem.getSaleWorkPriceType() != null ?
                goodsItem.getSaleWorkPriceType() : SalesTypeEnum.BY_NUM.getValue());
        goodsDetail.setSaleType(goods.getSalesType());

        // 设置成本价
        goodsDetail.setCostPrice(goods.getCostPrice() != null ? goods.getCostPrice() : 0L);

        // 设置金价银价
        if (goodsItem.getGoldPrice() != null) {
            goodsDetail.setGoldPrice(PriceUtil.yuan2fen(goodsItem.getGoldPrice()).longValue());
        }
        if (goodsItem.getSilverPrice() != null) {
            goodsDetail.setSilverPrice(PriceUtil.yuan2fen(goodsItem.getSilverPrice()).longValue());
        }

        // 按新公式计算价格
        // 1. 计算工费金额（根据工费计价方式）
        BigDecimal workAmount = calculateWorkAmount(goodsItem);

        // 2. 计算工费总额 = 工费金额 * 数量
        BigDecimal totalWorkAmount = workAmount.multiply(new BigDecimal(goodsItem.getNum()));

        // 3. 计算工费折扣金额 = 工费总额 * (100 - 工费折扣) / 100
        // 工费折扣90%表示打9折，优惠10%
        BigDecimal workPriceDiscountAmount = BigDecimal.ZERO;
        if (goodsItem.getWorkPriceDiscount() != null) {
            goodsDetail.setWorkPriceDiscount(goodsItem.getWorkPriceDiscount());
            if (goodsItem.getWorkPriceDiscount().compareTo(BigDecimal.ZERO) > 0 &&
                goodsItem.getWorkPriceDiscount().compareTo(BigDecimal.valueOf(100)) < 0) {
                // 优惠金额 = 工费总额 * (100 - 折扣) / 100
                BigDecimal discountRate = BigDecimal.valueOf(100).subtract(goodsItem.getWorkPriceDiscount())
                        .divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
                workPriceDiscountAmount = totalWorkAmount.multiply(discountRate).setScale(2, RoundingMode.HALF_UP);
            }
        } else {
            goodsDetail.setWorkPriceDiscount(BigDecimal.ZERO);
        }

        // 4. 计算销售价 = 标签价 * 数量 + (工费总额 - 工费折扣金额)
        BigDecimal salesPrice = goodsItem.getTagPrice().multiply(new BigDecimal(goodsItem.getNum()))
                .add(totalWorkAmount.subtract(workPriceDiscountAmount));

        // 5. 计算员工折扣金额 = 销售价 * (100 - 员工折扣) / 100
        // 员工折扣50%表示打5折，优惠50%
        BigDecimal staffDiscountAmount = BigDecimal.ZERO;
        if (goodsItem.getStaffDiscount() != null) {
            goodsDetail.setStaffDiscount(goodsItem.getStaffDiscount());
            if (goodsItem.getStaffDiscount().compareTo(BigDecimal.ZERO) > 0 &&
                goodsItem.getStaffDiscount().compareTo(BigDecimal.valueOf(100)) < 0) {
                // 优惠金额 = 销售价 * (100 - 折扣) / 100
                BigDecimal discountRate = BigDecimal.valueOf(100).subtract(goodsItem.getStaffDiscount())
                        .divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
                staffDiscountAmount = salesPrice.multiply(discountRate).setScale(2, RoundingMode.HALF_UP);
            }
        } else {
            goodsDetail.setStaffDiscount(BigDecimal.ZERO);
        }

        // 6. 计算货品金额 = (标签价 + 工费金额) * 数量
        BigDecimal goodsAmount = goodsItem.getTagPrice().add(workAmount)
                .multiply(new BigDecimal(goodsItem.getNum()));

        // 7. 计算总优惠金额 = 工费折扣金额 + 员工折扣金额
        BigDecimal totalDiscountAmount = workPriceDiscountAmount.add(staffDiscountAmount);

        // 9. 计算应收金额 = 货品金额 - 总优惠金额
        BigDecimal realAmount = goodsAmount.subtract(totalDiscountAmount);

        // 设置销售单价（标签价 + 工费金额）
        BigDecimal soldPrice = goodsItem.getTagPrice().add(workAmount);
        goodsDetail.setSoldPrice(PriceUtil.yuan2fen(soldPrice).longValue());

        // 转换为分并存储
        Long realAmountFen = PriceUtil.yuan2fen(realAmount).longValue();
        Long discountAmountFen = PriceUtil.yuan2fen(staffDiscountAmount).longValue();
        Integer workPriceDiscountAmountFen = PriceUtil.yuan2fenInt(workPriceDiscountAmount);

        // 存储的是单个货品的应收金额和优惠金额
        goodsDetail.setRealAmount(realAmountFen);
        goodsDetail.setDiscountAmount(discountAmountFen);
        goodsDetail.setWorkPriceDiscountAmount(workPriceDiscountAmountFen);
        goodsDetail.setRefundNum(0);

        // 设置金重银重
        if (goods.getNetGoldWeight() != null) {
            goodsDetail.setGoldWeight(goods.getNetGoldWeight());
        }
        if (goods.getNetSilverWeight() != null) {
            goodsDetail.setSilverWeight(goods.getNetSilverWeight());
        }

        // 设置实时价格
        if (actualPrice != null) {
            goodsDetail.setGoldPriceNow(actualPrice.getGoldPrice().intValue());
            goodsDetail.setSilverPriceNow(actualPrice.getSilverPrice().intValue());
            goodsDetail.setPlatinumPriceNow(actualPrice.getPlatinumPrice().intValue());
        }

        // 设置货品快照
        goodsDetail.setDataSnapshot(JSONUtil.toJsonStr(goods));
    }

    /**
     * 创建货品库存变更对象
     * 注意：在取单逻辑中，所有明细数量变更都应该操作冻结数量，
     * 因为在取单逻辑的最后会统一处理冻结转销售（adjustStockForStatusChange方法）
     */
    private StockNumChangeBO createGoodsStockChange(Long goodsId, int quantityChange, Integer soldReceiptStatus) {
        return StockNumChangeBO.builder()
                .goodsId(goodsId)
                .stockNum(-quantityChange) // 减少库存
                .frozenNum(quantityChange) // 增加冻结数量（统一操作冻结数量）
                .comment("取单更新货品-销售冻结")
                .build();
    }

    /**
     * 更新旧料明细
     */
    private void updateOldMaterialDetails(Long soldReceiptId, List<SoldReceiptCreateDTO.SoldOldMaterialItemDTO> oldMaterialList,
                                        Integer soldReceiptStatus, Long merchantId) {
        if (oldMaterialList == null) {
            oldMaterialList = List.of();
        }

        // 获取现有旧料明细
        List<SoldReceiptOldMaterialEntity> existingOldMaterialDetails = soldReceiptOldMaterialMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptOldMaterialEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        // 获取现有旧料明细ID集合
        Set<Long> existingOldMaterialDetailIds = existingOldMaterialDetails.stream()
                .map(SoldReceiptOldMaterialEntity::getId)
                .collect(Collectors.toSet());

        // 获取请求中的旧料明细ID集合
        Set<Long> requestOldMaterialDetailIds = oldMaterialList.stream()
                .map(SoldReceiptCreateDTO.SoldOldMaterialItemDTO::getOldMaterialDetailId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 需要删除的旧料明细ID
        Set<Long> toDeleteOldMaterialDetailIds = existingOldMaterialDetailIds.stream()
                .filter(id -> !requestOldMaterialDetailIds.contains(id))
                .collect(Collectors.toSet());

        // 删除不在请求中的旧料明细
        if (!toDeleteOldMaterialDetailIds.isEmpty()) {
            deleteOldMaterialDetails(toDeleteOldMaterialDetailIds, soldReceiptStatus);
        }

        // 处理每个旧料明细
        for (SoldReceiptCreateDTO.SoldOldMaterialItemDTO oldMaterialItem : oldMaterialList) {
            if (oldMaterialItem.getOldMaterialDetailId() != null && existingOldMaterialDetailIds.contains(oldMaterialItem.getOldMaterialDetailId())) {
                // 更新现有旧料明细
                updateExistingOldMaterialDetail(oldMaterialItem);
            } else {
                // 创建新旧料明细
                createNewOldMaterialDetail(soldReceiptId, oldMaterialItem, soldReceiptStatus, merchantId);
            }
        }
    }

    /**
     * 删除旧料明细
     */
    private void deleteOldMaterialDetails(Set<Long> oldMaterialDetailIds, Integer soldReceiptStatus) {
        List<SoldReceiptOldMaterialEntity> oldMaterialDetails = soldReceiptOldMaterialMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptOldMaterialEntity::getId).in(oldMaterialDetailIds)
        );

        // 删除旧料记录（如果销售单状态为已完成）
        if (soldReceiptStatus != null && soldReceiptStatus == 1) {
            List<Long> oldMaterialIds = oldMaterialDetails.stream()
                    .map(SoldReceiptOldMaterialEntity::getOldMaterialId)
                    .toList();

            if (!oldMaterialIds.isEmpty()) {
                oldMaterialMapper.deleteBatchByIds(oldMaterialIds);
            }
        }

        // 删除旧料明细记录
        soldReceiptOldMaterialMapper.deleteBatchByIds(oldMaterialDetailIds);

        // 删除对应的销售单明细记录
        soldReceiptDetailMapper.deleteByQuery(
                QueryWrapper.create()
                        .where(SoldReceiptDetailEntity::getDetailId).in(oldMaterialDetailIds)
                        .and(SoldReceiptDetailEntity::getType).eq(SoldReceiptDetailType.OLD_MATERIAL.getValue())
        );
    }

    /**
     * 更新现有旧料明细
     */
    private void updateExistingOldMaterialDetail(SoldReceiptCreateDTO.SoldOldMaterialItemDTO oldMaterialItem) {
        SoldReceiptOldMaterialEntity existingDetail = soldReceiptOldMaterialMapper.selectOneById(oldMaterialItem.getOldMaterialDetailId());
        if (existingDetail == null) {
            throw new BusinessException("旧料明细不存在");
        }

        // 更新旧料明细
        updateOldMaterialDetailEntity(existingDetail, oldMaterialItem);
        soldReceiptOldMaterialMapper.update(existingDetail);

        // 更新旧料主记录
        if (oldMaterialItem.getOldMaterialId() != null) {
            OldMaterialEntity oldMaterial = oldMaterialMapper.selectOneById(oldMaterialItem.getOldMaterialId());
            if (oldMaterial != null) {
                updateOldMaterialEntity(oldMaterial, oldMaterialItem);
                oldMaterialMapper.update(oldMaterial);
            }
        }
    }

    /**
     * 创建新旧料明细
     */
    private void createNewOldMaterialDetail(Long soldReceiptId, SoldReceiptCreateDTO.SoldOldMaterialItemDTO oldMaterialItem,
                                          Integer soldReceiptStatus, Long merchantId) {
        // 创建或获取旧料记录
        OldMaterialEntity oldMaterial;
        if (oldMaterialItem.getOldMaterialId() != null && oldMaterialItem.getOldMaterialId() > 0) {
            oldMaterial = oldMaterialMapper.selectOneById(oldMaterialItem.getOldMaterialId());
            if (oldMaterial == null) {
                throw new BusinessException("旧料记录不存在");
            }
            // 更新旧料信息
            updateOldMaterialEntity(oldMaterial, oldMaterialItem);
            oldMaterialMapper.update(oldMaterial);
        } else {
            // 创建新旧料记录
            oldMaterial = createOldMaterialEntity(oldMaterialItem, soldReceiptStatus, merchantId);
            oldMaterialMapper.insertSelective(oldMaterial);

            // 如果存在货品ID，复制货品图片到旧料
            if (oldMaterialItem.getGoodsId() != null && oldMaterialItem.getGoodsId() > 0) {
                copyGoodsImagesToOldMaterial(oldMaterialItem.getGoodsId(), oldMaterial.getId().longValue());
            }
        }

        // 创建旧料明细
        createSingleOldMaterialDetail(soldReceiptId, oldMaterialItem, oldMaterial, true);
    }

    /**
     * 更新旧料明细实体
     */
    private void updateOldMaterialDetailEntity(SoldReceiptOldMaterialEntity oldMaterialDetail,
                                             SoldReceiptCreateDTO.SoldOldMaterialItemDTO oldMaterialItem) {
        oldMaterialDetail.setOldMaterialSn(oldMaterialDetail.getOldMaterialSn()); // 保持原有编号
        oldMaterialDetail.setNum(oldMaterialItem.getNum());
        oldMaterialDetail.setNetGoldWeight(oldMaterialItem.getNetGoldWeight());
        oldMaterialDetail.setNetSilverWeight(oldMaterialItem.getNetSilverWeight());

        if (oldMaterialItem.getGoldPrice() != null) {
            oldMaterialDetail.setGoldPrice(PriceUtil.yuan2fen(oldMaterialItem.getGoldPrice()).longValue());
        }
        if (oldMaterialItem.getSilverPrice() != null) {
            oldMaterialDetail.setSilverPrice(PriceUtil.yuan2fen(oldMaterialItem.getSilverPrice()).longValue());
        }

        oldMaterialDetail.setRecyclePrice(PriceUtil.yuan2fen(oldMaterialItem.getRecyclePrice()).longValue());

        // 工费单价：为null时默认为0
        if (oldMaterialItem.getSaleWorkPrice() != null) {
            oldMaterialDetail.setSaleWorkPrice(PriceUtil.yuan2fen(oldMaterialItem.getSaleWorkPrice()).longValue());
        } else {
            oldMaterialDetail.setSaleWorkPrice(0L);
        }
    }

    /**
     * 更新旧料实体
     */
    private void updateOldMaterialEntity(OldMaterialEntity oldMaterial,
                                       SoldReceiptCreateDTO.SoldOldMaterialItemDTO oldMaterialItem) {
        oldMaterial.setGoodsId(oldMaterialItem.getGoodsId() != null ? oldMaterialItem.getGoodsId().intValue() : 0);

        // 如果存在货品ID，使用货品信息填充旧料表中参数没有但旧料表有的字段
        if (oldMaterialItem.getGoodsId() != null && oldMaterialItem.getGoodsId() > 0) {
            fillOldMaterialFromGoods(oldMaterial, oldMaterialItem);
        } else {
            // 没有货品ID时，直接使用传入的参数
            oldMaterial.setCategoryId(oldMaterialItem.getCategoryId());
            oldMaterial.setSubclassId(oldMaterialItem.getSubclassId());
            oldMaterial.setQualityId(oldMaterialItem.getQualityId());
            oldMaterial.setName(oldMaterialItem.getName());
            oldMaterial.setSalesType(oldMaterialItem.getSalesType());
        }

        // 设置价格和重量信息
        if (oldMaterialItem.getRecyclePrice() != null) {
            oldMaterial.setRecyclePrice(PriceUtil.yuan2fen(oldMaterialItem.getRecyclePrice()).longValue());
        }
        if (oldMaterialItem.getGoldPrice() != null) {
            oldMaterial.setGoldPrice(PriceUtil.yuan2fen(oldMaterialItem.getGoldPrice()).longValue());
        }
        if (oldMaterialItem.getSilverPrice() != null) {
            oldMaterial.setSilverPrice(PriceUtil.yuan2fen(oldMaterialItem.getSilverPrice()).longValue());
        }
        if (oldMaterialItem.getNetGoldWeight() != null) {
            oldMaterial.setNetGoldWeight(oldMaterialItem.getNetGoldWeight());
        }
        if (oldMaterialItem.getNetSilverWeight() != null) {
            oldMaterial.setNetSilverWeight(oldMaterialItem.getNetSilverWeight());
        }
        if (oldMaterialItem.getSaleWorkPrice() != null) {
            oldMaterial.setSaleWorkPrice(PriceUtil.yuan2fen(oldMaterialItem.getSaleWorkPrice()).longValue());
        }
        if (StringUtils.isNotBlank(oldMaterialItem.getSoldReceiptSn())) {
            oldMaterial.setSoldReceiptSn(oldMaterialItem.getSoldReceiptSn());
        }

        // 重量计算逻辑：如果重量为0或null，则按公式计算
        BigDecimal calculatedWeight = calculateWeight(
            oldMaterial.getNetGoldWeight(),
            oldMaterial.getNetSilverWeight(),
            oldMaterial.getMainStoneWeight(),
            oldMaterial.getMainStoneCount(),
            oldMaterial.getSubStoneWeight(),
            oldMaterial.getSubStoneCount()
        );

        BigDecimal finalWeight = (oldMaterial.getWeight() == null || oldMaterial.getWeight().compareTo(BigDecimal.ZERO) == 0)
            ? calculatedWeight : oldMaterial.getWeight();
        oldMaterial.setWeight(finalWeight);
    }

    /**
     * 使用货品信息填充旧料字段
     */
    private void fillOldMaterialFromGoods(OldMaterialEntity oldMaterial, SoldReceiptCreateDTO.SoldOldMaterialItemDTO oldMaterialItem) {
        GoodsEntity goods = goodsMapper.selectOneById(oldMaterialItem.getGoodsId());
        if (goods == null) {
            throw new BusinessException("货品不存在");
        }

        // 使用货品信息填充旧料表中参数没有但旧料表有的字段
        // 优先使用传入的参数，如果参数为空则使用货品信息
        oldMaterial.setCategoryId(oldMaterialItem.getCategoryId() != null ? oldMaterialItem.getCategoryId() : goods.getCategoryId());
        oldMaterial.setSubclassId(oldMaterialItem.getSubclassId() != null ? oldMaterialItem.getSubclassId() : goods.getSubclassId());
        oldMaterial.setQualityId(oldMaterialItem.getQualityId() != null ? oldMaterialItem.getQualityId() : goods.getQualityId());
        oldMaterial.setName(StrUtil.isNotBlank(oldMaterialItem.getName()) ? oldMaterialItem.getName() : goods.getName());
        oldMaterial.setSalesType(oldMaterialItem.getSalesType() != null ? oldMaterialItem.getSalesType() : goods.getSalesType());

        // 从货品复制其他字段（如果旧料表中为空）
        if (oldMaterial.getGoodsSn() == null) {
            oldMaterial.setGoodsSn(goods.getGoodsSn());
        }
        if (oldMaterial.getBatchNo() == null) {
            oldMaterial.setBatchNo(goods.getBatchNo());
        }
        if (oldMaterial.getCertNo() == null) {
            oldMaterial.setCertNo(goods.getCertNo());
        }
        if (oldMaterial.getRemark() == null) {
            oldMaterial.setRemark(goods.getRemark());
        }
        if (oldMaterial.getWeight() == null) {
            oldMaterial.setWeight(goods.getWeight());
        }
        if (oldMaterial.getNetGoldWeight() == null) {
            oldMaterial.setNetGoldWeight(goods.getNetGoldWeight());
        }
        if (oldMaterial.getNetSilverWeight() == null) {
            oldMaterial.setNetSilverWeight(goods.getNetSilverWeight());
        }
        if (oldMaterial.getMainStoneCount() == null) {
            oldMaterial.setMainStoneCount(goods.getMainStoneCount());
        }
        if (oldMaterial.getMainStoneWeight() == null) {
            oldMaterial.setMainStoneWeight(goods.getMainStoneWeight());
        }
        if (oldMaterial.getSubStoneCount() == null) {
            oldMaterial.setSubStoneCount(goods.getSubStoneCount());
        }
        if (oldMaterial.getSubStoneWeight() == null) {
            oldMaterial.setSubStoneWeight(goods.getSubStoneWeight());
        }
        if (oldMaterial.getCircleSize() == null) {
            oldMaterial.setCircleSize(goods.getCircleSize());
        }
        if (oldMaterial.getMainStoneId() == null) {
            oldMaterial.setMainStoneId(goods.getMainStoneId());
        }
        if (oldMaterial.getSubStoneId() == null) {
            oldMaterial.setSubStoneId(goods.getSubStoneId());
        }
        if (oldMaterial.getTechnologyId() == null) {
            oldMaterial.setTechnologyId(goods.getTechnologyId());
        }
        if (oldMaterial.getSupplierId() == null) {
            oldMaterial.setSupplierId(goods.getSupplierId());
        }
        if (oldMaterial.getCounterId() == null) {
            oldMaterial.setCounterId(goods.getCounterId());
        }
        if (oldMaterial.getCostPrice() == null) {
            oldMaterial.setCostPrice(goods.getCostPrice());
        }
        if (oldMaterial.getWorkPrice() == null) {
            oldMaterial.setWorkPrice(goods.getWorkPrice());
        }
        if (oldMaterial.getCertPrice() == null) {
            oldMaterial.setCertPrice(goods.getCertPrice());
        }
        if (oldMaterial.getTagPrice() == null) {
            oldMaterial.setTagPrice(goods.getTagPrice());
        }

        // 添加缺少的字段：品牌、款式、回收金价、回收银价
        if (oldMaterial.getBrandId() == null) {
            oldMaterial.setBrandId(goods.getBrandId());
        }
        if (oldMaterial.getStyleId() == null) {
            oldMaterial.setStyleId(goods.getStyleId());
        }


    }

    /**
     * 复制货品图片到旧料
     */
    private void copyGoodsImagesToOldMaterial(Long goodsId, Long oldMaterialId) {
        if (goodsId == null || oldMaterialId == null) {
            return;
        }

        // 查询货品图片
        List<GoodsHasImagesEntity> goodsImages = goodsHasImagesMapper.selectListByQuery(
                QueryWrapper.create()
                        .where(GoodsHasImagesEntity::getGoodsId).eq(goodsId)
                        .orderBy(GoodsHasImagesEntity::getSort, true)
        );

        if (goodsImages.isEmpty()) {
            return;
        }

        // 创建旧料图片关联
        List<OldMaterialHasImagesEntity> oldMaterialImages = goodsImages.stream()
                .map(goodsImage -> {
                    OldMaterialHasImagesEntity oldMaterialImage = new OldMaterialHasImagesEntity();
                    oldMaterialImage.setCompanyId(SecurityUtils.getCompanyId().intValue());
                    oldMaterialImage.setOldMaterialId(oldMaterialId.intValue());
                    oldMaterialImage.setImageId(goodsImage.getImageId().intValue());
                    oldMaterialImage.setUrl(goodsImage.getUrl());
                    oldMaterialImage.setSort(goodsImage.getSort());
                    return oldMaterialImage;
                })
                .collect(Collectors.toList());

        // 批量插入旧料图片关联
        if (!oldMaterialImages.isEmpty()) {
            oldMaterialHasImagesMapper.insertBatchSelective(oldMaterialImages);
        }
    }

    /**
     * 创建旧料实体
     */
    private OldMaterialEntity createOldMaterialEntity(SoldReceiptCreateDTO.SoldOldMaterialItemDTO oldMaterialItem,
                                                    Integer soldReceiptStatus, Long merchantId) {
        OldMaterialEntity oldMaterial = new OldMaterialEntity();
        oldMaterial.setCompanyId(SecurityUtils.getCompanyId().intValue());
        oldMaterial.setMerchantId(merchantId.intValue());
        oldMaterial.setOldMaterialSn(SnUtils.generateOldMaterialSn());

        // 设置数量：只有在销售单状态为已完成时才设置实际数量
        if (soldReceiptStatus != null && soldReceiptStatus == 1) {
            oldMaterial.setNum(oldMaterialItem.getNum());
        } else {
            oldMaterial.setNum(0); // 挂单状态数量为0
        }
        oldMaterial.setFrozenNum(0);

        updateOldMaterialEntity(oldMaterial, oldMaterialItem);

        return oldMaterial;
    }

    /**
     * 更新赠品明细
     */
    private void updateGiftDetails(Long soldReceiptId, List<SoldReceiptCreateDTO.SoldGiftItemDTO> giftList,
                                 Integer soldReceiptStatus) {
        if (giftList == null) {
            giftList = List.of();
        }

        // 获取现有赠品明细
        List<SoldReceiptGiftEntity> existingGiftDetails = soldReceiptGiftMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptGiftEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        // 获取现有赠品明细ID集合
        Set<Long> existingGiftDetailIds = existingGiftDetails.stream()
                .map(SoldReceiptGiftEntity::getId)
                .collect(Collectors.toSet());

        // 获取请求中的赠品明细ID集合
        Set<Long> requestGiftDetailIds = giftList.stream()
                .map(SoldReceiptCreateDTO.SoldGiftItemDTO::getGiftDetailId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 需要删除的赠品明细ID
        Set<Long> toDeleteGiftDetailIds = existingGiftDetailIds.stream()
                .filter(id -> !requestGiftDetailIds.contains(id))
                .collect(Collectors.toSet());

        // 删除不在请求中的赠品明细并恢复库存
        if (!toDeleteGiftDetailIds.isEmpty()) {
            deleteGiftDetailsAndRestoreStock(toDeleteGiftDetailIds, soldReceiptStatus);
        }

        // 验证并获取赠品信息
        Map<Long, GiftEntity> giftMap = validateAndGetGifts(giftList);

        // 处理每个赠品明细
        for (SoldReceiptCreateDTO.SoldGiftItemDTO giftItem : giftList) {
            GiftEntity gift = giftMap.get(giftItem.getGiftId());

            if (giftItem.getGiftDetailId() != null && existingGiftDetailIds.contains(giftItem.getGiftDetailId())) {
                // 更新现有赠品明细
                updateExistingGiftDetail(giftItem, gift, soldReceiptStatus);
            } else {
                // 创建新赠品明细
                createNewGiftDetail(soldReceiptId, giftItem, gift, soldReceiptStatus);
            }
        }
    }

    /**
     * 删除赠品明细并恢复库存
     */
    private void deleteGiftDetailsAndRestoreStock(Set<Long> giftDetailIds, Integer soldReceiptStatus) {
        List<SoldReceiptGiftEntity> giftDetails = soldReceiptGiftMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptGiftEntity::getId).in(giftDetailIds)
        );

        // 恢复库存
        List<GiftStockNumChangeBO> stockChanges = new ArrayList<>();
        for (SoldReceiptGiftEntity giftDetail : giftDetails) {
            GiftStockNumChangeBO.GiftStockNumChangeBOBuilder builder = GiftStockNumChangeBO.builder()
                    .giftId(giftDetail.getGiftId())
                    .stockNum(giftDetail.getNum()); // 增加库存

            if (soldReceiptStatus != null && soldReceiptStatus == 1) {
                // 已完成状态：减少销售数量
                builder.soldNum(-giftDetail.getNum()).comment("取单删除赠品-恢复库存");
            } else {
                // 挂单状态：减少冻结数量
                builder.frozenNum(-giftDetail.getNum()).comment("取单删除赠品-恢复库存");
            }

            stockChanges.add(builder.build());
        }

        if (!stockChanges.isEmpty()) {
            StockUtils.updateGiftStocks(stockChanges);
        }

        // 删除赠品明细记录
        soldReceiptGiftMapper.deleteBatchByIds(giftDetailIds);

        // 删除对应的销售单明细记录
        soldReceiptDetailMapper.deleteByQuery(
                QueryWrapper.create()
                        .where(SoldReceiptDetailEntity::getDetailId).in(giftDetailIds)
                        .and(SoldReceiptDetailEntity::getType).eq(SoldReceiptDetailType.GIFT.getValue())
        );
    }

    /**
     * 更新现有赠品明细
     */
    private void updateExistingGiftDetail(SoldReceiptCreateDTO.SoldGiftItemDTO giftItem,
                                        GiftEntity gift, Integer soldReceiptStatus) {
        SoldReceiptGiftEntity existingDetail = soldReceiptGiftMapper.selectOneById(giftItem.getGiftDetailId());
        if (existingDetail == null) {
            throw new BusinessException("赠品明细不存在");
        }

        // 计算数量变化
        int quantityChange = giftItem.getNum() - existingDetail.getNum();

        // 验证库存是否足够
        if (quantityChange > 0) {
            // 数量增加，检查库存
            if (gift.getStockNum() < quantityChange) {
                throw new BusinessException("赠品 " + gift.getName() + " 库存不足，最大可出库数: " +
                        (existingDetail.getNum() + gift.getStockNum()));
            }
        }

        // 更新库存
        if (quantityChange != 0) {
            updateGiftStock(gift.getId().longValue(), quantityChange, soldReceiptStatus);
        }

        // 更新赠品明细
        updateGiftDetailEntity(existingDetail, giftItem, gift);
        soldReceiptGiftMapper.update(existingDetail);
    }

    /**
     * 创建新赠品明细
     */
    private void createNewGiftDetail(Long soldReceiptId, SoldReceiptCreateDTO.SoldGiftItemDTO giftItem,
                                   GiftEntity gift, Integer soldReceiptStatus) {
        // 验证库存
        if (gift.getStockNum() < giftItem.getNum()) {
            throw new BusinessException("赠品 " + gift.getName() + " 库存不足");
        }

        // 创建赠品明细
        createSingleGiftDetail(soldReceiptId, giftItem, gift, true);

        // 更新库存
        updateGiftStock(gift.getId().longValue(), giftItem.getNum(), soldReceiptStatus);
    }

    /**
     * 更新赠品明细实体
     */
    private void updateGiftDetailEntity(SoldReceiptGiftEntity giftDetail, SoldReceiptCreateDTO.SoldGiftItemDTO giftItem,
                                      GiftEntity gift) {
        giftDetail.setGiftId(giftItem.getGiftId());
        giftDetail.setGiftSn(gift.getGiftSn());
        giftDetail.setNum(giftItem.getNum());
        giftDetail.setTagPrice(gift.getTagPrice().longValue());
        giftDetail.setSoldPrice(PriceUtil.yuan2fen(giftItem.getSoldPrice()).longValue());

        // 设置赠品快照
        giftDetail.setDataSnapshot(JSONUtil.toJsonStr(gift));
    }

    /**
     * 更新赠品库存
     * 注意：在取单逻辑中，所有明细数量变更都应该操作冻结数量，
     * 因为在取单逻辑的最后会统一处理冻结转销售（adjustStockForStatusChange方法）
     */
    private void updateGiftStock(Long giftId, int quantityChange, Integer soldReceiptStatus) {
        GiftStockNumChangeBO stockChange = GiftStockNumChangeBO.builder()
                .giftId(giftId)
                .stockNum(-quantityChange) // 减少库存
                .frozenNum(quantityChange) // 增加冻结数量（统一操作冻结数量）
                .comment("取单更新赠品-销售冻结")
                .build();

        StockUtils.updateGiftStocks(List.of(stockChange));
    }

    /**
     * 更新支付明细
     */
    private void updatePaymentDetails(Long soldReceiptId, List<SoldReceiptCreateDTO.SoldPaymentItemDTO> paymentList) {
        if (paymentList == null) {
            paymentList = List.of();
        }

        // 获取现有支付明细
        List<SoldReceiptPaymentEntity> existingPaymentDetails = soldReceiptPaymentMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptPaymentEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        // 获取现有支付明细ID集合
        Set<Long> existingPaymentDetailIds = existingPaymentDetails.stream()
                .map(SoldReceiptPaymentEntity::getId)
                .collect(Collectors.toSet());

        // 获取请求中的支付明细ID集合
        Set<Long> requestPaymentDetailIds = paymentList.stream()
                .map(SoldReceiptCreateDTO.SoldPaymentItemDTO::getPaymentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 需要删除的支付明细ID
        Set<Long> toDeletePaymentDetailIds = existingPaymentDetailIds.stream()
                .filter(id -> !requestPaymentDetailIds.contains(id))
                .collect(Collectors.toSet());

        // 校验是否有已有明细被删除
        if (!toDeletePaymentDetailIds.isEmpty()) {
            // 检查被删除的支付明细是否有金额
            List<SoldReceiptPaymentEntity> toDeletePayments = existingPaymentDetails.stream()
                    .filter(payment -> toDeletePaymentDetailIds.contains(payment.getId()))
                    .filter(payment -> payment.getAmount() != null && payment.getAmount() > 0)
                    .toList();

            if (!toDeletePayments.isEmpty()) {
                throw new BusinessException("不能删除已有金额的支付明细");
            }

            // 删除不在请求中的支付明细（只删除金额为0的明细）
            soldReceiptPaymentMapper.deleteBatchByIds(toDeletePaymentDetailIds);
        }

        // 处理每个支付明细
        for (SoldReceiptCreateDTO.SoldPaymentItemDTO paymentItem : paymentList) {
            if (paymentItem.getPaymentId() != null && existingPaymentDetailIds.contains(paymentItem.getPaymentId())) {
                // 验证现有支付明细
                validateExistingPaymentDetail(paymentItem);
            } else {
                // 创建新支付明细
                createNewPaymentDetail(soldReceiptId, paymentItem);
            }
        }
    }

    /**
     * 验证现有支付明细
     */
    private void validateExistingPaymentDetail(SoldReceiptCreateDTO.SoldPaymentItemDTO paymentItem) {
        SoldReceiptPaymentEntity existingDetail = soldReceiptPaymentMapper.selectOneById(paymentItem.getPaymentId());
        if (existingDetail == null) {
            throw new BusinessException("支付明细不存在");
        }

        // 如果已有的支付明细金额为零，则不校验
        if (existingDetail.getAmount() == null || existingDetail.getAmount().equals(0L)) {
            return;
        }

        // 检查金额是否一致
        Long expectedAmount = PriceUtil.yuan2fen(paymentItem.getAmount()).longValue();
        if (!existingDetail.getAmount().equals(expectedAmount)) {
            throw new BusinessException("支付明细金额与已收金额不一致");
        }

        // 检查支付类型是否一致
        if (!existingDetail.getType().equals(paymentItem.getType())) {
            throw new BusinessException("支付明细金额与已收金额不一致");
        }
    }

    /**
     * 创建新支付明细
     */
    private void createNewPaymentDetail(Long soldReceiptId, SoldReceiptCreateDTO.SoldPaymentItemDTO paymentItem) {
        createSinglePaymentDetail(soldReceiptId, paymentItem);
    }

    /**
     * 更新销售单统计数据
     */
    private void updateSoldReceiptSummary(SoldReceiptCreateDTO dto, SoldReceiptEntity existingSoldReceipt) {
        // 重新计算统计数据
        SoldReceiptSummary summary = calculateSummaryForUpdate(dto, existingSoldReceipt.getId());

        // 销售单基础信息更新
        existingSoldReceipt.setMemberId(dto.getMemberId() != null ? dto.getMemberId() : 0L);
        existingSoldReceipt.setCashierId(dto.getCashierId());
        existingSoldReceipt.setMainSellerId(dto.getMainSellerId());
        existingSoldReceipt.setSupportSellerId(dto.getSupportSellerId() != null ? dto.getSupportSellerId() : 0L);
        existingSoldReceipt.setRemark(dto.getRemark());

        // 更新销售单主记录
        existingSoldReceipt.setNum(summary.getNum());
        existingSoldReceipt.setWeight(summary.getWeight());
        existingSoldReceipt.setGoldWeight(summary.getGoldWeight());
        existingSoldReceipt.setSilverWeight(summary.getSilverWeight());
        existingSoldReceipt.setAmount(summary.getAmount());
        existingSoldReceipt.setGoodsAmount(summary.getGoodsAmount());
        existingSoldReceipt.setDiscountAmount(summary.getDiscountAmount());
        existingSoldReceipt.setDeductionAmount(summary.getDeductionAmount());
        existingSoldReceipt.setGiftAmount(summary.getGiftAmount());
        existingSoldReceipt.setAdjustAmount(summary.getAdjustAmount());
        existingSoldReceipt.setReceivedAmount(summary.getReceivedAmount());
        existingSoldReceipt.setPaidAmount(summary.getPaidAmount());

        // 获取销售时间
        Date now = new Date();
        Date saleTime = now; // 默认使用当前时间

        // 如果传入了datetime参数，则使用该时间作为销售时间
        if (StrUtil.isNotBlank(dto.getDatetime())) {
            try {
                saleTime = DateUtil.parse(dto.getDatetime(), "yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                log.warn("解析销售时间失败，使用当前时间: {}", dto.getDatetime(), e);
            }
        }

        existingSoldReceipt.setPendingAt(saleTime);

        // 根据已收金额确定状态
        if (summary.getReceivedAmount().equals(summary.getAmount())) {
            existingSoldReceipt.setStatus(1); // 已完成：已收金额等于订单金额
            existingSoldReceipt.setSoldAt(saleTime);
        } else {
            existingSoldReceipt.setStatus(0); // 挂单中：已收金额不等于订单金额
            existingSoldReceipt.setSoldAt(null);
        }

        soldReceiptMapper.update(existingSoldReceipt);
    }

    /**
     * 重新计算统计数据（用于更新）
     */
    private SoldReceiptSummary calculateSummaryForUpdate(SoldReceiptCreateDTO dto, Long soldReceiptId) {
        SoldReceiptSummary summary = new SoldReceiptSummary();

        // 从数据库获取最新的明细数据进行计算
        List<SoldReceiptGoodsEntity> goodsDetails = soldReceiptGoodsMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptGoodsEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        List<SoldReceiptGiftEntity> giftDetails = soldReceiptGiftMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptGiftEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        List<SoldReceiptOldMaterialEntity> oldMaterialDetails = soldReceiptOldMaterialMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptOldMaterialEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        // 获取货品信息用于计算重量
        Set<Long> goodsIds = goodsDetails.stream().map(SoldReceiptGoodsEntity::getGoodsId).collect(Collectors.toSet());
        Map<Long, GoodsEntity> goodsMap = goodsIds.isEmpty() ? Collections.emptyMap() :
            goodsMapper.selectListByIds(goodsIds).stream().collect(Collectors.toMap(GoodsEntity::getId, g -> g));

        // 计算货品相关统计
        for (SoldReceiptGoodsEntity goodsDetail : goodsDetails) {
            summary.addGoodsNum(goodsDetail.getNum());

            // 计算重量（从货品信息获取）
            GoodsEntity goods = goodsMap.get(goodsDetail.getGoodsId());
            if (goods != null && goods.getWeight() != null) {
                summary.addWeight(goods.getWeight().multiply(new BigDecimal(goodsDetail.getNum())));
            }

            // 获取货品销售金额和优惠金额（从数据库中获取已存储的值）
            Long soldPriceFen = goodsDetail.getSoldPrice();
            Long totalSoldAmountFen = soldPriceFen * goodsDetail.getNum();
            Long discountAmountFen = 0L;
            Long workPriceDiscountAmountFen = 0L;

            // 获取员工折扣优惠金额（从数据库中获取已存储的值）
            if (goodsDetail.getDiscountAmount() != null) {
                discountAmountFen = goodsDetail.getDiscountAmount();
            }

            // 获取工费折扣优惠金额（从数据库中获取已存储的值）
            if (goodsDetail.getWorkPriceDiscountAmount() != null) {
                workPriceDiscountAmountFen = goodsDetail.getWorkPriceDiscountAmount().longValue();
            }

            summary.addGoodsAmount(totalSoldAmountFen);
            summary.addDiscountAmount(discountAmountFen + workPriceDiscountAmountFen);

            if (goodsDetail.getGoldWeight() != null) {
                summary.addGoldWeight(goodsDetail.getGoldWeight().multiply(new BigDecimal(goodsDetail.getNum())));
            }
            if (goodsDetail.getSilverWeight() != null) {
                summary.addSilverWeight(goodsDetail.getSilverWeight().multiply(new BigDecimal(goodsDetail.getNum())));
            }
        }

        // 计算赠品金额
        for (SoldReceiptGiftEntity giftDetail : giftDetails) {
            summary.addGiftAmount(giftDetail.getSoldPrice() * giftDetail.getNum());
        }

        // 计算旧料抵扣金额
        for (SoldReceiptOldMaterialEntity oldMaterialDetail : oldMaterialDetails) {
            summary.addDeductionAmount(oldMaterialDetail.getRecyclePrice() * oldMaterialDetail.getNum());
        }

        // 计算订单金额
        Long adjustAmount = dto.getAdjustAmount() != null ? PriceUtil.yuan2fen(dto.getAdjustAmount()).longValue() : 0L;
        // 订单金额 = 货品金额 - 优惠金额 + 赠品金额 - 抹零金额 - 旧料抵扣金额
        Long orderAmount = summary.getGoodsAmount() - summary.getDiscountAmount() + summary.getGiftAmount() - adjustAmount - summary.getDeductionAmount();
        summary.setAmount(orderAmount);
        summary.setAdjustAmount(adjustAmount);

        // 计算已收金额和实付金额
        BigDecimal totalPayment = dto.getPaymentList().stream()
                .map(SoldReceiptCreateDTO.SoldPaymentItemDTO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Long paidAmount = PriceUtil.yuan2fen(totalPayment).longValue();
        summary.setReceivedAmount(paidAmount);
        summary.setPaidAmount(paidAmount);

        return summary;
    }

    /**
     * 更新销售单明细记录
     */
    private void updateSoldReceiptDetails(Long soldReceiptId, SoldReceiptCreateDTO dto) {
        // 删除现有的销售单明细记录
        soldReceiptDetailMapper.deleteByQuery(
                QueryWrapper.create().where(SoldReceiptDetailEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        // 重新创建销售单明细记录
        createSoldReceiptDetails(soldReceiptId, dto);
    }

    /**
     * 数据验证
     */
    private void validateCreateData(SoldReceiptCreateDTO dto) {
        // 验证支付金额
        BigDecimal totalPayment = dto.getPaymentList().stream()
                .map(SoldReceiptCreateDTO.SoldPaymentItemDTO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 支付金额可以为零（挂单状态），但不能为负数
        if (totalPayment.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("支付金额不能为负数");
        }
    }

    /**
     * 获取最新实时价格
     */
    private ActualGoldPriceEntity getLatestActualPrice() {
        return actualGoldPriceMapper.selectOneByQuery(
                QueryWrapper.create()
                        .where(ACTUAL_GOLD_PRICE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .orderBy(ACTUAL_GOLD_PRICE.CREATED_AT, false)
                        .limit(1)
        );
    }

    /**
     * 验证并获取货品信息
     */
    private Map<Long, GoodsEntity> validateAndGetGoods(List<SoldReceiptCreateDTO.SoldGoodsItemDTO> goodsList) {
        if (goodsList == null || goodsList.isEmpty()) {
            throw new BusinessException("货品列表不能为空");
        }

        List<Long> goodsIds = goodsList.stream()
                .map(SoldReceiptCreateDTO.SoldGoodsItemDTO::getGoodsId)
                .toList();

        List<GoodsEntity> goods = goodsMapper.selectListByQuery(
                QueryWrapper.create()
                        .where(GoodsEntity::getId).in(goodsIds)
                        .and(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );

        if (goods.size() != goodsIds.size()) {
            throw new BusinessException("部分货品不存在或不属于当前商户");
        }

        return goods.stream().collect(Collectors.toMap(GoodsEntity::getId, g -> g));
    }

    /**
     * 验证并获取赠品信息
     */
    private Map<Long, GiftEntity> validateAndGetGifts(List<SoldReceiptCreateDTO.SoldGiftItemDTO> giftList) {
        if (giftList == null || giftList.isEmpty()) {
            return Map.of();
        }

        List<Long> giftIds = giftList.stream()
                .map(SoldReceiptCreateDTO.SoldGiftItemDTO::getGiftId)
                .toList();

        List<GiftEntity> gifts = giftMapper.selectListByQuery(
                QueryWrapper.create()
                        .where(GiftEntity::getId).in(giftIds)
                        .and(GiftEntity::getCompanyId).eq(SecurityUtils.getCompanyId().intValue())
        );

        if (gifts.size() != giftIds.size()) {
            throw new BusinessException("部分赠品不存在或不属于当前商户");
        }

        // 验证库存
        for (GiftEntity giftEntity : gifts) {
            SoldReceiptCreateDTO.SoldGiftItemDTO giftItem = giftList.stream()
                    .filter(item -> item.getGiftId().equals(giftEntity.getId().longValue()))
                    .findFirst()
                    .orElseThrow(() -> new BusinessException("赠品数据异常"));

            if (giftEntity.getStockNum() < giftItem.getNum()) {
                throw new BusinessException("赠品 " + giftEntity.getName() + " 库存不足");
            }
        }

        return gifts.stream().collect(Collectors.toMap(g -> g.getId().longValue(), g -> g));
    }

    /**
     * 创建旧料记录
     */
    private List<OldMaterialEntity> createOldMaterials(List<SoldReceiptCreateDTO.SoldOldMaterialItemDTO> oldMaterialList, Long merchantId) {
        if (oldMaterialList == null || oldMaterialList.isEmpty()) {
            return List.of();
        }

        List<OldMaterialEntity> oldMaterials = new ArrayList<>();
        for (SoldReceiptCreateDTO.SoldOldMaterialItemDTO item : oldMaterialList) {
            OldMaterialEntity oldMaterial = new OldMaterialEntity();
            oldMaterial.setCompanyId(SecurityUtils.getCompanyId().intValue());
            oldMaterial.setMerchantId(merchantId.intValue());
            oldMaterial.setNum(0); // 初始数量为0，销售完成后才更新
            oldMaterial.setFrozenNum(0);
            oldMaterial.setOldMaterialSn(SnUtils.generateOldMaterialSn());
            oldMaterial.setSoldReceiptSn(item.getSoldReceiptSn());

            // 使用统一的方法处理旧料字段填充（包括从货品信息填充）
            updateOldMaterialEntity(oldMaterial, item);

            oldMaterials.add(oldMaterial);
        }

        // 批量插入旧料记录
        if (!oldMaterials.isEmpty()) {
            oldMaterialMapper.insertBatchSelective(oldMaterials);

            // 处理图片复制（对于有货品ID的旧料）
            for (int i = 0; i < oldMaterialList.size(); i++) {
                SoldReceiptCreateDTO.SoldOldMaterialItemDTO item = oldMaterialList.get(i);
                OldMaterialEntity oldMaterial = oldMaterials.get(i);

                if (item.getGoodsId() != null && item.getGoodsId() > 0) {
                    copyGoodsImagesToOldMaterial(item.getGoodsId(), oldMaterial.getId().longValue());
                }
            }
        }

        return oldMaterials;
    }

    /**
     * 计算销售单统计数据
     */
    private SoldReceiptSummary calculateSummary(SoldReceiptCreateDTO dto, Map<Long, GoodsEntity> goodsMap,
                                                Map<Long, GiftEntity> giftMap, List<OldMaterialEntity> oldMaterials) {
        SoldReceiptSummary summary = new SoldReceiptSummary();

        // 计算货品相关统计
        if (dto.getGoodsList() != null) {
            for (SoldReceiptCreateDTO.SoldGoodsItemDTO goodsItem : dto.getGoodsList()) {
                GoodsEntity goods = goodsMap.get(goodsItem.getGoodsId());
                
                summary.addGoodsNum(goodsItem.getNum());
                
                if (goods.getWeight() != null) {
                    summary.addWeight(goods.getWeight().multiply(new BigDecimal(goodsItem.getNum())));
                }
                if (goods.getNetGoldWeight() != null) {
                    summary.addGoldWeight(goods.getNetGoldWeight().multiply(new BigDecimal(goodsItem.getNum())));
                }
                if (goods.getNetSilverWeight() != null) {
                    summary.addSilverWeight(goods.getNetSilverWeight().multiply(new BigDecimal(goodsItem.getNum())));
                }

                // 按新公式计算价格
                // 1. 计算工费金额（根据工费计价方式）
                BigDecimal workAmount = calculateWorkAmount(goodsItem);

                // 2. 计算工费总额 = 工费金额 * 数量
                BigDecimal totalWorkAmount = workAmount.multiply(new BigDecimal(goodsItem.getNum()));

                // 3. 计算工费折扣金额 = 工费总额 * (100 - 工费折扣) / 100
                // 工费折扣90%表示打9折，优惠10%
                BigDecimal workPriceDiscountAmount = BigDecimal.ZERO;
                if (goodsItem.getWorkPriceDiscount() != null &&
                    goodsItem.getWorkPriceDiscount().compareTo(BigDecimal.ZERO) > 0 &&
                    goodsItem.getWorkPriceDiscount().compareTo(BigDecimal.valueOf(100)) < 0) {
                    // 优惠金额 = 工费总额 * (100 - 折扣) / 100
                    BigDecimal discountRate = BigDecimal.valueOf(100).subtract(goodsItem.getWorkPriceDiscount())
                            .divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
                    workPriceDiscountAmount = totalWorkAmount.multiply(discountRate).setScale(2, RoundingMode.HALF_UP);
                }

                // 4. 计算销售价 = 标签价 * 数量 + (工费总额 - 工费折扣金额)
                BigDecimal salesPrice = goodsItem.getTagPrice().multiply(new BigDecimal(goodsItem.getNum()))
                        .add(totalWorkAmount.subtract(workPriceDiscountAmount));

                // 5. 计算员工折扣金额 = 销售价 * (100 - 员工折扣) / 100
                // 员工折扣50%表示打5折，优惠50%
                BigDecimal staffDiscountAmount = BigDecimal.ZERO;
                if (goodsItem.getStaffDiscount() != null &&
                    goodsItem.getStaffDiscount().compareTo(BigDecimal.ZERO) > 0 &&
                    goodsItem.getStaffDiscount().compareTo(BigDecimal.valueOf(100)) < 0) {
                    // 优惠金额 = 销售价 * (100 - 折扣) / 100
                    BigDecimal discountRate = BigDecimal.valueOf(100).subtract(goodsItem.getStaffDiscount())
                            .divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
                    staffDiscountAmount = salesPrice.multiply(discountRate).setScale(2, RoundingMode.HALF_UP);
                }

                // 6. 计算货品金额 = (标签价 + 工费金额) * 数量
                BigDecimal goodsAmount = goodsItem.getTagPrice().add(workAmount)
                        .multiply(new BigDecimal(goodsItem.getNum()));

                // 7. 计算总优惠金额 = 工费折扣金额 + 员工折扣金额
                BigDecimal totalDiscountAmount = workPriceDiscountAmount.add(staffDiscountAmount);

                // 汇总逻辑
                summary.addGoodsAmount(PriceUtil.yuan2fen(goodsAmount).longValue());
                summary.addDiscountAmount(PriceUtil.yuan2fen(totalDiscountAmount).longValue());
            }
        }

        // 计算旧料抵扣金额
        if (dto.getOldMaterialList() != null) {
            for (SoldReceiptCreateDTO.SoldOldMaterialItemDTO oldMaterialItem : dto.getOldMaterialList()) {
                BigDecimal deductionAmount = oldMaterialItem.getRecyclePrice().multiply(new BigDecimal(oldMaterialItem.getNum()));
                summary.addDeductionAmount(PriceUtil.yuan2fen(deductionAmount).longValue());
            }
        }

        // 计算赠品金额
        if (dto.getGiftList() != null) {
            for (SoldReceiptCreateDTO.SoldGiftItemDTO giftItem : dto.getGiftList()) {
                BigDecimal giftAmount = giftItem.getSoldPrice().multiply(new BigDecimal(giftItem.getNum()));
                summary.addGiftAmount(PriceUtil.yuan2fen(giftAmount).longValue());
            }
        }

        // 计算订单金额
        Long adjustAmount = dto.getAdjustAmount() != null ? PriceUtil.yuan2fen(dto.getAdjustAmount()).longValue() : 0L;
        // 订单金额 = 货品金额 - 优惠金额 + 赠品金额 - 抹零金额 - 旧料抵扣金额
        Long orderAmount = summary.getGoodsAmount() - summary.getDiscountAmount() + summary.getGiftAmount() - adjustAmount - summary.getDeductionAmount();
        summary.setAmount(orderAmount);
        summary.setAdjustAmount(adjustAmount);

        // 计算已收金额和实付金额
        BigDecimal totalPayment = dto.getPaymentList().stream()
                .map(SoldReceiptCreateDTO.SoldPaymentItemDTO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Long paidAmount = PriceUtil.yuan2fen(totalPayment).longValue();
        summary.setReceivedAmount(paidAmount);
        summary.setPaidAmount(paidAmount);

        return summary;
    }

    /**
     * 销售单统计数据内部类
     */
    private static class SoldReceiptSummary {
        private Integer num = 0;
        private BigDecimal weight = BigDecimal.ZERO;
        private BigDecimal goldWeight = BigDecimal.ZERO;
        private BigDecimal silverWeight = BigDecimal.ZERO;
        private Long amount = 0L;
        private Long goodsAmount = 0L;
        private Long discountAmount = 0L;
        private Long deductionAmount = 0L;
        private Long giftAmount = 0L;
        private Long adjustAmount = 0L;
        private Long receivedAmount = 0L;
        private Long paidAmount = 0L;

        // Getters and setters
        public Integer getNum() { return num; }
        public void addGoodsNum(Integer num) { this.num += num; }
        
        public BigDecimal getWeight() { return weight; }
        public void addWeight(BigDecimal weight) { this.weight = this.weight.add(weight); }
        
        public BigDecimal getGoldWeight() { return goldWeight; }
        public void addGoldWeight(BigDecimal goldWeight) { this.goldWeight = this.goldWeight.add(goldWeight); }
        
        public BigDecimal getSilverWeight() { return silverWeight; }
        public void addSilverWeight(BigDecimal silverWeight) { this.silverWeight = this.silverWeight.add(silverWeight); }
        
        public Long getAmount() { return amount; }
        public void setAmount(Long amount) { this.amount = amount; }
        
        public Long getGoodsAmount() { return goodsAmount; }
        public void addGoodsAmount(Long goodsAmount) { this.goodsAmount += goodsAmount; }
        
        public Long getDiscountAmount() { return discountAmount; }
        public void addDiscountAmount(Long discountAmount) { this.discountAmount += discountAmount; }
        
        public Long getDeductionAmount() { return deductionAmount; }
        public void addDeductionAmount(Long deductionAmount) { this.deductionAmount += deductionAmount; }
        
        public Long getGiftAmount() { return giftAmount; }
        public void addGiftAmount(Long giftAmount) { this.giftAmount += giftAmount; }
        
        public Long getAdjustAmount() { return adjustAmount; }
        public void setAdjustAmount(Long adjustAmount) { this.adjustAmount = adjustAmount; }
        
        public Long getReceivedAmount() { return receivedAmount; }
        public void setReceivedAmount(Long receivedAmount) { this.receivedAmount = receivedAmount; }
        
        public Long getPaidAmount() { return paidAmount; }
        public void setPaidAmount(Long paidAmount) { this.paidAmount = paidAmount; }
    }

    /**
     * 创建销售单主记录
     */
    private SoldReceiptEntity createSoldReceiptEntity(SoldReceiptCreateDTO dto, SoldReceiptSummary summary) {
        SoldReceiptEntity soldReceipt = new SoldReceiptEntity();

        // 基本信息
        soldReceipt.setCompanyId(SecurityUtils.getCompanyId());
        soldReceipt.setMerchantId(dto.getMerchantId());
        soldReceipt.setReceiptSn(SnUtils.generateSalesCode());
        soldReceipt.setMemberId(dto.getMemberId() != null ? dto.getMemberId() : 0L);
        soldReceipt.setCashierId(dto.getCashierId());
        soldReceipt.setMainSellerId(dto.getMainSellerId());
        soldReceipt.setSupportSellerId(dto.getSupportSellerId() != null ? dto.getSupportSellerId() : 0L);
        soldReceipt.setRemark(dto.getRemark());

        // 统计数据
        soldReceipt.setNum(summary.getNum());
        soldReceipt.setWeight(summary.getWeight());
        soldReceipt.setGoldWeight(summary.getGoldWeight());
        soldReceipt.setSilverWeight(summary.getSilverWeight());
        soldReceipt.setAmount(summary.getAmount());
        soldReceipt.setGoodsAmount(summary.getGoodsAmount());
        soldReceipt.setDiscountAmount(summary.getDiscountAmount());
        soldReceipt.setDeductionAmount(summary.getDeductionAmount());
        soldReceipt.setGiftAmount(summary.getGiftAmount());
        soldReceipt.setAdjustAmount(summary.getAdjustAmount());
        soldReceipt.setReceivedAmount(summary.getReceivedAmount());
        soldReceipt.setPaidAmount(summary.getPaidAmount());
        soldReceipt.setRefundAmount(0L);

        // 时间和状态
        Date now = new Date();
        Date saleTime = now; // 默认使用当前时间

        // 如果传入了datetime参数，则使用该时间作为销售时间
        if (StrUtil.isNotBlank(dto.getDatetime())) {
            try {
                saleTime = DateUtil.parse(dto.getDatetime(), "yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                log.warn("解析销售时间失败，使用当前时间: {}", dto.getDatetime(), e);
            }
        }

        soldReceipt.setPendingAt(saleTime);

        // 根据已收金额确定状态
        if (summary.getReceivedAmount().equals(summary.getAmount())) {
            soldReceipt.setStatus(SoldReceiptStatusEnum.COMPLETED.getValue()); // 已完成：已收金额等于订单金额
            soldReceipt.setSoldAt(saleTime);
        } else {
            // 如果已收金额大于订单金额, 则给出异常
            if (summary.getReceivedAmount() > summary.getAmount()) {
                throw new BusinessException("已收金额不能大于订单金额");
            }

            soldReceipt.setStatus(SoldReceiptStatusEnum.PENDING.getValue()); // 挂单中：已收金额不等于订单金额（包括为零的情况）
        }

        soldReceipt.setCreatedBy(SecurityUtils.getUserId());

        return soldReceipt;
    }

    /**
     * 创建货品明细
     */
    private void createGoodsDetails(Long soldReceiptId, List<SoldReceiptCreateDTO.SoldGoodsItemDTO> goodsList,
                                   Map<Long, GoodsEntity> goodsMap, ActualGoldPriceEntity actualPrice) {
        if (goodsList == null || goodsList.isEmpty()) {
            return;
        }

        // 批量创建货品明细
        List<SoldReceiptGoodsEntity> goodsDetails = new ArrayList<>();
        for (SoldReceiptCreateDTO.SoldGoodsItemDTO goodsItem : goodsList) {
            GoodsEntity goods = goodsMap.get(goodsItem.getGoodsId());
            SoldReceiptGoodsEntity goodsDetail = new SoldReceiptGoodsEntity();
            goodsDetail.setSoldReceiptId(soldReceiptId);
            updateGoodsDetailEntity(goodsDetail, goodsItem, goods, actualPrice);
            goodsDetails.add(goodsDetail);
        }

        // 批量插入货品明细
        if (!goodsDetails.isEmpty()) {
            soldReceiptGoodsMapper.insertBatchSelective(goodsDetails);
        }
    }



    /**
     * 创建旧料明细
     */
    private void createOldMaterialDetails(Long soldReceiptId, List<SoldReceiptCreateDTO.SoldOldMaterialItemDTO> oldMaterialList,
                                         List<OldMaterialEntity> oldMaterials) {
        if (oldMaterialList == null || oldMaterialList.isEmpty()) {
            return;
        }

        // 批量创建旧料明细
        List<SoldReceiptOldMaterialEntity> oldMaterialDetails = new ArrayList<>();
        for (int i = 0; i < oldMaterialList.size(); i++) {
            SoldReceiptCreateDTO.SoldOldMaterialItemDTO oldMaterialItem = oldMaterialList.get(i);
            OldMaterialEntity oldMaterial = oldMaterials.get(i);

            SoldReceiptOldMaterialEntity oldMaterialDetail = new SoldReceiptOldMaterialEntity();
            oldMaterialDetail.setSoldReceiptId(soldReceiptId);
            oldMaterialDetail.setOldMaterialId(oldMaterial.getId().longValue());
            oldMaterialDetail.setOldMaterialSn(oldMaterial.getOldMaterialSn());
            updateOldMaterialDetailEntity(oldMaterialDetail, oldMaterialItem);
            oldMaterialDetail.setDataSnapshot(JSONUtil.toJsonStr(oldMaterial));
            oldMaterialDetails.add(oldMaterialDetail);
        }

        // 批量插入旧料明细
        if (!oldMaterialDetails.isEmpty()) {
            soldReceiptOldMaterialMapper.insertBatchSelective(oldMaterialDetails);
        }
    }

    /**
     * 创建单个旧料明细（通用方法）
     */
    private SoldReceiptOldMaterialEntity createSingleOldMaterialDetail(Long soldReceiptId, SoldReceiptCreateDTO.SoldOldMaterialItemDTO oldMaterialItem,
                                                                      OldMaterialEntity oldMaterial, boolean createDetailRecord) {
        SoldReceiptOldMaterialEntity oldMaterialDetail = new SoldReceiptOldMaterialEntity();
        oldMaterialDetail.setSoldReceiptId(soldReceiptId);
        oldMaterialDetail.setOldMaterialId(oldMaterial.getId().longValue());
        oldMaterialDetail.setOldMaterialSn(oldMaterial.getOldMaterialSn());
        updateOldMaterialDetailEntity(oldMaterialDetail, oldMaterialItem);

        // 设置旧料快照
        oldMaterialDetail.setDataSnapshot(JSONUtil.toJsonStr(oldMaterial));

        soldReceiptOldMaterialMapper.insertSelective(oldMaterialDetail);

        // 创建销售单明细记录
        if (createDetailRecord) {
            SoldReceiptDetailEntity detail = new SoldReceiptDetailEntity();
            detail.setSoldReceiptId(soldReceiptId);
            detail.setType(SoldReceiptDetailType.OLD_MATERIAL.getValue());
            detail.setDetailId(oldMaterialDetail.getId());
            soldReceiptDetailMapper.insertSelective(detail);
        }

        return oldMaterialDetail;
    }

    /**
     * 创建赠品明细
     */
    private void createGiftDetails(Long soldReceiptId, List<SoldReceiptCreateDTO.SoldGiftItemDTO> giftList,
                                  Map<Long, GiftEntity> giftMap) {
        if (giftList == null || giftList.isEmpty()) {
            return;
        }

        // 批量创建赠品明细
        List<SoldReceiptGiftEntity> giftDetails = new ArrayList<>();
        for (SoldReceiptCreateDTO.SoldGiftItemDTO giftItem : giftList) {
            GiftEntity gift = giftMap.get(giftItem.getGiftId());
            SoldReceiptGiftEntity giftDetail = new SoldReceiptGiftEntity();
            giftDetail.setSoldReceiptId(soldReceiptId);
            updateGiftDetailEntity(giftDetail, giftItem, gift);
            giftDetails.add(giftDetail);
        }

        // 批量插入赠品明细
        if (!giftDetails.isEmpty()) {
            soldReceiptGiftMapper.insertBatchSelective(giftDetails);
        }
    }

    /**
     * 创建单个赠品明细（通用方法）
     */
    private SoldReceiptGiftEntity createSingleGiftDetail(Long soldReceiptId, SoldReceiptCreateDTO.SoldGiftItemDTO giftItem,
                                                        GiftEntity gift, boolean createDetailRecord) {
        SoldReceiptGiftEntity giftDetail = new SoldReceiptGiftEntity();
        giftDetail.setSoldReceiptId(soldReceiptId);
        updateGiftDetailEntity(giftDetail, giftItem, gift);
        soldReceiptGiftMapper.insertSelective(giftDetail);

        // 创建销售单明细记录
        if (createDetailRecord) {
            SoldReceiptDetailEntity detail = new SoldReceiptDetailEntity();
            detail.setSoldReceiptId(soldReceiptId);
            detail.setType(SoldReceiptDetailType.GIFT.getValue());
            detail.setDetailId(giftDetail.getId());
            soldReceiptDetailMapper.insertSelective(detail);
        }

        return giftDetail;
    }

    /**
     * 创建支付明细
     */
    private void createPaymentDetails(Long soldReceiptId, List<SoldReceiptCreateDTO.SoldPaymentItemDTO> paymentList) {
        if (paymentList == null || paymentList.isEmpty()) {
            return;
        }

        // 批量创建支付明细
        List<SoldReceiptPaymentEntity> paymentDetails = new ArrayList<>();
        for (SoldReceiptCreateDTO.SoldPaymentItemDTO paymentItem : paymentList) {
            // 如果金额为零，则不创建支付明细
            if (paymentItem.getAmount() == null || paymentItem.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            SoldReceiptPaymentEntity paymentDetail = new SoldReceiptPaymentEntity();
            paymentDetail.setSoldReceiptId(soldReceiptId);
            paymentDetail.setType(paymentItem.getType());
            paymentDetail.setAmount(PriceUtil.yuan2fen(paymentItem.getAmount()).longValue());
            paymentDetails.add(paymentDetail);
        }

        // 批量插入支付明细
        if (!paymentDetails.isEmpty()) {
            soldReceiptPaymentMapper.insertBatchSelective(paymentDetails);
        }
    }

    /**
     * 创建单个支付明细（通用方法）
     */
    private SoldReceiptPaymentEntity createSinglePaymentDetail(Long soldReceiptId, SoldReceiptCreateDTO.SoldPaymentItemDTO paymentItem) {
        // 如果金额为零，则不创建支付明细
        if (paymentItem.getAmount() == null || paymentItem.getAmount().compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }

        SoldReceiptPaymentEntity paymentDetail = new SoldReceiptPaymentEntity();
        paymentDetail.setSoldReceiptId(soldReceiptId);
        paymentDetail.setType(paymentItem.getType());
        paymentDetail.setAmount(PriceUtil.yuan2fen(paymentItem.getAmount()).longValue());
        soldReceiptPaymentMapper.insertSelective(paymentDetail);
        return paymentDetail;
    }

    /**
     * 创建销售单明细记录
     */
    private void createSoldReceiptDetails(Long soldReceiptId, SoldReceiptCreateDTO dto) {
        List<SoldReceiptDetailEntity> details = new ArrayList<>();

        // 货品明细 - 批量查询后批量创建明细记录
        if (dto.getGoodsList() != null && !dto.getGoodsList().isEmpty()) {
            List<SoldReceiptGoodsEntity> goodsDetails = soldReceiptGoodsMapper.selectListByQuery(
                    QueryWrapper.create().where(SoldReceiptGoodsEntity::getSoldReceiptId).eq(soldReceiptId)
            );

            for (SoldReceiptGoodsEntity goodsDetail : goodsDetails) {
                SoldReceiptDetailEntity detail = new SoldReceiptDetailEntity();
                detail.setSoldReceiptId(soldReceiptId);
                detail.setType(SoldReceiptDetailType.GOODS.getValue());
                detail.setDetailId(goodsDetail.getId());
                details.add(detail);
            }
        }

        // 旧料明细 - 批量查询后批量创建明细记录
        if (dto.getOldMaterialList() != null && !dto.getOldMaterialList().isEmpty()) {
            List<SoldReceiptOldMaterialEntity> oldMaterialDetails = soldReceiptOldMaterialMapper.selectListByQuery(
                    QueryWrapper.create().where(SoldReceiptOldMaterialEntity::getSoldReceiptId).eq(soldReceiptId)
            );

            for (SoldReceiptOldMaterialEntity oldMaterialDetail : oldMaterialDetails) {
                SoldReceiptDetailEntity detail = new SoldReceiptDetailEntity();
                detail.setSoldReceiptId(soldReceiptId);
                detail.setType(SoldReceiptDetailType.OLD_MATERIAL.getValue());
                detail.setDetailId(oldMaterialDetail.getId());
                details.add(detail);
            }
        }

        // 赠品明细 - 批量查询后批量创建明细记录
        if (dto.getGiftList() != null && !dto.getGiftList().isEmpty()) {
            List<SoldReceiptGiftEntity> giftDetails = soldReceiptGiftMapper.selectListByQuery(
                    QueryWrapper.create().where(SoldReceiptGiftEntity::getSoldReceiptId).eq(soldReceiptId)
            );

            for (SoldReceiptGiftEntity giftDetail : giftDetails) {
                SoldReceiptDetailEntity detail = new SoldReceiptDetailEntity();
                detail.setSoldReceiptId(soldReceiptId);
                detail.setType(SoldReceiptDetailType.GIFT.getValue());
                detail.setDetailId(giftDetail.getId());
                details.add(detail);
            }
        }

        // 批量插入明细记录
        if (!details.isEmpty()) {
            soldReceiptDetailMapper.insertBatchSelective(details);
        }
    }

    /**
     * 更新库存
     *
     * @param dto 销售单创建DTO
     * @param oldMaterials 旧料列表
     * @param soldReceiptStatus 销售单状态 (0-挂单中, 1-已完成)
     * @param receiptSn 销售单号
     */
    private void updateStocks(SoldReceiptCreateDTO dto, List<OldMaterialEntity> oldMaterials, Integer soldReceiptStatus, String receiptSn) {
        boolean isCompleted = soldReceiptStatus != null && soldReceiptStatus == 1;

        // 更新货品库存
        if (dto.getGoodsList() != null && !dto.getGoodsList().isEmpty()) {
            List<StockNumChangeBO> goodsStockChanges = new ArrayList<>();
            for (SoldReceiptCreateDTO.SoldGoodsItemDTO goodsItem : dto.getGoodsList()) {
                StockNumChangeBO.StockNumChangeBOBuilder builder = StockNumChangeBO.builder()
                        .goodsId(goodsItem.getGoodsId())
                        .stockNum(-goodsItem.getNum()); // 减少库存

                if (isCompleted) {
                    // 已完成：减少库存，增加销售数量
                    builder.soldNum(goodsItem.getNum()).comment("销售出库");
                } else {
                    // 挂单中：减少库存，增加冻结数量
                    builder.frozenNum(goodsItem.getNum()).comment("销售冻结");
                }

                goodsStockChanges.add(builder.build());
            }
            StockUtils.updateStocks(goodsStockChanges);

            // 记录货品操作日志
            Set<Long> goodsIds = dto.getGoodsList().stream().map(SoldReceiptCreateDTO.SoldGoodsItemDTO::getGoodsId).collect(Collectors.toSet());
            Map<Long, GoodsEntity> goodsMap = goodsIds.isEmpty() ? Collections.emptyMap() :
                goodsMapper.selectListByIds(goodsIds).stream().collect(Collectors.toMap(GoodsEntity::getId, g -> g));

            for (StockNumChangeBO stockChange : goodsStockChanges) {
                GoodsEntity goods = goodsMap.get(stockChange.getGoodsId());
                if (goods != null) {
                    String logContent = buildGoodsLogContent(receiptSn, stockChange);
                    OpLogUtils.appendGoodsLog("销售管理-创建销售单", logContent, stockChange, goods);
                }
            }
        }

        // 更新赠品库存
        if (dto.getGiftList() != null && !dto.getGiftList().isEmpty()) {
            List<GiftStockNumChangeBO> giftStockChanges = new ArrayList<>();
            for (SoldReceiptCreateDTO.SoldGiftItemDTO giftItem : dto.getGiftList()) {
                GiftStockNumChangeBO.GiftStockNumChangeBOBuilder builder = GiftStockNumChangeBO.builder()
                        .giftId(giftItem.getGiftId())
                        .stockNum(-giftItem.getNum()); // 减少库存

                if (isCompleted) {
                    // 已完成：减少库存，增加销售数量
                    builder.soldNum(giftItem.getNum()).comment("销售出库");
                } else {
                    // 挂单中：减少库存，增加冻结数量
                    builder.frozenNum(giftItem.getNum()).comment("销售冻结");
                }

                giftStockChanges.add(builder.build());
            }
            StockUtils.updateGiftStocks(giftStockChanges);
        }

        // 更新旧料数量：只有在销售单状态为已完成时才更新旧料数量
        if (isCompleted && dto.getOldMaterialList() != null && !dto.getOldMaterialList().isEmpty()) {
            updateOldMaterialQuantities(dto.getOldMaterialList(), oldMaterials);
        }
    }

    /**
     * 更新旧料数量信息
     *
     * @param oldMaterialList 旧料明细列表
     * @param oldMaterials 旧料实体列表
     */
    private void updateOldMaterialQuantities(List<SoldReceiptCreateDTO.SoldOldMaterialItemDTO> oldMaterialList,
                                           List<OldMaterialEntity> oldMaterials) {
        List<OldMaterialStockNumChangeBO> stockChanges = new ArrayList<>();

        for (int i = 0; i < oldMaterialList.size(); i++) {
            SoldReceiptCreateDTO.SoldOldMaterialItemDTO oldMaterialItem = oldMaterialList.get(i);
            OldMaterialEntity oldMaterial = oldMaterials.get(i);

            // 使用StockUtils更新旧料数量：从0更新为实际销售数量
            OldMaterialStockNumChangeBO stockChange = OldMaterialStockNumChangeBO.builder()
                    .oldMaterialId(oldMaterial.getId().longValue())
                    .num(oldMaterialItem.getNum()) // 增加数量
                    .comment("销售单完成-旧料入库")
                    .build();
            stockChanges.add(stockChange);
        }

        // 批量更新旧料库存
        if (!stockChanges.isEmpty()) {
            StockUtils.updateOldMaterialStocks(stockChanges);
        }
    }

    /**
     * 调整库存状态变化 - 从挂单变为已完成时调用
     * 将冻结数量转为销售数量，并更新旧料数量
     */
    private void adjustStockForStatusChange(Long soldReceiptId) {
        // 调整货品库存：冻结 -> 销售
        List<SoldReceiptGoodsEntity> goodsDetails = soldReceiptGoodsMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptGoodsEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        if (!goodsDetails.isEmpty()) {
            List<StockNumChangeBO> goodsStockChanges = new ArrayList<>();
            for (SoldReceiptGoodsEntity goodsDetail : goodsDetails) {
                StockNumChangeBO stockChange = StockNumChangeBO.builder()
                        .goodsId(goodsDetail.getGoodsId())
                        .frozenNum(-goodsDetail.getNum()) // 减少冻结数量
                        .soldNum(goodsDetail.getNum())    // 增加销售数量
                        .comment("取单状态变更-冻结转销售")
                        .build();
                goodsStockChanges.add(stockChange);
            }
            StockUtils.updateStocks(goodsStockChanges);

            // 记录货品操作日志
            SoldReceiptEntity soldReceipt = soldReceiptMapper.selectOneById(soldReceiptId);
            String receiptSn = soldReceipt != null ? soldReceipt.getReceiptSn() : "未知";

            Set<Long> goodsIds = goodsDetails.stream().map(SoldReceiptGoodsEntity::getGoodsId).collect(Collectors.toSet());
            Map<Long, GoodsEntity> goodsMap = goodsIds.isEmpty() ? Collections.emptyMap() :
                goodsMapper.selectListByIds(goodsIds).stream().collect(Collectors.toMap(GoodsEntity::getId, g -> g));

            for (StockNumChangeBO stockChange : goodsStockChanges) {
                GoodsEntity goods = goodsMap.get(stockChange.getGoodsId());
                if (goods != null) {
                    String logContent = buildGoodsLogContent(receiptSn, stockChange);
                    OpLogUtils.appendGoodsLog("销售管理-取单", logContent, stockChange, goods);
                }
            }
        }

        // 调整赠品库存：冻结 -> 销售
        List<SoldReceiptGiftEntity> giftDetails = soldReceiptGiftMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptGiftEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        if (!giftDetails.isEmpty()) {
            List<GiftStockNumChangeBO> giftStockChanges = new ArrayList<>();
            for (SoldReceiptGiftEntity giftDetail : giftDetails) {
                GiftStockNumChangeBO stockChange = GiftStockNumChangeBO.builder()
                        .giftId(giftDetail.getGiftId())
                        .frozenNum(-giftDetail.getNum()) // 减少冻结数量
                        .soldNum(giftDetail.getNum())    // 增加销售数量
                        .comment("取单状态变更-冻结转销售")
                        .build();
                giftStockChanges.add(stockChange);
            }
            StockUtils.updateGiftStocks(giftStockChanges);
        }

        // 更新旧料数量：从挂单变为已完成时，需要将旧料数量从0更新为实际数量
        List<SoldReceiptOldMaterialEntity> oldMaterialDetails = soldReceiptOldMaterialMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptOldMaterialEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        if (!oldMaterialDetails.isEmpty()) {
            List<OldMaterialStockNumChangeBO> oldMaterialStockChanges = new ArrayList<>();
            for (SoldReceiptOldMaterialEntity oldMaterialDetail : oldMaterialDetails) {
                // 更新旧料表的数量
                OldMaterialEntity oldMaterial = oldMaterialMapper.selectOneById(oldMaterialDetail.getOldMaterialId());
                if (oldMaterial != null) {
                    // 计算数量变化：从0变为实际数量
                    int quantityChange = oldMaterialDetail.getNum() - oldMaterial.getNum();
                    if (quantityChange != 0) {
                        OldMaterialStockNumChangeBO stockChange = OldMaterialStockNumChangeBO.builder()
                                .oldMaterialId(oldMaterial.getId().longValue())
                                .num(quantityChange) // 增加数量
                                .comment("取单状态变更-更新旧料数量")
                                .build();
                        oldMaterialStockChanges.add(stockChange);
                    }
                }
            }

            // 批量更新旧料库存
            if (!oldMaterialStockChanges.isEmpty()) {
                StockUtils.updateOldMaterialStocks(oldMaterialStockChanges);

                // 记录旧料操作日志
                SoldReceiptEntity soldReceipt = soldReceiptMapper.selectOneById(soldReceiptId);
                String receiptSn = soldReceipt != null ? soldReceipt.getReceiptSn() : "未知";

                for (OldMaterialStockNumChangeBO stockChange : oldMaterialStockChanges) {
                    OldMaterialEntity oldMaterial = oldMaterialMapper.selectOneById(stockChange.getOldMaterialId().intValue());
                    if (oldMaterial != null) {
                        String logContent = String.format("销售单：%s，%s，数量变更：%d",
                            receiptSn, stockChange.getComment(), stockChange.getNum());
                        OpLogUtils.appendOpLog("销售管理-取单", logContent,
                            Map.of("旧料ID", oldMaterial.getId(), "旧料编号", oldMaterial.getOldMaterialSn(),
                                   "数量变更", stockChange.getNum()));
                    }
                }
            }
        }
    }

    /**
     * 构建货品日志内容，包含详细的数量变更信息
     */
    private String buildGoodsLogContent(String receiptSn, StockNumChangeBO stockChange) {
        StringBuilder logContent = new StringBuilder();
        logContent.append(String.format("销售单：%s，%s", receiptSn, stockChange.getComment()));

        // 补充数量变更信息
        if (stockChange.getStockNum() != 0) {
            logContent.append(String.format("，库存数量变更：%d", stockChange.getStockNum()));
        }
        if (stockChange.getSoldNum() != 0) {
            logContent.append(String.format("，售出数量变更：%d", stockChange.getSoldNum()));
        }
        if (stockChange.getFrozenNum() != 0) {
            logContent.append(String.format("，冻结数量变更：%d", stockChange.getFrozenNum()));
        }
        if (stockChange.getNum() != 0) {
            logContent.append(String.format("，原始数量变更：%d", stockChange.getNum()));
        }

        return logContent.toString();
    }

    /**
     * 计算重量
     * 重量 = 金重 + 银重 + 主石重 * 主石数量 * 0.2 + 辅石重 * 辅石数 * 0.2
     */
    private BigDecimal calculateWeight(BigDecimal netGoldWeight, BigDecimal netSilverWeight,
                                     BigDecimal mainStoneWeight, Integer mainStoneCount,
                                     BigDecimal subStoneWeight, Integer subStoneCount) {
        BigDecimal weight = BigDecimal.ZERO;

        // 金重
        if (netGoldWeight != null) {
            weight = weight.add(netGoldWeight);
        }

        // 银重
        if (netSilverWeight != null) {
            weight = weight.add(netSilverWeight);
        }

        // 主石重 * 主石数量 * 0.2
        if (mainStoneWeight != null && mainStoneCount != null && mainStoneCount > 0) {
            weight = weight.add(mainStoneWeight.multiply(new BigDecimal(mainStoneCount)).multiply(new BigDecimal("0.2")));
        }

        // 辅石重 * 辅石数 * 0.2
        if (subStoneWeight != null && subStoneCount != null && subStoneCount > 0) {
            weight = weight.add(subStoneWeight.multiply(new BigDecimal(subStoneCount)).multiply(new BigDecimal("0.2")));
        }

        return weight;
    }
}
