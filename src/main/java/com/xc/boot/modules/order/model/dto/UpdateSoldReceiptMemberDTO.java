package com.xc.boot.modules.order.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改销售单会员请求DTO
 */
@Data
@Schema(description = "修改销售单会员请求DTO")
public class UpdateSoldReceiptMemberDTO {

    @Schema(description = "销售单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "销售单ID不能为空")
    private Long soldReceiptId;

    @Schema(description = "会员ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "会员ID不能为空")
    private Long memberId;
}
