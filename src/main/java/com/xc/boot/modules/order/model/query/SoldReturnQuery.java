package com.xc.boot.modules.order.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class SoldReturnQuery extends BasePageQuery {

    @Schema(description = "退货单号")
    private String returnCode;

    @Schema(description = "销售单号")
    private String saleCode;

    @Schema(description = "开始时间")
    private Date[] timeRange;

    @Schema(description = "所属门店ID(英文逗号分割)")
    private String merchantIds;

    @Schema(description = "收银员ID(英文逗号分割)")
    private String cashierIds;

    @Schema(description = "会员ID(英文逗号分割)")
    private String memberIds;

    @Schema(description = "退货经办人ID(英文逗号分割)")
    private String transactorIds;
} 