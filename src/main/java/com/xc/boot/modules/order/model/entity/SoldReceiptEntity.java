package com.xc.boot.modules.order.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 销售单
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Table(value = "sold_receipt", comment = "销售单")
public class SoldReceiptEntity extends BaseEntity implements CreatedByListenerFlag {

    /**
     * 所属商户
     */
    @Column("company_id")
    private Long companyId;

    /**
     * 所属门店
     */
    @Column("merchant_id")
    private Long merchantId;

    /**
     * 销售单号
     */
    @Column("receipt_sn")
    private String receiptSn;

    /**
     * 会员ID
     */
    @Column("member_id")
    private Long memberId;

    /**
     * 收银员ID
     */
    @Column("cashier_id")
    private Long cashierId;

    /**
     * 主销导购ID
     */
    @Column("main_seller_id")
    private Long mainSellerId;

    /**
     * 辅销导购ID
     */
    @Column("support_seller_id")
    private Long supportSellerId;

    /**
     * 商品数量
     */
    @Column("num")
    private Integer num;

    /**
     * 总重量
     */
    @Column("weight")
    private BigDecimal weight;

    /**
     * 总金重
     */
    @Column("gold_weight")
    private BigDecimal goldWeight;

    /**
     * 总银重
     */
    @Column("silver_weight")
    private BigDecimal silverWeight;

    /**
     * 订单金额(分)
     */
    @Column("amount")
    private Long amount;

    /**
     * 货品金额(分)
     */
    @Column("goods_amount")
    private Long goodsAmount;

    /**
     * 优惠金额(分)
     */
    @Column("discount_amount")
    private Long discountAmount;

    /**
     * 抵扣金额-旧料(分)
     */
    @Column("deduction_amount")
    private Long deductionAmount;

    /**
     * 赠品金额(分)
     */
    @Column("gift_amount")
    private Long giftAmount;

    /**
     * 抹零金额(分)
     */
    @Column("adjust_amount")
    private Long adjustAmount;

    /**
     * 已收金额(分)
     */
    @Column("received_amount")
    private Long receivedAmount;

    /**
     * 实付金额(分)
     */
    @Column("paid_amount")
    private Long paidAmount;

    /**
     * 退款金额(分)
     */
    @Column("refund_amount")
    private Long refundAmount;

    /**
     * 挂单时间
     */
    @Column("pending_at")
    private Date pendingAt;

    /**
     * 销售时间
     */
    @Column("sold_at")
    private Date soldAt;

    /**
     * 状态(-1-已作废 0-挂单中 1-已完成 2-部分退货 3-全部退货)
     */
    @Column("status")
    private Integer status;

    /**
     * 备注
     */
    @Column("remark")
    private String remark;

    /**
     * 定金退款方式(0:现金,1:支付宝,2:微信,3:刷卡)
     */
    @Column("refund_type")
    private Integer refundType;

    /**
     * 定金退款备注
     */
    @Column("refund_remark")
    private String refundRemark;

    /**
     * 退款人
     */
    @Column("refund_by")
    private Long refundBy;

    /**
     * 创建人
     */
    @Column("created_by")
    private Long createdBy;

}
