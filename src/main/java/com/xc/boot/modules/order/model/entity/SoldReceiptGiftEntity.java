package com.xc.boot.modules.order.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 销售单赠品明细
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Table(value = "sold_receipt_gift", comment = "销售单赠品明细")
public class SoldReceiptGiftEntity extends BaseEntity {

    /**
     * 销售单ID
     */
    @Column("sold_receipt_id")
    private Long soldReceiptId;

    /**
     * 赠品ID
     */
    @Column("gift_id")
    private Long giftId;

    /**
     * 赠品编号
     */
    @Column("gift_sn")
    private String giftSn;

    /**
     * 数量
     */
    @Column("num")
    private Integer num;

    /**
     * 标签单价(分)
     */
    @Column("tag_price")
    private Long tagPrice;

    /**
     * 销售单价(分)
     */
    @Column("sold_price")
    private Long soldPrice;

    /**
     * 赠品快照
     */
    @Column("data_snapshot")
    private String dataSnapshot;

}
