package com.xc.boot.modules.order.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SoldReturnItemForm {
    @NotNull(message = "详情id不能为空")
    @Schema(description = "详情id")
    private Long id;

    @NotNull(message = "货品ID不能为空")
    @Schema(description = "货品ID")
    private Long goodsId;

    @NotBlank(message = "货品编号不能为空")
    @Schema(description = "货品编号")
    private String goodsSn;

    @NotBlank(message = "货品名称不能为空")
    @Schema(description = "货品名称")
    private String name;

    @Schema(description = "销售方式(1:按重量,2:按数量)")
    @NotNull(message = "销售方式不能为空")
    private Integer salesType;

    @Schema(description = "原购数量")
    @NotNull(message = "原购数量不能为空")
    private Integer originNum;

    @Min(value = 1, message = "退货数量不能小于1")
    @NotNull(message = "退货数量不能为空")
    @Schema(description = "退货数量")
    private Integer num;

    @Schema(description = "净金重(g)")
    @NotNull(message = "净金重不能为空")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")
    @NotNull(message = "净银重不能为空")
    private BigDecimal netSilverWeight;

    @Schema(description = "成本价(元)")
    @NotNull(message = "成本价不能为空")
    private BigDecimal costPrice;

    @Schema(description = "标签价(元)")
    @NotNull(message = "标签价不能为空")
    private BigDecimal tagPrice;

    @Schema(description = "实收金额(元)")
    @NotNull(message = "实收金额不能为空")
    private BigDecimal revenuePrice = BigDecimal.ZERO;

    @Schema(description = "退货金额(元)")
    @NotNull(message = "退货金额不能为空")
    private BigDecimal returnPrice = BigDecimal.ZERO;

    @Schema(description = "折旧费(元)")
    @NotNull(message = "折旧费不能为空")
    private BigDecimal depreciationPrice = BigDecimal.ZERO;

    @Schema(description = "应退金额(元)")
    @NotNull(message = "应退金额不能为空")
    private BigDecimal payPrice = BigDecimal.ZERO;
} 