package com.xc.boot.modules.order.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;

/**
 * 回收单更新表单
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "回收单更新表单")
public class MaterialRecycleUpdateForm {

    @Schema(description = "回收单ID")
    @NotNull(message = "回收单ID不能为空")
    private Integer id;

    @Schema(description = "主导购ID")
    private Integer mainAdviserId;

    @Schema(description = "辅购ID")
    private Integer subAdviserId;

    @Schema(description = "会员ID")
    private Long memberId;

    @Schema(description = "收银员ID")
    private Long cashierId;
} 