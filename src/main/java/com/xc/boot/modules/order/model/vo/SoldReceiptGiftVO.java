package com.xc.boot.modules.order.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 销售单赠品明细VO
 */
@Data
@Schema(description = "销售单赠品明细VO")
public class SoldReceiptGiftVO {

    @Schema(description = "赠品明细ID")
    private Long giftDetailId;

    @Schema(description = "销售单ID")
    private Long soldReceiptId;

    @Schema(description = "赠品ID")
    private Long giftId;
    
    @Schema(description = "赠品编号")
    private String giftSn;
    
    @Schema(description = "赠品名称")
    private String giftName;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "库存数量")
    private Integer stockNum;

    @Schema(description = "价值(元)")
    private BigDecimal tagPrice;

    @Schema(description = "销售金额(元)")
    private BigDecimal soldPrice;
}
