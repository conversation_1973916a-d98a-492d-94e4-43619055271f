package com.xc.boot.modules.order.model.form;

import com.xc.boot.common.util.PriceUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

/**
 * 回收单明细项表单
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "回收单明细项表单")
public class MaterialRecycleItemForm {

    @Schema(description = "所属大类ID")
    @NotNull(message = "所属大类不能为空")
    private Integer categoryId;

    @Schema(description = "货品小类ID")
    @NotNull(message = "货品小类不能为空")
    private Integer subclassId;

    @Schema(description = "成色ID")
    private Integer qualityId;

    @Schema(description = "旧料名称")
    @NotNull(message = "旧料名称不能为空")
    @Length(max = 255, message = "旧料名称长度不能超过255")
    private String name;

    @Schema(description = "回收计价方式(1:按重量,2:按数量)")
    @NotNull(message = "回收计价方式不能为空")
    private Integer salesType;

    @Schema(description = "数量")
    @NotNull(message = "数量不能为空")
    private Integer num;

    @Schema(description = "金重")
    private BigDecimal netGoldWeight;

    @Schema(description = "银重")
    private BigDecimal netSilverWeight;

    @Schema(description = "回收单价(元/件)")
    private BigDecimal recyclePrice;

    @Schema(description = "回收金价(元/克)")
    private BigDecimal goldPrice;

    @Schema(description = "回收银价(元/克)")
    private BigDecimal silverPrice;

    @Schema(description = "回收金额(元)")
    private BigDecimal recycleAmount;

    public BigDecimal getNetGoldWeight() {
        return PriceUtil.formatThreeDecimal(netGoldWeight);
    }

    public BigDecimal getNetSilverWeight() {
        return PriceUtil.formatThreeDecimal(netSilverWeight);
    }

    public BigDecimal getRecyclePrice() {
        return PriceUtil.formatTwoDecimal(recyclePrice);
    }

    public BigDecimal getRecycleAmount() {
        return PriceUtil.formatTwoDecimal(recycleAmount);
    }
}