package com.xc.boot.modules.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.baseColum.OtherColumEnum;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtilV2;
import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialHasImagesMapper;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialHasImagesEntity;
import com.xc.boot.modules.order.model.enums.PayTypeEnum;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.member.service.MemberService;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialEntity;
import com.xc.boot.modules.oldmaterial.service.OldMaterialService;
import com.xc.boot.modules.order.mapper.SoldReceiptGoodsMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptMapper;
import com.xc.boot.modules.order.mapper.SoldReturnDetailMapper;
import com.xc.boot.modules.order.mapper.SoldReturnMapper;
import com.xc.boot.modules.order.model.entity.SoldReceiptEntity;
import com.xc.boot.modules.order.model.entity.SoldReceiptGoodsEntity;
import com.xc.boot.modules.order.model.entity.SoldReturnDetailEntity;
import com.xc.boot.modules.order.model.entity.SoldReturnEntity;
import com.xc.boot.modules.order.model.form.SoldReturnForm;
import com.xc.boot.modules.order.model.form.SoldReturnItemForm;
import com.xc.boot.modules.order.model.form.UpdateSoldReturnMemberForm;
import com.xc.boot.modules.order.model.form.UpdateSoldReturnTransactorForm;
import com.xc.boot.modules.order.model.query.SoldReturnQuery;
import com.xc.boot.modules.order.model.vo.SoldReturnGoodsDetailVO;
import com.xc.boot.modules.order.model.vo.SoldReturnInfoDetailVO;
import com.xc.boot.modules.order.model.vo.SoldReturnInfoVO;
import com.xc.boot.modules.order.model.vo.SoldReturnPageVO;
import com.xc.boot.modules.order.service.SoldReturnService;
import com.xc.boot.system.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptGoodsTableDef.SOLD_RECEIPT_GOODS;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptTableDef.SOLD_RECEIPT;
import static com.xc.boot.modules.order.model.entity.table.SoldReturnDetailTableDef.SOLD_RETURN_DETAIL;
import static com.xc.boot.modules.order.model.entity.table.SoldReturnTableDef.SOLD_RETURN;


/**
 * <AUTHOR>
 * @since 2024-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SoldReturnServiceImpl extends ServiceImpl<SoldReturnMapper, SoldReturnEntity> implements SoldReturnService {

    private final SoldReturnDetailMapper soldReturnDetailMapper;
    private final ListFillService listFillService;
    private final OldMaterialService oldMaterialService;
    private final UserService userService;
    private final SoldReceiptGoodsMapper soldReceiptGoodsMapper;
    private final SoldReceiptMapper soldReceiptMapper;
    private final GoodsMapper goodsMapper;
    private final MemberService memberService;
    private final OldMaterialHasImagesMapper oldMaterialHasImagesMapper;

    @Override
    public Page<SoldReturnPageVO> getPage(SoldReturnQuery query) {
        QueryWrapper queryWrapper = buildWrapper(query);
        queryWrapper.orderBy(SOLD_RETURN.ID, false);
        if (query.getExport() != null && query.getExport().equals(1)) {
            doExport(queryWrapper);
            return new Page<>();
        }
        if (query.getPrint() != null && query.getPrint().equals(1)) {
            long count = this.count(queryWrapper);
            query.setPrintNum(count);
        }
        
        Page<SoldReturnPageVO> page = this.mapper.paginateAs(query.getPageNum(), query.getPageSize(), queryWrapper, SoldReturnPageVO.class);
        fillPageList(page.getRecords());

        return page;
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper buildWrapper(SoldReturnQuery query) {
        QueryWrapper wrapper = QueryWrapper.create()
                .from(SOLD_RETURN)
                .where(SOLD_RETURN.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RETURN.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));

        // 多选门店
        if (query.getMerchantIds() != null && !query.getMerchantIds().isEmpty()) {
            String[] ids = query.getMerchantIds().split(",");
            wrapper.and(SOLD_RETURN.MERCHANT_ID.in(List.of(ids)));
        }
        // 退货单号
        if (query.getReturnCode() != null && !query.getReturnCode().isEmpty()) {
            wrapper.and(SOLD_RETURN.RETURN_CODE.like(query.getReturnCode()));
        }
        // 销售单号
        if (query.getSaleCode() != null && !query.getSaleCode().isEmpty()) {
            wrapper.and(SOLD_RETURN.RECEIPT_SN.like(query.getSaleCode()));
        }
        // 多选收银员
        if (query.getCashierIds() != null && !query.getCashierIds().isEmpty()) {
            String[] ids = query.getCashierIds().split(",");
            wrapper.and(SOLD_RETURN.CASHIER_ID.in((Object[]) ids));
        }
        // 多选会员
        if (query.getMemberIds() != null && !query.getMemberIds().isEmpty()) {
            String[] ids = query.getMemberIds().split(",");
            wrapper.and(SOLD_RETURN.MEMBER_ID.in((Object[]) ids));
        }
        // 多选经办人
        if (query.getTransactorIds() != null && !query.getTransactorIds().isEmpty()) {
            String[] ids = query.getTransactorIds().split(",");
            wrapper.and(SOLD_RETURN.TRANSACTOR_ID.in((Object[]) ids));
        }
        // 时间范围
        if (query.getTimeRange() != null && query.getTimeRange().length > 0) {
            wrapper.and(SOLD_RETURN.CREATED_AT.between(query.getTimeRange()));
        }
        if (CollectionUtil.isNotEmpty(query.getIds())) {
            wrapper.and(SOLD_RETURN.ID.in(query.getIds()));
        }
        return wrapper;
    }

    @Override
    public SoldReturnPageVO getDetail(Long id) {
        SoldReturnEntity soldReturnEntity = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(SOLD_RETURN.ID.eq(id))
                .where(SOLD_RETURN.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        Assert.notNull(soldReturnEntity, "退货单不存在");
        Assert.isTrue(SecurityUtils.getMerchantIds().contains(soldReturnEntity.getMerchantId()), "无权查看该门店数据");

        SoldReturnPageVO vo = BeanUtil.copyProperties(soldReturnEntity, SoldReturnPageVO.class);
        // 填充主表额外信息
        fillPageList(List.of(vo));
        return vo;
    }

    @Override
    public List<SoldReturnGoodsDetailVO> getDetailList(Long returnId) {
        List<SoldReturnGoodsDetailVO> details = soldReturnDetailMapper.selectListByQueryAs(
                QueryWrapper.create().from(SOLD_RETURN_DETAIL)
                        .leftJoin(GOODS).on(SOLD_RETURN_DETAIL.GOODS_ID.eq(GOODS.ID))
                        .where(SOLD_RETURN_DETAIL.RETURN_ID.eq(returnId))
                        .where(SOLD_RETURN_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(SOLD_RETURN_DETAIL.ALL_COLUMNS,
                                GOODS.NAME,
                                GOODS.CATEGORY_ID,
                                GOODS.SUBCLASS_ID,
                                GOODS.QUALITY_ID), SoldReturnGoodsDetailVO.class
        );

        if (CollUtil.isEmpty(details)) {
            return Collections.emptyList();
        }
        // 填充列表信息
        fillDetailList(details);
        return details;
    }

    @Override
    public void updateTransactor(UpdateSoldReturnTransactorForm form) {
        SoldReturnEntity soldReturnEntity = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(SOLD_RETURN.ID.eq(form.getId()))
                .where(SOLD_RETURN.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        Assert.notNull(soldReturnEntity, "退货单不存在");
        Assert.isTrue(SecurityUtils.getMerchantIds().contains(soldReturnEntity.getMerchantId()), "无权查看该门店数据");

        SoldReturnEntity updateEntity = new SoldReturnEntity();
        updateEntity.setId(form.getId());
        updateEntity.setTransactorId(form.getTransactorId());
        this.updateById(updateEntity);
        String old = userService.getUserName(soldReturnEntity.getTransactorId());
        String userName = userService.getUserName(form.getTransactorId());
        OpLogUtils.appendOpLog("销售退货单", String.format("""
                退货单: %s
                修改经办人: %s 修改为 %s""", soldReturnEntity.getReturnCode(),old, userName), updateEntity);
    }

    @Override
    public void updateMember(UpdateSoldReturnMemberForm form) {
        SoldReturnEntity soldReturnEntity = this.getById(form.getId());
        if (soldReturnEntity == null || !SecurityUtils.getCompanyId().equals(soldReturnEntity.getCompanyId())) {
            throw new BusinessException("退货单不存在");
        }
        if (!SecurityUtils.getMerchantIds().contains(soldReturnEntity.getMerchantId())) {
            throw new BusinessException("无权修改该门店数据");
        }

        SoldReturnEntity updateEntity = new SoldReturnEntity();
        updateEntity.setId(form.getId());
        updateEntity.setMemberId(form.getMemberId());
        this.updateById(updateEntity);
        String userName = memberService.getMemberName(form.getMemberId());
        String old = memberService.getMemberName(soldReturnEntity.getMemberId());
        OpLogUtils.appendOpLog("销售退货单", String.format("""
                退货单: %s
                修改会员: %s 修改为 %s""", soldReturnEntity.getReturnCode(), old, userName), updateEntity);
    }

    @Override
    public List<SoldReturnInfoVO> getSaleInfo(String keyword, Long merchantId) {
        Long companyId = SecurityUtils.getCompanyId();

        // 构建门店过滤条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(SOLD_RECEIPT.COMPANY_ID.eq(companyId))
                .where(SOLD_RECEIPT.STATUS.in(1, 2))
                .where(SOLD_RECEIPT.RECEIPT_SN.eq(keyword).or(QueryMethods.exists(
                                QueryWrapper.create()
                                        .from(SOLD_RECEIPT_GOODS)
                                        .where(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.eq(SOLD_RECEIPT.ID))
                                        .where(SOLD_RECEIPT_GOODS.GOODS_SN.eq(keyword))
                        )
                ));

        // 根据merchantId参数决定门店过滤逻辑
        if (merchantId != null) {
            // 指定门店ID，需要验证用户是否有权限访问该门店
            Set<Long> userMerchantIds = SecurityUtils.getMerchantIds();
            if (!SecurityUtils.isMain() && !userMerchantIds.contains(merchantId)) {
                CommonUtils.abort("无权限查看该门店数据");
            }
            queryWrapper.where(SOLD_RECEIPT.MERCHANT_ID.eq(merchantId));
        } else {
            // 未指定门店ID，按原逻辑过滤用户拥有的门店数据
            queryWrapper.where(SOLD_RECEIPT.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }

        List<SoldReturnInfoVO> soldReceipts = soldReceiptMapper.selectListByQueryAs(queryWrapper, SoldReturnInfoVO.class);
        Assert.notEmpty(soldReceipts, "未查询到销售单");
        Set<Long> receiptIds = soldReceipts.stream().map(SoldReturnInfoVO::getId).collect(Collectors.toSet());
        List<SoldReturnInfoDetailVO> detailVOS = soldReceiptGoodsMapper.selectListByQueryAs(QueryWrapper.create()
                .where(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.in(receiptIds))
                .where(SOLD_RECEIPT_GOODS.REFUND_NUM.lt(SOLD_RECEIPT_GOODS.NUM))
                .leftJoin(GOODS).on(SOLD_RECEIPT_GOODS.GOODS_ID.eq(GOODS.ID))
                .select(SOLD_RECEIPT_GOODS.ALL_COLUMNS,
                        SOLD_RECEIPT_GOODS.REAL_AMOUNT.as(SoldReturnInfoDetailVO::getRevenuePrice),
                        SOLD_RECEIPT_GOODS.ID.as(SoldReturnInfoDetailVO::getId),
                        SOLD_RECEIPT_GOODS.NUM.as(SoldReturnInfoDetailVO::getOriginNum),
                        SOLD_RECEIPT_GOODS.COST_PRICE.as(SoldReturnInfoDetailVO::getCostPrice),
                        SOLD_RECEIPT_GOODS.TAG_PRICE.as(SoldReturnInfoDetailVO::getTagPrice),
                        SOLD_RECEIPT_GOODS.GOLD_WEIGHT.as(SoldReturnInfoDetailVO::getNetGoldWeight),
                        SOLD_RECEIPT_GOODS.SILVER_WEIGHT.as(SoldReturnInfoDetailVO::getNetSilverWeight),
                        SOLD_RECEIPT_GOODS.SALE_TYPE.as(SoldReturnInfoDetailVO::getSalesType),
                        GOODS.ID.as(SoldReturnInfoDetailVO::getGoodsId),
                        GOODS.CATEGORY_ID.as(SoldReturnInfoDetailVO::getCategoryId),
                        GOODS.SUBCLASS_ID.as(SoldReturnInfoDetailVO::getSubclassId),
                        GOODS.QUALITY_ID.as(SoldReturnInfoDetailVO::getQualityId),
                        GOODS.STYLE_ID.as(SoldReturnInfoDetailVO::getStyleId),
                        GOODS.TECHNOLOGY_ID.as(SoldReturnInfoDetailVO::getTechnologyId),
                        GOODS.NAME.as(SoldReturnInfoDetailVO::getName)
                        ), SoldReturnInfoDetailVO.class);
        Assert.notEmpty(detailVOS, "未查询到销售单详情");
        // 填充详情
        Map<Long, List<SoldReturnInfoDetailVO>> detailMap = detailVOS.stream().collect(Collectors.groupingBy(SoldReturnInfoDetailVO::getSoldReceiptId, Collectors.toList()));
        ListFillUtilV2.of(detailVOS)
                .build(listFillService::getCategoryNameById, SoldReturnInfoDetailVO::getCategoryId, SoldReturnInfoDetailVO::setCategory)
                .build(listFillService::getSubclassNameById, SoldReturnInfoDetailVO::getSubclassId, SoldReturnInfoDetailVO::setSubclass)
                .build(listFillService::getQualityNameById, SoldReturnInfoDetailVO::getQualityId, SoldReturnInfoDetailVO::setQuality)
                .build(listFillService::getStyleNameById, SoldReturnInfoDetailVO::getStyleId, SoldReturnInfoDetailVO::setStyle)
                .build(listFillService::getTechnologyNameById, SoldReturnInfoDetailVO::getTechnologyId, SoldReturnInfoDetailVO::setTechnology)
                .build(listFillService::getGoodsImgByGoodsId, SoldReturnInfoDetailVO::getGoodsId, SoldReturnInfoDetailVO::setImages)
                .peek(vo -> {
                    vo.setRevenuePrice(PriceUtil.fen2yuan(vo.getRevenuePrice()));
                    vo.setRefundableNum(vo.getOriginNum() - vo.getRefundNum());
                    vo.setSalesTypeText(OtherColumEnum.getSalesTypeText(vo.getSalesType().toString()));
                    vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
                    vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
                })
                .handle();
        // 填充销售单
        ListFillUtilV2.of(soldReceipts)
                .build(listFillService::getMerchantNameById, SoldReturnInfoVO::getMerchantId, SoldReturnInfoVO::setMerchant)
                .build(listFillService::getMemberNameById, SoldReturnInfoVO::getMemberId, SoldReturnInfoVO::setMember)
                .build(listFillService::getUserNameByUserId, SoldReturnInfoVO::getCashierId, SoldReturnInfoVO::setCashier)
                .build(listFillService::getUserNameByUserId, SoldReturnInfoVO::getMainSellerId, SoldReturnInfoVO::setMainSeller)
                .peek(vo -> {
                    vo.setDetails(detailMap.get(vo.getId()));
                    vo.setAmount(PriceUtil.fen2yuan(vo.getAmount()));
                })
                .handle();
        return soldReceipts;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(SoldReturnForm form) {
        Long companyId = SecurityUtils.getCompanyId();
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        SoldReceiptEntity receiptEntity = soldReceiptMapper.selectOneByQuery(QueryWrapper.create()
                .where(SOLD_RECEIPT.ID.eq(form.getSoldReceiptId()))
                .where(SOLD_RECEIPT.COMPANY_ID.eq(companyId))
                .where(SOLD_RECEIPT.MERCHANT_ID.in(merchantIds))
                .forUpdate());
        Assert.notNull(receiptEntity, "未查询到销售单");
        Assert.notEmpty(form.getDetails(), "销售单明细不能为空");
        List<SoldReceiptGoodsEntity> detailList = soldReceiptGoodsMapper.selectListByQuery(QueryWrapper.create()
                .where(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.eq(form.getSoldReceiptId()))
                .where(SOLD_RECEIPT_GOODS.ID.in(form.getDetails().stream().map(SoldReturnItemForm::getId).collect(Collectors.toSet())))
                .forUpdate());
        Map<Long, SoldReceiptGoodsEntity> detailMap = detailList.stream().collect(Collectors.toMap(SoldReceiptGoodsEntity::getId, entity -> entity));
        Assert.notEmpty(detailList, "未查询到销售单明细");
        String code = SnUtils.generateReturnCode();
        // 1. 数量合计、校验
        int totalNum = 0;
        BigDecimal totalNetGoldWeight = BigDecimal.ZERO;
        BigDecimal totalNetSilverWeight = BigDecimal.ZERO;
        // 总实收金额
        long totalRevenuePrice = 0L;
        // 总退货金额
        long totalReturnPrice = 0L;
        // 总折旧金额
        long totalDepreciationPrice = 0L;
        // 总应退金额
        long totalPayPrice = 0L;
        Set<Long> goodsIds = new HashSet<>();
        for (SoldReturnItemForm item : form.getDetails()) {
            Assert.isTrue(detailMap.containsKey(item.getId()), String.format("未查询到销售单明细: %s", item.getGoodsSn()));
            SoldReceiptGoodsEntity detail = detailMap.get(item.getId());
            Assert.isTrue(detail.getNum() >= item.getNum() + detail.getRefundNum(), String.format("销售单明细: %s 退货数量不能大于可退货数量", item.getGoodsSn()));
            // 退货金额 = 实付/原购数 * 退货数
            BigDecimal returnPrice = item.getRevenuePrice().divide(new BigDecimal(item.getOriginNum()), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(item.getNum()));
            Assert.isTrue(item.getDepreciationPrice().compareTo(returnPrice) <= 0, String.format("销售单明细: %s 折旧费不能大于退货金额", item.getGoodsSn()));
            // 应退 = 退货金额 - 折扣
            BigDecimal payPrice = returnPrice.subtract(item.getDepreciationPrice());
            Assert.isTrue(returnPrice.compareTo(item.getReturnPrice()) == 0, "退货金额计算错误");
            Assert.isTrue(payPrice.compareTo(item.getPayPrice()) == 0, "应退金额计算错误");
            totalNum += item.getNum();
            totalNetGoldWeight = totalNetGoldWeight.add(item.getNetGoldWeight() != null ? item.getNetGoldWeight().multiply(BigDecimal.valueOf(item.getNum())) : BigDecimal.ZERO);
            totalNetSilverWeight = totalNetSilverWeight.add(item.getNetSilverWeight() != null ? item.getNetSilverWeight().multiply(BigDecimal.valueOf(item.getNum())) : BigDecimal.ZERO);
            totalRevenuePrice += PriceUtil.yuan2fen(item.getRevenuePrice()).longValue();
            totalReturnPrice += PriceUtil.yuan2fen(item.getReturnPrice()).longValue();
            totalDepreciationPrice += PriceUtil.yuan2fen(item.getDepreciationPrice()).longValue();
            totalPayPrice += PriceUtil.yuan2fen(item.getPayPrice()).longValue();
            goodsIds.add(item.getGoodsId());
        }

        // 2. 创建主单
        SoldReturnEntity soldReturnEntity = BeanUtil.copyProperties(form, SoldReturnEntity.class);
        soldReturnEntity.setCompanyId(SecurityUtils.getCompanyId());
        soldReturnEntity.setCreatedAt(form.getReturnAt());
        soldReturnEntity.setNum(totalNum);
        soldReturnEntity.setSoldId(form.getSoldReceiptId());
        soldReturnEntity.setTotalNetGoldWeight(totalNetGoldWeight);
        soldReturnEntity.setTotalNetSilverWeight(totalNetSilverWeight);
        soldReturnEntity.setTotalRevenuePrice(totalRevenuePrice);
        soldReturnEntity.setTotalReturnPrice(totalReturnPrice);
        soldReturnEntity.setTotalDepreciationPrice(totalDepreciationPrice);
        soldReturnEntity.setTotalPayPrice(totalPayPrice);
        soldReturnEntity.setReturnCode(code);
        this.mapper.insertSelective(soldReturnEntity);

        BigDecimal totalWeight = BigDecimal.ZERO;
        // 3. 创建明细和旧料
        Map<Long, GoodsEntity> goodsMap = goodsMapper.selectListByQuery(QueryWrapper.create()
                        .where(GoodsEntity::getId).in(goodsIds)
                        .where(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId()))
                .stream().collect(Collectors.toMap(GoodsEntity::getId, e -> e));

        // 校验货品门店ID与表单门店ID是否匹配
        for (SoldReturnItemForm item : form.getDetails()) {
            GoodsEntity goods = goodsMap.get(item.getGoodsId());
            Assert.notNull(goods, "商品不存在");
            Assert.isTrue(goods.getMerchantId().equals(form.getMerchantId()),
                String.format("货品 %s 所属门店与退货单门店不匹配", item.getGoodsSn()));
        }

        Map<String, List<GoodsHasImagesEntity>> goodsImgByGoodsId = listFillService.getGoodsImgByGoodsId(goodsIds);
        List<Long> fileIds = new ArrayList<>();
        for (SoldReturnItemForm item : form.getDetails()) {
            // 3.1 创建旧料
            OldMaterialEntity oldMaterial = new OldMaterialEntity();
            GoodsEntity goods = goodsMap.get(item.getGoodsId());
            Assert.notNull(goods, "商品不存在");
            BeanUtil.copyProperties(goods, oldMaterial);
            oldMaterial.setOldMaterialSn(SnUtils.generateOldMaterialSn());
            oldMaterial.setNum(item.getNum());
            oldMaterial.setFrozenNum(0);
            oldMaterial.setId(null);
            oldMaterial.setRecyclePrice(PriceUtil.yuan2fenLong(item.getPayPrice().divide(new BigDecimal(item.getNum()), 2, RoundingMode.HALF_UP)));
            oldMaterial.setNetGoldWeight(item.getNetGoldWeight());
            oldMaterial.setNetSilverWeight(item.getNetSilverWeight());
            oldMaterial.setWeight(goods.getWeight());
            oldMaterial.setCostPrice(PriceUtil.yuan2fenLong(item.getCostPrice()));
            oldMaterial.setTagPrice(PriceUtil.yuan2fenLong(item.getTagPrice()));
            oldMaterial.setGoodsId(item.getGoodsId().intValue());
            oldMaterial.setGoldPrice(0L);
            oldMaterial.setSilverPrice(0L);
            oldMaterial.setSalesType(2);
            oldMaterial.setSoldReceiptSn(form.getReceiptSn());
            oldMaterial.setCreatedAt(new Date());
            // 重量为0时处理: 金重 + 银重 + 主石重 * 主石数量 * 0.2 + 辅石重 * 辅石数 *  0.2
            if (Objects.isNull(oldMaterial.getWeight()) || oldMaterial.getWeight().compareTo(BigDecimal.ZERO) == 0) {
                BigDecimal weight = PriceUtil.formatThreeDecimal(oldMaterial.getNetGoldWeight())
                        .add(PriceUtil.formatThreeDecimal(oldMaterial.getNetSilverWeight()))
                        .add(PriceUtil.formatThreeDecimal(oldMaterial.getMainStoneWeight())
                                .multiply(new BigDecimal(oldMaterial.getMainStoneCount() != null ? oldMaterial.getMainStoneCount() : 0))
                                .multiply(new BigDecimal("0.2")))
                        .add(PriceUtil.formatThreeDecimal(oldMaterial.getSubStoneWeight())
                                .multiply(new BigDecimal(oldMaterial.getSubStoneCount() != null ? oldMaterial.getSubStoneCount() : 0))
                                .multiply(new BigDecimal("0.2")));
                oldMaterial.setWeight(PriceUtil.formatThreeDecimal(weight));
            }
            oldMaterialService.save(oldMaterial);

            // 3.2 创建退货明细
            SoldReturnDetailEntity detail = new SoldReturnDetailEntity();
            BeanUtil.copyProperties(item, detail);
            detail.setCompanyId(SecurityUtils.getCompanyId());
            detail.setMerchantId(form.getMerchantId());
            detail.setReturnId(soldReturnEntity.getId());
            detail.setReturnCode(soldReturnEntity.getReturnCode());
            detail.setOldMaterialId(oldMaterial.getId());
            detail.setCostPrice(PriceUtil.yuan2fenLong(item.getCostPrice()));
            detail.setTagPrice(PriceUtil.yuan2fenLong(item.getTagPrice()));
            detail.setRevenuePrice(PriceUtil.yuan2fenLong(item.getRevenuePrice()));
            detail.setReturnPrice(PriceUtil.yuan2fenLong(item.getReturnPrice()));
            detail.setDepreciationPrice(PriceUtil.yuan2fenLong(item.getDepreciationPrice()));
            detail.setPayPrice(PriceUtil.yuan2fenLong(item.getPayPrice()));
            detail.setId(null);
            soldReturnDetailMapper.insertSelective(detail);

            // 3.3 修改销售明细退货数
            SoldReceiptGoodsEntity soldDetail = detailMap.get(item.getId());
            soldDetail.setRefundNum(soldDetail.getRefundNum() + item.getNum());
            soldReceiptGoodsMapper.update(soldDetail);

            // 3.4 统计总重量
            totalWeight = totalWeight.add(goods.getWeight().multiply(new BigDecimal(item.getNum())));

            // 3.5 保存旧料图片
            List<GoodsHasImagesEntity> list = Optional.ofNullable(goodsImgByGoodsId.get(item.getGoodsId().toString())).orElse(new ArrayList<>());
            List<OldMaterialHasImagesEntity> oldImgList = list.stream().map(img -> {
                OldMaterialHasImagesEntity entity = new OldMaterialHasImagesEntity();
                entity.setOldMaterialId(oldMaterial.getId().intValue())
                        .setUrl(img.getUrl())
                        .setImageId(img.getImageId().intValue())
                        .setSort(img.getSort())
                        .setCompanyId(companyId.intValue());
                fileIds.add(img.getImageId());
                return entity;
            }).toList();
            if (CollectionUtil.isNotEmpty(oldImgList)) {
                oldMaterialHasImagesMapper.insertBatchSelective(oldImgList);
            }
        }
        // 更新图片状态
        CommonUtils.batchUpdateFileStatusSync(fileIds, 1);
        // 更新销售单信息 退款金额、状态
        receiptEntity.setRefundAmount(receiptEntity.getRefundAmount() + totalPayPrice);
        long count = soldReceiptGoodsMapper.selectCountByQuery(QueryWrapper.create()
                .where(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.eq(receiptEntity.getId()))
                .where(SOLD_RECEIPT_GOODS.NUM.gt(SOLD_RECEIPT_GOODS.REFUND_NUM)));
        // 2-部分退货 3-全部退货
        receiptEntity.setStatus(count == 0 ? 3 : 2);
        soldReceiptMapper.update(receiptEntity);
        // 更新重量
        soldReturnEntity.setTotalWeight(totalWeight);
        this.mapper.update(soldReturnEntity);
        OpLogUtils.appendOpLog("销售退货单", String.format("""
                创建销售退货单: %s""", soldReturnEntity.getReturnCode()), soldReturnEntity);
    }

    /**
     * 填充列表额外信息
     */
    private void fillPageList(List<SoldReturnPageVO> list) {
        ListFillUtilV2.of(list)
                .build(listFillService::getMerchantNameById, SoldReturnPageVO::getMerchantId, SoldReturnPageVO::setMerchant)
                .build(listFillService::getUserNameByUserId, SoldReturnPageVO::getCashierId, SoldReturnPageVO::setCashier)
                .build(listFillService::getMemberNameById, SoldReturnPageVO::getMemberId, SoldReturnPageVO::setMember)
                .build(listFillService::getUserNameByUserId, SoldReturnPageVO::getTransactorId, SoldReturnPageVO::setTransactor)
                .peek(vo -> {
                    // 设置支付方式文本
                    if (vo.getPayType() != null) {
                        vo.setPayTypeText(getPayTypeText(vo.getPayType()));
                    }
                    vo.setTotalRevenuePrice(PriceUtil.fen2yuan(vo.getTotalRevenuePrice()));
                    vo.setTotalReturnPrice(PriceUtil.fen2yuan(vo.getTotalReturnPrice()));
                    vo.setTotalDepreciationPrice(PriceUtil.fen2yuan(vo.getTotalDepreciationPrice()));
                    vo.setTotalPayPrice(PriceUtil.fen2yuan(vo.getTotalPayPrice()));
                })
                .handle();
    }

    /**
     * 填充明细列表额外信息
     */
    private void fillDetailList(List<SoldReturnGoodsDetailVO> list) {
        ListFillUtilV2.of(list)
                .build(listFillService::getCategoryNameById, SoldReturnGoodsDetailVO::getCategoryId, SoldReturnGoodsDetailVO::setCategory)
                .build(listFillService::getSubclassNameById, SoldReturnGoodsDetailVO::getSubclassId, SoldReturnGoodsDetailVO::setSubclass)
                .build(listFillService::getQualityNameById, SoldReturnGoodsDetailVO::getQualityId, SoldReturnGoodsDetailVO::setQuality)
                .build(listFillService::getGoodsImgByGoodsId, SoldReturnGoodsDetailVO::getGoodsId, SoldReturnGoodsDetailVO::setImage)
                .peek(vo -> {
                    // 设置销售方式文本
                    if (vo.getSalesType() != null) {
                        vo.setSalesTypeText(OtherColumEnum.getSalesTypeText(vo.getSalesType().toString()));
                    }
                    // 转换价格单位（分->元）
                    vo.setRevenuePrice(PriceUtil.fen2yuan(vo.getRevenuePrice()));
                    vo.setReturnPrice(PriceUtil.fen2yuan(vo.getReturnPrice()));
                    vo.setDepreciationPrice(PriceUtil.fen2yuan(vo.getDepreciationPrice()));
                    vo.setPayPrice(PriceUtil.fen2yuan(vo.getPayPrice()));
                })
                .handle();
    }

    /**
     * 导出数据
     */
    private void doExport(QueryWrapper queryWrapper) {
        ExcelUtil.of(this.mapper, queryWrapper, SoldReturnPageVO.class, "sold_return", "销售退货列表")
                .getData((mapper, wrapper) -> {
                    List<SoldReturnPageVO> voList = mapper.selectListByQueryAs(wrapper, SoldReturnPageVO.class);
                    fillPageList(voList);
                    return voList;
                })
                .doExport();
    }

    /**
     * 获取支付方式文本
     */
    private String getPayTypeText(Integer payType) {
        if (payType == null) {
            return "未知";
        }

        String typeName = IBaseEnum.getLabelByValue(payType, PayTypeEnum.class);
        return typeName != null ? typeName : "未知";
    }

}