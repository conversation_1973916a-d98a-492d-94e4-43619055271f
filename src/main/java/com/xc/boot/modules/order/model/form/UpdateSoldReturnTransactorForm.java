package com.xc.boot.modules.order.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class UpdateSoldReturnTransactorForm {
    @NotNull(message = "退货单id不能为空")
    @Schema(description = "退货单id")
    private Long id;

    @NotNull(message = "经办人id不能为空")
    @Schema(description = "经办人id")
    private Long transactorId;
} 