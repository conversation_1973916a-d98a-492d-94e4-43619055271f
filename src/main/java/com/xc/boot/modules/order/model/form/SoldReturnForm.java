package com.xc.boot.modules.order.model.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import java.util.Date;
import java.util.List;

@Data
public class SoldReturnForm {
    @NotBlank(message = "销售单号不能为空")
    @Schema(description = "销售单号")
    private String receiptSn;

    @NotNull(message = "销售单id不能为空")
    @Schema(description = "销售单id")
    private Long soldReceiptId;

    @Schema(description = "会员id")
    private Long memberId;

    @NotNull(message = "门店id不能为空")
    @Schema(description = "门店id")
    private Long merchantId;

    @NotNull(message = "收银员id不能为空")
    @Schema(description = "收银员id")
    private Long cashierId;

    @NotNull(message = "经办人id不能为空")
    @Schema(description = "经办人id")
    private Long transactorId;

    @NotNull(message = "退货日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "退货日期")
    private Date returnAt;

    @NotNull(message = "退款方式不能为空")
    @Schema(description = "退款方式(0:现金,1:支付宝,2:微信,3:刷卡)")
    @Range(min = 0, max = 3, message = "退款方式参数超出范围")
    private Integer payType;

    @Schema(description = "备注")
    @Length(max = 255, message = "备注长度不能超过255")
    private String remark;

    @Valid
    @NotEmpty(message = "退货明细不能为空")
    @Schema(description = "退货明细")
    private List<SoldReturnItemForm> details;
} 