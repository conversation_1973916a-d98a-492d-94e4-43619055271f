package com.xc.boot.modules.order.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 销售单退定金请求DTO
 */
@Data
@Schema(description = "销售单退定金请求DTO")
public class SoldReceiptRefundDTO {

    @Schema(description = "销售单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "销售单ID不能为空")
    private Long soldReceiptId;

    @Schema(description = "退款备注", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "退款备注不能为空")
    private String refundRemark;

    @Schema(description = "退款方式(参考PayTypeEnum: 0:现金,1:支付宝,2:微信,3:刷卡)，已收金额为0时不需要传")
    private Integer refundType;
}
