package com.xc.boot.modules.order.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 销售单列表分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "销售单列表分页查询对象")
public class SoldReceiptListPageQuery extends BasePageQuery {
    
    @Schema(description = "订单号")
    private String receiptSn;
    
    @Schema(description = "会员手机号")
    private String memberPhone;
    
    @Schema(description = "所属门店ID(英文逗号分割)")
    private String merchantIds;
    
    @Schema(description = "主销导购ID(英文逗号分割)")
    private String mainSellerIds;
    
    @Schema(description = "收银员ID(英文逗号分割)")
    private String cashierIds;
    
    @Schema(description = "辅助导购ID(英文逗号分割)")
    private String supportSellerIds;
    
    @Schema(description = "订单状态(英文逗号分割)(-1-已作废 0-挂单中 1-已完成 2-部分退货 3-全部退货)")
    private String statuses;

    @Schema(description = "备注")
    private String remark;
    
    @Schema(description = "销售时间范围")
    private List<String> soldTimeRange;
}
