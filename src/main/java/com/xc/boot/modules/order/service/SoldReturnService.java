package com.xc.boot.modules.order.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.order.model.entity.SoldReturnEntity;
import com.xc.boot.modules.order.model.form.SoldReturnForm;
import com.xc.boot.modules.order.model.form.UpdateSoldReturnMemberForm;
import com.xc.boot.modules.order.model.form.UpdateSoldReturnTransactorForm;
import com.xc.boot.modules.order.model.query.SoldReturnQuery;
import com.xc.boot.modules.order.model.vo.SoldReturnInfoVO;
import com.xc.boot.modules.order.model.vo.SoldReturnGoodsDetailVO;
import com.xc.boot.modules.order.model.vo.SoldReturnPageVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-29
 */
public interface SoldReturnService extends IService<SoldReturnEntity> {

    /**
     * 销售退货单分页列表
     *
     * @param query 查询参数
     * @return 分页列表
     */
    Page<SoldReturnPageVO> getPage(SoldReturnQuery query);

    /**
     * 查看单个退货单详情
     *
     * @param id id
     * @return 详情
     */
    SoldReturnPageVO getDetail(Long id);

    /**
     * 查看退货单明细列表
     *
     * @param returnId 退货单id
     * @return 明细列表
     */
    List<SoldReturnGoodsDetailVO> getDetailList(Long returnId);

    /**
     * 修改退货单经办人
     *
     * @param form 参数
     */
    void updateTransactor(UpdateSoldReturnTransactorForm form);

    /**
     * 修改退货单会员
     *
     * @param form 参数
     */
    void updateMember(UpdateSoldReturnMemberForm form);

    /**
     * 根据货品条码或者销售单号查询销售单
     *
     * @param keyword 货品条码或者销售单号
     * @param merchantId 门店ID，为空时按原逻辑过滤用户拥有的门店数据
     * @return 销售单列表
     */
    List<SoldReturnInfoVO> getSaleInfo(String keyword, Long merchantId);

    /**
     * 销售退货开单
     *
     * @param form 参数
     */
    void create(SoldReturnForm form);
} 