package com.xc.boot.modules.order.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xc.boot.common.annotation.Flatten;
import com.xc.boot.common.annotation.GoodsColumn;
import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 销售开单-货品查询分页VO
 */
@Data
@Schema(description = "销售开单-货品查询分页VO")
public class SoldGoodsPageVO {
    
    @Schema(description = "货品ID")
    private Long id;
    
    @Schema(description = "所属商户ID")
    @GoodsColumn(value = "company_id")
    private Long companyId;
    
    @Schema(description = "所属门店ID")
    @GoodsColumn(value = "merchant_id")
    private Long merchantId;
    
    @Schema(description = "所属门店名称")
    private String merchant;
    
    @Schema(description = "所属柜台ID")
    @GoodsColumn(value = "counter_id")
    private Integer counterId;
    
    @Schema(description = "所属柜台名称")
    private String counter;
    
    @Schema(description = "供应商ID")
    @GoodsColumn(value = "supplier_id")
    private Integer supplierId;
    
    @Schema(description = "供应商名称")
    private String supplier;
    
    @Schema(description = "所属大类ID")
    @GoodsColumn(value = "category_id")
    private Integer categoryId;
    
    @Schema(description = "所属大类名称")
    private String category;
    
    @Schema(description = "所属小类ID")
    @GoodsColumn(value = "subclass_id")
    private Integer subclassId;
    
    @Schema(description = "所属小类名称")
    private String subclass;
    
    @Schema(description = "成色ID")
    @GoodsColumn(value = "quality_id")
    private Integer qualityId;
    
    @Schema(description = "成色名称")
    private String quality;
    
    @Schema(description = "品牌ID")
    @GoodsColumn(value = "brand_id")
    private Integer brandId;
    
    @Schema(description = "品牌名称")
    private String brand;
    
    @Schema(description = "款式ID")
    @GoodsColumn(value = "style_id")
    private Integer styleId;
    
    @Schema(description = "款式名称")
    private String style;
    
    @Schema(description = "工艺ID")
    @GoodsColumn(value = "technology_id")
    private Integer technologyId;
    
    @Schema(description = "工艺名称")
    private String technology;
    
    @Schema(description = "主石ID")
    @GoodsColumn(value = "main_stone_id")
    private Integer mainStoneId;
    
    @Schema(description = "主石名称")
    private String mainStone;
    
    @Schema(description = "副石ID")
    @GoodsColumn(value = "sub_stone_id")
    private Integer subStoneId;
    
    @Schema(description = "副石名称")
    private String subStone;
    
    @Schema(description = "货品条码")
    @GoodsColumn(value = "goods_sn")
    private String goodsSn;
    
    @Schema(description = "货品名称")
    @GoodsColumn(value = "name")
    private String name;
    
    @Schema(description = "销售方式(1:按重量,2:按数量)")
    @GoodsColumn(value = "sales_type")
    private Integer salesType;
    
    @Schema(description = "批次号")
    @GoodsColumn(value = "batch_no")
    private String batchNo;
    
    @Schema(description = "证书号")
    @GoodsColumn(value = "cert_no")
    private String certNo;
    
    @Schema(description = "备注")
    @GoodsColumn(value = "remark")
    private String remark;
    
    @Schema(description = "重量(g)")
    @GoodsColumn(value = "weight")
    private BigDecimal weight;
    
    @Schema(description = "净金重(g)")
    @GoodsColumn(value = "net_gold_weight")
    private BigDecimal netGoldWeight;
    
    @Schema(description = "净银重(g)")
    @GoodsColumn(value = "net_silver_weight")
    private BigDecimal netSilverWeight;
    
    @Schema(description = "主石重(ct)")
    @GoodsColumn(value = "main_stone_weight")
    private BigDecimal mainStoneWeight;
    
    @Schema(description = "副石重(ct)")
    @GoodsColumn(value = "sub_stone_weight")
    private BigDecimal subStoneWeight;
    
    @Schema(description = "成本价(元)")
    @GoodsColumn(value = "cost_price")
    private BigDecimal costPrice;
    
    @Schema(description = "金价(元)")
    @GoodsColumn(value = "gold_price")
    private BigDecimal goldPrice;
    
    @Schema(description = "银价(元)")
    @GoodsColumn(value = "silver_price")
    private BigDecimal silverPrice;
    
    @Schema(description = "工费(元)")
    @GoodsColumn(value = "work_price")
    private BigDecimal workPrice;
    
    @Schema(description = "证书费(元)")
    @GoodsColumn(value = "cert_price")
    private BigDecimal certPrice;
    
    @Schema(description = "销售工费(元)")
    @GoodsColumn(value = "sale_work_price")
    private BigDecimal saleWorkPrice;

    @Schema(description = "销工费计价方式(1:按重量,2:按数量)")
    @GoodsColumn(value = "sale_work_price_type")
    private Integer saleWorkPriceType;

    @Schema(description = "销工费计价方式名称")
    private String saleWorkPriceTypeName;

    @Schema(description = "标签价(元)")
    @GoodsColumn(value = "tag_price")
    private BigDecimal tagPrice;

    @Schema(description = "销售金价(元)")
    private BigDecimal saleGoldPrice;

    @Schema(description = "销售银价(元)")
    private BigDecimal saleSilverPrice;

    @Schema(description = "原始数量")
    @GoodsColumn(value = "num")
    private Integer num;
    
    @Schema(description = "库存数量")
    @GoodsColumn(value = "stock_num")
    private Integer stockNum;
    
    @Schema(description = "采购退数量")
    @GoodsColumn(value = "return_num")
    private Integer returnNum;
    
    @Schema(description = "售出数量")
    @GoodsColumn(value = "sold_num")
    private Integer soldNum;
    
    @Schema(description = "调拨中数量")
    @GoodsColumn(value = "transfer_num")
    private Integer transferNum;
    
    @Schema(description = "冻结数量")
    @GoodsColumn(value = "frozen_num")
    private Integer frozenNum;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
    
    @Schema(description = "自定义字段")
    @Flatten
    private List<CustomColumnItemDTO> customerColumns = new ArrayList<>();
    
    @Schema(description = "商品图片列表")
    @GoodsColumn(value = "image")
    @JsonIgnore
    private List<GoodsHasImagesEntity> images = new ArrayList<>();
    
    public BigDecimal getWeight() {
        return PriceUtil.formatThreeDecimal(weight);
    }

    public BigDecimal getNetGoldWeight() {
        return PriceUtil.formatThreeDecimal(netGoldWeight);
    }

    public BigDecimal getNetSilverWeight() {
        return PriceUtil.formatThreeDecimal(netSilverWeight);
    }

    public BigDecimal getMainStoneWeight() {
        return PriceUtil.formatThreeDecimal(mainStoneWeight);
    }

    public BigDecimal getSubStoneWeight() {
        return PriceUtil.formatThreeDecimal(subStoneWeight);
    }
}
