package com.xc.boot.modules.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.common.enums.baseColum.OtherColumEnum;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtilV2;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialEntity;
import com.xc.boot.modules.oldmaterial.service.OldMaterialService;
import com.xc.boot.modules.order.mapper.MaterialRecycleDetailMapper;
import com.xc.boot.modules.order.mapper.MaterialRecycleMapper;
import com.xc.boot.modules.order.model.entity.MaterialRecycleDetailEntity;
import com.xc.boot.modules.order.model.entity.MaterialRecycleEntity;
import com.xc.boot.modules.order.model.enums.PayTypeEnum;
import com.xc.boot.modules.order.model.form.MaterialRecycleForm;
import com.xc.boot.modules.order.model.form.MaterialRecycleItemForm;
import com.xc.boot.modules.order.model.form.MaterialRecycleUpdateForm;
import com.xc.boot.modules.order.model.query.MaterialRecycleQuery;
import com.xc.boot.modules.order.model.vo.MaterialRecycleDetailVO;
import com.xc.boot.modules.order.model.vo.MaterialRecyclePageVo;
import com.xc.boot.modules.order.service.MaterialRecycleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.xc.boot.modules.oldmaterial.model.entity.table.OldMaterialTableDef.OLD_MATERIAL;
import static com.xc.boot.modules.order.model.entity.table.MaterialRecycleDetailTableDef.MATERIAL_RECYCLE_DETAIL;
import static com.xc.boot.modules.order.model.entity.table.MaterialRecycleTableDef.MATERIAL_RECYCLE;


/**
 * 回收单服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialRecycleServiceImpl extends ServiceImpl<MaterialRecycleMapper, MaterialRecycleEntity> implements MaterialRecycleService {

    private final MaterialRecycleDetailMapper materialRecycleDetailMapper;
    private final OldMaterialService oldMaterialService;
    private final ListFillService listFillService;

    @Override
    public Page<MaterialRecyclePageVo> page(MaterialRecycleQuery query) {
        QueryWrapper wrapper = buildQuery(query);
        wrapper.orderBy(MATERIAL_RECYCLE.ID, false);

        if (query.getExport().equals(1)) {
            doExport(wrapper);
            return new Page<>();
        }
        if (query.getPrint().equals(1)) {
            long count = this.count(wrapper);
            query.setPrintNum(count);
        }
        Page<MaterialRecyclePageVo> page = this.mapper.paginateAs(query.getPageNum(), query.getPageSize(), wrapper, MaterialRecyclePageVo.class);
        // 填充列表
        fillList(page.getRecords());
        return page;
    }

    @Override
    public MaterialRecyclePageVo getRecycleDetail(Integer id) {
        MaterialRecycleEntity entity = this.getById(id);
        Assert.notNull(entity, "回收单不存在");
        MaterialRecyclePageVo vo = BeanUtil.copyProperties(entity, MaterialRecyclePageVo.class);
        fillList(List.of(vo));
        return vo;
    }

    @Override
    public List<MaterialRecycleDetailVO> getRecycleDetailList(Integer recycleId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        MATERIAL_RECYCLE_DETAIL.ALL_COLUMNS,
                        MATERIAL_RECYCLE_DETAIL.ID.as("id"),
                        OLD_MATERIAL.ALL_COLUMNS
                )
                .from(MATERIAL_RECYCLE_DETAIL)
                .leftJoin(OLD_MATERIAL).on(MATERIAL_RECYCLE_DETAIL.MATERIAL_ID.eq(OLD_MATERIAL.ID))
                .where(MATERIAL_RECYCLE_DETAIL.RECYCLE_ID.eq(recycleId));

        List<MaterialRecycleDetailVO> voList = materialRecycleDetailMapper.selectListByQueryAs(queryWrapper, MaterialRecycleDetailVO.class);
        fillDetailList(voList);
        return voList;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRecycle(MaterialRecycleForm form) {
        Long companyId = SecurityUtils.getCompanyId();
        // 生成回收单号
        String recycleCode = SnUtils.generateRecycleCode();
        // 计算总金额
        long totalPrice = 0L;
        Integer totalNum = 0;
        BigDecimal totalNetGoldWeight = BigDecimal.ZERO;
        BigDecimal totalNetSilverWeight = BigDecimal.ZERO;

        List<OldMaterialEntity> oldMaterialList = new ArrayList<>();
        for (MaterialRecycleItemForm item : form.getItems()) {
            if (item.getNetGoldWeight() != null) {
                totalNetGoldWeight = totalNetGoldWeight.add(item.getNetGoldWeight().multiply(BigDecimal.valueOf(item.getNum())));
            }
            if (item.getNetSilverWeight() != null) {
                totalNetSilverWeight = totalNetSilverWeight.add(item.getNetSilverWeight().multiply(BigDecimal.valueOf(item.getNum())));
            }
            totalNum += item.getNum();
            // 创建旧料记录
            String oldMaterialSn = SnUtils.generateOldMaterialSn();
            OldMaterialEntity oldMaterial = BeanUtil.copyProperties(item, OldMaterialEntity.class);
            oldMaterial.setCompanyId(companyId.intValue());
            oldMaterial.setMerchantId(form.getMerchantId());
            oldMaterial.setOldMaterialSn(oldMaterialSn);
            oldMaterial.setGoodsSn("");
            oldMaterial.setWeight(BigDecimal.ZERO);
            oldMaterial.setRecyclePrice(PriceUtil.yuan2fenLong(item.getRecyclePrice()));
            oldMaterial.setGoldPrice(PriceUtil.yuan2fenLong(item.getGoldPrice()));
            oldMaterial.setSilverPrice(PriceUtil.yuan2fenLong(item.getSilverPrice()));
            oldMaterial.setCreatedAt(form.getRecycleDate());
            // 计算回收价
            calculateRecyclePrice(oldMaterial);
            totalPrice += oldMaterial.getRecyclePrice() * oldMaterial.getNum();

            // 重量为0时处理: 金重 + 银重 + 主石重 * 主石数量 * 0.2 + 辅石重 * 辅石数 *  0.2
            if (Objects.isNull(oldMaterial.getWeight()) || oldMaterial.getWeight().compareTo(BigDecimal.ZERO) == 0) {
                BigDecimal weight = PriceUtil.formatThreeDecimal(oldMaterial.getNetGoldWeight())
                        .add(PriceUtil.formatThreeDecimal(oldMaterial.getNetSilverWeight()))
                        .add(PriceUtil.formatThreeDecimal(oldMaterial.getMainStoneWeight())
                                .multiply(new BigDecimal(oldMaterial.getMainStoneCount() != null ? oldMaterial.getMainStoneCount() : 0))
                                .multiply(new BigDecimal("0.2")))
                        .add(PriceUtil.formatThreeDecimal(oldMaterial.getSubStoneWeight())
                                .multiply(new BigDecimal(oldMaterial.getSubStoneCount() != null ? oldMaterial.getSubStoneCount() : 0))
                                .multiply(new BigDecimal("0.2")));
                oldMaterial.setWeight(PriceUtil.formatThreeDecimal(weight));
            }
            oldMaterialList.add(oldMaterial);
        }
        oldMaterialService.saveBatch(oldMaterialList);

        // 创建回收单
        MaterialRecycleEntity recycle = new MaterialRecycleEntity()
                .setRecycleCode(recycleCode)
                .setCompanyId(companyId)
                .setMerchantId(form.getMerchantId())
                .setMainAdviserId(form.getMainAdviserId())
                .setSubAdviserId(form.getSubAdviserId())
                .setMemberId(form.getMemberId())
                .setNum(totalNum)
                .setPayType(form.getPayType())
                .setTotalNetGoldWeight(totalNetGoldWeight)
                .setTotalNetSilverWeight(totalNetSilverWeight)
                .setTotalPrice(totalPrice)
                .setCashierId(form.getCashierId())
                .setRemark(form.getRemark());
        recycle.setCreatedAt(form.getRecycleDate());
        this.save(recycle);
        // 创建回收明细
        List<MaterialRecycleDetailEntity> detailList = new ArrayList<>();
        for (OldMaterialEntity oldMaterial : oldMaterialList) {
            // 创建回收明细
            MaterialRecycleDetailEntity detail = new MaterialRecycleDetailEntity()
                    .setRecycleCode(recycleCode)
                    .setCompanyId(companyId)
                    .setMerchantId(form.getMerchantId())
                    .setRecycleId(recycle.getId())
                    .setMaterialId(oldMaterial.getId());
            detail.setCreatedAt(form.getRecycleDate());
            detailList.add(detail);
        }
        materialRecycleDetailMapper.insertBatchSelective(detailList);
        OpLogUtils.appendOpLog("回收管理-创建回收单", String.format("创建回收单: %s", recycleCode), recycle);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRecycle(MaterialRecycleUpdateForm form) {
        MaterialRecycleEntity recycle = this.getById(form.getId());
        Assert.notNull(recycle, "回收单不存在");
        Assert.isTrue(recycle.getCompanyId().equals(SecurityUtils.getCompanyId()), "无权操作该回收单");
        Assert.isTrue(SecurityUtils.getMerchantIds().contains(recycle.getMerchantId().longValue()), "无权操作该回收单");

        // 更新字段
        if (form.getMainAdviserId() != null) {
            recycle.setMainAdviserId(form.getMainAdviserId());
        }
        if (form.getSubAdviserId() != null) {
            recycle.setSubAdviserId(form.getSubAdviserId());
        }
        if (form.getMemberId() != null) {
            recycle.setMemberId(form.getMemberId());
        }
        if (form.getCashierId() != null) {
            recycle.setCashierId(form.getCashierId());
        }
        this.updateById(recycle);
        OpLogUtils.appendOpLog("回收管理-修改回收单", String.format("更新回收单: %s", recycle.getRecycleCode()), recycle);
    }

    private void fillDetailList(List<MaterialRecycleDetailVO> voList) {
        ListFillUtilV2.of(voList)
                .build(listFillService::getCategoryNameById, MaterialRecycleDetailVO::getCategoryId, MaterialRecycleDetailVO::setCategoryName)
                .build(listFillService::getSubclassNameById, MaterialRecycleDetailVO::getSubclassId, MaterialRecycleDetailVO::setSubclassName)
                .build(listFillService::getQualityNameById, MaterialRecycleDetailVO::getQualityId, MaterialRecycleDetailVO::setQualityName)
                .build(listFillService::getOldMaterialImgByOldMaterialId, MaterialRecycleDetailVO::getMaterialId, MaterialRecycleDetailVO::setImage)
                .peek(vo -> {
                    // 分转元
                    vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
                    vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
                    vo.setRecyclePrice(PriceUtil.fen2yuan(vo.getRecyclePrice()));
                    // 设置计价方式文本
                    if (vo.getSalesType() != null) {
                        vo.setSalesTypeText(OtherColumEnum.getSalesTypeText(vo.getSalesType().toString()));
                    }
                    // 计算回收金额
                    if (vo.getRecyclePrice() != null && vo.getNum() != null) {
                        vo.setRecycleAmount(vo.getRecyclePrice().multiply(new BigDecimal(vo.getNum())));
                    }
                })
                .handle();
    }

    /**
     * 计算回收单价
     */
    private void calculateRecyclePrice(OldMaterialEntity oldMaterial) {
        if (oldMaterial.getSalesType() == 2) {
            return;
        }
        // 按重量计算
        CategoryEnum category = CategoryEnum.getByValue(oldMaterial.getCategoryId().longValue());
        if (category == null) {
            return;
        }
        long recyclePrice = 0;
        switch (category) {
            case GOLD_SILVER:
                // 金包银：回收单价 = 回收金价 * 净金重 + 银价 * 净银重
                if (oldMaterial.getGoldPrice() != null && oldMaterial.getNetGoldWeight() != null) {
                    recyclePrice += (new BigDecimal(oldMaterial.getGoldPrice()).multiply(oldMaterial.getNetGoldWeight())).setScale(0, RoundingMode.HALF_UP).longValue();
                }
                if (oldMaterial.getSilverPrice() != null && oldMaterial.getNetSilverWeight() != null) {
                    recyclePrice += (new BigDecimal(oldMaterial.getSilverPrice()).multiply(oldMaterial.getNetSilverWeight())).setScale(0, RoundingMode.HALF_UP).longValue();
                }
                break;
            case SILVER:
                // 银饰：回收单价 = 银价 * 净银重
                if (oldMaterial.getSilverPrice() != null && oldMaterial.getNetSilverWeight() != null) {
                    recyclePrice += (new BigDecimal(oldMaterial.getSilverPrice()).multiply(oldMaterial.getNetSilverWeight())).setScale(0, RoundingMode.HALF_UP).longValue();
                }
                break;
            default:
                // 非银饰且非金包银：回收单价 = 回收金价 * 净金重
                if (oldMaterial.getGoldPrice() != null && oldMaterial.getNetGoldWeight() != null) {
                    recyclePrice += (new BigDecimal(oldMaterial.getGoldPrice()).multiply(oldMaterial.getNetGoldWeight())).setScale(0, RoundingMode.HALF_UP).longValue();
                }
                break;
        }
        oldMaterial.setRecyclePrice(recyclePrice);
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper buildQuery(MaterialRecycleQuery query) {
        QueryWrapper wrapper = QueryWrapper.create()
                .where(MATERIAL_RECYCLE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(MATERIAL_RECYCLE.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));

        // 所属门店
        if (StringUtils.isNotBlank(query.getMerchantIds())) {
            List<String> merchantIdList = Arrays.asList(query.getMerchantIds().split(","));
            wrapper.where(MATERIAL_RECYCLE.MERCHANT_ID.in(merchantIdList));
        }
        
        // 回收单号
        if (StringUtils.isNotBlank(query.getRecycleCode())) {
            wrapper.where(MATERIAL_RECYCLE.RECYCLE_CODE.eq(query.getRecycleCode()));
        }
        
        // 主导购
        if (StringUtils.isNotBlank(query.getMainAdviserIds())) {
            List<String> mainAdviserIdList = Arrays.asList(query.getMainAdviserIds().split(","));
            wrapper.where(MATERIAL_RECYCLE.MAIN_ADVISER_ID.in(mainAdviserIdList));
        }
        
        // 辅购
        if (StringUtils.isNotBlank(query.getSubAdviserIds())) {
            List<String> subAdviserIdList = Arrays.asList(query.getSubAdviserIds().split(","));
            wrapper.where(MATERIAL_RECYCLE.SUB_ADVISER_ID.in(subAdviserIdList));
        }
        
        // 收银员
        if (StringUtils.isNotBlank(query.getCashierIds())) {
            List<String> cashierIdList = Arrays.asList(query.getCashierIds().split(","));
            wrapper.where(MATERIAL_RECYCLE.CASHIER_ID.in(cashierIdList));
        }
        
        // 会员
        if (StringUtils.isNotBlank(query.getMemberIds())) {
            List<String> memberIdList = Arrays.asList(query.getMemberIds().split(","));
            wrapper.where(MATERIAL_RECYCLE.MEMBER_ID.in(memberIdList));
        }
        
        // 创建人
        if (StringUtils.isNotBlank(query.getCreatedByIds())) {
            List<String> createdByIdList = Arrays.asList(query.getCreatedByIds().split(","));
            wrapper.where(MATERIAL_RECYCLE.CREATED_BY.in(createdByIdList));
        }
        
        // 创建时间
        if (query.getTimeRange() != null && query.getTimeRange().length == 2) {
            wrapper.where(MATERIAL_RECYCLE.CREATED_AT.between(query.getTimeRange()));
        }
        
        // 最小回收金额
        if (query.getMinTotalPrice() != null) {
            wrapper.where(MATERIAL_RECYCLE.TOTAL_PRICE.ge(PriceUtil.yuan2fenLong(query.getMinTotalPrice())));
        }
        
        // 最大回收金额
        if (query.getMaxTotalPrice() != null) {
            wrapper.where(MATERIAL_RECYCLE.TOTAL_PRICE.le(PriceUtil.yuan2fenLong(query.getMaxTotalPrice())));
        }
        
        // 支付方式
        if (query.getPayType() != null) {
            wrapper.where(MATERIAL_RECYCLE.PAY_TYPE.eq(query.getPayType()));
        }
        if (CollectionUtil.isNotEmpty(query.getIds())) {
            wrapper.where(MATERIAL_RECYCLE.ID.in(query.getIds()));
        }
        
        return wrapper;
    }

    /**
     * 填充列表数据
     */
    private void fillList(List<MaterialRecyclePageVo> list) {
        ListFillUtilV2.of(list)
                .build(listFillService::getMerchantNameById, MaterialRecyclePageVo::getMerchantId, MaterialRecyclePageVo::setMerchantName)
                .build(listFillService::getUserNameByUserId, MaterialRecyclePageVo::getMainAdviserId, MaterialRecyclePageVo::setMainAdviserName)
                .build(listFillService::getUserNameByUserId, MaterialRecyclePageVo::getSubAdviserId, MaterialRecyclePageVo::setSubAdviserName)
                .build(listFillService::getUserNameByUserId, MaterialRecyclePageVo::getCashierId, MaterialRecyclePageVo::setCashierName)
                .build(listFillService::getMemberNameById, MaterialRecyclePageVo::getMemberId, MaterialRecyclePageVo::setMemberName)
                .build(listFillService::getUserNameByUserId, MaterialRecyclePageVo::getCreatedBy, MaterialRecyclePageVo::setCreatedByName)
                .peek(vo -> {
                    // 设置支付方式文本
                    if (vo.getPayType() != null) {
                        vo.setPayTypeText(getPayTypeText(vo.getPayType()));
                    }
                    vo.setTotalPrice(PriceUtil.fen2yuan(vo.getTotalPrice()));
                })
                .handle();
    }

    /**
     * 导出
     */
    private void doExport(QueryWrapper query) {
        ExcelUtil.of(this.mapper, query, MaterialRecyclePageVo.class, "material_recycle", "回收列表")
                .getData((mapper, wrapper) -> {
                    List<MaterialRecyclePageVo> voList = mapper.selectListByQueryAs(wrapper, MaterialRecyclePageVo.class);
                    fillList(voList);
                    return voList;
                })
                .doExport();
    }

    /**
     * 获取支付方式文本
     */
    private String getPayTypeText(Integer payType) {
        if (payType == null) {
            return "未知";
        }

        String typeName = IBaseEnum.getLabelByValue(payType, PayTypeEnum.class);
        return typeName != null ? typeName : "未知";
    }


} 