package com.xc.boot.modules.order.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 销售单列表分页VO
 */
@Data
@Schema(description = "销售单列表分页VO")
public class SoldReceiptListPageVO {
    
    @Schema(description = "销售单ID")
    private Long id;
    
    @Schema(description = "订单号")
    private String receiptSn;
    
    @Schema(description = "会员ID")
    private Long memberId;
    
    @Schema(description = "会员信息")
    private String memberInfo;

    @Schema(description = "会员姓名")
    private String memberName;

    @Schema(description = "会员手机号")
    private String memberPhone;
    
    @Schema(description = "所属门店ID")
    private Long merchantId;
    
    @Schema(description = "所属门店")
    private String merchantName;
    
    @Schema(description = "主销导购ID")
    private Long mainSellerId;
    
    @Schema(description = "主销导购")
    private String mainSellerName;
    
    @Schema(description = "辅销导购ID")
    private Long supportSellerId;
    
    @Schema(description = "辅销导购")
    private String supportSellerName;
    
    @Schema(description = "收银员ID")
    private Long cashierId;
    
    @Schema(description = "收银员")
    private String cashierName;
    
    @Schema(description = "数量")
    private Integer num;
    
    @Schema(description = "重量(g)")
    private BigDecimal weight;
    
    @Schema(description = "净金重(g)")
    private BigDecimal goldWeight;
    
    @Schema(description = "净银重(g)")
    private BigDecimal silverWeight;
    
    @Schema(description = "订单金额(元)")
    private BigDecimal amount;
    
    @Schema(description = "退款金额(元)")
    private BigDecimal refundAmount;
    
    @Schema(description = "支付方式")
    private String paymentMethods;
    
    @Schema(description = "销售时间")
    private Date soldAt;
    
    @Schema(description = "状态(-1-已作废 0-挂单中 1-已完成 2-部分退货 3-全部退货)")
    private Integer status;
    
    @Schema(description = "状态名称")
    private String statusName;

    @Schema(description = "备注")
    private String remark;
}
