package com.xc.boot.modules.order.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.order.mapper.SoldReceiptMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptGoodsMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptGiftMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptOldMaterialMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptPaymentMapper;
import com.xc.boot.modules.order.model.dto.SoldReceiptCreateDTO;
import com.xc.boot.modules.order.model.dto.SoldReceiptRefundDTO;
import com.xc.boot.modules.order.model.dto.UpdateSoldReceiptMemberDTO;
import com.xc.boot.modules.order.model.dto.UpdateSoldReceiptSellerDTO;
import com.xc.boot.modules.order.model.entity.SoldReceiptEntity;
import com.xc.boot.modules.order.model.entity.SoldReceiptGoodsEntity;
import com.xc.boot.modules.order.model.entity.SoldReceiptGiftEntity;
import com.xc.boot.modules.order.model.entity.SoldReceiptOldMaterialEntity;
import com.xc.boot.modules.order.model.enums.PayTypeEnum;
import com.xc.boot.modules.order.model.enums.SoldReceiptStatusEnum;
import com.xc.boot.modules.order.model.query.SoldReceiptListPageQuery;
import com.xc.boot.modules.order.model.query.SoldReceiptPendingPageQuery;
import com.xc.boot.modules.order.model.vo.SoldReceiptListPageVO;
import com.xc.boot.modules.order.model.vo.SoldReceiptPendingPageVO;
import com.xc.boot.modules.order.service.SoldReceiptCreateService;
import com.xc.boot.modules.order.service.SoldReceiptService;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.StockUtils;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.modules.goods.model.bo.StockNumChangeBO;
import com.xc.boot.modules.gift.model.bo.GiftStockNumChangeBO;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialMapper;
import com.xc.boot.modules.member.service.MemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.xc.boot.modules.order.model.entity.table.SoldReceiptTableDef.SOLD_RECEIPT;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptPaymentTableDef.SOLD_RECEIPT_PAYMENT;
import static com.xc.boot.modules.member.model.entity.table.MemberTableDef.MEMBER;
import org.springframework.stereotype.Service;

/**
 * 销售单服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SoldReceiptServiceImpl extends ServiceImpl<SoldReceiptMapper, SoldReceiptEntity> implements SoldReceiptService {

    private final SoldReceiptCreateService soldReceiptCreateService;
    private final ListFillService listFillService;
    private final SoldReceiptGoodsMapper soldReceiptGoodsMapper;
    private final SoldReceiptGiftMapper soldReceiptGiftMapper;
    private final SoldReceiptOldMaterialMapper soldReceiptOldMaterialMapper;
    private final SoldReceiptPaymentMapper soldReceiptPaymentMapper;
    private final OldMaterialMapper oldMaterialMapper;
    private final MemberService memberService;

    @Override
    public Long createSoldReceipt(SoldReceiptCreateDTO dto) {
        return soldReceiptCreateService.createSoldReceipt(dto);
    }

    @Override
    public Page<SoldReceiptPendingPageVO> pendingPage(SoldReceiptPendingPageQuery query) {
        QueryWrapper wrapper = buildPendingWrapper(query);
        wrapper.orderBy(SOLD_RECEIPT.PENDING_AT, false);

        Page<SoldReceiptPendingPageVO> page = this.mapper.paginateAs(
            query.getPageNum(),
            query.getPageSize(),
            wrapper,
            SoldReceiptPendingPageVO.class
        );

        List<SoldReceiptPendingPageVO> records = page.getRecords();
        if (records.isEmpty()) {
            return page;
        }

        // 填充关联数据
        fillPendingList(records);

        return page;
    }

    /**
     * 构建挂单销售单查询条件
     */
    private QueryWrapper buildPendingWrapper(SoldReceiptPendingPageQuery query) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(SOLD_RECEIPT.STATUS.eq(SoldReceiptStatusEnum.PENDING.getValue())); // 只查询挂单中的销售单

        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        merchantIds.add(0L);
        // 非主账号限制门店
        queryWrapper.where(SOLD_RECEIPT.MERCHANT_ID.in(merchantIds, !SecurityUtils.isMain()));

        // 门店筛选
        if (StringUtils.isNotBlank(query.getMerchantIds())) {
            queryWrapper.where(SOLD_RECEIPT.MERCHANT_ID.in(List.of(query.getMerchantIds().split(","))));
        }

        // 订单号筛选
        if (StringUtils.isNotBlank(query.getReceiptSn())) {
            queryWrapper.where(SOLD_RECEIPT.RECEIPT_SN.like(query.getReceiptSn()));
        }

        // 会员筛选
        if (StringUtils.isNotBlank(query.getMemberIds())) {
            queryWrapper.where(SOLD_RECEIPT.MEMBER_ID.in(List.of(query.getMemberIds().split(","))));
        }

        // 主销导购筛选
        if (StringUtils.isNotBlank(query.getMainSellerIds())) {
            queryWrapper.where(SOLD_RECEIPT.MAIN_SELLER_ID.in(List.of(query.getMainSellerIds().split(","))));
        }

        // 辅销导购筛选
        if (StringUtils.isNotBlank(query.getSupportSellerIds())) {
            queryWrapper.where(SOLD_RECEIPT.SUPPORT_SELLER_ID.in(List.of(query.getSupportSellerIds().split(","))));
        }

        // 挂单时间范围筛选
        if (query.getPendingTimeRange() != null && query.getPendingTimeRange().length == 2) {
            if (query.getPendingTimeRange()[0] != null) {
                queryWrapper.where(SOLD_RECEIPT.PENDING_AT.ge(query.getPendingTimeRange()[0]));
            }
            if (query.getPendingTimeRange()[1] != null) {
                queryWrapper.where(SOLD_RECEIPT.PENDING_AT.le(query.getPendingTimeRange()[1]));
            }
        }

        // IDs筛选（用于打印导出指定记录）
        if (query.getIds() != null && !query.getIds().isEmpty()) {
            queryWrapper.where(SOLD_RECEIPT.ID.in(query.getIds()));
        }

        return queryWrapper;
    }

    /**
     * 填充挂单销售单列表数据
     */
    private void fillPendingList(List<SoldReceiptPendingPageVO> list) {
        Set<Long> merchantIds = list.stream().map(SoldReceiptPendingPageVO::getMerchantId).collect(Collectors.toSet());
        Set<Long> memberIds = list.stream().map(SoldReceiptPendingPageVO::getMemberId).filter(id -> id != null && id > 0).collect(Collectors.toSet());
        Set<Long> mainSellerIds = list.stream().map(SoldReceiptPendingPageVO::getMainSellerId).filter(id -> id != null && id > 0).collect(Collectors.toSet());
        Set<Long> supportSellerIds = list.stream().map(SoldReceiptPendingPageVO::getSupportSellerId).filter(id -> id != null && id > 0).collect(Collectors.toSet());

        ListFillUtil.of(list)
                .build(listFillService::getMerchantNameById, merchantIds, "merchantId", "merchantName")
                .build(listFillService::getMemberNameById, memberIds, "memberId", "memberName")
                .build(listFillService::getUserNameByUserId, mainSellerIds, "mainSellerId", "mainSellerName")
                .build(listFillService::getUserNameByUserId, supportSellerIds, "supportSellerId", "supportSellerName")
                .peek(obj -> {
                    SoldReceiptPendingPageVO vo = (SoldReceiptPendingPageVO) obj;
                    // 价格字段分转元
                    vo.setAmount(PriceUtil.fen2yuan(vo.getAmount()));
                    vo.setReceivedAmount(PriceUtil.fen2yuan(vo.getReceivedAmount()));
                })
                .handle();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundDeposit(SoldReceiptRefundDTO dto) {
        // 1. 查询销售单信息
        SoldReceiptEntity soldReceipt = this.getById(dto.getSoldReceiptId());
        if (soldReceipt == null) {
            CommonUtils.abort("销售单不存在");
        }

        // 2. 验证销售单状态
        if (!SoldReceiptStatusEnum.PENDING.getValue().equals(soldReceipt.getStatus())) {
            CommonUtils.abort("只有挂单中的销售单才可以操作");
        }

        // 3. 验证权限
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        if (!SecurityUtils.isMain() && !merchantIds.contains(soldReceipt.getMerchantId())) {
            CommonUtils.abort("无权限操作此销售单");
        }

        // 4. 判断操作类型
        Long receivedAmount = soldReceipt.getReceivedAmount() != null ? soldReceipt.getReceivedAmount() : 0L;
        boolean isCancelOrder = receivedAmount.equals(0L);
        String operationType = isCancelOrder ? "取消挂单" : "退定金";

        // 5. 验证退款方式
        if (!isCancelOrder && dto.getRefundType() == null) {
            CommonUtils.abort("退定金时必须选择退款方式");
        }

        // 6. 恢复库存和删除旧料记录
        restoreStocksAndDeleteOldMaterials(dto.getSoldReceiptId(), receivedAmount);

        // 7. 更新销售单信息
        SoldReceiptEntity updateEntity = new SoldReceiptEntity();
        updateEntity.setId(dto.getSoldReceiptId());
        updateEntity.setStatus(SoldReceiptStatusEnum.CANCELLED.getValue()); // 已作废
        updateEntity.setRefundRemark(dto.getRefundRemark());
        updateEntity.setRefundBy(SecurityUtils.getUserId());

        if (!isCancelOrder) {
            updateEntity.setRefundType(dto.getRefundType());
            updateEntity.setRefundAmount(receivedAmount);
        }

        this.updateById(updateEntity);

        // 8. 记录操作日志
        String logContent = String.format("%s - 销售单号: %s, 退款备注: %s",
            operationType, soldReceipt.getReceiptSn(), dto.getRefundRemark());
        if (!isCancelOrder) {
            String refundTypeName = getRefundTypeName(dto.getRefundType());
            logContent += String.format(", 退款方式: %s, 退款金额: %.2f元",
                refundTypeName, receivedAmount / 100.0);
        }

        OpLogUtils.appendOpLog("销售管理-" + operationType, operationType, logContent);

        log.info("{}成功 - 销售单ID: {}, 操作人: {}", operationType, dto.getSoldReceiptId(), SecurityUtils.getUserId());
    }

    /**
     * 获取退款方式名称
     */
    private String getRefundTypeName(Integer refundType) {
        if (refundType == null) {
            return "未知";
        }

        for (PayTypeEnum payType : PayTypeEnum.values()) {
            if (payType.getValue().equals(refundType)) {
                return payType.getLabel();
            }
        }
        return "未知";
    }

    /**
     * 恢复库存和删除旧料记录
     */
    private void restoreStocksAndDeleteOldMaterials(Long soldReceiptId, Long receivedAmount) {
        // 判断操作类型
        boolean isCancelOrder = receivedAmount.equals(0L);
        String operationType = isCancelOrder ? "取消挂单" : "退定金";

        // 1. 恢复货品库存
        restoreGoodsStock(soldReceiptId, operationType);

        // 2. 恢复赠品库存
        restoreGiftStock(soldReceiptId, operationType);

        // 3. 删除旧料记录
        deleteOldMaterialRecords(soldReceiptId);
    }

    /**
     * 恢复货品库存
     */
    private void restoreGoodsStock(Long soldReceiptId, String operationType) {
        // 查询销售单货品明细
        List<SoldReceiptGoodsEntity> goodsDetails = soldReceiptGoodsMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptGoodsEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        if (goodsDetails.isEmpty()) {
            return;
        }

        // 构建库存变更对象
        List<StockNumChangeBO> stockChanges = new ArrayList<>();
        for (SoldReceiptGoodsEntity goodsDetail : goodsDetails) {
            // 只操作挂单中的销售单：增加库存，减少冻结数量
            StockNumChangeBO stockChange = StockNumChangeBO.builder()
                    .goodsId(goodsDetail.getGoodsId())
                    .stockNum(goodsDetail.getNum()) // 增加库存
                    .frozenNum(-goodsDetail.getNum()) // 减少冻结数量
                    .comment(operationType + "-恢复货品库存")
                    .build();

            stockChanges.add(stockChange);
        }

        // 批量更新库存
        StockUtils.updateStocks(stockChanges);
    }

    /**
     * 恢复赠品库存
     */
    private void restoreGiftStock(Long soldReceiptId, String operationType) {
        // 查询销售单赠品明细
        List<SoldReceiptGiftEntity> giftDetails = soldReceiptGiftMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptGiftEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        if (giftDetails.isEmpty()) {
            return;
        }

        // 构建库存变更对象
        List<GiftStockNumChangeBO> stockChanges = new ArrayList<>();
        for (SoldReceiptGiftEntity giftDetail : giftDetails) {
            // 只操作挂单中的销售单：增加库存，减少冻结数量
            GiftStockNumChangeBO stockChange = GiftStockNumChangeBO.builder()
                    .giftId(giftDetail.getGiftId())
                    .stockNum(giftDetail.getNum()) // 增加库存
                    .frozenNum(-giftDetail.getNum()) // 减少冻结数量
                    .comment(operationType + "-恢复赠品库存")
                    .build();

            stockChanges.add(stockChange);
        }

        // 批量更新库存
        StockUtils.updateGiftStocks(stockChanges);
    }

    /**
     * 删除旧料记录
     */
    private void deleteOldMaterialRecords(Long soldReceiptId) {
        // 查询销售单旧料明细
        List<SoldReceiptOldMaterialEntity> oldMaterialDetails = soldReceiptOldMaterialMapper.selectListByQuery(
                QueryWrapper.create().where(SoldReceiptOldMaterialEntity::getSoldReceiptId).eq(soldReceiptId)
        );

        if (oldMaterialDetails.isEmpty()) {
            return;
        }

        // 获取旧料ID列表
        List<Long> oldMaterialIds = oldMaterialDetails.stream()
                .map(SoldReceiptOldMaterialEntity::getOldMaterialId)
                .toList();

        // 删除旧料记录
        if (!oldMaterialIds.isEmpty()) {
            oldMaterialMapper.deleteBatchByIds(oldMaterialIds);
            log.info("退定金/取消挂单删除旧料记录成功，删除数量: {}", oldMaterialIds.size());
        }
    }

    /**
     * 构建销售单列表查询条件
     */
    private QueryWrapper buildListPageWrapper(SoldReceiptListPageQuery query) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        SOLD_RECEIPT.ID,
                        SOLD_RECEIPT.RECEIPT_SN,
                        SOLD_RECEIPT.MERCHANT_ID,
                        SOLD_RECEIPT.MEMBER_ID,
                        SOLD_RECEIPT.MAIN_SELLER_ID,
                        SOLD_RECEIPT.SUPPORT_SELLER_ID,
                        SOLD_RECEIPT.CASHIER_ID,
                        SOLD_RECEIPT.NUM,
                        SOLD_RECEIPT.WEIGHT,
                        SOLD_RECEIPT.GOLD_WEIGHT,
                        SOLD_RECEIPT.SILVER_WEIGHT,
                        SOLD_RECEIPT.AMOUNT,
                        SOLD_RECEIPT.REFUND_AMOUNT,
                        SOLD_RECEIPT.SOLD_AT,
                        SOLD_RECEIPT.STATUS,
                        SOLD_RECEIPT.REMARK,
                        MEMBER.MOBILE.as("memberPhone"),
                        MEMBER.NAME.as("memberName")
                )
                .from(SOLD_RECEIPT)
                .leftJoin(MEMBER).on(SOLD_RECEIPT.MEMBER_ID.eq(MEMBER.ID))
                .orderBy(SOLD_RECEIPT.ID.desc())
                .where(SOLD_RECEIPT.STATUS.notIn(List.of(
                        SoldReceiptStatusEnum.PENDING.getValue(),  // 排除挂单中
                        SoldReceiptStatusEnum.CANCELLED.getValue() // 排除已作废
                )))
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()));

        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        merchantIds.add(0L);
        // 非主账号限制门店
        queryWrapper.where(SOLD_RECEIPT.MERCHANT_ID.in(merchantIds, !SecurityUtils.isMain()));

        // 订单号筛选
        if (StringUtils.isNotBlank(query.getReceiptSn())) {
            queryWrapper.where(SOLD_RECEIPT.RECEIPT_SN.like(query.getReceiptSn()));
        }

        // 会员手机号筛选
        if (StringUtils.isNotBlank(query.getMemberPhone())) {
            queryWrapper.where(MEMBER.MOBILE.like(query.getMemberPhone()));
        }

        // 门店筛选
        if (StringUtils.isNotBlank(query.getMerchantIds())) {
            queryWrapper.where(SOLD_RECEIPT.MERCHANT_ID.in(List.of(query.getMerchantIds().split(","))));
        }

        // 主销导购筛选
        if (StringUtils.isNotBlank(query.getMainSellerIds())) {
            queryWrapper.where(SOLD_RECEIPT.MAIN_SELLER_ID.in(List.of(query.getMainSellerIds().split(","))));
        }

        // 收银员筛选
        if (StringUtils.isNotBlank(query.getCashierIds())) {
            queryWrapper.where(SOLD_RECEIPT.CASHIER_ID.in(List.of(query.getCashierIds().split(","))));
        }

        // 备注筛选
        if (StringUtils.isNotBlank(query.getRemark())) {
            queryWrapper.where(SOLD_RECEIPT.REMARK.like(query.getRemark()));
        }

        // 辅助导购筛选
        if (StringUtils.isNotBlank(query.getSupportSellerIds())) {
            queryWrapper.where(SOLD_RECEIPT.SUPPORT_SELLER_ID.in(List.of(query.getSupportSellerIds().split(","))));
        }

        // 订单状态筛选
        if (StringUtils.isNotBlank(query.getStatuses())) {
            queryWrapper.where(SOLD_RECEIPT.STATUS.in(List.of(query.getStatuses().split(","))));
        }

        // 销售时间范围筛选
        if (query.getSoldTimeRange() != null && query.getSoldTimeRange().size() == 2) {
            queryWrapper.where(SOLD_RECEIPT.SOLD_AT.between(query.getSoldTimeRange().get(0), query.getSoldTimeRange().get(1)));
        }

        // IDs筛选（用于打印导出指定记录）
        if (query.getIds() != null && !query.getIds().isEmpty()) {
            queryWrapper.where(SOLD_RECEIPT.ID.in(query.getIds()));
        }

        return queryWrapper;
    }

    @Override
    public Page<SoldReceiptListPageVO> listPage(SoldReceiptListPageQuery query) {
        QueryWrapper wrapper = buildListPageWrapper(query);
        wrapper.orderBy(SOLD_RECEIPT.SOLD_AT, false);

        // 导出处理
        if (query.getExport().equals(1)) {
            exportSoldReceipts(wrapper, query);
            return new Page<>();
        }

        int pageNum = query.getPageNum();
        int pageSize = query.getPageSize();

        // 打印处理
        if (query.getPrint().equals(1)) {
            return printSoldReceipts(wrapper, query);
        }

        Page<SoldReceiptListPageVO> page = this.mapper.paginateAs(
            pageNum,
            pageSize,
            wrapper,
            SoldReceiptListPageVO.class
        );

        List<SoldReceiptListPageVO> records = page.getRecords();
        if (records.isEmpty()) {
            return page;
        }

        // 填充关联数据
        fillListPageData(records);

        return page;
    }

    /**
     * 填充销售单列表数据
     */
    private void fillListPageData(List<SoldReceiptListPageVO> list) {
        if (list.isEmpty()) {
            return;
        }

        Set<Long> merchantIds = list.stream().map(SoldReceiptListPageVO::getMerchantId).filter(id -> id != null && id > 0).collect(Collectors.toSet());
        Set<Long> cashierIds = list.stream().map(SoldReceiptListPageVO::getCashierId).filter(id -> id != null && id > 0).collect(Collectors.toSet());
        Set<Long> mainSellerIds = list.stream().map(SoldReceiptListPageVO::getMainSellerId).filter(id -> id != null && id > 0).collect(Collectors.toSet());
        Set<Long> supportSellerIds = list.stream().map(SoldReceiptListPageVO::getSupportSellerId).filter(id -> id != null && id > 0).collect(Collectors.toSet());
        Set<Long> soldReceiptIds = list.stream().map(SoldReceiptListPageVO::getId).collect(Collectors.toSet());

        // 获取支付方式信息
        Map<Long, String> paymentMethodsMap = getPaymentMethodsMap(soldReceiptIds);

        ListFillUtil.of(list)
                .build(listFillService::getMerchantNameById, merchantIds, "merchantId", "merchantName")
                .build(listFillService::getUserNameByUserId, cashierIds, "cashierId", "cashierName")
                .build(listFillService::getUserNameByUserId, mainSellerIds, "mainSellerId", "mainSellerName")
                .build(listFillService::getUserNameByUserId, supportSellerIds, "supportSellerId", "supportSellerName")
                .peek(obj -> {
                    SoldReceiptListPageVO vo = (SoldReceiptListPageVO) obj;

                    // 价格字段分转元
                    vo.setAmount(PriceUtil.fen2yuan(vo.getAmount()));
                    vo.setRefundAmount(PriceUtil.fen2yuan(vo.getRefundAmount()));

                    // 设置状态名称
                    vo.setStatusName(getStatusName(vo.getStatus()));

                    // 设置会员信息 - 格式：姓名 (手机号)
                    if (vo.getMemberId() != null) {
                        String memberName = vo.getMemberName();
                        String memberPhone = vo.getMemberPhone();

                        if (memberName != null && memberPhone != null) {
                            vo.setMemberInfo(memberName + " (" + memberPhone + ")");
                        } else if (memberPhone != null) {
                            vo.setMemberInfo("(" + memberPhone + ")");
                        } else if (memberName != null) {
                            vo.setMemberInfo(memberName);
                        }
                    }

                    // 设置支付方式
                    vo.setPaymentMethods(paymentMethodsMap.getOrDefault(vo.getId(), ""));
                })
                .handle();
    }

    /**
     * 获取支付方式映射
     */
    @SuppressWarnings("rawtypes")
    private Map<Long, String> getPaymentMethodsMap(Set<Long> soldReceiptIds) {
        if (soldReceiptIds.isEmpty()) {
            return new HashMap<>();
        }

        List<Map> paymentData = soldReceiptPaymentMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(SOLD_RECEIPT_PAYMENT.SOLD_RECEIPT_ID.as("soldReceiptId"), SOLD_RECEIPT_PAYMENT.TYPE.as("type"))
                        .where(SOLD_RECEIPT_PAYMENT.SOLD_RECEIPT_ID.in(soldReceiptIds)),
                Map.class
        );

        Map<Long, Set<String>> paymentTypesMap = new HashMap<>();
        for (Map payment : paymentData) {
            Object soldReceiptIdObj = payment.get("soldReceiptId");
            Integer type = (Integer) payment.get("type");
            String typeName = getPaymentTypeName(type);

            if (soldReceiptIdObj != null) {
                Long soldReceiptId = soldReceiptIdObj instanceof Integer ?
                    ((Integer) soldReceiptIdObj).longValue() : (Long) soldReceiptIdObj;
                paymentTypesMap.computeIfAbsent(soldReceiptId, k -> new HashSet<>()).add(typeName);
            }
        }

        Map<Long, String> result = new HashMap<>();
        for (Map.Entry<Long, Set<String>> entry : paymentTypesMap.entrySet()) {
            result.put(entry.getKey(), String.join(",", entry.getValue()));
        }

        return result;
    }

    /**
     * 获取支付方式名称
     */
    private String getPaymentTypeName(Integer type) {
        if (type == null) {
            return "未知";
        }

        String typeName = IBaseEnum.getLabelByValue(type, PayTypeEnum.class);
        return typeName != null ? typeName : "未知";
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return "未知";
        }

        for (SoldReceiptStatusEnum statusEnum : SoldReceiptStatusEnum.values()) {
            if (statusEnum.getValue().equals(status)) {
                return statusEnum.getLabel();
            }
        }
        return "未知";
    }

    /**
     * 导出销售单列表
     */
    private void exportSoldReceipts(QueryWrapper wrapper, SoldReceiptListPageQuery query) {
        // 检查导出数量限制
        long count = this.mapper.selectCountByQuery(wrapper);
        CommonUtils.abortIf(count > CommonUtils.getMaxExportSize(), "导出数量超过限制");

        ExcelUtil.of(this.mapper, wrapper, SoldReceiptListPageVO.class, "sold_receipt_list", "销售单列表")
                .getData((mapper, queryWrapper) -> {
                    List<SoldReceiptListPageVO> voList = mapper.selectListByQueryAs(queryWrapper, SoldReceiptListPageVO.class);
                    fillListPageData(voList);

                    // 处理导出字段转换 - 状态字段替换为文本
                    for (SoldReceiptListPageVO vo : voList) {
                        vo.setStatusName(getStatusName(vo.getStatus()));
                    }

                    return voList;
                })
                .modifyConfig(config -> {
                    config.setTitle("销售单列表");
                    config.setWidth(15);
                })
                .doExport();
    }

    /**
     * 打印销售单列表
     */
    private Page<SoldReceiptListPageVO> printSoldReceipts(QueryWrapper wrapper, SoldReceiptListPageQuery query) {
        // 检查打印数量限制
        long count = this.mapper.selectCountByQuery(wrapper);
        CommonUtils.abortIf(count > CommonUtils.getMaxPrintSize(), "打印数量超过限制");

        List<SoldReceiptListPageVO> voList = this.mapper.selectListByQueryAs(wrapper, SoldReceiptListPageVO.class);
        fillListPageData(voList);

        // 返回Page对象，pageNum=1, pageSize=voList.size(), total=count
        Page<SoldReceiptListPageVO> page = new Page<>(1, voList.size(), count);
        page.setRecords(voList);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMember(UpdateSoldReceiptMemberDTO dto) {
        // 1. 查询销售单信息
        SoldReceiptEntity soldReceipt = this.getById(dto.getSoldReceiptId());
        if (soldReceipt == null) {
            CommonUtils.abort("销售单不存在");
        }

        // 2. 验证权限
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        if (!SecurityUtils.isMain() && !merchantIds.contains(soldReceipt.getMerchantId())) {
            CommonUtils.abort("无权限操作此销售单");
        }

        // 3. 获取原会员和新会员信息用于日志记录
        String oldMemberName = soldReceipt.getMemberId() != null ?
            memberService.getMemberName(soldReceipt.getMemberId()) : "无";
        String newMemberName = memberService.getMemberName(dto.getMemberId());

        // 4. 更新销售单会员信息
        SoldReceiptEntity updateEntity = new SoldReceiptEntity();
        updateEntity.setId(dto.getSoldReceiptId());
        updateEntity.setMemberId(dto.getMemberId());
        this.updateById(updateEntity);

        // 5. 记录操作日志
        String logContent = String.format("销售单: %s, 修改会员: %s 修改为 %s",
            soldReceipt.getReceiptSn(), oldMemberName, newMemberName);
        OpLogUtils.appendOpLog("销售管理-修改会员", "修改会员", logContent);

        log.info("修改销售单会员成功 - 销售单ID: {}, 原会员: {}, 新会员: {}, 操作人: {}",
            dto.getSoldReceiptId(), oldMemberName, newMemberName, SecurityUtils.getUserId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSeller(UpdateSoldReceiptSellerDTO dto) {
        // 1. 查询销售单信息
        SoldReceiptEntity soldReceipt = this.getById(dto.getSoldReceiptId());
        if (soldReceipt == null) {
            CommonUtils.abort("销售单不存在");
        }

        // 2. 验证权限
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        if (!SecurityUtils.isMain() && !merchantIds.contains(soldReceipt.getMerchantId())) {
            CommonUtils.abort("无权限操作此销售单");
        }

        // 3. 获取原导购和新导购信息用于日志记录
        // 收集所有需要查询的用户ID，一次性查询避免多次数据库访问
        Set<Long> userIds = new HashSet<>();
        if (soldReceipt.getMainSellerId() != null) {
            userIds.add(soldReceipt.getMainSellerId());
        }
        if (soldReceipt.getSupportSellerId() != null) {
            userIds.add(soldReceipt.getSupportSellerId());
        }
        if (soldReceipt.getCashierId() != null) {
            userIds.add(soldReceipt.getCashierId());
        }
        userIds.add(dto.getMainSellerId());
        if (dto.getSupportSellerId() != null) {
            userIds.add(dto.getSupportSellerId());
        }
        userIds.add(dto.getCashierId());

        // 一次性获取所有用户名称
        Map<String, String> userNameMap = listFillService.getUserNameByUserId(userIds);

        String oldMainSellerName = soldReceipt.getMainSellerId() != null ?
            userNameMap.getOrDefault(soldReceipt.getMainSellerId().toString(), "无") : "无";
        String oldSupportSellerName = soldReceipt.getSupportSellerId() != null ?
            userNameMap.getOrDefault(soldReceipt.getSupportSellerId().toString(), "无") : "无";
        String oldCashierName = soldReceipt.getCashierId() != null ?
            userNameMap.getOrDefault(soldReceipt.getCashierId().toString(), "无") : "无";

        String newMainSellerName = userNameMap.getOrDefault(dto.getMainSellerId().toString(), "无");
        String newSupportSellerName = dto.getSupportSellerId() != null ?
            userNameMap.getOrDefault(dto.getSupportSellerId().toString(), "无") : "无";
        String newCashierName = userNameMap.getOrDefault(dto.getCashierId().toString(), "无");

        // 4. 更新销售单导购信息
        SoldReceiptEntity updateEntity = new SoldReceiptEntity();
        updateEntity.setId(dto.getSoldReceiptId());
        updateEntity.setMainSellerId(dto.getMainSellerId());
        updateEntity.setSupportSellerId(dto.getSupportSellerId());
        updateEntity.setCashierId(dto.getCashierId());
        this.updateById(updateEntity);

        // 5. 记录操作日志
        String logContent = String.format("""
            销售单: %s
            修改主销导购: %s 修改为 %s
            修改辅销导购: %s 修改为 %s
            修改收银员: %s 修改为 %s""",
            soldReceipt.getReceiptSn(),
            oldMainSellerName, newMainSellerName,
            oldSupportSellerName, newSupportSellerName,
            oldCashierName, newCashierName);
        OpLogUtils.appendOpLog("销售管理-修改导购", "修改导购", logContent);

        log.info("修改销售单导购成功 - 销售单ID: {}, 操作人: {}",
            dto.getSoldReceiptId(), SecurityUtils.getUserId());
    }
}