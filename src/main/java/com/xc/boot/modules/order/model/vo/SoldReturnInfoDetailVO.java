package com.xc.boot.modules.order.model.vo;

import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024-07-29
 */
@Data
public class SoldReturnInfoDetailVO {

    @Schema(description = "销售详情id")
    private Long id;

    @Schema(description = "销售单id")
    private Long soldReceiptId;

    @Schema(description = "货品id")
    private Long goodsId;

    @Schema(description = "货品条码")
    private String goodsSn;

    @Schema(description = "货品名称")
    private String name;

    @Schema(description = "货品图片")
    private List<GoodsHasImagesEntity> images;

    @Schema(description = "大类ID")
    private Long categoryId;

    @Schema(description = "大类")
    private String category;

    @Schema(description = "小类ID")
    private Long subclassId;

    @Schema(description = "小类")
    private String subclass;

    @Schema(description = "成色ID")
    private Long qualityId;

    @Schema(description = "成色")
    private String quality;

    @Schema(description = "款式")
    private String style;

    @Schema(description = "款式")
    private Long styleId;

    @Schema(description = "工艺")
    private Long technologyId;

    @Schema(description = "工艺")
    private String technology;

    @Schema(description = "销售方式(1:按重量,2:按数量)")
    private Integer salesType;

    @Schema(description = "销售方式(1:按重量,2:按数量)")
    private String salesTypeText;

    @Schema(description = "净金重(g)")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")
    private BigDecimal netSilverWeight;

    @Schema(description = "成本价(元)")
    private BigDecimal costPrice;

    @Schema(description = "标签价(元)")
    private BigDecimal tagPrice;

    @Schema(description = "原购数量")
    private Integer originNum;

    @Schema(description = "实收金额(元)(即销售详情中的应收金额)")
    private BigDecimal revenuePrice;

    @Schema(description = "已退数量")
    private Integer refundNum;

    @Schema(description = "可退数量")
    private Integer refundableNum;

    public BigDecimal getNetGoldWeight() {
        return PriceUtil.formatThreeDecimal(netGoldWeight);
    }

    public BigDecimal getNetSilverWeight() {
        return PriceUtil.formatThreeDecimal(netSilverWeight);
    }

    public BigDecimal getRevenuePrice() {
        return PriceUtil.formatTwoDecimal(revenuePrice);
    }
}