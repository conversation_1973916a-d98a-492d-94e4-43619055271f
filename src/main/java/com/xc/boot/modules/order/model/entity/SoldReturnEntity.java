package com.xc.boot.modules.order.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 销售退货单
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Table(value = "sold_return", comment = "销售退货单")
public class SoldReturnEntity extends BaseEntity implements CreatedByListenerFlag {

    /**
     * 退货单号
     */
    @Column("return_code")
    private String returnCode;

    /**
     * 销售单号
     */
    @Column("receipt_sn")
    private String receiptSn;

    /**
     * 销售单ID
     */
    @Column("sold_id")
    private Long soldId;

    /**
     * 所属商户ID
     */
    @Column("company_id")
    private Long companyId;

    /**
     * 所属门店ID
     */
    @Column("merchant_id")
    private Long merchantId;

    /**
     * 经办人ID
     */
    @Column("transactor_id")
    private Long transactorId;

    /**
     * 会员ID
     */
    @Column("member_id")
    private Long memberId;

    /**
     * 收银员ID
     */
    @Column("cashier_id")
    private Long cashierId;

    /**
     * 数量
     */
    @Column("num")
    private Integer num;

    /**
     * 总重(g)
     */
    @Column("total_weight")
    private BigDecimal totalWeight;

    /**
     * 总金重(g)
     */
    @Column("total_net_gold_weight")
    private BigDecimal totalNetGoldWeight;

    /**
     * 总银重(g)
     */
    @Column("total_net_silver_weight")
    private BigDecimal totalNetSilverWeight;

    /**
     * 实收金额(分)
     */
    @Column("total_revenue_price")
    private Long totalRevenuePrice;

    /**
     * 退货金额(分)
     */
    @Column("total_return_price")
    private Long totalReturnPrice;

    /**
     * 折旧费(分)
     */
    @Column("total_depreciation_price")
    private Long totalDepreciationPrice;

    /**
     * 应退金额(分)
     */
    @Column("total_pay_price")
    private Long totalPayPrice;

    /**
     * 支付方式(0:现金,1:支付宝,2:微信,3:刷卡)
     */
    @Column("pay_type")
    private Integer payType;

    /**
     * 备注
     */
    @Column("remark")
    private String remark;

    /**
     * 创建人ID
     */
    @Column("created_by")
    private Long createdBy;

} 