package com.xc.boot.modules.order.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 销售开单-货品查询分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "销售开单-货品查询分页查询对象")
public class SoldGoodsPageQuery extends BasePageQuery {
    @Schema(description = "门店ID")
    private String merchantId;
    
    @Schema(description = "柜台ID(英文逗号分割)")
    private String counterIds;
    
    @Schema(description = "所属大类ID(英文逗号分割)")
    private String categoryIds;
    
    @Schema(description = "货品小类ID(英文逗号分割)")
    private String subclassIds;
    
    @Schema(description = "成色ID(英文逗号分割)")
    private String qualityIds;
    
    @Schema(description = "款式ID(英文逗号分割)")
    private String styleIds;
    
    @Schema(description = "货品名称")
    private String name;
    
    @Schema(description = "货品条码")
    private String goodsSn;
}
