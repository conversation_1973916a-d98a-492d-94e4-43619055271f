package com.xc.boot.modules.order.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 销售开单-赠品查询分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "销售开单-赠品查询分页查询对象")
public class SoldGiftPageQuery extends BasePageQuery {

    @Schema(description = "门店ID")
    private String merchantId;

    @Schema(description = "赠品编号")
    private String giftSn;

    @Schema(description = "赠品名称")
    private String name;
}
