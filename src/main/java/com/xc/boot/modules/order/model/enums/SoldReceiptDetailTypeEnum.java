package com.xc.boot.modules.order.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 销售单明细类型枚举
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Getter
@AllArgsConstructor
public enum SoldReceiptDetailTypeEnum implements IBaseEnum<Integer> {

    /**
     * 货品
     */
    GOODS(1, "货品"),

    /**
     * 旧料
     */
    OLD_MATERIAL(2, "旧料"),

    /**
     * 赠品
     */
    GIFT(3, "赠品");

    private final Integer value;
    private final String label;

}
