package com.xc.boot.modules.order.model.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 销售单状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Getter
@AllArgsConstructor
public enum SoldReceiptStatusEnum implements IBaseEnum<Integer> {

    /**
     * 已作废
     */
    CANCELLED(-1, "已作废"),

    /**
     * 挂单中
     */
    PENDING(0, "挂单中"),

    /**
     * 已完成
     */
    COMPLETED(1, "已完成"),

    /**
     * 部分退货
     */
    PARTIAL_REFUND(2, "部分退货"),

    /**
     * 全部退货
     */
    FULL_REFUND(3, "全部退货");

    private final Integer value;
    private final String label;

}
