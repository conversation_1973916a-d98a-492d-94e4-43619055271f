package com.xc.boot.modules.order.model.vo;

import com.xc.boot.common.util.PriceUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-29
 */
@Data
public class SoldReturnInfoVO {

    @Schema(description = "销售单id")
    private Long id;

    @Schema(description = "销售单号")
    private String receiptSn;

    @Schema(description = "门店id")
    private Long merchantId;

    @Schema(description = "门店")
    private String merchant;

    @Schema(description = "会员id")
    private Long memberId;

    @Schema(description = "会员信息")
    private String member;

    @Schema(description = "收银员id")
    private Long cashierId;

    @Schema(description = "收银员")
    private String cashier;

    @Schema(description = "主销导购id")
    private Long mainSellerId;

    @Schema(description = "主销导购")
    private String mainSeller;

    @Schema(description = "净金重(g)")
    private BigDecimal goldWeight;

    @Schema(description = "净银重(g)")
    private BigDecimal silverWeight;

    @Schema(description = "销售金额(元)")
    private BigDecimal amount;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "销售时间")
    private Date soldAt;

    @Schema(description = "销售单详情")
    private List<SoldReturnInfoDetailVO> details;

    public BigDecimal getGoldWeight() {
        return PriceUtil.formatThreeDecimal(goldWeight);
    }

    public BigDecimal getSilverWeight() {
        return PriceUtil.formatThreeDecimal(silverWeight);
    }

    public BigDecimal getAmount() {
        return PriceUtil.formatTwoDecimal(amount);
    }
}