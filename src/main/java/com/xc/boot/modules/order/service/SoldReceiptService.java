package com.xc.boot.modules.order.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.order.model.dto.SoldReceiptCreateDTO;
import com.xc.boot.modules.order.model.dto.SoldReceiptRefundDTO;
import com.xc.boot.modules.order.model.dto.UpdateSoldReceiptMemberDTO;
import com.xc.boot.modules.order.model.dto.UpdateSoldReceiptSellerDTO;
import com.xc.boot.modules.order.model.entity.SoldReceiptEntity;
import com.xc.boot.modules.order.model.query.SoldReceiptListPageQuery;
import com.xc.boot.modules.order.model.query.SoldReceiptPendingPageQuery;
import com.xc.boot.modules.order.model.vo.SoldReceiptListPageVO;
import com.xc.boot.modules.order.model.vo.SoldReceiptPendingPageVO;

/**
 * 销售单服务接口
 */
public interface SoldReceiptService extends IService<SoldReceiptEntity> {

    /**
     * 创建销售单
     *
     * @param dto 创建请求DTO
     * @return 销售单ID
     */
    Long createSoldReceipt(SoldReceiptCreateDTO dto);

    /**
     * 销售单挂单分页列表
     *
     * @param query 查询参数
     * @return 分页列表
     */
    Page<SoldReceiptPendingPageVO> pendingPage(SoldReceiptPendingPageQuery query);

    /**
     * 销售单列表分页查询（含打印导出）
     *
     * @param query 查询参数
     * @return 分页列表
     */
    Page<SoldReceiptListPageVO> listPage(SoldReceiptListPageQuery query);

    /**
     * 退定金/取消挂单
     *
     * @param dto 退定金请求DTO
     */
    void refundDeposit(SoldReceiptRefundDTO dto);

    /**
     * 修改销售单会员
     *
     * @param dto 修改会员请求DTO
     */
    void updateMember(UpdateSoldReceiptMemberDTO dto);

    /**
     * 修改销售单导购
     *
     * @param dto 修改导购请求DTO
     */
    void updateSeller(UpdateSoldReceiptSellerDTO dto);
}
