package com.xc.boot.modules.order.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;


/**
 * 回收单明细实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "material_recycle_detail")
public class MaterialRecycleDetailEntity extends BaseEntity {
    
    /**
     * 回收单号
     */
    @Column(value = "recycle_code")
    private String recycleCode;

    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 所属门店ID
     */
    @Column(value = "merchant_id")
    private Integer merchantId;

    /**
     * 回收单ID
     */
    @Column(value = "recycle_id")
    private Long recycleId;

    /**
     * 回收物料ID
     */
    @Column(value = "material_id")
    private Long materialId;
} 