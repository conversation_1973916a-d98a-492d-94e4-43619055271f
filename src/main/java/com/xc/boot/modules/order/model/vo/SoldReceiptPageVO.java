package com.xc.boot.modules.order.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 销售单分页VO
 */
@Data
@Schema(description = "销售单分页VO")
public class SoldReceiptPageVO {
    
    @Schema(description = "销售单ID")
    private Long id;
    
    @Schema(description = "销售单号")
    private String receiptSn;
    
    @Schema(description = "所属门店ID")
    private Long merchantId;
    
    @Schema(description = "所属门店名称")
    private String merchantName;
    
    @Schema(description = "会员ID")
    private Long memberId;
    
    @Schema(description = "会员名称")
    private String memberName;
    
    @Schema(description = "收银员ID")
    private Long cashierId;
    
    @Schema(description = "收银员名称")
    private String cashierName;
    
    @Schema(description = "主销导购ID")
    private Long mainSellerId;
    
    @Schema(description = "主销导购名称")
    private String mainSellerName;
    
    @Schema(description = "辅销导购ID")
    private Long supportSellerId;
    
    @Schema(description = "辅销导购名称")
    private String supportSellerName;
    
    @Schema(description = "商品数量")
    private Integer num;
    
    @Schema(description = "总重量(g)")
    private BigDecimal weight;
    
    @Schema(description = "总金重(g)")
    private BigDecimal goldWeight;
    
    @Schema(description = "总银重(g)")
    private BigDecimal silverWeight;
    
    @Schema(description = "订单金额(元)")
    private BigDecimal amount;
    
    @Schema(description = "货品金额(元)")
    private BigDecimal goodsAmount;
    
    @Schema(description = "优惠金额(元)")
    private BigDecimal discountAmount;
    
    @Schema(description = "抵扣金额-旧料(元)")
    private BigDecimal deductionAmount;
    
    @Schema(description = "赠品金额(元)")
    private BigDecimal giftAmount;
    
    @Schema(description = "抹零金额(元)")
    private BigDecimal adjustAmount;
    
    @Schema(description = "已收金额(元)")
    private BigDecimal receivedAmount;
    
    @Schema(description = "实付金额(元)")
    private BigDecimal paidAmount;
    
    @Schema(description = "退款金额(元)")
    private BigDecimal refundAmount;
    
    @Schema(description = "挂单时间")
    private Date pendingAt;
    
    @Schema(description = "销售时间")
    private Date soldAt;
    
    @Schema(description = "状态(-1-已作废 0-挂单中 1-已完成 2-部分退货 3-全部退货)")
    private Integer status;
    
    @Schema(description = "状态名称")
    private String statusName;
    
    @Schema(description = "备注")
    private String remark;
    
    @Schema(description = "货品列表")
    private List<SoldReceiptGoodsVO> goodsList = new ArrayList<>();
}
