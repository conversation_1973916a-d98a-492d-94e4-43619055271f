package com.xc.boot.modules.order.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 销售单货品明细
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Table(value = "sold_receipt_goods", comment = "销售单货品明细")
public class SoldReceiptGoodsEntity extends BaseEntity {

    /**
     * 销售单ID
     */
    @Column("sold_receipt_id")
    private Long soldReceiptId;

    /**
     * 货品ID
     */
    @Column("goods_id")
    private Long goodsId;

    /**
     * 货品条码
     */
    @Column("goods_sn")
    private String goodsSn;

    /**
     * 数量
     */
    @Column("num")
    private Integer num;

    /**
     * 标签单价(分)
     */
    @Column("tag_price")
    private Long tagPrice;

    /**
     * 工费单价(分)
     */
    @Column("sale_work_price")
    private Long saleWorkPrice;

    /**
     * 销工费计价方式(1:按重量,2:按数量)
     */
    @Column("sale_work_price_type")
    private Integer saleWorkPriceType;

    /**
     * 计价方式(1:按重量,2:按数量)
     */
    @Column("sale_type")
    private Integer saleType;

    // 兼容
    public Integer getSalesType() {
        return saleType;
    }

    /**
     * 金价(分)
     */
    @Column("gold_price")
    private Long goldPrice;

    /**
     * 银价(分)
     */
    @Column("silver_price")
    private Long silverPrice;

    /**
     * 销售单价(分)
     */
    @Column("sold_price")
    private Long soldPrice;

    /**
     * 员工折扣优惠金额(分)
     */
    @Column("discount_amount")
    private Long discountAmount;

    /**
     * 应收金额(分)
     */
    @Column("real_amount")
    private Long realAmount;

    /**
     * 员工折扣(百分比)
     */
    @Column("staff_discount")
    private BigDecimal staffDiscount;

    /**
     * 工费折扣优惠金额(分)
     */
    @Column("work_price_discount_amount")
    private Integer workPriceDiscountAmount;

    /**
     * 工费折扣(百分比)
     */
    @Column("work_price_discount")
    private BigDecimal workPriceDiscount;

    /**
     * 退货数量
     */
    @Column("refund_num")
    private Integer refundNum;

    /**
     * 金重(g)
     */
    @Column("gold_weight")
    private BigDecimal goldWeight;

    /**
     * 银重(g)
     */
    @Column("silver_weight")
    private BigDecimal silverWeight;

    /**
     * 货品快照
     */
    @Column("data_snapshot")
    private String dataSnapshot;

    /**
     * 实时黄金价格(分)
     */
    @Column("gold_price_now")
    private Integer goldPriceNow;

    /**
     * 实时白银价格(分)
     */
    @Column("silver_price_now")
    private Integer silverPriceNow;

    /**
     * 实时铂金价格(分)
     */
    @Column("platinum_price_now")
    private Integer platinumPriceNow;

    /**
     * 成本价(分)
     */
    @Column("cost_price")
    private Long costPrice;

}
