package com.xc.boot.modules.order.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024-07-29
 */
@Data
public class SoldReturnGoodsDetailVO {

    @Schema(description = "货品条码")
    private String goodsSn;

    @Schema(description = "货品名称")
    private String name;

    @Schema(description = "货品图片")
    private List<GoodsHasImagesEntity> image;

    @Schema(description = "所属大类")
    private String category;

    @Schema(description = "所属大类")
    private Long categoryId;

    @Schema(description = "货品小类")
    private String subclass;

    @Schema(description = "货品小类")
    private Long subclassId;

    @Schema(description = "成色")
    private String quality;

    @Schema(description = "成色")
    private Long qualityId;

    @Schema(description = "销售方式(1:按重量,2:按数量)")
    private Integer salesType;

    @Schema(description = "销售方式(1:按重量,2:按数量)")
    private String salesTypeText;

    @Schema(description = "净金重(g)")
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")
    private BigDecimal netSilverWeight;

    @Schema(description = "原购数量")
    private Integer originNum;

    @Schema(description = "实收金额(元)")
    private BigDecimal revenuePrice;

    @Schema(description = "退货数量")
    private Integer num;

    @Schema(description = "退货金额(元)")
    private BigDecimal returnPrice;

    @Schema(description = "折旧费(元)")
    private BigDecimal depreciationPrice;

    @Schema(description = "应退金额(元)")
    private BigDecimal payPrice;

    @JsonIgnore
    private Long goodsId;

    @SuppressWarnings("unused")
    public BigDecimal getNetGoldWeight() {
        return PriceUtil.formatDecimal(netGoldWeight, 3);
    }

    @SuppressWarnings("unused")
    public BigDecimal getNetSilverWeight() {
        return PriceUtil.formatDecimal(netSilverWeight, 3);
    }


    @SuppressWarnings("unused")
    public BigDecimal getRevenuePrice() {
        return PriceUtil.formatDecimal(revenuePrice, 2);
    }


    @SuppressWarnings("unused")
    public BigDecimal getReturnPrice() {
        return PriceUtil.formatDecimal(returnPrice, 2);
    }

    @SuppressWarnings("unused")
    public BigDecimal getDepreciationPrice() {
        return PriceUtil.formatDecimal(depreciationPrice, 2);
    }

    @SuppressWarnings("unused")
    public BigDecimal getPayPrice() {
        return PriceUtil.formatDecimal(payPrice, 2);
    }
}