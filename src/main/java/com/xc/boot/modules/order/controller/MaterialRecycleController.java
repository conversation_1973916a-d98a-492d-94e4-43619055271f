package com.xc.boot.modules.order.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.modules.order.model.form.MaterialRecycleForm;
import com.xc.boot.modules.order.model.form.MaterialRecycleUpdateForm;
import com.xc.boot.modules.order.model.query.MaterialRecycleQuery;
import com.xc.boot.modules.order.model.vo.MaterialRecycleDetailVO;
import com.xc.boot.modules.order.model.vo.MaterialRecyclePageVo;
import com.xc.boot.modules.order.service.MaterialRecycleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 旧料回收控制层
 */
@Slf4j
@Tag(name = "销售管理-回收单列表")
@RestController
@RequestMapping("/api/recycle")
@RequiredArgsConstructor
public class MaterialRecycleController {

    private final MaterialRecycleService materialRecycleService;

    @Operation(summary = "回收单分页列表")
    @PostMapping("/page")
    public PageResult<MaterialRecyclePageVo> page(@Validated @RequestBody MaterialRecycleQuery query) {
        Page<MaterialRecyclePageVo> page = materialRecycleService.page(query);
        return PageResult.success(page);
    }

    @Operation(summary = "查看单个回收单详情")
    @GetMapping("/detail")
    public Result<MaterialRecyclePageVo> getRecycleDetail(@RequestParam @Validated @NotNull(message = "id不能为空") Integer id) {
        MaterialRecyclePageVo vo = materialRecycleService.getRecycleDetail(id);
        return Result.success(vo);
    }

    @Operation(summary = "查看回收单明细列表")
    @GetMapping("/detailList")
    public Result<List<MaterialRecycleDetailVO>> getRecycleDetailList(@Schema(description = "回收单ID")
                                                                          @RequestParam
                                                                          @Validated
                                                                          @NotNull(message = "Id不能为空") Integer id) {
        List<MaterialRecycleDetailVO> list = materialRecycleService.getRecycleDetailList(id);
        return Result.success(list);
    }

    @Operation(summary = "修改回收单导购")
    @PostMapping("/updateAdviser")
    public Result<?> updateAdviser(@RequestBody @Validated MaterialRecycleUpdateForm form) {
        materialRecycleService.updateRecycle(form);
        return Result.success();
    }

    @Operation(summary = "修改回收单会员")
    @PostMapping("/updateMember")
    public Result<?> updateMember(@RequestBody @Validated MaterialRecycleUpdateForm form) {
        materialRecycleService.updateRecycle(form);
        return Result.success();
    }

    @Operation(summary = "回收开单")
    @PostMapping("/create")
    public Result<?> createRecycle(@RequestBody @Validated MaterialRecycleForm form) {
        try {
            materialRecycleService.createRecycle(form);
        }catch (Exception e) {
            log.error("回收开单异常", e);
            SnUtils.syncRecycleMaxSequence();
            SnUtils.syncOldMaterialMaxSequence();
            throw new BusinessException(e.getMessage(), e);
        }
        return Result.success();
    }
} 