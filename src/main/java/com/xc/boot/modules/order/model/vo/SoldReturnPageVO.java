package com.xc.boot.modules.order.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xc.boot.common.util.PriceUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @since 2024-07-29
 */
@Data
public class SoldReturnPageVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "退货单号")
    private String returnCode;

    @Schema(description = "销售单号")
    private String receiptSn;

    @Schema(description = "所属门店")
    private String merchant;

    @Schema(description = "所属门店")
    private Long merchantId;

    @Schema(description = "退货经办人")
    private String transactor;

    @Schema(description = "退货经办人")
    private Long transactorId;

    @Schema(description = "收银员")
    private String cashier;

    @Schema(description = "收银员")
    private Long cashierId;

    @Schema(description = "会员信息")
    private String member;

    @Schema(description = "会员信息")
    private Long memberId;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "总重量")
    private BigDecimal totalWeight;

    @Schema(description = "总金重(g)")
    private BigDecimal totalNetGoldWeight;

    @Schema(description = "总银重(g)")
    private BigDecimal totalNetSilverWeight;

    @Schema(description = "实收金额(元)")
    private BigDecimal totalRevenuePrice;

    @Schema(description = "退货金额(元)")
    private BigDecimal totalReturnPrice;

    @Schema(description = "折旧费(元)")
    private BigDecimal totalDepreciationPrice;

    @Schema(description = "应退金额(元)")
    private BigDecimal totalPayPrice;

    @Schema(description = "退款方式(0:现金,1:支付宝,2:微信,3:刷卡)")
    private Integer payType;

    @Schema(description = "退款方式(0:现金,1:支付宝,2:微信,3:刷卡)")
    private String payTypeText;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "退货时间")
    private Date createdAt;

    @Schema(description = "备注")
    private String remark;


    public BigDecimal getTotalNetGoldWeight() {
        return PriceUtil.formatThreeDecimal(totalNetGoldWeight);
    }

    public BigDecimal getTotalNetSilverWeight() {
        return PriceUtil.formatThreeDecimal(totalNetSilverWeight);
    }

    public BigDecimal getTotalWeight() {
        return PriceUtil.formatThreeDecimal(totalWeight);
    }

    public BigDecimal getTotalRevenuePrice() {
        return PriceUtil.formatTwoDecimal(totalRevenuePrice);
    }

    public BigDecimal getTotalReturnPrice() {
        return PriceUtil.formatTwoDecimal(totalReturnPrice);
    }

    public BigDecimal getTotalDepreciationPrice() {
        return PriceUtil.formatTwoDecimal(totalDepreciationPrice);
    }

    public BigDecimal getTotalPayPrice() {
        return PriceUtil.formatTwoDecimal(totalPayPrice);
    }
}