package com.xc.boot.modules.order.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 回收单实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "material_recycle")
public class MaterialRecycleEntity extends BaseEntity implements CreatedByListenerFlag {
    
    /**
     * 回收单号
     */
    @Column(value = "recycle_code")
    private String recycleCode;

    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 所属门店ID
     */
    @Column(value = "merchant_id")
    private Integer merchantId;

    /**
     * 主导购ID
     */
    @Column(value = "main_adviser_id")
    private Integer mainAdviserId;

    /**
     * 辅购ID
     */
    @Column(value = "sub_adviser_id")
    private Integer subAdviserId;

    /**
     * 会员ID
     */
    @Column(value = "member_id")
    private Long memberId;

    /**
     * 数量
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 总金重(g)
     */
    @Column(value = "total_net_gold_weight")
    private BigDecimal totalNetGoldWeight;

    /**
     * 总银重(g)
     */
    @Column(value = "total_net_silver_weight")
    private BigDecimal totalNetSilverWeight;

    /**
     * 总回收金额(分)
     */
    @Column(value = "total_price")
    private Long totalPrice;

    /**
     * 支付方式(0:现金,1:支付宝,2:微信,3:刷卡)
     */
    @Column(value = "pay_type")
    private Integer payType;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 收银员ID
     */
    @Column(value = "cashier_id")
    private Long cashierId;

    /**
     * 创建人ID
     */
    @Column(value = "created_by")
    private Long createdBy;
} 