package com.xc.boot.modules.order.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.modules.order.model.dto.SoldReceiptCreateDTO;
import com.xc.boot.modules.order.model.dto.SoldReceiptRefundDTO;
import com.xc.boot.modules.order.model.dto.UpdateSoldReceiptMemberDTO;
import com.xc.boot.modules.order.model.dto.UpdateSoldReceiptSellerDTO;
import com.xc.boot.modules.order.model.query.SoldGiftPageQuery;
import com.xc.boot.modules.order.model.query.SoldGoodsPageQuery;
import com.xc.boot.modules.order.model.query.SoldReceiptListPageQuery;
import com.xc.boot.modules.order.model.query.SoldReceiptPageQuery;
import com.xc.boot.modules.order.model.query.SoldReceiptPendingPageQuery;
import com.xc.boot.modules.order.model.vo.*;
import com.xc.boot.modules.order.service.SoldQueryService;
import com.xc.boot.modules.order.service.SoldReceiptCreateService;
import com.xc.boot.modules.order.service.SoldReceiptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 销售开单
 */
@Tag(name = "销售管理-销售开单")
@RestController
@RequestMapping("/api/sold")
@RequiredArgsConstructor
public class SoldController {

    private final SoldQueryService soldQueryService;
    private final SoldReceiptCreateService soldReceiptCreateService;
    private final SoldReceiptService soldReceiptService;

    @Operation(summary = "货品查询分页列表")
    @PostMapping("/goods/page")
    public PageResult<?> goodsPage(@Validated @RequestBody SoldGoodsPageQuery query) {
        Page<SoldGoodsPageVO> page = soldQueryService.goodsPage(query);

        return PageResult.success((Page<?>)ColumnEncryptUtil.process(page));
    }

    @Operation(summary = "销售单查询分页列表")
    @PostMapping("/query/receipt")
    public PageResult<SoldReceiptPageVO> receiptPage(@Validated @RequestBody SoldReceiptPageQuery query) {
        Page<SoldReceiptPageVO> page = soldQueryService.receiptPage(query);
        return PageResult.success(page);
    }

    @Operation(summary = "赠品查询分页列表")
    @PostMapping("/query/gift")
    public PageResult<SoldGiftPageVO> giftPage(@Validated @RequestBody SoldGiftPageQuery query) {
        Page<SoldGiftPageVO> page = soldQueryService.giftPage(query);
        return PageResult.success(page);
    }

    @Operation(summary = "销售单挂单分页列表")
    @PostMapping("/pending/page")
    public PageResult<SoldReceiptPendingPageVO> pendingPage(@Validated @RequestBody SoldReceiptPendingPageQuery query) {
        Page<SoldReceiptPendingPageVO> page = soldReceiptService.pendingPage(query);
        return PageResult.success(page);
    }

    @Operation(summary = "销售单列表分页查询（含打印导出）")
    @PostMapping("/list/page")
    public PageResult<SoldReceiptListPageVO> listPage(@Validated @RequestBody SoldReceiptListPageQuery query) {
        Page<SoldReceiptListPageVO> page = soldReceiptService.listPage(query);
        return PageResult.success(page);
    }

    @Operation(summary = "退定金/取消挂单")
    @PostMapping("/refund")
    public Result<Void> refundDeposit(@Validated @RequestBody SoldReceiptRefundDTO dto) {
        soldReceiptService.refundDeposit(dto);
        return Result.success();
    }

    @Operation(summary = "创建销售单")
    @PostMapping("/receipt/create")
    public Result<Long> createSoldReceipt(@Validated @RequestBody SoldReceiptCreateDTO dto) {
        try {
            Long soldReceiptId = soldReceiptCreateService.createSoldReceipt(dto);
            return Result.success(soldReceiptId);
        } catch (Exception e) {
            SnUtils.syncSalesMaxSequence();
            SnUtils.syncOldMaterialMaxSequence();
            throw e;
        }
    }

    @Operation(summary = "取单 - 获取挂单中销售单详情")
    @GetMapping("/receipt/pending")
    public Result<SoldReceiptDetailVO> getPendingReceiptDetail(@RequestParam Long soldReceiptId) {
        return Result.success(soldQueryService.getPendingReceiptDetail(soldReceiptId));
    }

    @Operation(summary = "获取销售单详情")
    @GetMapping("/receipt/detail")
    public Result<SoldReceiptFullDetailVO> getSoldReceiptDetail(@RequestParam Long soldReceiptId) {
        return Result.success(soldQueryService.getSoldReceiptDetail(soldReceiptId));
    }

    @Operation(summary = "修改销售单会员")
    @PostMapping("/receipt/updateMember")
    public Result<Void> updateMember(@Validated @RequestBody UpdateSoldReceiptMemberDTO dto) {
        soldReceiptService.updateMember(dto);
        return Result.success();
    }

    @Operation(summary = "修改销售单导购")
    @PostMapping("/receipt/updateSeller")
    public Result<Void> updateSeller(@Validated @RequestBody UpdateSoldReceiptSellerDTO dto) {
        soldReceiptService.updateSeller(dto);
        return Result.success();
    }

}