package com.xc.boot.modules.report.model.bo;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 销售统计数据BO
 * 用于接收销售相关的SQL查询结果
 */
@Data
public class SalesStatisticsBo {
    
    /**
     * 订单数量
     */
    private Integer orderNum = 0;
    
    /**
     * 货品金额(分)
     */
    private Long goodsAmount = 0L;
    
    /**
     * 实收金额(分)
     */
    private Long receivedAmount = 0L;
    
    /**
     * 优惠金额(分)
     */
    private Long discountAmount = 0L;
    
    /**
     * 旧料抵扣金额(分)
     */
    private Long deductionAmount = 0L;
    
    /**
     * 货品毛利(分)
     */
    private Long grossProfit = 0L;
    
    /**
     * 货品总数
     */
    private Integer goodsNum = 0;
    
    /**
     * 货品金重(g)
     */
    private BigDecimal goldWeight = BigDecimal.ZERO;
    
    /**
     * 货品银重(g)
     */
    private BigDecimal silverWeight = BigDecimal.ZERO;
    
    /**
     * 旧料总数
     */
    private Integer oldMaterialNum = 0;
    
    /**
     * 旧料金重(g)
     */
    private BigDecimal oldMaterialGoldWeight = BigDecimal.ZERO;
    
    /**
     * 旧料银重(g)
     */
    private BigDecimal oldMaterialSilverWeight = BigDecimal.ZERO;
    
    /**
     * 总销售金额(分) - 用于毛利计算查询
     */
    private Long totalSalesAmount = 0L;
    
    /**
     * 总成本金额(分) - 用于毛利计算查询
     */
    private Long totalCostAmount = 0L;
} 