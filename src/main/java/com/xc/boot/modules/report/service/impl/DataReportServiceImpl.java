package com.xc.boot.modules.report.service.impl;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.common.enums.baseColum.OtherColumEnum;
import com.xc.boot.common.enums.baseColum.PriceColumEnum;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.merchant.mapper.ActualGoldPriceMapper;
import com.xc.boot.modules.merchant.mapper.SubclassMapper;
import com.xc.boot.modules.merchant.model.entity.ActualGoldPriceEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.entity.SubclassEntity;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialMapper;
import com.xc.boot.modules.order.mapper.*;
import com.xc.boot.modules.report.model.bo.*;
import com.xc.boot.modules.report.model.query.GoodsDataReportQuery;
import com.xc.boot.modules.report.model.query.SoldDataReportQuery;
import com.xc.boot.modules.report.model.vo.CoordinateVo;
import com.xc.boot.modules.report.model.vo.GoodsDataReportVo;
import com.xc.boot.modules.report.model.vo.ReportVo;
import com.xc.boot.modules.report.model.vo.SoldDataReportVo;
import com.xc.boot.modules.report.service.DataReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.mybatisflex.core.query.QueryMethods.*;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
import static com.xc.boot.modules.merchant.model.entity.table.ActualGoldPriceTableDef.ACTUAL_GOLD_PRICE;
import static com.xc.boot.modules.oldmaterial.model.entity.table.OldMaterialTableDef.OLD_MATERIAL;
import static com.xc.boot.modules.order.model.entity.table.MaterialRecycleDetailTableDef.MATERIAL_RECYCLE_DETAIL;
import static com.xc.boot.modules.order.model.entity.table.MaterialRecycleTableDef.MATERIAL_RECYCLE;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptGoodsTableDef.SOLD_RECEIPT_GOODS;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptOldMaterialTableDef.SOLD_RECEIPT_OLD_MATERIAL;
import static com.xc.boot.modules.order.model.entity.table.SoldReceiptTableDef.SOLD_RECEIPT;
import static com.xc.boot.modules.order.model.entity.table.SoldReturnDetailTableDef.SOLD_RETURN_DETAIL;
import static com.xc.boot.modules.order.model.entity.table.SoldReturnTableDef.SOLD_RETURN;


@Slf4j
@Service
@RequiredArgsConstructor
public class DataReportServiceImpl implements DataReportService {
    
    // 常量定义
    private static final List<Integer> COMPLETED_STATUS = List.of(1, 2, 3); // 已完成、部分退货、全部退货
    private static final int MAX_QUERY_DAYS = 365; // 最大查询天数限制
    
    private final GoodsMapper goodsMapper;
    private final ActualGoldPriceMapper actualGoldPriceMapper;
    private final SubclassMapper subclassMapper;
    private final SoldReceiptMapper soldReceiptMapper;
    private final SoldReceiptGoodsMapper soldReceiptGoodsMapper;
    private final SoldReceiptOldMaterialMapper soldReceiptOldMaterialMapper;
    private final SoldReturnMapper soldReturnMapper;
    private final SoldReturnDetailMapper soldReturnDetailMapper;
    private final MaterialRecycleMapper materialRecycleMapper;
    private final OldMaterialMapper oldMaterialMapper;

    @Override
    public GoodsDataReportVo goodsDataReport(GoodsDataReportQuery query) {
        GoodsDataReportVo vo = new GoodsDataReportVo();
        QueryWrapper wrapper = buildWrapper(query);

        // 1. 总览卡片数据
        fillGoodsSummary(vo, wrapper);

        // 2. 大类分布
        vo.setCategoryList(getCategoryDistribution(wrapper));

        // 3. 小类分布
        vo.setSubclassList(getSubclassDistribution(wrapper));

        // 4. 库龄分布
        vo.setAgeList(getStockAgeDistribution(wrapper));

        // 5. 库龄价值分析
        vo.setAgeValueList(getStockAgeValueAnalysis(wrapper));

        return vo;
    }

    private QueryWrapper buildWrapper(GoodsDataReportQuery query) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS.MERCHANT_ID.in(SecurityUtils.getMerchantIds(), !SecurityUtils.isMain()))
                .where(GOODS.STOCK_NUM.gt(0))
                .where(GOODS.NUM.gt(0));
        if (query != null) {
            if (query.getMerchantIds() != null && !query.getMerchantIds().isEmpty()) {
                queryWrapper.and(GOODS.MERCHANT_ID.in(Arrays.asList(query.getMerchantIds().split(","))));
            }
            if (query.getCategoryIds() != null && !query.getCategoryIds().isEmpty()) {
                queryWrapper.and(GOODS.CATEGORY_ID.in(Arrays.asList(query.getCategoryIds().split(","))));
            }
            if (query.getSubclassIds() != null && !query.getSubclassIds().isEmpty()) {
                queryWrapper.and(GOODS.SUBCLASS_ID.in(Arrays.asList(query.getSubclassIds().split(","))));
            }
        }
        return queryWrapper;
    }

    /**
     * 实时金价信息
     */
    private static class PriceInfo {
        String goldPrice;
        String silverPrice;
        String platinumPrice;
        boolean hasGoldPrice() { return goldPrice != null; }
        boolean hasSilverPrice() { return silverPrice != null; }
        boolean hasPlatinumPrice() { return platinumPrice != null; }
    }

    /**
     * 获取最新实时金价信息
     */
    private PriceInfo getLatestPriceInfo() {
        ActualGoldPriceEntity priceEntity = actualGoldPriceMapper.selectOneByQuery(QueryWrapper.create()
                .where(ACTUAL_GOLD_PRICE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .orderBy(ACTUAL_GOLD_PRICE.ID, false)
                .limit(1));
        PriceInfo info = new PriceInfo();
        info.goldPrice = priceEntity != null && priceEntity.getGoldPrice() != null ? PriceUtil.fen2yuanString(priceEntity.getGoldPrice()) : null;
        info.silverPrice = priceEntity != null && priceEntity.getSilverPrice() != null ? PriceUtil.fen2yuanString(priceEntity.getSilverPrice()) : null;
        info.platinumPrice = priceEntity != null && priceEntity.getPlatinumPrice() != null ? PriceUtil.fen2yuanString(priceEntity.getPlatinumPrice()) : null;
        return info;
    }

    /**
     * 构建价值case表达式
     */
    private QueryColumn buildValueCase(PriceInfo priceInfo) {
        return case_()
                .when(GOODS.CATEGORY_ID.eq(CategoryEnum.SILVER.getValue()))
                .then(priceInfo.hasSilverPrice() ?
                        GOODS.NET_SILVER_WEIGHT.multiply(QueryMethods.column(priceInfo.silverPrice)).multiply(GOODS.STOCK_NUM)
                        : GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM).divide(100.0))
                .when(GOODS.CATEGORY_ID.eq(CategoryEnum.GOLD_SILVER.getValue()))
                .then((priceInfo.hasGoldPrice() && priceInfo.hasSilverPrice()) ?
                        GOODS.NET_GOLD_WEIGHT.multiply(QueryMethods.column(priceInfo.goldPrice)).multiply(GOODS.STOCK_NUM)
                                .add(GOODS.NET_SILVER_WEIGHT.multiply(QueryMethods.column(priceInfo.silverPrice)).multiply(GOODS.STOCK_NUM))
                        : GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM).divide(100.0))
                .when(GOODS.CATEGORY_ID.eq(CategoryEnum.GOLD.getValue()))
                .then(priceInfo.hasGoldPrice() ?
                        GOODS.NET_GOLD_WEIGHT.multiply(QueryMethods.column(priceInfo.goldPrice)).multiply(GOODS.STOCK_NUM)
                        : GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM).divide(100.0))
                .when(GOODS.CATEGORY_ID.eq(CategoryEnum.PLATINUM.getValue()))
                .then(priceInfo.hasPlatinumPrice() ?
                        GOODS.NET_GOLD_WEIGHT.multiply(QueryMethods.column(priceInfo.platinumPrice)).multiply(GOODS.STOCK_NUM)
                        : GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM).divide(100.0))
                .else_(GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM).divide(100.0))
                .end();
    }

    /**
     * 填充总览卡片数据
     */
    private void fillGoodsSummary(GoodsDataReportVo vo, QueryWrapper wrapper) {
        QueryWrapper summaryWrapper = wrapper.clone();
        summaryWrapper.select(
                sum(GOODS.STOCK_NUM).as("totalNum"),
                sum(GOODS.NET_GOLD_WEIGHT.multiply(GOODS.STOCK_NUM)).as("totalGoldWeight"),
                sum(GOODS.NET_SILVER_WEIGHT.multiply(GOODS.STOCK_NUM)).as("totalSilverWeight"),
                sum(GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM)).as("totalCostPrice")
        );
        PriceInfo priceInfo = getLatestPriceInfo();
        summaryWrapper.select(
                sum(buildValueCase(priceInfo)).as("totalValue")
        );
        GoodsDataReportVo summary = goodsMapper.selectOneByQueryAs(summaryWrapper, GoodsDataReportVo.class);
        if (summary == null) {
            return;
        }
        GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(PriceColumEnum.COST_PRICE.getSign());
        vo.setTotalNum(summary.getTotalNum());
        vo.setTotalGoldWeight(PriceUtil.formatThreeDecimal(summary.getTotalGoldWeight()));
        vo.setTotalSilverWeight(PriceUtil.formatThreeDecimal(summary.getTotalSilverWeight()));
        vo.setTotalCostPrice(ColumnEncryptUtil.handleEncryptPrice(PriceUtil.fen2yuanString(summary.getTotalCostPrice()), column));
        vo.setTotalValue(ColumnEncryptUtil.handleEncryptPrice(PriceUtil.formatTwoDecimal(summary.getTotalValue()).toPlainString(), column));
    }

    /**
     * 统计货品大类分布
     */
    private List<ReportVo> getCategoryDistribution(QueryWrapper wrapper) {
        QueryWrapper categoryWrapper = wrapper.clone();
        categoryWrapper.select(GOODS.CATEGORY_ID.as("categoryId"),
                        sum(GOODS.STOCK_NUM).as("num"))
                .groupBy(GOODS.CATEGORY_ID);
        List<CategoryDistributionBo> list = goodsMapper.selectListByQueryAs(categoryWrapper, CategoryDistributionBo.class);
        int total = list.stream().mapToInt(bo -> bo.getNum() != null ? bo.getNum() : 0).sum();
        List<ReportVo> result = new ArrayList<>();
        for (CategoryDistributionBo bo : list) {
            Integer categoryId = bo.getCategoryId();
            int num = bo.getNum() != null ? bo.getNum() : 0;
            String label = categoryId != null ? (CategoryEnum.getByValue(categoryId.longValue()) != null ? CategoryEnum.getByValue(categoryId.longValue()).getLabel() : "未知") : "未知";
            result.add(ReportVo.builder()
                    .index(categoryId == null ? "" : categoryId.toString())
                    .label(label)
                    .num(String.valueOf(num))
                    .percent(total > 0 ? String.format("%.2f%%", num * 100.0 / total) : "0.00%")
                    .build());
        }
        return result;
    }

    /**
     * 统计货品小类分布
     */
    private List<ReportVo> getSubclassDistribution(QueryWrapper wrapper) {
        QueryWrapper subclassWrapper = wrapper.clone();
        subclassWrapper.select(GOODS.SUBCLASS_ID.as("subclassId"), sum(GOODS.STOCK_NUM).as("num"))
                .groupBy(GOODS.SUBCLASS_ID);
        List<CategoryDistributionBo> list = goodsMapper.selectListByQueryAs(subclassWrapper, CategoryDistributionBo.class);
        int total = list.stream().mapToInt(bo -> bo.getNum() != null ? bo.getNum() : 0).sum();
        
        Set<Integer> subclassIds = new HashSet<>();
        for (CategoryDistributionBo bo : list) {
            if (bo.getSubclassId() != null) {
                subclassIds.add(bo.getSubclassId());
            }
        }
        Map<Integer, String> subclassNameMap = new HashMap<>();
        if (!subclassIds.isEmpty()) {
            List<SubclassEntity> subclassEntities = subclassMapper.selectListByIds(subclassIds);
            for (SubclassEntity entity : subclassEntities) {
                subclassNameMap.put(entity.getId().intValue(), entity.getName());
            }
        }
        List<ReportVo> result = new ArrayList<>();
        for (CategoryDistributionBo bo : list) {
            Integer subclassId = bo.getSubclassId();
            int num = bo.getNum() != null ? bo.getNum() : 0;
            String label = subclassId != null ? subclassNameMap.getOrDefault(subclassId, "未设置小类") : "未设置小类";
            result.add(ReportVo.builder()
                    .index(subclassId == null ? "" : subclassId.toString())
                    .label(label)
                    .num(String.valueOf(num))
                    .percent(total > 0 ? String.format("%.2f%%", num * 100.0 / total) : "0.00%")
                    .build());
        }
        return result;
    }

    /**
     * 固定库龄区间
     */
    private static final List<String> AGE_LABELS = Arrays.asList("0-30天", "31-60天", "61-90天", "91-180天", "180天以上");

    /**
     * 统计库龄分布，所有区间都返回，无数据补0
     */
    private List<ReportVo> getStockAgeDistribution(QueryWrapper wrapper) {
        // 只查询必要的字段：id、创建时间、库存数量
        QueryWrapper ageWrapper = wrapper.clone();
        ageWrapper.select(GOODS.ID, GOODS.CREATED_AT, GOODS.STOCK_NUM);
        
        List<GoodsEntity> goodsList = goodsMapper.selectListByQuery(ageWrapper);
        
        // 在内存中计算库龄分布
        Map<String, Integer> ageNumMap = new HashMap<>();
        int total = 0;
        LocalDate now = LocalDate.now();
        
        for (GoodsEntity goods : goodsList) {
            if (goods.getCreatedAt() != null && goods.getStockNum() != null && goods.getStockNum() > 0) {
                // 计算库龄天数
                LocalDate createdAt = goods.getCreatedAt().toInstant()
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDate();
                long daysDiff = now.toEpochDay() - createdAt.toEpochDay();
                String ageLabel = calculateAgeLabel(daysDiff);
                
                if (AGE_LABELS.contains(ageLabel)) {
                    ageNumMap.put(ageLabel, ageNumMap.getOrDefault(ageLabel, 0) + goods.getStockNum());
                    total += goods.getStockNum();
                }
            }
        }

        List<ReportVo> result = new ArrayList<>();
        for (String label : AGE_LABELS) {
            int num = ageNumMap.getOrDefault(label, 0);
            result.add(ReportVo.builder()
                    .index(label)
                    .label(label)
                    .num(String.valueOf(num))
                    .percent(total > 0 ? String.format("%.2f%%", num * 100.0 / total) : "0.00%")
                    .build());
        }
        return result;
    }

    /**
     * 库龄价值分析
     */
    private List<CoordinateVo> getStockAgeValueAnalysis(QueryWrapper wrapper) {
        // 只查询必要的字段：id、创建时间、库存数量、成本价、分类、净金重、净银重
        QueryWrapper ageValueWrapper = wrapper.clone();
        ageValueWrapper.select(
                GOODS.ID, 
                GOODS.CREATED_AT, 
                GOODS.STOCK_NUM,
                GOODS.COST_PRICE,
                GOODS.CATEGORY_ID,
                GOODS.NET_GOLD_WEIGHT,
                GOODS.NET_SILVER_WEIGHT
        );
        
        List<GoodsEntity> goodsList = goodsMapper.selectListByQuery(ageValueWrapper);
        
        // 获取实时金价信息
        PriceInfo priceInfo = getLatestPriceInfo();
        
        // 在内存中计算库龄分布和价值
        Map<String, Integer> ageNumMap = new HashMap<>();
        Map<String, BigDecimal> ageValueMap = new HashMap<>();
        LocalDate now = LocalDate.now();
        
        for (GoodsEntity goods : goodsList) {
            if (goods.getCreatedAt() != null && goods.getStockNum() != null && goods.getStockNum() > 0) {
                // 计算库龄天数
                LocalDate createdAt = goods.getCreatedAt().toInstant()
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDate();
                long daysDiff = now.toEpochDay() - createdAt.toEpochDay();
                String ageLabel = calculateAgeLabel(daysDiff);
                
                if (AGE_LABELS.contains(ageLabel)) {
                    // 累计数量
                    ageNumMap.put(ageLabel, ageNumMap.getOrDefault(ageLabel, 0) + goods.getStockNum());
                    
                    // 计算单个货品的价值
                    BigDecimal goodsValue = calculateGoodsValue(goods, priceInfo);
                    
                    // 累计价值
                    ageValueMap.put(ageLabel, ageValueMap.getOrDefault(ageLabel, BigDecimal.ZERO).add(goodsValue));
                }
            }
        }
        
        // 格式化结果
        GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(PriceColumEnum.COST_PRICE.getSign());
        List<CoordinateVo> result = new ArrayList<>();
        
        for (String label : AGE_LABELS) {
            int num = ageNumMap.getOrDefault(label, 0);
            BigDecimal value = ageValueMap.getOrDefault(label, BigDecimal.ZERO);
            String formattedValue = ColumnEncryptUtil.handleEncryptPrice(PriceUtil.formatTwoDecimal(value).toString(), column);
            
            List<ReportVo> children = new ArrayList<>();
            children.add(ReportVo.builder().index("1").label("库存数量").num(String.valueOf(num)).build());
            children.add(ReportVo.builder().index("2").label("库存价值").num(formattedValue).build());
            result.add(new CoordinateVo(label, children));
        }
        return result;
    }

    @Override
    public SoldDataReportVo soldDataReport(SoldDataReportQuery query) {
        if (query == null || query.getRange() == null || query.getRange().length != 2) {
            throw new BusinessException("查询参数不能为空");
        }

        LocalDate startDate = query.getRange()[0];
        LocalDate endDate = query.getRange()[1];
        
        // 日期范围校验
        if (startDate.isAfter(endDate)) {
            throw new BusinessException("开始日期不能晚于结束日期");
        }

        long daysBetween = endDate.toEpochDay() - startDate.toEpochDay();
        if (daysBetween > MAX_QUERY_DAYS) {
            throw new BusinessException("查询日期范围不能超过" + MAX_QUERY_DAYS + "天");
        }

        SoldDataReportVo vo = new SoldDataReportVo();

        try {
            // 转换当期和上期的日期范围为 DateTime
            LocalDateTime[] currentPeriod = convertToDateTimeRange(startDate, endDate);
            LocalDate[] previousPeriodDates = calculatePreviousPeriod(startDate, endDate);
            LocalDateTime[] previousPeriod = convertToDateTimeRange(previousPeriodDates[0], previousPeriodDates[1]);

            // 解析商户ID
            List<String> merchantIds = parseMerchantIds(query.getMerchantIds());
            // 根据大类id筛选销售单id列表
            Set<Long> receiptIds = parseCategoryIds(query.getCategoryIds(), previousPeriod[0], currentPeriod[1]);
            // 根据大类id筛选回收单id列表
            Set<Long> recycleIds = parseReCategoryIds(query.getCategoryIds(), previousPeriod[0], currentPeriod[1]);
            // 根据大类id筛选退货单id列表
            Set<Long> returnIds = parseReturnCategoryIds(query.getCategoryIds(), previousPeriod[0], currentPeriod[1]);

            // 1. 销售统计
            fillSalesStatistics(vo, currentPeriod, previousPeriod, merchantIds, receiptIds);

            // 2. 回收统计
            fillRecycleStatistics(vo, currentPeriod, previousPeriod, merchantIds, recycleIds);

            // 3. 退货统计
            fillReturnStatistics(vo, currentPeriod, previousPeriod, merchantIds, returnIds);

            // 4. 销售趋势图
            fillSalesTrends(vo, currentPeriod, merchantIds, receiptIds);

            // 5. 回收趋势图
            fillRecycleTrends(vo, currentPeriod, merchantIds, recycleIds);

            // 6. 退货趋势图
            fillReturnTrends(vo, currentPeriod, merchantIds, returnIds);

            // 7. 货品大类占比图
            fillSoldCategoryPieChart(vo, currentPeriod, merchantIds, receiptIds);

            // 8. 回收旧料大类占比图
            fillRecycleCategoryPieChart(vo, currentPeriod, merchantIds, recycleIds);

            // 9. 退货货品大类占比图
            fillReturnCategoryPieChart(vo, currentPeriod, merchantIds, returnIds);

        } catch (Exception e) {
            log.error("获取销售数据统计失败", e);
            throw new BusinessException("获取销售数据统计失败：" + e.getMessage());
        }

        return vo;
    }



    /**
     * 解析商户ID字符串为最终生效的商户ID列表
     * 如果传入的商户ID为空，则返回当前用户有权限的商户ID列表
     */
    private List<String> parseMerchantIds(String merchantIdsStr) {
        if (merchantIdsStr == null || merchantIdsStr.trim().isEmpty()) {
            return SecurityUtils.getMerchantIds().stream().map(String::valueOf).toList();
        }
        return Arrays.asList(merchantIdsStr.split(","));
    }

    private Set<Long> parseCategoryIds(String categoryIds, LocalDateTime start, LocalDateTime end) {
        if (StringUtils.isBlank(categoryIds)) {
            return new HashSet<>();
        }
        List<String> categoryIdsList = Arrays.asList(categoryIds.split(","));
        List<Long> receiptIds = soldReceiptMapper.selectListByQueryAs(QueryWrapper.create()
                        .select(distinct(SOLD_RECEIPT.ID))
                        .from(SOLD_RECEIPT)
                        .leftJoin(SOLD_RECEIPT_GOODS).on(SOLD_RECEIPT.ID.eq(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID))
                        .leftJoin(SOLD_RECEIPT_OLD_MATERIAL).on(SOLD_RECEIPT.ID.eq(SOLD_RECEIPT_OLD_MATERIAL.SOLD_RECEIPT_ID))
                        .where(exists(QueryWrapper.create()
                                .from(GOODS)
                                .where(GOODS.ID.eq(SOLD_RECEIPT_GOODS.GOODS_ID))
                                .where(GOODS.CATEGORY_ID.in(categoryIdsList)))
                                .or(exists(QueryWrapper.create()
                                        .from(OLD_MATERIAL)
                                        .where(OLD_MATERIAL.ID.eq(SOLD_RECEIPT_OLD_MATERIAL.OLD_MATERIAL_ID))
                                        .where(OLD_MATERIAL.CATEGORY_ID.in(categoryIdsList))))
                        )
                        .where(SOLD_RECEIPT.SOLD_AT.between(start, end)),
                Long.class);
        HashSet<Long> set = new HashSet<>(receiptIds);
        set.add(0L);
        return set;
    }

    private Set<Long> parseReCategoryIds(String categoryIds, LocalDateTime start, LocalDateTime end) {
        if (StringUtils.isBlank(categoryIds)) {
            return new HashSet<>();
        }
        List<String> categoryIdsList = Arrays.asList(categoryIds.split(","));
        List<Long> receiptIds = materialRecycleMapper.selectListByQueryAs(QueryWrapper.create()
                        .select(distinct(MATERIAL_RECYCLE.ID))
                        .from(MATERIAL_RECYCLE)
                        .leftJoin(MATERIAL_RECYCLE_DETAIL).on(MATERIAL_RECYCLE.ID.eq(MATERIAL_RECYCLE_DETAIL.RECYCLE_ID))
                        .where(exists(QueryWrapper.create()
                                .from(OLD_MATERIAL)
                                .where(OLD_MATERIAL.ID.eq(MATERIAL_RECYCLE_DETAIL.MATERIAL_ID))
                                .where(OLD_MATERIAL.CATEGORY_ID.in(categoryIdsList))))
                        .where(MATERIAL_RECYCLE.CREATED_AT.between(start, end)),
                Long.class);
        HashSet<Long> set = new HashSet<>(receiptIds);
        set.add(0L);
        return set;
    }

    private Set<Long> parseReturnCategoryIds(String categoryIds, LocalDateTime start, LocalDateTime end) {
        if (StringUtils.isBlank(categoryIds)) {
            return new HashSet<>();
        }
        List<String> categoryIdsList = Arrays.asList(categoryIds.split(","));
        List<Long> receiptIds = soldReturnMapper.selectListByQueryAs(QueryWrapper.create()
                        .select(distinct(SOLD_RETURN.ID))
                        .from(SOLD_RETURN)
                        .leftJoin(SOLD_RETURN_DETAIL).on(SOLD_RETURN_DETAIL.RETURN_ID.eq(SOLD_RETURN.ID))
                        .where(exists(QueryWrapper.create()
                                .from(GOODS)
                                .where(GOODS.ID.eq(SOLD_RETURN_DETAIL.GOODS_ID))
                                .where(GOODS.CATEGORY_ID.in(categoryIdsList))))
                        .where(SOLD_RETURN.CREATED_AT.between(start, end)),
                Long.class);
        HashSet<Long> set = new HashSet<>(receiptIds);
        set.add(0L);
        return set;
    }

    /**
     * 将日期范围转换为 DateTime 范围
     */
    private LocalDateTime[] convertToDateTimeRange(LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = startDate.atStartOfDay(); // 00:00:00
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59, 999999999); // 23:59:59.999999999
        return new LocalDateTime[] { startDateTime, endDateTime };
    }

    /**
     * 计算上个周期的日期范围
     */
    private LocalDate[] calculatePreviousPeriod(LocalDate startDate, LocalDate endDate) {
        long periodDays = endDate.toEpochDay() - startDate.toEpochDay() + 1;
        LocalDate previousStartDate = startDate.minusDays(periodDays);
        LocalDate previousEndDate = endDate.minusDays(periodDays);
        return new LocalDate[] { previousStartDate, previousEndDate };
    }

    /**
     * 计算涨幅百分比
     */
    private String calculateGrowthRateStr(BigDecimal currentValue, BigDecimal previousValue) {
        if (previousValue == null || previousValue.compareTo(BigDecimal.ZERO) == 0) {
            return currentValue != null && currentValue.compareTo(BigDecimal.ZERO) > 0 ? "100.00%" : "0.00%";
        }
        if (currentValue == null) {
            return "-100.00%";
        }
        BigDecimal growth = currentValue.subtract(previousValue)
                .divide(previousValue, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
        return String.format("%.2f%%", growth);
    }

    /**
     * 生成完整的日期范围列表（按日）
     */
    private List<LocalDate> generateDateRange(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate current = startDate;
        while (!current.isAfter(endDate)) {
            dates.add(current);
            current = current.plusDays(1);
        }
        return dates;
    }

    /**
     * 生成完整的月份范围列表（按月）
     */
    private List<String> generateMonthRange(LocalDate startDate, LocalDate endDate) {
        List<String> months = new ArrayList<>();
        LocalDate current = startDate.withDayOfMonth(1); // 确保从月初开始
        LocalDate end = endDate.withDayOfMonth(1);
        
        while (!current.isAfter(end)) {
            months.add(current.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            current = current.plusMonths(1);
        }
        return months;
    }

    /**
     * 填充销售统计数据
     */
    private void fillSalesStatistics(SoldDataReportVo vo, LocalDateTime[] currentPeriod, 
                                   LocalDateTime[] previousPeriod, List<String> merchantIds, Set<Long> receiptIds) {
        // 使用合并查询获取当期和上期数据
        CombinedSalesStatisticsBo combinedData = getCombinedSalesStatisticsData(
                currentPeriod[0], currentPeriod[1], 
                previousPeriod[0], previousPeriod[1], 
                merchantIds, receiptIds);

        // 设置当期数据并计算增长率
        // 总订单数
        vo.setTotalOrderNum(String.valueOf(combinedData.getCurrentOrderNum()));
        vo.setTotalOrderNumGrowth(calculateGrowthRateStr(
                new BigDecimal(combinedData.getCurrentOrderNum()), 
                new BigDecimal(combinedData.getPreviousOrderNum())));

        // 货品总金额
        BigDecimal currentGoodsAmount = PriceUtil.fen2yuan(combinedData.getCurrentGoodsAmount());
        BigDecimal previousGoodsAmount = PriceUtil.fen2yuan(combinedData.getPreviousGoodsAmount());
        vo.setTotalGoodsAmount(currentGoodsAmount.toPlainString());
        vo.setTotalGoodsAmountGrowth(calculateGrowthRateStr(currentGoodsAmount, previousGoodsAmount));

        // 实收金额
        BigDecimal currentReceivedAmount = PriceUtil.fen2yuan(combinedData.getCurrentReceivedAmount());
        BigDecimal previousReceivedAmount = PriceUtil.fen2yuan(combinedData.getPreviousReceivedAmount());
        vo.setTotalReceivedAmount(currentReceivedAmount.toPlainString());
        vo.setTotalReceivedAmountGrowth(calculateGrowthRateStr(currentReceivedAmount, previousReceivedAmount));

        // 优惠金额
        BigDecimal currentDiscountAmount = PriceUtil.fen2yuan(combinedData.getCurrentDiscountAmount());
        BigDecimal previousDiscountAmount = PriceUtil.fen2yuan(combinedData.getPreviousDiscountAmount());
        vo.setTotalDiscountAmount(currentDiscountAmount.toPlainString());
        vo.setTotalDiscountAmountGrowth(calculateGrowthRateStr(currentDiscountAmount, previousDiscountAmount));

        // 旧料抵扣
        BigDecimal currentDeductionAmount = PriceUtil.fen2yuan(combinedData.getCurrentDeductionAmount());
        BigDecimal previousDeductionAmount = PriceUtil.fen2yuan(combinedData.getPreviousDeductionAmount());
        vo.setTotalOldMaterialDeduction(currentDeductionAmount.toPlainString());
        vo.setTotalOldMaterialDeductionGrowth(calculateGrowthRateStr(currentDeductionAmount, previousDeductionAmount));

        // 货品毛利
        BigDecimal currentGrossProfit = PriceUtil.fen2yuan(combinedData.getCurrentTotalSalesAmount() - combinedData.getCurrentTotalCostAmount());
        BigDecimal previousGrossProfit = PriceUtil.fen2yuan(combinedData.getPreviousTotalSalesAmount() - combinedData.getPreviousTotalCostAmount());
        GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(PriceColumEnum.COST_PRICE.getSign());
        String cur = ColumnEncryptUtil.handleEncryptPrice(currentGrossProfit.toPlainString(), column);
        vo.setTotalGoodsProfit(cur);
        String growth = ColumnEncryptUtil.handleEncryptPrice(calculateGrowthRateStr(currentGrossProfit, previousGrossProfit), column);
        vo.setTotalGoodsProfitGrowth(growth);

        // 货品总数
        vo.setTotalGoodsNum(String.valueOf(combinedData.getCurrentGoodsNum()));
        vo.setTotalGoodsNumGrowth(calculateGrowthRateStr(
                new BigDecimal(combinedData.getCurrentGoodsNum()), 
                new BigDecimal(combinedData.getPreviousGoodsNum())));

        // 货品金重
        vo.setTotalGoodsGoldWeight(combinedData.getCurrentGoldWeight());
        vo.setTotalGoodsGoldWeightGrowth(calculateGrowthRateStr(
                combinedData.getCurrentGoldWeight(), 
                combinedData.getPreviousGoldWeight()));

        // 货品银重
        vo.setTotalGoodsSilverWeight(combinedData.getCurrentSilverWeight());
        vo.setTotalGoodsSilverWeightGrowth(calculateGrowthRateStr(
                combinedData.getCurrentSilverWeight(), 
                combinedData.getPreviousSilverWeight()));

        // 旧料总数
        vo.setTotalOldMaterialNum(String.valueOf(combinedData.getCurrentOldMaterialNum()));
        vo.setTotalOldMaterialNumGrowth(calculateGrowthRateStr(
                new BigDecimal(combinedData.getCurrentOldMaterialNum()), 
                new BigDecimal(combinedData.getPreviousOldMaterialNum())));

        // 旧料金重
        vo.setTotalOldMaterialGoldWeight(combinedData.getCurrentOldMaterialGoldWeight());
        vo.setTotalOldMaterialGoldWeightGrowth(calculateGrowthRateStr(
                combinedData.getCurrentOldMaterialGoldWeight(), 
                combinedData.getPreviousOldMaterialGoldWeight()));

        // 旧料银重
        vo.setTotalOldMaterialSilverWeight(combinedData.getCurrentOldMaterialSilverWeight());
        vo.setTotalOldMaterialSilverWeightGrowth(calculateGrowthRateStr(
                combinedData.getCurrentOldMaterialSilverWeight(), 
                combinedData.getPreviousOldMaterialSilverWeight()));
    }

    /**
     * 获取合并的销售统计数据（一次查询同时获取当期和上期数据）
     */
    private CombinedSalesStatisticsBo getCombinedSalesStatisticsData(LocalDateTime currentStart, LocalDateTime currentEnd,
                                                                   LocalDateTime previousStart, LocalDateTime previousEnd,
                                                                   List<String> merchantIds, Set<Long> receiptIds) {
        // 基础销售统计查询 - 使用CASE WHEN同时获取当期和上期数据
        QueryWrapper salesWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(1).else_(0).end()
                        ).as("currentOrderNum"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(1).else_(0).end()
                        ).as("previousOrderNum"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RECEIPT.GOODS_AMOUNT).else_(0).end()
                        ).as("currentGoodsAmount"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RECEIPT.GOODS_AMOUNT).else_(0).end()
                        ).as("previousGoodsAmount"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RECEIPT.PAID_AMOUNT).else_(0).end()
                        ).as("currentReceivedAmount"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RECEIPT.PAID_AMOUNT).else_(0).end()
                        ).as("previousReceivedAmount"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RECEIPT.DISCOUNT_AMOUNT).else_(0).end()
                        ).as("currentDiscountAmount"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RECEIPT.DISCOUNT_AMOUNT).else_(0).end()
                        ).as("previousDiscountAmount"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RECEIPT.DEDUCTION_AMOUNT).else_(0).end()
                        ).as("currentDeductionAmount"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RECEIPT.DEDUCTION_AMOUNT).else_(0).end()
                        ).as("previousDeductionAmount"))
                .from(SOLD_RECEIPT)
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RECEIPT.STATUS.in(COMPLETED_STATUS))
                .and(SOLD_RECEIPT.SOLD_AT.between(previousStart, currentEnd)); // 覆盖整个时间范围

        salesWrapper.and(SOLD_RECEIPT.MERCHANT_ID.in(merchantIds));
        salesWrapper.and(SOLD_RECEIPT.ID.in(receiptIds, !receiptIds.isEmpty()));

        CombinedSalesStatisticsBo result = soldReceiptMapper.selectOneByQueryAs(salesWrapper, CombinedSalesStatisticsBo.class);
        if (result == null) {
            result = new CombinedSalesStatisticsBo();
        }

        // 获取货品统计数据（毛利计算）
        getCombinedSalesGoodsStatistics(result, currentStart, currentEnd, previousStart, previousEnd, merchantIds, receiptIds);

        // 获取旧料统计数据
        getCombinedSalesOldMaterialStatistics(result, currentStart, currentEnd, previousStart, previousEnd, merchantIds, receiptIds);

        return result;
    }

    /**
     * 获取合并的销售货品统计数据
     */
    private void getCombinedSalesGoodsStatistics(CombinedSalesStatisticsBo result, 
                                               LocalDateTime currentStart, LocalDateTime currentEnd,
                                               LocalDateTime previousStart, LocalDateTime previousEnd,
                                               List<String> merchantIds, Set<Long> receiptIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RECEIPT_GOODS.NUM).else_(0).end()
                        ).as("currentGoodsNum"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RECEIPT_GOODS.NUM).else_(0).end()
                        ).as("previousGoodsNum"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RECEIPT_GOODS.GOLD_WEIGHT.multiply(SOLD_RECEIPT_GOODS.NUM)).else_(0).end()
                        ).as("currentGoldWeight"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RECEIPT_GOODS.GOLD_WEIGHT.multiply(SOLD_RECEIPT_GOODS.NUM)).else_(0).end()
                        ).as("previousGoldWeight"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RECEIPT_GOODS.SILVER_WEIGHT.multiply(SOLD_RECEIPT_GOODS.NUM)).else_(0).end()
                        ).as("currentSilverWeight"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RECEIPT_GOODS.SILVER_WEIGHT.multiply(SOLD_RECEIPT_GOODS.NUM)).else_(0).end()
                        ).as("previousSilverWeight"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RECEIPT_GOODS.REAL_AMOUNT).else_(0).end()
                        ).as("currentTotalSalesAmount"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RECEIPT_GOODS.REAL_AMOUNT).else_(0).end()
                        ).as("previousTotalSalesAmount"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RECEIPT_GOODS.COST_PRICE.multiply(SOLD_RECEIPT_GOODS.NUM)).else_(0).end()
                        ).as("currentTotalCostAmount"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RECEIPT_GOODS.COST_PRICE.multiply(SOLD_RECEIPT_GOODS.NUM)).else_(0).end()
                        ).as("previousTotalCostAmount"))
                .from(SOLD_RECEIPT_GOODS)
                .leftJoin(SOLD_RECEIPT).on(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.eq(SOLD_RECEIPT.ID))
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RECEIPT.STATUS.in(COMPLETED_STATUS))
                .and(SOLD_RECEIPT.SOLD_AT.between(previousStart, currentEnd))
                .and(SOLD_RECEIPT.MERCHANT_ID.in(merchantIds))
                .and(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.in(receiptIds, !receiptIds.isEmpty()));

        CombinedSalesStatisticsBo goodsStats = soldReceiptGoodsMapper.selectOneByQueryAs(wrapper, CombinedSalesStatisticsBo.class);
        if (goodsStats != null) {
            result.setCurrentGoodsNum(goodsStats.getCurrentGoodsNum());
            result.setPreviousGoodsNum(goodsStats.getPreviousGoodsNum());
            result.setCurrentGoldWeight(goodsStats.getCurrentGoldWeight());
            result.setPreviousGoldWeight(goodsStats.getPreviousGoldWeight());
            result.setCurrentSilverWeight(goodsStats.getCurrentSilverWeight());
            result.setPreviousSilverWeight(goodsStats.getPreviousSilverWeight());
            result.setCurrentTotalSalesAmount(goodsStats.getCurrentTotalSalesAmount());
            result.setPreviousTotalSalesAmount(goodsStats.getPreviousTotalSalesAmount());
            result.setCurrentTotalCostAmount(goodsStats.getCurrentTotalCostAmount());
            result.setPreviousTotalCostAmount(goodsStats.getPreviousTotalCostAmount());
        }
    }

    /**
     * 获取合并的销售旧料统计数据
     */
    private void getCombinedSalesOldMaterialStatistics(CombinedSalesStatisticsBo result,
                                                      LocalDateTime currentStart, LocalDateTime currentEnd,
                                                      LocalDateTime previousStart, LocalDateTime previousEnd,
                                                      List<String> merchantIds, Set<Long> receiptIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RECEIPT_OLD_MATERIAL.NUM).else_(0).end()
                        ).as("currentOldMaterialNum"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RECEIPT_OLD_MATERIAL.NUM).else_(0).end()
                        ).as("previousOldMaterialNum"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RECEIPT_OLD_MATERIAL.NET_GOLD_WEIGHT.multiply(SOLD_RECEIPT_OLD_MATERIAL.NUM)).else_(0).end()
                        ).as("currentOldMaterialGoldWeight"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RECEIPT_OLD_MATERIAL.NET_GOLD_WEIGHT.multiply(SOLD_RECEIPT_OLD_MATERIAL.NUM)).else_(0).end()
                        ).as("previousOldMaterialGoldWeight"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RECEIPT_OLD_MATERIAL.NET_SILVER_WEIGHT.multiply(SOLD_RECEIPT_OLD_MATERIAL.NUM)).else_(0).end()
                        ).as("currentOldMaterialSilverWeight"),
                        QueryMethods.sum(
                                case_().when(SOLD_RECEIPT.SOLD_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RECEIPT_OLD_MATERIAL.NET_SILVER_WEIGHT.multiply(SOLD_RECEIPT_OLD_MATERIAL.NUM)).else_(0).end()
                        ).as("previousOldMaterialSilverWeight"))
                .from(SOLD_RECEIPT_OLD_MATERIAL)
                .leftJoin(SOLD_RECEIPT).on(SOLD_RECEIPT_OLD_MATERIAL.SOLD_RECEIPT_ID.eq(SOLD_RECEIPT.ID))
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RECEIPT.STATUS.in(COMPLETED_STATUS))
                .and(SOLD_RECEIPT.SOLD_AT.between(previousStart, currentEnd))
                .and(SOLD_RECEIPT.MERCHANT_ID.in(merchantIds))
                .and(SOLD_RECEIPT_OLD_MATERIAL.SOLD_RECEIPT_ID.in(receiptIds, !receiptIds.isEmpty()));

        CombinedSalesStatisticsBo oldMaterialStats = soldReceiptOldMaterialMapper.selectOneByQueryAs(wrapper, CombinedSalesStatisticsBo.class);
        if (oldMaterialStats != null) {
            result.setCurrentOldMaterialNum(oldMaterialStats.getCurrentOldMaterialNum());
            result.setPreviousOldMaterialNum(oldMaterialStats.getPreviousOldMaterialNum());
            result.setCurrentOldMaterialGoldWeight(oldMaterialStats.getCurrentOldMaterialGoldWeight());
            result.setPreviousOldMaterialGoldWeight(oldMaterialStats.getPreviousOldMaterialGoldWeight());
            result.setCurrentOldMaterialSilverWeight(oldMaterialStats.getCurrentOldMaterialSilverWeight());
            result.setPreviousOldMaterialSilverWeight(oldMaterialStats.getPreviousOldMaterialSilverWeight());
        }
    }

    /**
     * 获取合并的回收统计数据（一次查询同时获取当期和上期数据）
     */
    private CombinedRecycleStatisticsBo getCombinedRecycleStatisticsData(LocalDateTime currentStart, LocalDateTime currentEnd,
                                                                       LocalDateTime previousStart, LocalDateTime previousEnd,
                                                                       List<String> merchantIds, Set<Long> receiptIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.sum(
                                case_().when(MATERIAL_RECYCLE.CREATED_AT.between(currentStart, currentEnd))
                                        .then(1).else_(0).end()
                        ).as("currentOrderNum"),
                        QueryMethods.sum(
                                case_().when(MATERIAL_RECYCLE.CREATED_AT.between(previousStart, previousEnd))
                                        .then(1).else_(0).end()
                        ).as("previousOrderNum"),
                        QueryMethods.sum(
                                case_().when(MATERIAL_RECYCLE.CREATED_AT.between(currentStart, currentEnd))
                                        .then(MATERIAL_RECYCLE.TOTAL_PRICE).else_(0).end()
                        ).as("currentAmount"),
                        QueryMethods.sum(
                                case_().when(MATERIAL_RECYCLE.CREATED_AT.between(previousStart, previousEnd))
                                        .then(MATERIAL_RECYCLE.TOTAL_PRICE).else_(0).end()
                        ).as("previousAmount"),
                        QueryMethods.sum(
                                case_().when(MATERIAL_RECYCLE.CREATED_AT.between(currentStart, currentEnd))
                                        .then(MATERIAL_RECYCLE.NUM).else_(0).end()
                        ).as("currentNum"),
                        QueryMethods.sum(
                                case_().when(MATERIAL_RECYCLE.CREATED_AT.between(previousStart, previousEnd))
                                        .then(MATERIAL_RECYCLE.NUM).else_(0).end()
                        ).as("previousNum"),
                        QueryMethods.sum(
                                case_().when(MATERIAL_RECYCLE.CREATED_AT.between(currentStart, currentEnd))
                                        .then(MATERIAL_RECYCLE.TOTAL_NET_GOLD_WEIGHT).else_(0).end()
                        ).as("currentGoldWeight"),
                        QueryMethods.sum(
                                case_().when(MATERIAL_RECYCLE.CREATED_AT.between(previousStart, previousEnd))
                                        .then(MATERIAL_RECYCLE.TOTAL_NET_GOLD_WEIGHT).else_(0).end()
                        ).as("previousGoldWeight"),
                        QueryMethods.sum(
                                case_().when(MATERIAL_RECYCLE.CREATED_AT.between(currentStart, currentEnd))
                                        .then(MATERIAL_RECYCLE.TOTAL_NET_SILVER_WEIGHT).else_(0).end()
                        ).as("currentSilverWeight"),
                        QueryMethods.sum(
                                case_().when(MATERIAL_RECYCLE.CREATED_AT.between(previousStart, previousEnd))
                                        .then(MATERIAL_RECYCLE.TOTAL_NET_SILVER_WEIGHT).else_(0).end()
                        ).as("previousSilverWeight"))
                .from(MATERIAL_RECYCLE)
                .where(MATERIAL_RECYCLE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(MATERIAL_RECYCLE.CREATED_AT.between(previousStart, currentEnd))
                .and(MATERIAL_RECYCLE.MERCHANT_ID.in(merchantIds))
                .and(MATERIAL_RECYCLE.ID.in(receiptIds, !receiptIds.isEmpty()));

        CombinedRecycleStatisticsBo result = materialRecycleMapper.selectOneByQueryAs(wrapper, CombinedRecycleStatisticsBo.class);
        if (result == null) {
            result = new CombinedRecycleStatisticsBo();
        }
        return result;
    }

    /**
     * 获取合并的退货统计数据（一次查询同时获取当期和上期数据）
     */
    private CombinedReturnStatisticsBo getCombinedReturnStatisticsData(LocalDateTime currentStart, LocalDateTime currentEnd,
                                                                     LocalDateTime previousStart, LocalDateTime previousEnd,
                                                                     List<String> merchantIds, Set<Long> receiptIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.sum(
                                case_().when(SOLD_RETURN.CREATED_AT.between(currentStart, currentEnd))
                                        .then(1).else_(0).end()
                        ).as("currentOrderNum"),
                        QueryMethods.sum(
                                case_().when(SOLD_RETURN.CREATED_AT.between(previousStart, previousEnd))
                                        .then(1).else_(0).end()
                        ).as("previousOrderNum"),
                        QueryMethods.sum(
                                case_().when(SOLD_RETURN.CREATED_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RETURN.TOTAL_PAY_PRICE).else_(0).end()
                        ).as("currentAmount"),
                        QueryMethods.sum(
                                case_().when(SOLD_RETURN.CREATED_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RETURN.TOTAL_PAY_PRICE).else_(0).end()
                        ).as("previousAmount"),
                        QueryMethods.sum(
                                case_().when(SOLD_RETURN.CREATED_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RETURN.TOTAL_DEPRECIATION_PRICE).else_(0).end()
                        ).as("currentDepreciation"),
                        QueryMethods.sum(
                                case_().when(SOLD_RETURN.CREATED_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RETURN.TOTAL_DEPRECIATION_PRICE).else_(0).end()
                        ).as("previousDepreciation"),
                        QueryMethods.sum(
                                case_().when(SOLD_RETURN.CREATED_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RETURN.NUM).else_(0).end()
                        ).as("currentNum"),
                        QueryMethods.sum(
                                case_().when(SOLD_RETURN.CREATED_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RETURN.NUM).else_(0).end()
                        ).as("previousNum"),
                        QueryMethods.sum(
                                case_().when(SOLD_RETURN.CREATED_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RETURN.TOTAL_NET_GOLD_WEIGHT).else_(0).end()
                        ).as("currentGoldWeight"),
                        QueryMethods.sum(
                                case_().when(SOLD_RETURN.CREATED_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RETURN.TOTAL_NET_GOLD_WEIGHT).else_(0).end()
                        ).as("previousGoldWeight"),
                        QueryMethods.sum(
                                case_().when(SOLD_RETURN.CREATED_AT.between(currentStart, currentEnd))
                                        .then(SOLD_RETURN.TOTAL_NET_SILVER_WEIGHT).else_(0).end()
                        ).as("currentSilverWeight"),
                        QueryMethods.sum(
                                case_().when(SOLD_RETURN.CREATED_AT.between(previousStart, previousEnd))
                                        .then(SOLD_RETURN.TOTAL_NET_SILVER_WEIGHT).else_(0).end()
                        ).as("previousSilverWeight"))
                .from(SOLD_RETURN)
                .where(SOLD_RETURN.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RETURN.CREATED_AT.between(previousStart, currentEnd))
                .and(SOLD_RETURN.MERCHANT_ID.in(merchantIds))
                .and(SOLD_RETURN.ID.in(receiptIds, !receiptIds.isEmpty()));

        CombinedReturnStatisticsBo result = soldReturnMapper.selectOneByQueryAs(wrapper, CombinedReturnStatisticsBo.class);
        if (result == null) {
            result = new CombinedReturnStatisticsBo();
        }
        return result;
    }

    /**
     * 填充回收统计数据（优化版：一次查询同时获取当期和上期数据）
     */
    private void fillRecycleStatistics(SoldDataReportVo vo, LocalDateTime[] currentPeriod, 
                                     LocalDateTime[] previousPeriod, List<String> merchantIds, Set<Long> receiptIds) {
        // 使用合并查询获取当期和上期数据
        CombinedRecycleStatisticsBo combinedData = getCombinedRecycleStatisticsData(
                currentPeriod[0], currentPeriod[1], 
                previousPeriod[0], previousPeriod[1], 
                merchantIds, receiptIds);

        // 回收单总数
        vo.setRecycleOrderNum(String.valueOf(combinedData.getCurrentOrderNum()));
        vo.setRecycleOrderNumGrowth(calculateGrowthRateStr(
                new BigDecimal(combinedData.getCurrentOrderNum()), 
                new BigDecimal(combinedData.getPreviousOrderNum())));

        // 回收金额
        BigDecimal currentAmount = PriceUtil.fen2yuan(combinedData.getCurrentAmount());
        BigDecimal previousAmount = PriceUtil.fen2yuan(combinedData.getPreviousAmount());
        vo.setRecycleAmount(currentAmount.toPlainString());
        vo.setRecycleAmountGrowth(calculateGrowthRateStr(currentAmount, previousAmount));

        // 回收旧料总数
        vo.setRecycleOldMaterialNum(String.valueOf(combinedData.getCurrentNum()));
        vo.setRecycleOldMaterialNumGrowth(calculateGrowthRateStr(
                new BigDecimal(combinedData.getCurrentNum()), 
                new BigDecimal(combinedData.getPreviousNum())));

        // 回收旧料金重
        vo.setRecycleOldMaterialGoldWeight(combinedData.getCurrentGoldWeight());
        vo.setRecycleOldMaterialGoldWeightGrowth(calculateGrowthRateStr(
                combinedData.getCurrentGoldWeight(), 
                combinedData.getPreviousGoldWeight()));

        // 回收旧料银重
        vo.setRecycleOldMaterialSilverWeight(combinedData.getCurrentSilverWeight());
        vo.setRecycleOldMaterialSilverWeightGrowth(calculateGrowthRateStr(
                combinedData.getCurrentSilverWeight(), 
                combinedData.getPreviousSilverWeight()));
    }

    /**
     * 填充退货统计数据（优化版：一次查询同时获取当期和上期数据）
     */
    private void fillReturnStatistics(SoldDataReportVo vo, LocalDateTime[] currentPeriod, 
                                    LocalDateTime[] previousPeriod, List<String> merchantIds, Set<Long> receiptIds) {
        // 使用合并查询获取当期和上期数据
        CombinedReturnStatisticsBo combinedData = getCombinedReturnStatisticsData(
                currentPeriod[0], currentPeriod[1], 
                previousPeriod[0], previousPeriod[1], 
                merchantIds, receiptIds);

        // 退货单总数
        vo.setReturnOrderNum(String.valueOf(combinedData.getCurrentOrderNum()));
        vo.setReturnOrderNumGrowth(calculateGrowthRateStr(
                new BigDecimal(combinedData.getCurrentOrderNum()), 
                new BigDecimal(combinedData.getPreviousOrderNum())));

        // 退货金额
        BigDecimal currentAmount = PriceUtil.fen2yuan(combinedData.getCurrentAmount());
        BigDecimal previousAmount = PriceUtil.fen2yuan(combinedData.getPreviousAmount());
        vo.setReturnAmount(currentAmount.toPlainString());
        vo.setReturnAmountGrowth(calculateGrowthRateStr(currentAmount, previousAmount));

        // 折旧费
        BigDecimal currentDepreciation = PriceUtil.fen2yuan(combinedData.getCurrentDepreciation());
        BigDecimal previousDepreciation = PriceUtil.fen2yuan(combinedData.getPreviousDepreciation());
        vo.setDepreciationFee(currentDepreciation.toPlainString());
        vo.setDepreciationFeeGrowth(calculateGrowthRateStr(currentDepreciation, previousDepreciation));

        // 退货数量
        vo.setReturnGoodsNum(String.valueOf(combinedData.getCurrentNum()));
        vo.setReturnGoodsNumGrowth(calculateGrowthRateStr(
                new BigDecimal(combinedData.getCurrentNum()), 
                new BigDecimal(combinedData.getPreviousNum())));

        // 退货金重
        vo.setReturnGoodsGoldWeight(combinedData.getCurrentGoldWeight());
        vo.setReturnGoodsGoldWeightGrowth(calculateGrowthRateStr(
                combinedData.getCurrentGoldWeight(), 
                combinedData.getPreviousGoldWeight()));

        // 退货银重
        vo.setReturnGoodsSilverWeight(combinedData.getCurrentSilverWeight());
        vo.setReturnGoodsSilverWeightGrowth(calculateGrowthRateStr(
                combinedData.getCurrentSilverWeight(), 
                combinedData.getPreviousSilverWeight()));
    }

    /**
     * 填充销售趋势图数据（优化：合并查询减少数据库交互）
     */
    private void fillSalesTrends(SoldDataReportVo vo, LocalDateTime[] currentPeriod, List<String> merchantIds, Set<Long> receiptIds) {
        // 按日趋势
        vo.setSoldTrendListByDay(getSalesTrendsByDay(currentPeriod[0], currentPeriod[1], merchantIds, receiptIds));
        // 按月趋势
        vo.setSoldTrendListByMonth(getSalesTrendsByMonth(currentPeriod[0], currentPeriod[1], merchantIds, receiptIds));
    }

    /**
     * 获取按日销售趋势数据
     */
    private List<CoordinateVo> getSalesTrendsByDay(LocalDateTime startDateTime, LocalDateTime endDateTime, List<String> merchantIds, Set<Long> receiptIds) {
        // 分别查询销售金额和货品详情，避免重复计算
        // 1. 查询销售金额和订单数（不涉及JOIN，避免重复计算）
        QueryWrapper salesWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.date(SOLD_RECEIPT.SOLD_AT).as("trendDate"),
                        QueryMethods.sum(SOLD_RECEIPT.PAID_AMOUNT).as("salesAmount"),
                        QueryMethods.count(SOLD_RECEIPT.ID).as("orderCount"))
                .from(SOLD_RECEIPT)
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RECEIPT.STATUS.in(COMPLETED_STATUS))
                .and(SOLD_RECEIPT.SOLD_AT.between(startDateTime, endDateTime))
                .and(SOLD_RECEIPT.MERCHANT_ID.in(merchantIds))
                .and(SOLD_RECEIPT.ID.in(receiptIds, !receiptIds.isEmpty()))
                .groupBy(QueryMethods.date(SOLD_RECEIPT.SOLD_AT));

        // 2. 查询货品详情
        QueryWrapper goodsWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.date(SOLD_RECEIPT.SOLD_AT).as("trendDate"),
                        QueryMethods.sum(SOLD_RECEIPT_GOODS.NUM).as("goodsNum"),
                        QueryMethods.sum(SOLD_RECEIPT_GOODS.GOLD_WEIGHT.multiply(SOLD_RECEIPT_GOODS.NUM)).as("goldWeight"),
                        QueryMethods.sum(SOLD_RECEIPT_GOODS.SILVER_WEIGHT.multiply(SOLD_RECEIPT_GOODS.NUM)).as("silverWeight"))
                .from(SOLD_RECEIPT_GOODS)
                .leftJoin(SOLD_RECEIPT).on(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.eq(SOLD_RECEIPT.ID))
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RECEIPT.STATUS.in(COMPLETED_STATUS))
                .and(SOLD_RECEIPT.SOLD_AT.between(startDateTime, endDateTime))
                .and(SOLD_RECEIPT.MERCHANT_ID.in(merchantIds))
                .and(SOLD_RECEIPT.ID.in(receiptIds, !receiptIds.isEmpty()))
                .groupBy(QueryMethods.date(SOLD_RECEIPT.SOLD_AT));

        List<TrendDataBo> salesResults = soldReceiptMapper.selectListByQueryAs(salesWrapper, TrendDataBo.class);
        List<TrendDataBo> goodsResults = soldReceiptGoodsMapper.selectListByQueryAs(goodsWrapper, TrendDataBo.class);

        // 合并数据
        Map<String, TrendDataBo> salesDataMap = new HashMap<>();
        Map<String, TrendDataBo> goodsDataMap = new HashMap<>();

        for (TrendDataBo result : salesResults) {
            salesDataMap.put(result.getTrendDate(), result);
        }
        for (TrendDataBo result : goodsResults) {
            goodsDataMap.put(result.getTrendDate(), result);
        }

        List<CoordinateVo> trends = new ArrayList<>();
        List<LocalDate> dateRange = generateDateRange(startDateTime.toLocalDate(), endDateTime.toLocalDate());
        
        for (LocalDate date : dateRange) {
            String dateStr = date.toString();
            TrendDataBo salesResult = salesDataMap.get(dateStr);
            TrendDataBo goodsResult = goodsDataMap.get(dateStr);
            
            BigDecimal salesAmount = BigDecimal.ZERO;
            Integer orderCount = 0;
            Integer goodsNum = 0;
            BigDecimal goldWeight = BigDecimal.ZERO;
            BigDecimal silverWeight = BigDecimal.ZERO;
            
            if (salesResult != null) {
                salesAmount = PriceUtil.fen2yuan(salesResult.getSalesAmount());
                orderCount = salesResult.getOrderCount();
            }
            
            if (goodsResult != null) {
                goodsNum = goodsResult.getGoodsNum() != null ? goodsResult.getGoodsNum() : 0;
                goldWeight = goodsResult.getGoldWeight() != null ? goodsResult.getGoldWeight() : BigDecimal.ZERO;
                silverWeight = goodsResult.getSilverWeight() != null ? goodsResult.getSilverWeight() : BigDecimal.ZERO;
            }
            
            List<ReportVo> children = new ArrayList<>();
            children.add(ReportVo.builder().index("1").label("销售额").num(salesAmount.toPlainString()).build());
            children.add(ReportVo.builder().index("2").label("订单数").num(String.valueOf(orderCount)).build());
            children.add(ReportVo.builder().index("3").label("货品数量").num(String.valueOf(goodsNum)).build());
            children.add(ReportVo.builder().index("4").label("金重").num(goldWeight.toPlainString()).build());
            children.add(ReportVo.builder().index("5").label("银重").num(silverWeight.toPlainString()).build());
            
            trends.add(new CoordinateVo(dateStr, children));
        }
        
        return trends;
    }

    /**
     * 获取按月销售趋势数据
     */
    private List<CoordinateVo> getSalesTrendsByMonth(LocalDateTime startDateTime, LocalDateTime endDateTime, List<String> merchantIds, Set<Long> receiptIds) {
        // 分别查询销售金额和货品详情，避免重复计算
        // 1. 查询销售金额和订单数（不涉及JOIN，避免重复计算）
        QueryWrapper salesWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column("DATE_FORMAT(sold_at, '%Y-%m') as trendMonth"),
                        QueryMethods.sum(SOLD_RECEIPT.PAID_AMOUNT).as("salesAmount"),
                        QueryMethods.count(SOLD_RECEIPT.ID).as("orderCount"))
                .from(SOLD_RECEIPT)
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RECEIPT.STATUS.in(COMPLETED_STATUS))
                .and(SOLD_RECEIPT.SOLD_AT.between(startDateTime, endDateTime))
                .and(SOLD_RECEIPT.MERCHANT_ID.in(merchantIds))
                .and(SOLD_RECEIPT.ID.in(receiptIds, !receiptIds.isEmpty()))
                .groupBy("DATE_FORMAT(sold_at, '%Y-%m')");

        // 2. 查询货品详情
        QueryWrapper goodsWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column("DATE_FORMAT(sold_at, '%Y-%m') as trendMonth"),
                        QueryMethods.sum(SOLD_RECEIPT_GOODS.NUM).as("goodsNum"),
                        QueryMethods.sum(SOLD_RECEIPT_GOODS.GOLD_WEIGHT.multiply(SOLD_RECEIPT_GOODS.NUM)).as("goldWeight"),
                        QueryMethods.sum(SOLD_RECEIPT_GOODS.SILVER_WEIGHT.multiply(SOLD_RECEIPT_GOODS.NUM)).as("silverWeight"))
                .from(SOLD_RECEIPT_GOODS)
                .leftJoin(SOLD_RECEIPT).on(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.eq(SOLD_RECEIPT.ID))
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RECEIPT.STATUS.in(COMPLETED_STATUS))
                .and(SOLD_RECEIPT.SOLD_AT.between(startDateTime, endDateTime))
                .and(SOLD_RECEIPT.MERCHANT_ID.in(merchantIds))
                .and(SOLD_RECEIPT.ID.in(receiptIds, !receiptIds.isEmpty()))
                .groupBy("DATE_FORMAT(sold_at, '%Y-%m')");

        List<TrendDataBo> salesResults = soldReceiptMapper.selectListByQueryAs(salesWrapper, TrendDataBo.class);
        List<TrendDataBo> goodsResults = soldReceiptGoodsMapper.selectListByQueryAs(goodsWrapper, TrendDataBo.class);

        // 合并数据
        Map<String, TrendDataBo> salesDataMap = new HashMap<>();
        Map<String, TrendDataBo> goodsDataMap = new HashMap<>();

        for (TrendDataBo result : salesResults) {
            salesDataMap.put(result.getTrendMonth(), result);
        }
        for (TrendDataBo result : goodsResults) {
            goodsDataMap.put(result.getTrendMonth(), result);
        }
        
        List<CoordinateVo> trends = new ArrayList<>();
        List<String> monthRange = generateMonthRange(startDateTime.toLocalDate(), endDateTime.toLocalDate());
        
        for (String month : monthRange) {
            TrendDataBo salesResult = salesDataMap.get(month);
            TrendDataBo goodsResult = goodsDataMap.get(month);
            
            BigDecimal salesAmount = BigDecimal.ZERO;
            Integer orderCount = 0;
            Integer goodsNum = 0;
            BigDecimal goldWeight = BigDecimal.ZERO;
            BigDecimal silverWeight = BigDecimal.ZERO;
            
            if (salesResult != null) {
                salesAmount = PriceUtil.fen2yuan(salesResult.getSalesAmount());
                orderCount = salesResult.getOrderCount();
            }
            
            if (goodsResult != null) {
                goodsNum = goodsResult.getGoodsNum() != null ? goodsResult.getGoodsNum() : 0;
                goldWeight = goodsResult.getGoldWeight() != null ? goodsResult.getGoldWeight() : BigDecimal.ZERO;
                silverWeight = goodsResult.getSilverWeight() != null ? goodsResult.getSilverWeight() : BigDecimal.ZERO;
            }
            
            List<ReportVo> children = new ArrayList<>();
            children.add(ReportVo.builder().index("1").label("销售额").num(salesAmount.toPlainString()).build());
            children.add(ReportVo.builder().index("2").label("订单数").num(String.valueOf(orderCount)).build());
            children.add(ReportVo.builder().index("3").label("货品数量").num(String.valueOf(goodsNum)).build());
            children.add(ReportVo.builder().index("4").label("金重").num(goldWeight.toPlainString()).build());
            children.add(ReportVo.builder().index("5").label("银重").num(silverWeight.toPlainString()).build());
            
            trends.add(new CoordinateVo(month, children));
        }
        
        return trends;
    }

    /**
     * 填充回收趋势图数据
     */
    private void fillRecycleTrends(SoldDataReportVo vo, LocalDateTime[] currentPeriod, List<String> merchantIds, Set<Long> receiptIds) {
        vo.setRecycleTrendListByDay(getRecycleTrendsByDay(currentPeriod[0], currentPeriod[1], merchantIds, receiptIds));
        vo.setRecycleTrendListByMonth(getRecycleTrendsByMonth(currentPeriod[0], currentPeriod[1], merchantIds, receiptIds));
    }

    /**
     * 获取按日回收趋势数据
     */
    private List<CoordinateVo> getRecycleTrendsByDay(LocalDateTime startDateTime, LocalDateTime endDateTime, List<String> merchantIds, Set<Long> receiptIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.date(MATERIAL_RECYCLE.CREATED_AT).as("trendDate"),
                        QueryMethods.sum(MATERIAL_RECYCLE.TOTAL_PRICE).as("recycleAmount"),
                        QueryMethods.sum(MATERIAL_RECYCLE.NUM).as("recycleNum"),
                        QueryMethods.sum(MATERIAL_RECYCLE.TOTAL_NET_GOLD_WEIGHT).as("goldWeight"),
                        QueryMethods.sum(MATERIAL_RECYCLE.TOTAL_NET_SILVER_WEIGHT).as("silverWeight"))
                .from(MATERIAL_RECYCLE)
                .where(MATERIAL_RECYCLE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(MATERIAL_RECYCLE.CREATED_AT.between(startDateTime, endDateTime))
                .and(MATERIAL_RECYCLE.MERCHANT_ID.in(merchantIds))
                .and(MATERIAL_RECYCLE.ID.in(receiptIds, !receiptIds.isEmpty()))
                .groupBy(QueryMethods.date(MATERIAL_RECYCLE.CREATED_AT))
                .orderBy(QueryMethods.date(MATERIAL_RECYCLE.CREATED_AT), true);

        List<TrendDataBo> results = materialRecycleMapper.selectListByQueryAs(wrapper, TrendDataBo.class);
        
        // 创建数据映射
        Map<String, TrendDataBo> dataMap = new HashMap<>();
        for (TrendDataBo result : results) {
            dataMap.put(result.getTrendDate(), result);
        }
        
        List<CoordinateVo> trends = new ArrayList<>();
        List<LocalDate> dateRange = generateDateRange(startDateTime.toLocalDate(), endDateTime.toLocalDate());
        
        for (LocalDate date : dateRange) {
            String dateStr = date.toString();
            TrendDataBo result = dataMap.get(dateStr);
            
            BigDecimal recycleAmount = BigDecimal.ZERO;
            Integer recycleNum = 0;
            BigDecimal goldWeight = BigDecimal.ZERO;
            BigDecimal silverWeight = BigDecimal.ZERO;
            
            if (result != null) {
                recycleAmount = PriceUtil.fen2yuan(result.getRecycleAmount());
                recycleNum = result.getRecycleNum();
                goldWeight = result.getGoldWeight();
                silverWeight = result.getSilverWeight();
            }
            
            List<ReportVo> children = new ArrayList<>();
            children.add(ReportVo.builder().index("1").label("回收金额").num(recycleAmount.toPlainString()).build());
            children.add(ReportVo.builder().index("2").label("回收总数").num(String.valueOf(recycleNum)).build());
            children.add(ReportVo.builder().index("3").label("金重").num(goldWeight.toPlainString()).build());
            children.add(ReportVo.builder().index("4").label("银重").num(silverWeight.toPlainString()).build());
            
            trends.add(new CoordinateVo(dateStr, children));
        }
        
        return trends;
    }

    /**
     * 获取按月回收趋势数据
     */
    private List<CoordinateVo> getRecycleTrendsByMonth(LocalDateTime startDateTime, LocalDateTime endDateTime, List<String> merchantIds, Set<Long> receiptIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column("DATE_FORMAT(created_at, '%Y-%m') as trendMonth"),
                        QueryMethods.sum(MATERIAL_RECYCLE.TOTAL_PRICE).as("recycleAmount"),
                        QueryMethods.sum(MATERIAL_RECYCLE.NUM).as("recycleNum"),
                        QueryMethods.sum(MATERIAL_RECYCLE.TOTAL_NET_GOLD_WEIGHT).as("goldWeight"),
                        QueryMethods.sum(MATERIAL_RECYCLE.TOTAL_NET_SILVER_WEIGHT).as("silverWeight"))
                .from(MATERIAL_RECYCLE)
                .where(MATERIAL_RECYCLE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(MATERIAL_RECYCLE.CREATED_AT.between(startDateTime, endDateTime))
                .and(MATERIAL_RECYCLE.MERCHANT_ID.in(merchantIds))
                .and(MATERIAL_RECYCLE.ID.in(receiptIds, !receiptIds.isEmpty()))
                .groupBy("DATE_FORMAT(created_at, '%Y-%m')")
                .orderBy("DATE_FORMAT(created_at, '%Y-%m')", true);

        List<TrendDataBo> results = materialRecycleMapper.selectListByQueryAs(wrapper, TrendDataBo.class);
        
        // 创建数据映射
        Map<String, TrendDataBo> dataMap = new HashMap<>();
        for (TrendDataBo result : results) {
            dataMap.put(result.getTrendMonth(), result);
        }
        
        List<CoordinateVo> trends = new ArrayList<>();
        List<String> monthRange = generateMonthRange(startDateTime.toLocalDate(), endDateTime.toLocalDate());
        
        for (String month : monthRange) {
            TrendDataBo result = dataMap.get(month);
            
            BigDecimal recycleAmount = BigDecimal.ZERO;
            Integer recycleNum = 0;
            BigDecimal goldWeight = BigDecimal.ZERO;
            BigDecimal silverWeight = BigDecimal.ZERO;
            
            if (result != null) {
                recycleAmount = PriceUtil.fen2yuan(result.getRecycleAmount());
                recycleNum = result.getRecycleNum();
                goldWeight = result.getGoldWeight();
                silverWeight = result.getSilverWeight();
            }
            
            List<ReportVo> children = new ArrayList<>();
            children.add(ReportVo.builder().index("1").label("回收金额").num(recycleAmount.toPlainString()).build());
            children.add(ReportVo.builder().index("2").label("回收总数").num(String.valueOf(recycleNum)).build());
            children.add(ReportVo.builder().index("3").label("金重").num(goldWeight.toPlainString()).build());
            children.add(ReportVo.builder().index("4").label("银重").num(silverWeight.toPlainString()).build());
            
            trends.add(new CoordinateVo(month, children));
        }
        
        return trends;
    }

    /**
     * 填充退货趋势图数据
     */
    private void fillReturnTrends(SoldDataReportVo vo, LocalDateTime[] currentPeriod, List<String> merchantIds, Set<Long> receiptIds) {
        vo.setReturnTrendListByDay(getReturnTrendsByDay(currentPeriod[0], currentPeriod[1], merchantIds, receiptIds));
        vo.setReturnTrendListByMonth(getReturnTrendsByMonth(currentPeriod[0], currentPeriod[1], merchantIds, receiptIds));
    }

    /**
     * 获取按日退货趋势数据
     */
    private List<CoordinateVo> getReturnTrendsByDay(LocalDateTime startDateTime, LocalDateTime endDateTime, List<String> merchantIds, Set<Long> receiptIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        QueryMethods.date(SOLD_RETURN.CREATED_AT).as("trendDate"),
                        QueryMethods.sum(SOLD_RETURN.TOTAL_PAY_PRICE).as("returnAmount"),
                        QueryMethods.sum(SOLD_RETURN.NUM).as("returnNum"),
                        QueryMethods.sum(SOLD_RETURN.TOTAL_NET_GOLD_WEIGHT).as("goldWeight"),
                        QueryMethods.sum(SOLD_RETURN.TOTAL_NET_SILVER_WEIGHT).as("silverWeight"))
                .from(SOLD_RETURN)
                .where(SOLD_RETURN.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RETURN.CREATED_AT.between(startDateTime, endDateTime))
                .and(SOLD_RETURN.MERCHANT_ID.in(merchantIds))
                .and(SOLD_RETURN.ID.in(receiptIds, !receiptIds.isEmpty()))
                .groupBy(QueryMethods.date(SOLD_RETURN.CREATED_AT))
                .orderBy(QueryMethods.date(SOLD_RETURN.CREATED_AT), true);

        List<TrendDataBo> results = soldReturnMapper.selectListByQueryAs(wrapper, TrendDataBo.class);

        // 创建数据映射
        Map<String, TrendDataBo> dataMap = new HashMap<>();
        for (TrendDataBo result : results) {
            dataMap.put(result.getTrendDate(), result);
        }

        List<CoordinateVo> trends = new ArrayList<>();
        List<LocalDate> dateRange = generateDateRange(startDateTime.toLocalDate(), endDateTime.toLocalDate());

        for (LocalDate date : dateRange) {
            String dateStr = date.toString();
            TrendDataBo result = dataMap.get(dateStr);

            BigDecimal returnAmount = BigDecimal.ZERO;
            Integer returnNum = 0;
            BigDecimal goldWeight = BigDecimal.ZERO;
            BigDecimal silverWeight = BigDecimal.ZERO;

            if (result != null) {
                returnAmount = PriceUtil.fen2yuan(result.getReturnAmount());
                returnNum = result.getReturnNum();
                goldWeight = result.getGoldWeight();
                silverWeight = result.getSilverWeight();
            }

            List<ReportVo> children = new ArrayList<>();
            children.add(ReportVo.builder().index("1").label("退货金额").num(returnAmount.toPlainString()).build());
            children.add(ReportVo.builder().index("2").label("退货总数").num(String.valueOf(returnNum)).build());
            children.add(ReportVo.builder().index("3").label("金重").num(goldWeight.toPlainString()).build());
            children.add(ReportVo.builder().index("4").label("银重").num(silverWeight.toPlainString()).build());

            trends.add(new CoordinateVo(dateStr, children));
        }

        return trends;
    }

    /**
     * 获取按月退货趋势数据
     */
    private List<CoordinateVo> getReturnTrendsByMonth(LocalDateTime startDateTime, LocalDateTime endDateTime, List<String> merchantIds, Set<Long> receiptIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(QueryMethods.column("DATE_FORMAT(created_at, '%Y-%m') as trendMonth"),
                        QueryMethods.sum(SOLD_RETURN.TOTAL_PAY_PRICE).as("returnAmount"),
                        QueryMethods.sum(SOLD_RETURN.NUM).as("returnNum"),
                        QueryMethods.sum(SOLD_RETURN.TOTAL_NET_GOLD_WEIGHT).as("goldWeight"),
                        QueryMethods.sum(SOLD_RETURN.TOTAL_NET_SILVER_WEIGHT).as("silverWeight"))
                .from(SOLD_RETURN)
                .where(SOLD_RETURN.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RETURN.CREATED_AT.between(startDateTime, endDateTime))
                .and(SOLD_RETURN.MERCHANT_ID.in(merchantIds))
                .and(SOLD_RETURN.ID.in(receiptIds, !receiptIds.isEmpty()))
                .groupBy("DATE_FORMAT(created_at, '%Y-%m')")
                .orderBy("DATE_FORMAT(created_at, '%Y-%m')", true);

        List<TrendDataBo> results = soldReturnMapper.selectListByQueryAs(wrapper, TrendDataBo.class);
        
        // 创建数据映射
        Map<String, TrendDataBo> dataMap = new HashMap<>();
        for (TrendDataBo result : results) {
            dataMap.put(result.getTrendMonth(), result);
        }
        
        List<CoordinateVo> trends = new ArrayList<>();
        List<String> monthRange = generateMonthRange(startDateTime.toLocalDate(), endDateTime.toLocalDate());
        
        for (String month : monthRange) {
            TrendDataBo result = dataMap.get(month);
            
            BigDecimal returnAmount = BigDecimal.ZERO;
            Integer returnNum = 0;
            BigDecimal goldWeight = BigDecimal.ZERO;
            BigDecimal silverWeight = BigDecimal.ZERO;
            
            if (result != null) {
                returnAmount = PriceUtil.fen2yuan(result.getReturnAmount());
                returnNum = result.getReturnNum();
                goldWeight = result.getGoldWeight();
                silverWeight = result.getSilverWeight();
            }
            
            List<ReportVo> children = new ArrayList<>();
            children.add(ReportVo.builder().index("1").label("退货金额").num(returnAmount.toPlainString()).build());
            children.add(ReportVo.builder().index("2").label("退货总数").num(String.valueOf(returnNum)).build());
            children.add(ReportVo.builder().index("3").label("金重").num(goldWeight.toPlainString()).build());
            children.add(ReportVo.builder().index("4").label("银重").num(silverWeight.toPlainString()).build());
            
            trends.add(new CoordinateVo(month, children));
        }
        
        return trends;
    }

    /**
     * 填充货品大类占比图数据
     */
    private void fillSoldCategoryPieChart(SoldDataReportVo vo, LocalDateTime[] currentPeriod, List<String> merchantIds, Set<Long> receiptIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        GOODS.CATEGORY_ID.as("categoryId"),
                        SOLD_RECEIPT_GOODS.SALE_TYPE.as("salesType"),
                        QueryMethods.sum(SOLD_RECEIPT_GOODS.REAL_AMOUNT).as("amount"))
                .from(SOLD_RECEIPT_GOODS)
                .leftJoin(SOLD_RECEIPT).on(SOLD_RECEIPT_GOODS.SOLD_RECEIPT_ID.eq(SOLD_RECEIPT.ID))
                .leftJoin(GOODS).on(SOLD_RECEIPT_GOODS.GOODS_ID.eq(GOODS.ID))
                .where(SOLD_RECEIPT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RECEIPT.STATUS.in(List.of(1, 2, 3)))
                .and(SOLD_RECEIPT.SOLD_AT.between(currentPeriod[0], currentPeriod[1]))
                .and(SOLD_RECEIPT.MERCHANT_ID.in(merchantIds))
                .and(SOLD_RECEIPT.ID.in(receiptIds, !receiptIds.isEmpty()))
                .groupBy(GOODS.CATEGORY_ID)
                .groupBy(SOLD_RECEIPT_GOODS.SALE_TYPE);

        List<CategoryDistributionBo> results = soldReceiptGoodsMapper.selectListByQueryAs(wrapper, CategoryDistributionBo.class);

        // 计算总金额
        BigDecimal totalAmount = results.stream()
                .map(result -> result.getAmount() != null ? result.getAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        List<ReportVo> pieData = new ArrayList<>();
        
        // 定义剩余百分比
        BigDecimal remainingPercent = new BigDecimal("100");
        
        for (int i = 0; i < results.size(); i++) {
            CategoryDistributionBo result = results.get(i);
            if (result.getCategoryId() == null || result.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            Integer categoryId = result.getCategoryId();
            
            // 计算百分比
            String percent;
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                if (i == results.size() - 1) {
                    // 最后一个元素，直接使用剩余百分比
                    percent = remainingPercent.setScale(2, RoundingMode.HALF_UP).toPlainString() + "%";
                } else {
                    // 计算当前百分比
                    BigDecimal currentPercent = result.getAmount().divide(totalAmount, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                    percent = currentPercent.toPlainString() + "%";
                    // 更新剩余百分比
                    remainingPercent = remainingPercent.subtract(currentPercent);
                }
            } else {
                percent = "0.00%";
            }
            
            BigDecimal amount = PriceUtil.fen2yuan(result.getAmount());

            pieData.add(ReportVo.builder()
                    .index(categoryId.toString())
                    .label(CategoryEnum.getByValue(categoryId.longValue()).getLabel() + "(" + (result.getSalesType().equals(1) ? "克" : "件") + ")")
                    .num(amount.toPlainString())
                    .percent(percent)
                    .build());
        }
        vo.setSoldCategoryPieList(pieData);
    }

    /**
     * 填充回收旧料大类占比图数据
     * 直接基于old_material表，按大类统计旧料回收金额分布
     */
    private void fillRecycleCategoryPieChart(SoldDataReportVo vo, LocalDateTime[] currentPeriod, List<String> merchantIds, Set<Long> receiptIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        OLD_MATERIAL.CATEGORY_ID.as("categoryId"),
                        OLD_MATERIAL.SALES_TYPE.as("salesType"),
                        QueryMethods.sum(OLD_MATERIAL.RECYCLE_PRICE.multiply(OLD_MATERIAL.NUM)).as("amount"))
                .from(OLD_MATERIAL)
                .where(OLD_MATERIAL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(OLD_MATERIAL.CREATED_AT.between(currentPeriod[0], currentPeriod[1]))
                .and(OLD_MATERIAL.MERCHANT_ID.in(merchantIds))
                .groupBy(OLD_MATERIAL.CATEGORY_ID)
                .groupBy(OLD_MATERIAL.SALES_TYPE);
            wrapper.and(QueryMethods.exists(QueryWrapper.create()
                    .from(MATERIAL_RECYCLE_DETAIL)
                    .where(MATERIAL_RECYCLE_DETAIL.MATERIAL_ID.eq(OLD_MATERIAL.ID))
                    .where(MATERIAL_RECYCLE_DETAIL.RECYCLE_ID.in(receiptIds, !receiptIds.isEmpty()))));
        List<CategoryDistributionBo> results = oldMaterialMapper.selectListByQueryAs(wrapper, CategoryDistributionBo.class);

        // 计算总金额
        BigDecimal totalAmount = results.stream()
                .map(result -> result.getAmount() != null ? result.getAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        List<ReportVo> pieData = new ArrayList<>();
        
        // 定义剩余百分比
        BigDecimal remainingPercent = new BigDecimal("100");
        
        for (int i = 0; i < results.size(); i++) {
            CategoryDistributionBo result = results.get(i);
            if (result.getCategoryId() == null || result.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            Integer categoryId = result.getCategoryId();
            
            // 计算百分比
            String percent;
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                if (i == results.size() - 1) {
                    // 最后一个元素，直接使用剩余百分比
                    percent = remainingPercent.setScale(2, RoundingMode.HALF_UP).toPlainString() + "%";
                } else {
                    // 计算当前百分比
                    BigDecimal currentPercent = result.getAmount().divide(totalAmount, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                    percent = currentPercent.toPlainString() + "%";
                    // 更新剩余百分比
                    remainingPercent = remainingPercent.subtract(currentPercent);
                }
            } else {
                percent = "0.00%";
            }
            
            BigDecimal amount = PriceUtil.fen2yuan(result.getAmount());
            pieData.add(ReportVo.builder()
                    .index(categoryId.toString())
                    .label(CategoryEnum.getByValue(categoryId.longValue()).getLabel() + "(" + (result.getSalesType().equals(1) ? "克" : "件") + ")")
                    .num(amount.toPlainString())
                    .percent(percent)
                    .build());
        }
        vo.setRecycleCategoryPieList(pieData);
    }

    /**
     * 填充退货货品大类占比图数据
     * 基于sold_return_detail表和goods表，按大类统计退货数量
     */
    private void fillReturnCategoryPieChart(SoldDataReportVo vo, LocalDateTime[] currentPeriod, List<String> merchantIds, Set<Long> receiptIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(
                        GOODS.CATEGORY_ID.as("categoryId"),
                        SOLD_RETURN_DETAIL.SALES_TYPE.as("salesType"),
                        QueryMethods.sum(SOLD_RETURN_DETAIL.PAY_PRICE).as("amount"))
                .from(SOLD_RETURN_DETAIL)
                .leftJoin(GOODS).on(SOLD_RETURN_DETAIL.GOODS_ID.eq(GOODS.ID))
                .where(SOLD_RETURN_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .and(SOLD_RETURN_DETAIL.CREATED_AT.between(currentPeriod[0], currentPeriod[1]))
                .and(SOLD_RETURN_DETAIL.MERCHANT_ID.in(merchantIds))
                .and(SOLD_RETURN_DETAIL.RETURN_ID.in(receiptIds, !receiptIds.isEmpty()))
                .groupBy(GOODS.CATEGORY_ID)
                .groupBy(SOLD_RETURN_DETAIL.SALES_TYPE);

        List<CategoryDistributionBo> results = soldReturnDetailMapper.selectListByQueryAs(wrapper, CategoryDistributionBo.class);

        // 计算总金额
        BigDecimal totalAmount = results.stream()
                .map(result -> result.getAmount() != null ? result.getAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        List<ReportVo> pieData = new ArrayList<>();
        
        // 定义剩余百分比
        BigDecimal remainingPercent = new BigDecimal("100");
        
        for (int i = 0; i < results.size(); i++) {
            CategoryDistributionBo result = results.get(i);
            if (result.getCategoryId() == null || result.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            Integer categoryId = result.getCategoryId();
            
            // 计算百分比
            String percent;
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                if (i == results.size() - 1) {
                    // 最后一个元素，直接使用剩余百分比
                    percent = remainingPercent.setScale(2, RoundingMode.HALF_UP).toPlainString() + "%";
                } else {
                    // 计算当前百分比
                    BigDecimal currentPercent = result.getAmount().divide(totalAmount, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                    percent = currentPercent.toPlainString() + "%";
                    // 更新剩余百分比
                    remainingPercent = remainingPercent.subtract(currentPercent);
                }
            } else {
                percent = "0.00%";
            }
            
            BigDecimal amount = PriceUtil.fen2yuan(result.getAmount());
            pieData.add(ReportVo.builder()
                    .index(categoryId.toString())
                    .label(CategoryEnum.getByValue(categoryId.longValue()).getLabel() + "(" + (result.getSalesType().equals(1) ? "克" : "件") + ")")
                    .num(amount.toPlainString())
                    .percent(percent)
                    .build());
        }
        vo.setReturnCategoryPieList(pieData);
    }




    /**
     * 根据库龄天数计算对应的标签
     */
    private String calculateAgeLabel(long daysDiff) {
        if (daysDiff <= 30) {
            return "0-30天";
        } else if (daysDiff <= 60) {
            return "31-60天";
        } else if (daysDiff <= 90) {
            return "61-90天";
        } else if (daysDiff <= 180) {
            return "91-180天";
        } else {
            return "180天以上";
        }
    }

    /**
     * 计算单个货品的价值（基于实时金价或成本价）
     */
    private BigDecimal calculateGoodsValue(GoodsEntity goods, PriceInfo priceInfo) {
        if (goods.getCategoryId() == null) {
            // 如果没有分类，使用成本价计算
            return getCostPriceValue(goods);
        }

        Integer categoryId = goods.getCategoryId();
        BigDecimal netGoldWeight = goods.getNetGoldWeight() != null ? goods.getNetGoldWeight() : BigDecimal.ZERO;
        BigDecimal netSilverWeight = goods.getNetSilverWeight() != null ? goods.getNetSilverWeight() : BigDecimal.ZERO;
        BigDecimal stockNumDecimal = new BigDecimal(goods.getStockNum());

        // 根据分类计算价值
        if (CategoryEnum.SILVER.getValue().equals(categoryId.longValue())) {
            // 银类
            if (priceInfo.hasSilverPrice()) {
                BigDecimal silverPrice = new BigDecimal(priceInfo.silverPrice);
                return netSilverWeight.multiply(silverPrice).multiply(stockNumDecimal);
            }
        } else if (CategoryEnum.GOLD.getValue().equals(categoryId.longValue())) {
            // 金类
            if (priceInfo.hasGoldPrice()) {
                BigDecimal goldPrice = new BigDecimal(priceInfo.goldPrice);
                return netGoldWeight.multiply(goldPrice).multiply(stockNumDecimal);
            }
        } else if (CategoryEnum.GOLD_SILVER.getValue().equals(categoryId.longValue())) {
            // 金银类
            if (priceInfo.hasGoldPrice() && priceInfo.hasSilverPrice()) {
                BigDecimal goldPrice = new BigDecimal(priceInfo.goldPrice);
                BigDecimal silverPrice = new BigDecimal(priceInfo.silverPrice);
                BigDecimal goldValue = netGoldWeight.multiply(goldPrice).multiply(stockNumDecimal);
                BigDecimal silverValue = netSilverWeight.multiply(silverPrice).multiply(stockNumDecimal);
                return goldValue.add(silverValue);
            }
        } else if (CategoryEnum.PLATINUM.getValue().equals(categoryId.longValue())) {
            // 铂金类
            if (priceInfo.hasPlatinumPrice()) {
                BigDecimal platinumPrice = new BigDecimal(priceInfo.platinumPrice);
                return netGoldWeight.multiply(platinumPrice).multiply(stockNumDecimal);
            }
        }

        // 如果没有对应的实时金价，使用成本价
        return getCostPriceValue(goods);
    }

    /**
     * 使用成本价计算货品价值
     */
    private BigDecimal getCostPriceValue(GoodsEntity goods) {
        long costPriceFen = goods.getCostPrice() != null ? goods.getCostPrice() : 0L;
        BigDecimal costPrice = new BigDecimal(costPriceFen);
        return costPrice.multiply(new BigDecimal(goods.getStockNum())).divide(new BigDecimal("100.0"), 2, RoundingMode.HALF_UP);
    }
}