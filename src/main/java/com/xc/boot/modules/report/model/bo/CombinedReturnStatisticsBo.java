package com.xc.boot.modules.report.model.bo;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 合并退货统计数据BO
 * 用于接收同时包含当期和上期数据的SQL查询结果
 */
@Data
public class CombinedReturnStatisticsBo {
    
    // 当期数据
    private Integer currentOrderNum = 0;
    private Long currentAmount = 0L;
    private Long currentDepreciation = 0L;
    private Integer currentNum = 0;
    private BigDecimal currentGoldWeight = BigDecimal.ZERO;
    private BigDecimal currentSilverWeight = BigDecimal.ZERO;
    
    // 上期数据
    private Integer previousOrderNum = 0;
    private Long previousAmount = 0L;
    private Long previousDepreciation = 0L;
    private Integer previousNum = 0;
    private BigDecimal previousGoldWeight = BigDecimal.ZERO;
    private BigDecimal previousSilverWeight = BigDecimal.ZERO;
} 