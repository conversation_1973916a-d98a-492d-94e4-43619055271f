package com.xc.boot.modules.report.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

@Data
public class SoldDataReportQuery {
    @Schema(description = "日期间隔 eg:\"range\": [\"2025-07-20\",\"2025-07-25\"]")
    @NotNull(message = "日期不能为空")
    private LocalDate[] range;

    @Schema(description = "门店ID，多个用逗号分隔")
    private String merchantIds;

    @Schema(description = "大类ID，多个用逗号分隔")
    private String categoryIds;
} 