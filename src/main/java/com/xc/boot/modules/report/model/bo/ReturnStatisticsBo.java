package com.xc.boot.modules.report.model.bo;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 退货统计数据BO
 * 用于接收退货相关的SQL查询结果
 */
@Data
public class ReturnStatisticsBo {
    
    /**
     * 退货单数量
     */
    private Integer orderNum = 0;
    
    /**
     * 退货金额(分)
     */
    private Long amount = 0L;
    
    /**
     * 折旧费(分)
     */
    private Long depreciation = 0L;
    
    /**
     * 退货总数
     */
    private Integer num = 0;
    
    /**
     * 退货金重(g)
     */
    private BigDecimal goldWeight = BigDecimal.ZERO;
    
    /**
     * 退货银重(g)
     */
    private BigDecimal silverWeight = BigDecimal.ZERO;
} 