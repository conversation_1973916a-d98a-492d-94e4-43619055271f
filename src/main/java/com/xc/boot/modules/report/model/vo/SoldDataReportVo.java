package com.xc.boot.modules.report.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "销售统计vo")
@Accessors(chain = true)
public class SoldDataReportVo {
    // 销售统计
    @Schema(description = "总订单数")
    private String totalOrderNum = "0";

    @Schema(description = "总订单数环比增长率")
    private String totalOrderNumGrowth = "0.00%";

    @Schema(description = "货品总金额")
    private String totalGoodsAmount = "0.00";

    @Schema(description = "货品总金额环比增长率")
    private String totalGoodsAmountGrowth = "0.00%";

    @Schema(description = "实收金额")
    private String totalReceivedAmount = "0.00";

    @Schema(description = "实收金额环比增长率")
    private String totalReceivedAmountGrowth = "0.00%";

    @Schema(description = "优惠金额")
    private String totalDiscountAmount = "0.00";

    @Schema(description = "优惠金额环比增长率")
    private String totalDiscountAmountGrowth = "0.00%";

    @Schema(description = "旧料抵扣")
    private String totalOldMaterialDeduction = "0.00";

    @Schema(description = "旧料抵扣环比增长率")
    private String totalOldMaterialDeductionGrowth = "0.00%";

    @Schema(description = "货品毛利")
    private String totalGoodsProfit = "0.00";

    @Schema(description = "货品毛利环比增长率")
    private String totalGoodsProfitGrowth = "0.00%";

    @Schema(description = "货品总数")
    private String totalGoodsNum = "0";

    @Schema(description = "货品总数环比增长率")
    private String totalGoodsNumGrowth = "0.00%";

    @Schema(description = "货品金重(g)")
    private BigDecimal totalGoodsGoldWeight = BigDecimal.ZERO;

    @Schema(description = "货品金重环比增长率")
    private String totalGoodsGoldWeightGrowth = "0.00%";

    @Schema(description = "货品银重(g)")
    private BigDecimal totalGoodsSilverWeight = BigDecimal.ZERO;

    @Schema(description = "货品银重环比增长率")
    private String totalGoodsSilverWeightGrowth = "0.00%";

    @Schema(description = "旧料总数")
    private String totalOldMaterialNum = "0";

    @Schema(description = "旧料总数环比增长率")
    private String totalOldMaterialNumGrowth = "0.00%";

    @Schema(description = "旧料金重(g)")
    private BigDecimal totalOldMaterialGoldWeight = BigDecimal.ZERO;

    @Schema(description = "旧料金重环比增长率")
    private String totalOldMaterialGoldWeightGrowth = "0.00%";

    @Schema(description = "旧料银重(g)")
    private BigDecimal totalOldMaterialSilverWeight = BigDecimal.ZERO;

    @Schema(description = "旧料银重环比增长率")
    private String totalOldMaterialSilverWeightGrowth = "0.00%";

    // 回收统计
    @Schema(description = "回收单总数")
    private String recycleOrderNum = "0";

    @Schema(description = "回收单总数环比增长率")
    private String recycleOrderNumGrowth = "0.00%";

    @Schema(description = "回收金额")
    private String recycleAmount = "0.00";

    @Schema(description = "回收金额环比增长率")
    private String recycleAmountGrowth = "0.00%";

    @Schema(description = "回收旧料总数")
    private String recycleOldMaterialNum = "0";

    @Schema(description = "回收旧料总数环比增长率")
    private String recycleOldMaterialNumGrowth = "0.00%";

    @Schema(description = "回收旧料金重(g)")
    private BigDecimal recycleOldMaterialGoldWeight = BigDecimal.ZERO;

    @Schema(description = "回收旧料金重环比增长率")
    private String recycleOldMaterialGoldWeightGrowth = "0.00%";

    @Schema(description = "回收旧料银重(g)")
    private BigDecimal recycleOldMaterialSilverWeight = BigDecimal.ZERO;

    @Schema(description = "回收旧料银重环比增长率")
    private String recycleOldMaterialSilverWeightGrowth = "0.00%";

    // 退货统计
    @Schema(description = "退货单总数")
    private String returnOrderNum = "0";

    @Schema(description = "退货单总数环比增长率")
    private String returnOrderNumGrowth = "0.00%";

    @Schema(description = "退货金额")
    private String returnAmount = "0.00";

    @Schema(description = "退货金额环比增长率")
    private String returnAmountGrowth = "0.00%";

    @Schema(description = "折旧费")
    private String depreciationFee = "0.00";

    @Schema(description = "折旧费环比增长率")
    private String depreciationFeeGrowth = "0.00%";

    @Schema(description = "退货数量")
    private String returnGoodsNum = "0";

    @Schema(description = "退货数量环比增长率")
    private String returnGoodsNumGrowth = "0.00%";

    @Schema(description = "退货金重(g)")
    private BigDecimal returnGoodsGoldWeight = BigDecimal.ZERO;

    @Schema(description = "退货金重环比增长率")
    private String returnGoodsGoldWeightGrowth = "0.00%";

    @Schema(description = "退货银重(g)")
    private BigDecimal returnGoodsSilverWeight = BigDecimal.ZERO;
    
    @Schema(description = "退货银重环比增长率")
    private String returnGoodsSilverWeightGrowth = "0.00%";

    // 销售趋势图（按日/月）
    @Schema(description = "销售趋势图,包含销售额、订单数、货品数量、金重、银重等趋势")
    private List<CoordinateVo> soldTrendListByDay;

    @Schema(description = "销售趋势图,包含销售额、订单数、货品数量、金重、银重等趋势")
    private List<CoordinateVo> soldTrendListByMonth;

    // 回收趋势图（按日/月）
    @Schema(description = "回收趋势图")
    private List<CoordinateVo> recycleTrendListByDay;

    @Schema(description = "回收趋势图")
    private List<CoordinateVo> recycleTrendListByMonth;

    // 退货趋势图（按日/月）
    @Schema(description = "退货趋势图")
    private List<CoordinateVo> returnTrendListByDay;

    @Schema(description = "退货趋势图")
    private List<CoordinateVo> returnTrendListByMonth;

    // 旧料大类占比图（饼图）
    @Schema(description = "旧料大类占比图（饼图）")
    private List<ReportVo> recycleCategoryPieList;

    // 货品大类占比图（饼图）
    @Schema(description = "货品大类占比图（饼图），展示各大类和销售方式货品的销售数量或金额占比")
    private List<ReportVo> soldCategoryPieList;

    // 退货货品大类占比图（饼图）
    @Schema(description = "退货货品大类占比图（饼图）")
    private List<ReportVo> returnCategoryPieList;
}
