package com.xc.boot.modules.report.model.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 分类分布数据BO
 * 用于接收大类分布、小类分布相关的SQL查询结果
 */
@Data
public class CategoryDistributionBo {
    
    /**
     * 分类ID
     */
    private Integer categoryId;
    
    /**
     * 小类ID
     */
    private Integer subclassId;

    /**
     * 销售方式
     */
    private Integer salesType;

    /**
     * 金额
     */
    private BigDecimal amount = BigDecimal.ZERO;

    /**
     * 数量
     */
    private Integer num = 0;
    
    /**
     * 销售数量
     */
    private Integer salesNum = 0;
    
    /**
     * 退货数量
     */
    private Integer returnNum = 0;
    
    /**
     * 总数量（用于旧料统计）
     */
    private Integer totalNum = 0;
} 