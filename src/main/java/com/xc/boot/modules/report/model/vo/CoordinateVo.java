package com.xc.boot.modules.report.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CoordinateVo {
    @Schema(description = "名称")
    private String label;
    @Schema(description = "子集")
    private List<ReportVo> children;
}
