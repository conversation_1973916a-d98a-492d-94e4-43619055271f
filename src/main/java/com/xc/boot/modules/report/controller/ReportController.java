package com.xc.boot.modules.report.controller;

import com.xc.boot.common.result.Result;
import com.xc.boot.modules.report.model.query.SoldDataReportQuery;
import com.xc.boot.modules.report.model.vo.GoodsDataReportVo;
import com.xc.boot.modules.report.model.query.GoodsDataReportQuery;
import com.xc.boot.modules.report.model.vo.SoldDataReportVo;
import com.xc.boot.modules.report.service.DataReportService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/report")
@Tag(name = "数据报表-商户端报表接口", description = "报表相关接口")
public class ReportController {
    private final DataReportService dataReportService;

    @PostMapping("/goods")
    public Result<GoodsDataReportVo> goodsDataReport(@RequestBody GoodsDataReportQuery query) {
        GoodsDataReportVo vo = dataReportService.goodsDataReport(query);
        return Result.success(vo);
    }

    @PostMapping("/sold")
    public Result<SoldDataReportVo> soldDataReport(@RequestBody @Validated SoldDataReportQuery query) {
        SoldDataReportVo vo = dataReportService.soldDataReport(query);
        return Result.success(vo);
    }
}
