package com.xc.boot.modules.report.model.bo;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 合并销售统计数据BO
 * 用于接收同时包含当期和上期数据的SQL查询结果
 */
@Data
public class CombinedSalesStatisticsBo {
    
    // 当期数据
    private Integer currentOrderNum = 0;
    private Long currentGoodsAmount = 0L;
    private Long currentReceivedAmount = 0L;
    private Long currentDiscountAmount = 0L;
    private Long currentDeductionAmount = 0L;
    private Integer currentGoodsNum = 0;
    private BigDecimal currentGoldWeight = BigDecimal.ZERO;
    private BigDecimal currentSilverWeight = BigDecimal.ZERO;
    private Integer currentOldMaterialNum = 0;
    private BigDecimal currentOldMaterialGoldWeight = BigDecimal.ZERO;
    private BigDecimal currentOldMaterialSilverWeight = BigDecimal.ZERO;
    private Long currentTotalSalesAmount = 0L;
    private Long currentTotalCostAmount = 0L;
    
    // 上期数据
    private Integer previousOrderNum = 0;
    private Long previousGoodsAmount = 0L;
    private Long previousReceivedAmount = 0L;
    private Long previousDiscountAmount = 0L;
    private Long previousDeductionAmount = 0L;
    private Integer previousGoodsNum = 0;
    private BigDecimal previousGoldWeight = BigDecimal.ZERO;
    private BigDecimal previousSilverWeight = BigDecimal.ZERO;
    private Integer previousOldMaterialNum = 0;
    private BigDecimal previousOldMaterialGoldWeight = BigDecimal.ZERO;
    private BigDecimal previousOldMaterialSilverWeight = BigDecimal.ZERO;
    private Long previousTotalSalesAmount = 0L;
    private Long previousTotalCostAmount = 0L;
} 