package com.xc.boot.modules.report.service;

import com.xc.boot.modules.report.model.query.GoodsDataReportQuery;
import com.xc.boot.modules.report.model.query.SoldDataReportQuery;
import com.xc.boot.modules.report.model.vo.GoodsDataReportVo;
import com.xc.boot.modules.report.model.vo.SoldDataReportVo;

public interface DataReportService {

    GoodsDataReportVo goodsDataReport(GoodsDataReportQuery query);

    SoldDataReportVo soldDataReport(SoldDataReportQuery query);
}