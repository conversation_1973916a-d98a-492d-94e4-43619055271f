package com.xc.boot.modules.report.model.bo;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 趋势数据BO
 * 用于接收趋势图相关的SQL查询结果
 */
@Data
public class TrendDataBo {
    
    /**
     * 趋势日期 (YYYY-MM-DD)
     */
    private String trendDate;
    
    /**
     * 趋势月份 (YYYY-MM)
     */
    private String trendMonth;
    
    /**
     * 销售金额(分)
     */
    private Long salesAmount = 0L;
    
    /**
     * 订单数量
     */
    private Integer orderCount = 0;
    
    /**
     * 货品数量
     */
    private Integer goodsNum = 0;
    
    /**
     * 金重(g)
     */
    private BigDecimal goldWeight = BigDecimal.ZERO;
    
    /**
     * 银重(g)
     */
    private BigDecimal silverWeight = BigDecimal.ZERO;
    
    /**
     * 回收金额(分)
     */
    private Long recycleAmount = 0L;
    
    /**
     * 回收数量
     */
    private Integer recycleNum = 0;
    
    /**
     * 退货金额(分)
     */
    private Long returnAmount = 0L;
    
    /**
     * 退货数量
     */
    private Integer returnNum = 0;
} 