package com.xc.boot.modules.report.model.bo;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 回收统计数据BO
 * 用于接收回收相关的SQL查询结果
 */
@Data
public class RecycleStatisticsBo {
    
    /**
     * 回收单数量
     */
    private Integer orderNum = 0;
    
    /**
     * 回收金额(分)
     */
    private Long amount = 0L;
    
    /**
     * 回收旧料总数
     */
    private Integer num = 0;
    
    /**
     * 回收旧料金重(g)
     */
    private BigDecimal goldWeight = BigDecimal.ZERO;
    
    /**
     * 回收旧料银重(g)
     */
    private BigDecimal silverWeight = BigDecimal.ZERO;
} 