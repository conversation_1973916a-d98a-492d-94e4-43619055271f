package com.xc.boot.modules.report.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportVo {
    @Schema(description = "标识")
    private String index;
    @Schema(description = "名称")
    private String label;
    @Schema(description = "数量")
    private String num;
    @Schema(description = "百分比 xx.xx%")
    private String percent;
}
