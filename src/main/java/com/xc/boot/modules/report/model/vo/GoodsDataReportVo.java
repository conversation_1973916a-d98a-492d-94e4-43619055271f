package com.xc.boot.modules.report.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "货品统计vo")
@Accessors(chain = true)
public class GoodsDataReportVo {
    @Schema(description = "库存总数")
    private String totalNum = "0";

    @Schema(description = "总金重(g)")
    private BigDecimal totalGoldWeight = BigDecimal.ZERO;

    @Schema(description = "总银重(g)")
    private BigDecimal totalSilverWeight = BigDecimal.ZERO;

    @Schema(description = "总成本价(元)")
    private String totalCostPrice = "0.00";

    @Schema(description = "总价值")
    private String totalValue = "0.00";

    @Schema(description = "货品大类分布")
    private List<ReportVo> categoryList;

    @Schema(description = "货品小类分布")
    private List<ReportVo> subclassList;

    @Schema(description = "库龄分布")
    private List<ReportVo> ageList;

    @Schema(description = "库龄价值分析 index 1:库存数量|2:库存价值")
    private List<CoordinateVo> ageValueList;
}
