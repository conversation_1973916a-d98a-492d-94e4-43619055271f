package com.xc.boot.common.result;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.config.MybatisFlexConfiguration;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页响应结构体
 *
 * <AUTHOR>
 * @since 2022/2/18
 */
@Data
public class PageResult<T> implements Serializable {

    private String code;

    private Data<T> data;

    private String msg;

    private List<String> sqlDebug;

    private long total;

    public static <T> PageResult<T> success(Page<T> page) {
        PageResult<T> result = new PageResult<>();
        result.setCode(ResultCode.SUCCESS.getCode());

        Data<T> data = new Data<>();
        data.setList(page.getRecords());
        data.setTotal(page.getTotalRow());
        result.setTotal(page.getTotalRow());

        result.setData(data);
        result.setMsg(ResultCode.SUCCESS.getMsg());

        result.setSqlDebug(MybatisFlexConfiguration.getSqlDebugAndClear());
        return result;
    }

    @lombok.Data
    public static class Data<T> {

        private List<T> list;

        private long total;

    }

}
