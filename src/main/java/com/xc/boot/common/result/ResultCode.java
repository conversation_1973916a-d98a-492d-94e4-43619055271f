package com.xc.boot.common.result;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 响应码枚举
 * <p>
 * 参考阿里巴巴开发手册响应码规范
 *
 * <AUTHOR>
 * @since 2020/6/23
 **/
@AllArgsConstructor
@NoArgsConstructor
public enum ResultCode implements IResultCode, Serializable {

    SUCCESS("0", "操作成功"),

    USER_ERROR("A0001", "用户端错误"),
    REPEAT_SUBMIT_ERROR("A0002", "您的请求已提交，请不要重复提交或等待片刻再尝试。"),

    USER_LOGIN_ERROR("A0200", "用户登录异常"),

    USER_NOT_EXIST("A0201", "账号不存在或已被禁用"),
    USER_ACCOUNT_LOCKED("A0202", "账号已被禁用"),
    USER_ACCOUNT_INVALID("A0203", "用户账户已作废"),
    COMPANY_ACCOUNT_LOCKED("A0204", "商家已被禁用"),
    COMPANY_ACCOUNT_EXPIRED("A0205", "商家已过期，请联系客服续费"),
    USERNAME_USED("A0206", "该账号已被使用"),
    USERNAME_CODE_NOTFOUND("A0207", "验证码已失效,请重新获取验证码"),
    USERNAME_CODE_ERROR("A0208", "验证码错误"),
    USERNAME_CODE_BLOCK_ERROR("A0209", "错误次数过多，请两分钟后再试"),

    USERNAME_OR_PASSWORD_ERROR("A0210", "用户名或密码错误"),
    PASSWORD_ENTER_EXCEED_LIMIT("A0211", "用户输入密码次数超限"),
    CLIENT_AUTHENTICATION_FAILED("A0212", "客户端认证失败"),

    VERIFY_CODE_TIMEOUT("A0213", "验证码已过期"),
    VERIFY_CODE_ERROR("A0214", "验证码错误"),
    VERIFY_CODE_AWAIT_ERROR("A0215", "该账号发送验证码频繁，请稍后再试"),
    VERIFY_CODE_OUT_LIMIT_ERROR("A0216", "今日发送验证码次数超过限制，请联系客服"),

    TOKEN_INVALID("A0230", "登录状态过期"),
    COMPANY_STATUS_INVALID("A0231", "商家账户不可用"),
    COMPANY_EXPIRED_INVALID("A0232", "商家服务已到期"),
    TOKEN_ACCESS_FORBIDDEN("A0233", "账号已停用"),
    REPEAT_LOGIN("A0234", "账号在其他设备登录"),


    AUTHORIZED_ERROR("A0300", "访问权限异常"),
    ACCESS_UNAUTHORIZED("A0301", "访问未授权"),
    FORBIDDEN_OPERATION("A0302", "演示环境禁止新增、修改和删除数据，请本地部署后测试"),

    PERMISSION_DENIED("A0403", "无访问权限"),


    PARAM_ERROR("A0400", "用户请求参数错误"),
    RESOURCE_NOT_FOUND("A0401", "请求资源不存在"),
    PARAM_IS_NULL("A0410", "请求必填参数为空"),

    USER_UPLOAD_FILE_ERROR("A0700", "用户上传文件异常"),
    USER_UPLOAD_FILE_TYPE_NOT_MATCH("A0701", "用户上传文件类型不匹配"),
    USER_UPLOAD_FILE_SIZE_EXCEEDS("A0702", "用户上传文件太大"),
    USER_UPLOAD_IMAGE_SIZE_EXCEEDS("A0703", "用户上传图片太大"),

    SYSTEM_EXECUTION_ERROR("B0001", "系统执行出错"),
    SYSTEM_EXECUTION_TIMEOUT("B0100", "系统执行超时"),
    SYSTEM_ORDER_PROCESSING_TIMEOUT("B0100", "系统订单处理超时"),

    SYSTEM_DISASTER_RECOVERY_TRIGGER("B0200", "系统容灾功能被触发"),
    FLOW_LIMITING("B0210", "系统限流,请稍后再试"),
    DEGRADATION("B0220", "系统功能降级"),

    SYSTEM_RESOURCE_ERROR("B0300", "系统资源异常"),
    SYSTEM_RESOURCE_EXHAUSTION("B0310", "系统资源耗尽"),
    SYSTEM_RESOURCE_ACCESS_ERROR("B0320", "系统资源访问异常"),
    SYSTEM_READ_DISK_FILE_ERROR("B0321", "系统读取磁盘文件失败"),

    CALL_THIRD_PARTY_SERVICE_ERROR("C0001", "调用第三方服务出错"),
    MIDDLEWARE_SERVICE_ERROR("C0100", "中间件服务出错"),
    INTERFACE_NOT_EXIST("C0113", "接口不存在"),

    MESSAGE_SERVICE_ERROR("C0120", "消息服务出错"),
    MESSAGE_DELIVERY_ERROR("C0121", "消息投递出错"),
    MESSAGE_CONSUMPTION_ERROR("C0122", "消息消费出错"),
    MESSAGE_SUBSCRIPTION_ERROR("C0123", "消息订阅出错"),
    MESSAGE_GROUP_NOT_FOUND("C0124", "消息分组未查到"),

    DATABASE_ERROR("C0300", "数据库服务出错"),
    DATABASE_TABLE_NOT_EXIST("C0311", "表不存在"),
    DATABASE_COLUMN_NOT_EXIST("C0312", "列不存在"),
    DATABASE_DUPLICATE_COLUMN_NAME("C0321", "多表关联中存在多个相同名称的列"),
    DATABASE_DEADLOCK("C0331", "数据库死锁"),
    DATABASE_PRIMARY_KEY_CONFLICT("C0341", "主键冲突");

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    private String code;

    private String msg;

    @Override
    public String toString() {
        return "{" +
                "\"code\":\"" + code + '\"' +
                ", \"msg\":\"" + msg + '\"' +
                '}';
    }


    public static ResultCode getValue(String code) {
        for (ResultCode value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return SYSTEM_EXECUTION_ERROR; // 默认系统执行错误
    }
}
