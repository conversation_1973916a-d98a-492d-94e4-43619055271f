package com.xc.boot.common.listener;

import cn.hutool.core.util.ReflectUtil;
import com.mybatisflex.annotation.UpdateListener;
import com.xc.boot.core.security.util.SecurityUtils;

import java.lang.reflect.Method;

public class UpdatedByListener implements UpdateListener {
    @Override
    public void onUpdate(Object entity) {
        Method updatedBy = ReflectUtil.getMethodByName(entity.getClass(), "setUpdatedBy");
        if (updatedBy != null && SecurityUtils.getUserId() != null) {
            ReflectUtil.invoke(entity, "setUpdatedBy", SecurityUtils.getUserId());
        }
    }
}
