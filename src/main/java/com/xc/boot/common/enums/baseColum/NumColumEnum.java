package com.xc.boot.common.enums.baseColum;

import lombok.Getter;

/**
 * 数量字段枚举
 */
@Getter
public enum NumColumEnum {
    NUM("num",  "num", "总数"),
    STOCK_NUM("stock_num", "stockNum", "库存"),
    RETURN_NUM("return_num","returnNum", "采购退"),
    SOLD_NUM("sold_num", "soldNum", "售出"),
    TRANSFER_NUM("transfer_num",  "transferNum", "调拨中"),
    FROZEN_NUM("frozen_num",  "frozenNum", "冻结"),
    NUM_DETAIL("numDetail",  "numDetail", "数量情况");

    private final String sign;
    private final String field;
    private final String label;

    NumColumEnum(String sign, String field, String label) {
        this.sign = sign;
        this.field = field;
        this.label = label;
    }

    /**
     * 判断是否是数量字段
     */
    @SuppressWarnings("unused")
    public static boolean isNumColumn(String sign) {
        for (NumColumEnum value : values()) {
            if (value.sign.equals(sign)) {
                return true;
            }
        }
        return false;
    }
}