package com.xc.boot.common.enums.baseColum;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.modules.order.model.enums.SalesTypeEnum;

/**
 * 其余字段枚举
 */
@Getter
public enum OtherColumEnum {
    SALES_TYPE("sales_type", "销售方式"),
    WORK_PRICE_TYPE("work_price_type", "进工费计价方式"),
    SALE_WORK_PRICE_TYPE("sale_work_price_type", "销工费计价方式"),
    IMAGE("image", "图片"),
    IMAGES("images", "图片列表");

    private final String sign;
    private final String label;

    OtherColumEnum(String sign, String label) {
        this.sign = sign;
        this.label = label;
    }

    /**
     * 判断是否是其余字段
     */
    @SuppressWarnings("unused")
    public static boolean isOtherColumn(String sign) {
        for (OtherColumEnum value : values()) {
            if (value.sign.equals(sign)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取销售方式文本
     */
    public static String getSalesTypeText(String salesType) {
        if(StringUtils.isBlank(salesType)) {
            return "";
        }
        try {
            Integer value = Integer.valueOf(salesType);
            String label = IBaseEnum.getLabelByValue(value, SalesTypeEnum.class);
            return label != null ? label : "";
        } catch (NumberFormatException e) {
            return "";
        }
    }

    /**
     * 获取工费计价方式文本
     */
    public static String getWorkPriceTypeText(String workPriceType) {
        if(StringUtils.isBlank(workPriceType)) {
            return "";
        }
        try {
            Integer value = Integer.valueOf(workPriceType);
            String label = IBaseEnum.getLabelByValue(value, SalesTypeEnum.class);
            return label != null ? label : "";
        } catch (NumberFormatException e) {
            return "";
        }
    }
}