package com.xc.boot.common.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 设备端枚举
 */
@Getter
public enum SideEnum implements IBaseEnum<String> {

    PC("pc", "web端"),
    PDA("pda", "pda端"),
    MINI_APP("app", "小程序端");
    private final String value;

    private final String label;

    SideEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
