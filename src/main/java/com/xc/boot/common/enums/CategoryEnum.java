package com.xc.boot.common.enums;

import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.common.model.Option;
import com.xc.boot.shared.common.model.query.OptionQuery;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

/**
 * 商品类别枚举
 *
 * <AUTHOR>
 * @since 4.0.0
 */
@Getter
public enum CategoryEnum implements IBaseEnum<Long> {

    GOLD(1L, "黄金", "#FFD700"),
    JADE(2L, "翡翠", "#87AD87"),
    DIAMOND(3L, "钻石", "#B9F2FF"),
    GOLD_INLAY(4L, "镶嵌", "#FFA500"),
    SILVER(5L, "银饰", "#C0C0C0"),
    PLATINUM(6L, "铂金", "#E5E4E2"),
    K_GOLD(7L, "K金", "#FFA500"),
    WATCH(8L, "手表", "#E5E4E2"),
    AFRICAN_JADE(9L, "非洲翠", "#87AD87"),
    GEMSTONE(10L, "配石", "#B9F2FF"),
    GOLD_SILVER(11L, "金包银", "#FFD700");

    private final Long value;
    private final String label;
    private final String color;

    CategoryEnum(Long value, String label, String color) {
        this.value = value;
        this.label = label;
        this.color = color;
    }

    @Override
    public Long getValue() {
        return value;
    }

    @Override
    public String getLabel() {
        return label;
    }

    /**
     * 将枚举值转换为 List<Option<Long>>
     *
     * @return List<Option<Long>>
     */
    public static List<Option<Long>> toOptions(OptionQuery query) {
        Stream<CategoryEnum> values = Stream.of(values());
        if (query.getKeyword() != null) {
            values = values.filter(e -> e.getLabel().contains(query.getKeyword()));
        }
        if (query.getEcho() != null) {
            values = values.filter(e -> Set.of(query.getEcho().split(",")).contains(e.getValue().toString()));
        }
        return values.map(category -> new Option<>(category.getValue(), category.getLabel(), category.getColor())).toList();
    }

    public static CategoryEnum getByValue(Long value) {
        return Arrays.stream(values()).filter(category -> category.getValue().equals(value)).findFirst().orElse(null);
    }
}