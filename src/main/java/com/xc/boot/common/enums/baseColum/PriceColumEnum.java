package com.xc.boot.common.enums.baseColum;

import lombok.Getter;

/**
 * 价格字段枚举
 */
@Getter
public enum PriceColumEnum {
    COST_PRICE("cost_price"),
    GOLD_PRICE("gold_price"),
    SILVER_PRICE("silver_price"),
    WORK_PRICE("work_price"),
    CERT_PRICE("cert_price"),
    SALE_WORK_PRICE("sale_work_price"),
    TAG_PRICE("tag_price");

    private final String sign;

    PriceColumEnum(String sign) {
        this.sign = sign;
    }

    /**
     * 判断是否是价格字段
     */
    public static boolean isPriceColumn(String sign) {
        for (PriceColumEnum value : values()) {
            if (value.sign.equals(sign)) {
                return true;
            }
        }
        return false;
    }
}