package com.xc.boot.common.base;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 文件对象
 */
@Data
@Schema(description = "文件对象")
public class FileItemDTO {

    @Schema(description = "文件ID")
    @NotNull(message = "文件ID不能为空")
    private Long id;

    @Schema(description = "文件ID (备用)")
    private Long imageId;

    @Schema(description = "文件链接")
    private String url;
} 