package com.xc.boot.common.base;

import cn.hutool.core.lang.Assert;
import com.xc.boot.common.util.CommonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 基础分页请求对象
 *
 * <AUTHOR>
 * @since 2021/2/28
 */
@Data
@Schema
public class BasePageQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "页码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private int pageNum = 1;

    @Schema(description = "每页记录数", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private int pageSize = 10;

    @Schema(description = "导出标记")
    private Integer export = 0;

    @Schema(description = "打印标记")
    private Integer print = 0;

    @Schema(description = "ID列表")
    private List<Long> ids;

    public void setPrintNum(Long count) {
        Assert.isTrue(count <= CommonUtils.getMaxPrintSize(), String.format("打印数据条数超出最大限制%d条", CommonUtils.getMaxPrintSize()));
        pageNum = 1;
        pageSize = CommonUtils.getMaxPrintSize();
    }
}
