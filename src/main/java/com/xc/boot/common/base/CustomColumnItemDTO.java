package com.xc.boot.common.base;

import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnTypeEnum;

import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "自定义列Item")
public class CustomColumnItemDTO {
    
    @Schema(description = "字段ID")
    @NotNull(message = "字段ID不能为空")
    private Integer columnId;

    @Schema(description = "货品Id")
    private Long goodsId;

    @Schema(description = "入库单Id")
    private Long incomeId;

    @Schema(description = "入库单明细Id")
    private Long incomeDetailId;

    @Schema(description = "字段标识")
    @NotNull(message = "字段标识不能为空")
    private String columnSign;

    @Schema(description = "图片ID")
    private Integer imageId;

    @Schema(description = "字段值")
    private Object value;

    @Schema(description = "字段值")
    private String valueStr;

    // getter
    public String getValueStr() {
        if(value == null){
            return "";
        }

        GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(columnSign);

        if(column.getType().equals(GoodsColumnTypeEnum.SELECT.getValue())){
            return JSONUtil.toJsonStr(value);
        }

        return value.toString();
    }

    @Schema(description = "字段属性(1-文本 2-数字 3-多行文本 4-日期 5-下拉列表")
    @NotNull(message = "字段属性不能为空")
    private Integer type;

    @Schema(description = "机密级别(1-常规 2-敏感 3-机密)")
    private Integer secretLevel;

    @Schema(description = "小数位数")
    private Integer numberPrecision;

    @Schema(description = "下拉列")
    private String options;

    @Schema(description = "是否多选")
    private Integer isMultiple;
} 