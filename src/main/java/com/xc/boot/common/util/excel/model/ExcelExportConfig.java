package com.xc.boot.common.util.excel.model;

import com.xc.boot.system.model.enums.ImageSizeCtrlEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName ExcelExportConfig
 * @Date: 2025/6/7 09:45
 * @Description: excel导出配置类
 */
@Data
public class ExcelExportConfig {
    /**
     * sheet(如果未配置默认为fileName)
     */
    private String sheetName;

    /**
     * 标题
     */
    private String title;

    /**
     * 列宽(全局)
     */
    private Integer width = 18;

    /**
     * 如果不为空，以此为图片高度
     */
    private Integer imgHeight;

    /**
     * 如果不为空，以此为图片宽度
     */
    private Integer imgWidth;

    /**
     * 图片控制类型
     */
    private ImageSizeCtrlEnum sizeCtrlEnum;


}
