package com.xc.boot.common.util.listFill;

import cn.hutool.core.util.ReflectUtil;

import java.util.*;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @ClassName FillListUtil
 * @Date: 2025/6/2 12:20
 * @Description: list填充工具类
 */

public class ListFillUtil {
    private List<?> list;
    private Map<String, Map<String, ?>> valuesMap;
    private List<FillHandleNode> handles;
    private Consumer<Object> peek;

    private ListFillUtil() {
    }

    public static ListFillUtil of(List<?> list) {
        ListFillUtil listFillUtil = new ListFillUtil();
        listFillUtil.list = list;
        listFillUtil.valuesMap = new HashMap<>();
        listFillUtil.handles = new ArrayList<>();
        return listFillUtil;
    }

    public ListFillUtil build(FillUtilHandle handle, Set<?> keys, String keyName, String fieldName) {
        this.handles.add(new FillHandleNode(handle, keys, keyName, fieldName));
        return this;
    }

    public ListFillUtil peek (Consumer<Object> consumer) {
        peek = consumer;
        return this;
    }

    public void handle() {
        for (Object obj : list) {
            for (FillHandleNode node : handles) {
                Map<String, ?> resultMap;
                if (!valuesMap.containsKey(node.getFieldName())) {
                    resultMap = node.getHandle().getFillData(node.getKeys());
                    valuesMap.put(node.getFieldName(), resultMap);
                }else {
                    resultMap = valuesMap.getOrDefault(node.getFieldName(), new HashMap<>());
                }
                String key = Optional.ofNullable(ReflectUtil.getFieldValue(obj, node.getKeyName())).orElse("").toString();
                ReflectUtil.setFieldValue(obj, node.getFieldName(), resultMap.get(key));
            }
            if (peek != null) {
                peek.accept(obj);
            }
        }
    }
}
