package com.xc.boot.common.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.mybatisflex.core.util.LambdaGetter;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.config.interceptor.SqlStatementInterceptor;
import com.xc.boot.core.security.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.UncheckedIOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class LoadDataUtil<T>  {
    private Map<String, LambdaGetter<T>> columnMapping;
    private List<T> dataList;
    private String tableName;

    private LoadDataUtil() {
    }

    public static <T> LoadDataUtil<T> of(List<T> dataList, String tableName) {
        LoadDataUtil<T> util = new LoadDataUtil<>();
        util.columnMapping = new LinkedHashMap<>();
        util.tableName = tableName;
        util.dataList = dataList;
        return util;
    }

    public LoadDataUtil<T> addColumn(String columnName, LambdaGetter<T> getter) {
        columnMapping.put(columnName, getter);
        return this;
    }

    public void loadData() {
        Path tempFile = null;
        try {
            tempFile = Files.createTempFile(this.tableName + "_" + IdUtil.simpleUUID().substring(0, 8), ".tsv");
            try (BufferedWriter writer = Files.newBufferedWriter(tempFile, StandardCharsets.UTF_8)) {
                for (T data : dataList) {
                    writer.write(columnMapping.values().stream()
                            .map(getter -> formatValue(getter.get(data)))
                            .collect(Collectors.joining("\t")));
                    writer.newLine();
                }
            } catch (IOException e) {
                throw new UncheckedIOException(e);
            }
            String columns = String.join(", ", columnMapping.keySet());
            String loadDataSql = String.format(
                    "LOAD DATA LOCAL INFILE '%s' " +
                            "INTO TABLE %s " +
                            "CHARACTER SET utf8mb4 " +
                            "FIELDS TERMINATED BY '\\t' " +
                            "LINES TERMINATED BY '\\n' " +
                            "(%s)",
                    tempFile.toAbsolutePath().toString().replace("\\", "/"),
                    this.tableName,
                    columns
            );
            executeSqlByCurrentDb(loadDataSql);
        } catch (IOException e) {
            log.error("创建临时文件失败", e);
            throw new BusinessException("创建临时文件失败");
        } catch (Exception e) {
            log.error("LOAD DATA INFILE 失败", e);
            throw new BusinessException("批量插入失败", e);
        } finally {
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    log.error("删除临时文件失败: " + tempFile, e);
                }
            }
        }
    }

    public static void executeSqlByCurrentDb(String sql) {
        DataSource datasource = SqlStatementInterceptor.getDatasourceByLogin();
        try {
            log.info("文件批量写入数据库:{} 执行SQL: {}", SecurityUtils.getCompanyId(), sql);
            Connection connection = datasource.getConnection();
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.execute();
        } catch (SQLException e) {
            throw new BusinessException(e);
        }
    }

    private static String formatValue(Object value) {
        if (value == null) {
            return "\\N"; // LOAD DATA INFILE 中用于表示NULL
        }
        if (value instanceof Date date) {
            // MySql LOAD DATA 默认的日期格式是 'YYYY-MM-DD HH:MM:SS'
            return DateUtil.format(date, "yyyy-MM-dd HH:mm:ss");
        }
        // 对字符串进行转义，防止制表符、换行符等特殊字符导致格式错乱
        return value.toString().replace("\\", "\\\\")
                .replace("\t", "\\t")
                .replace("\n", "\\n")
                .replace("\r", "\\r");
    }
}
