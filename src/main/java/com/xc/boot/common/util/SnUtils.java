package com.xc.boot.common.util;

import cn.hutool.core.date.DateUtil;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.mapper.GoodsOutcomeMapper;
import com.xc.boot.modules.goods.mapper.GoodsTakeMapper;
import com.xc.boot.modules.goods.mapper.GoodsTransferMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsOutcomeEntity;
import com.xc.boot.modules.goods.model.entity.GoodsTakeEntity;
import com.xc.boot.modules.goods.model.entity.GoodsTransferEntity;
import com.xc.boot.modules.income.mapper.GoodsIncomeMapper;
import com.xc.boot.modules.income.model.entity.GoodsIncomeEntity;
import com.xc.boot.modules.order.mapper.MaterialRecycleMapper;
import com.xc.boot.modules.order.mapper.SoldReceiptMapper;
import com.xc.boot.modules.order.mapper.SoldReturnMapper;
import com.xc.boot.modules.order.model.entity.MaterialRecycleEntity;
import com.xc.boot.modules.gift.mapper.GiftTransferMapper;
import com.xc.boot.modules.gift.model.entity.GiftTransferEntity;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialMapper;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialOrderMapper;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialEntity;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialOrderEntity;
import com.xc.boot.modules.order.model.entity.SoldReceiptEntity;
import com.xc.boot.modules.order.model.entity.SoldReturnEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 单号生成公共封装
 */
@Slf4j
@Component
public class SnUtils implements ApplicationContextAware {
    private static GoodsIncomeMapper goodsIncomeMapper;
    private static GoodsOutcomeMapper goodsOutcomeMapper;
    private static GoodsMapper goodsMapper;
    private static StringRedisTemplate redisTemplate;
    @SuppressWarnings("unused")
    private static ApplicationContext applicationContext;
    private static GoodsTransferMapper goodsTransferMapper;
    private static GoodsTakeMapper goodsTakeMapper;
    private static MaterialRecycleMapper materialRecycleMapper;
    private static GiftTransferMapper giftTransferMapper;
    private static OldMaterialMapper oldMaterialMapper;
    private static SoldReturnMapper soldReturnMapper;
    private static OldMaterialOrderMapper oldMaterialOrderMapper;
    private static SoldReceiptMapper soldReceiptMapper;

    /**
     * 缓存前缀
     */
    private static final String CACHE_PREFIX = "generate_sn";

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SnUtils.applicationContext = applicationContext;

        goodsIncomeMapper = applicationContext.getBean(GoodsIncomeMapper.class);
        goodsOutcomeMapper = applicationContext.getBean(GoodsOutcomeMapper.class);
        goodsMapper = applicationContext.getBean(GoodsMapper.class);
        redisTemplate = applicationContext.getBean(StringRedisTemplate.class);
        goodsTransferMapper = applicationContext.getBean(GoodsTransferMapper.class);
        goodsTakeMapper = applicationContext.getBean(GoodsTakeMapper.class);
        materialRecycleMapper = applicationContext.getBean(MaterialRecycleMapper.class);
        giftTransferMapper = applicationContext.getBean(GiftTransferMapper.class);
        oldMaterialMapper = applicationContext.getBean(OldMaterialMapper.class);
        oldMaterialOrderMapper = applicationContext.getBean(OldMaterialOrderMapper.class);
        soldReturnMapper = applicationContext.getBean(SoldReturnMapper.class);
        soldReceiptMapper = applicationContext.getBean(SoldReceiptMapper.class);
    }

    /**
     * 获取公司ID
     */
    private static Long getCompanyId() {
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            throw new BusinessException("获取公司ID失败");
        }
        return companyId;
    }

    /**
     * 生成Redis key
     */
    private static String generateRedisKey(String type, String date) {
        return String.format("%s:%s:%d:%s", CACHE_PREFIX, type, getCompanyId(), date);
    }

    /**
     * 更新Redis计数器
     */
    private static Long updateRedisCounter(String key) {
        Long sequence = redisTemplate.opsForValue().increment(key);
        if (sequence != null && sequence == 1) {
            redisTemplate.expire(key, java.time.Duration.ofDays(7));
        }
        return sequence;
    }

    /**
     * 格式化序号
     */
    private static String formatSequence(Long sequence) {
        return String.format("%05d", sequence);
    }

    /**
     * 生成入库单号
     * 规则: RK + 年(4位) + 月 + 日 + 5 位顺序数字
     * 
     */
    public static String generateIncomeCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("income", date);
        Long sequence = updateRedisCounter(key);
        return "RK" + date + formatSequence(sequence);
    }

    /**
     * 生成出库单号
     * 规则: CK + 年(4位) + 月 + 日 + 5 位顺序数字
     *
     */
    public static String generateOutcomeCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("outcome", date);
        Long sequence = updateRedisCounter(key);
        return "CK" + date + formatSequence(sequence);
    }

    /**
     * 生成盘点单号
     * 规则: PD + 年(4位) + 月 + 日 + 5 位顺序数字
     *
     */
    public static String generateTakeCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("take", date);
        Long sequence = updateRedisCounter(key);
        return "PD" + date + formatSequence(sequence);
    }

    /**
     * 生成货品条码
     * 规则: 年(2位) + 月 + 日 + 5 位顺序数字
     * 
     */
    public static String generateGoodsSn() {
        String date = DateUtil.format(DateUtil.date(), "yyMMdd");
        String key = generateRedisKey("goods", date);
        Long sequence = updateRedisCounter(key);
        return date + formatSequence(sequence);
    }

    /**
     * 生成调拨单号
     * 规则: DB + 年(4位) + 月 + 日 + 5 位顺序数字
     */
    public static String generateTransferCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("transfer", date);
        Long sequence = updateRedisCounter(key);
        return "DB" + date + formatSequence(sequence);
    }

    /**
     * 生成回收单号
     * 规则: HS + 年(4位) + 月 + 日 + 5 位顺序数字
     */
    public static String generateRecycleCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("recycle", date);
        Long sequence = updateRedisCounter(key);
        return "HS" + date + formatSequence(sequence);
    }

    /**
     * 生成采购退货单号
     * 规则: TH + 年(4位) + 月 + 日 + 5 位顺序数字
     */
    @SuppressWarnings("unused")
    public static String generatePurchaseReturnCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("purchase_return", date);
        Long sequence = updateRedisCounter(key);
        return "TH" + date + formatSequence(sequence);
    }

    /**
     * 生成赠品调拨单号
     * 规则: DB + 年(4位) + 月 + 日 + 5 位顺序数字
     */
    @SuppressWarnings("unused")
    public static String generateGiftTransferCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("gift_transfer", date);
        Long sequence = updateRedisCounter(key);
        return "DB" + date + formatSequence(sequence);
    }

    /**
     * 生成旧料编号
     * 规则: O + 年(2位) + 月 + 日 + 5 位顺序数字
     */
    public static String generateOldMaterialSn() {
        String date = DateUtil.format(DateUtil.date(), "yyMMdd");
        String key = generateRedisKey("old_material", date);
        Long sequence = updateRedisCounter(key);
        return "O" + date + formatSequence(sequence);
    }

    /**
     * 生成销售单号
     * 规则: XS + 年(4位) + 月 + 日 + 5 位顺序数字
     */
    public static String generateSalesCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("sales", date);
        Long sequence = updateRedisCounter(key);
        return "XS" + date + formatSequence(sequence);
    }

    /**
     * 生成退货单号
     * 规则: XT + 年(4位) + 月 + 日 + 5 位顺序数字
     */
    public static String generateReturnCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("sold_return", date);
        Long sequence = updateRedisCounter(key);
        return "XT" + date + formatSequence(sequence);
    }

    /**
     * 生成旧料销售单号
     * 规则: OXS + 年(4位) + 月 + 日 + 5 位顺序数字
     */
    public static String generateOldMaterialOrderCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("old_material_order", date);
        Long sequence = updateRedisCounter(key);
        return "OXS" + date + formatSequence(sequence);
    }

    /**
     * 同步入库单号最大序号
     */
    public static void syncIncomeMaxSequence() {
        Long companyId = getCompanyId();
        CommonUtils.asyncExecute(() -> {
            try {
                String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
                String key = generateRedisKey("income", date);
                Long maxSequence = goodsIncomeMapper.selectOneByQueryAs(
                    QueryWrapper.create()
                        .select(QueryMethods.max(QueryMethods.substring(GoodsIncomeEntity::getIncomeCode, -5)).as("max_sequence"))
                        .where(GoodsIncomeEntity::getCompanyId).eq(companyId)
                        .and(GoodsIncomeEntity::getIncomeCode).likeLeft("RK" + date),
                    Long.class
                );
                if (maxSequence == null) {
                    maxSequence = 0L;
                }
                redisTemplate.opsForValue().set(key, String.valueOf(maxSequence));
                redisTemplate.expire(key, java.time.Duration.ofDays(7));
            } catch (Exception e) {
                log.error("同步入库单号最大序号失败", e);
            }
        });
    }

    /**
     * 同步货品条码最大序号
     */
    public static void syncGoodsMaxSequence() {
        Long companyId = getCompanyId();
        CommonUtils.asyncExecute(() -> {
            try {
                String date = DateUtil.format(DateUtil.date(), "yyMMdd");
                String key = generateRedisKey("goods", date);
                Long maxSequence = goodsMapper.selectOneByQueryAs(
                    QueryWrapper.create()
                        .select(QueryMethods.max(QueryMethods.substring(GoodsEntity::getGoodsSn, -5)).as("max_sequence"))
                        .where(GoodsEntity::getCompanyId).eq(companyId)
                        .and(GoodsEntity::getGoodsSn).likeLeft(date),
                    Long.class
                );
                if (maxSequence == null) {
                    maxSequence = 0L;
                }
                redisTemplate.opsForValue().set(key, String.valueOf(maxSequence));
                redisTemplate.expire(key, java.time.Duration.ofDays(7));
            } catch (Exception e) {
                log.error("同步货品条码最大序号失败", e);
            }
        });
    }

    /**
     * 同步出库单号最大序号
     */
    public static void syncOutcomeMaxSequence() {
        Long companyId = getCompanyId();
        CommonUtils.asyncExecute(() -> {
            try {
                String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
                String key = generateRedisKey("outcome", date);
                Long maxSequence = goodsOutcomeMapper.selectOneByQueryAs(
                    QueryWrapper.create()
                        .select(QueryMethods.max(QueryMethods.substring(GoodsOutcomeEntity::getOutcomeCode, -5)).as("max_sequence"))
                        .where(GoodsOutcomeEntity::getCompanyId).eq(companyId)
                        .and(GoodsOutcomeEntity::getOutcomeCode).likeLeft("CK" + date),
                    Long.class
                );
                if (maxSequence == null) {
                    maxSequence = 0L;
                }
                redisTemplate.opsForValue().set(key, String.valueOf(maxSequence));
                redisTemplate.expire(key, java.time.Duration.ofDays(7));
            } catch (Exception e) {
                log.error("同步出库单号最大序号失败", e);
            }
        });
    }

    /**
     * 同步调拨单号最大序号
     */
    @SuppressWarnings("unused")
    public static void syncTransferMaxSequence() {
        Long companyId = getCompanyId();
        CommonUtils.asyncExecute(() -> {
            try {
                String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
                String key = generateRedisKey("transfer", date);
                Long maxSequence = goodsTransferMapper.selectOneByQueryAs(
                    QueryWrapper.create()
                        .select(QueryMethods.max(QueryMethods.substring(GoodsTransferEntity::getTransferSn, -5)).as("max_sequence"))
                        .where(GoodsTransferEntity::getCompanyId).eq(companyId)
                        .and(GoodsTransferEntity::getTransferSn).likeLeft("DB" + date),
                    Long.class
                );
                if (maxSequence == null) {
                    maxSequence = 0L;
                }
                redisTemplate.opsForValue().set(key, String.valueOf(maxSequence));
                redisTemplate.expire(key, java.time.Duration.ofDays(7));
            } catch (Exception e) {
                log.error("同步调拨单号最大序号失败", e);
            }
        });
    }

    /**
     * 同步盘点单号最大序号
     */
    public static void syncTakeMaxSequence() {
        Long companyId = getCompanyId();
        CommonUtils.asyncExecute(() -> {
            try {
                String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
                String key = generateRedisKey("take", date);
                Long maxSequence = goodsTakeMapper.selectOneByQueryAs(
                    QueryWrapper.create()
                        .select(QueryMethods.max(QueryMethods.substring(GoodsTakeEntity::getTakeCode, -5)).as("max_sequence"))
                        .where(GoodsTakeEntity::getCompanyId).eq(companyId)
                        .and(GoodsTakeEntity::getTakeCode).likeLeft("PD" + date),
                    Long.class
                );
                if (maxSequence == null) {
                    maxSequence = 0L;
                }
                redisTemplate.opsForValue().set(key, String.valueOf(maxSequence));
                redisTemplate.expire(key, java.time.Duration.ofDays(7));
            } catch (Exception e) {
                log.error("同步盘点单号最大序号失败", e);
            }
        });
    }

    /**
     * 同步回收单号最大序号
     */
    public static void syncRecycleMaxSequence() {
        Long companyId = getCompanyId();
        CommonUtils.asyncExecute(() -> {
            try {
                String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
                String key = generateRedisKey("recycle", date);
                Long maxSequence = materialRecycleMapper.selectOneByQueryAs(
                    QueryWrapper.create()
                        .select(QueryMethods.max(QueryMethods.substring(MaterialRecycleEntity::getRecycleCode, -5)).as("max_sequence"))
                        .where(MaterialRecycleEntity::getCompanyId).eq(companyId)
                        .and(MaterialRecycleEntity::getRecycleCode).likeLeft("HS" + date),
                    Long.class
                );
                if (maxSequence == null) {
                    maxSequence = 0L;
                }
                redisTemplate.opsForValue().set(key, String.valueOf(maxSequence));
                redisTemplate.expire(key, java.time.Duration.ofDays(7));
            } catch (Exception e) {
                log.error("同步回收单号最大序号失败", e);
            }
        });
    }

    /**
     * 同步赠品调拨单号最大序号
     */
    @SuppressWarnings("unused")
    public static void syncGiftTransferMaxSequence() {
        Long companyId = getCompanyId();
        CommonUtils.asyncExecute(() -> {
            try {
                String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
                String key = generateRedisKey("gift_transfer", date);
                Long maxSequence = giftTransferMapper.selectOneByQueryAs(
                    QueryWrapper.create()
                        .select(QueryMethods.max(QueryMethods.substring(GiftTransferEntity::getTransferSn, -5)).as("max_sequence"))
                        .where(GiftTransferEntity::getCompanyId).eq(companyId)
                        .and(GiftTransferEntity::getTransferSn).likeLeft("DB" + date),
                    Long.class
                );
                if (maxSequence == null) {
                    maxSequence = 0L;
                }
                redisTemplate.opsForValue().set(key, String.valueOf(maxSequence));
                redisTemplate.expire(key, java.time.Duration.ofDays(7));
            } catch (Exception e) {
                log.error("同步赠品调拨单号最大序号失败", e);
            }
        });
    }

    /**
     * 同步旧料编号最大序号
     */
    public static void syncOldMaterialMaxSequence() {
        Long companyId = getCompanyId();
        CommonUtils.asyncExecute(() -> {
            try {
                String date = DateUtil.format(DateUtil.date(), "yyMMdd");
                String key = generateRedisKey("old_material", date);
                Long maxSequence = oldMaterialMapper.selectOneByQueryAs(
                    QueryWrapper.create()
                        .select(QueryMethods.max(QueryMethods.substring(OldMaterialEntity::getOldMaterialSn, -5)).as("max_sequence"))
                        .where(OldMaterialEntity::getCompanyId).eq(companyId)
                        .and(OldMaterialEntity::getOldMaterialSn).likeLeft("O" + date),
                    Long.class
                );
                if (maxSequence == null) {
                    maxSequence = 0L;
                }
                redisTemplate.opsForValue().set(key, String.valueOf(maxSequence));
                redisTemplate.expire(key, java.time.Duration.ofDays(7));
            } catch (Exception e) {
                log.error("同步旧料编号最大序号失败", e);
            }
        });
    }

    /**
     * 同步采购退货单号最大序号
     * TODO: 采购退货单表尚未创建，需要后续完成sync逻辑
     */
    @SuppressWarnings("unused")
    public static void syncPurchaseReturnMaxSequence() {
        // TODO: 实现采购退货单号同步逻辑
    }

    /**
     * 同步销售单号最大序号
     */
    public static void syncSalesMaxSequence() {
        Long companyId = getCompanyId();
        CommonUtils.asyncExecute(() -> {
            try {
                String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
                String key = generateRedisKey("sales", date);
                Long maxSequence = soldReceiptMapper.selectOneByQueryAs(
                    QueryWrapper.create()
                        .select(QueryMethods.max(QueryMethods.substring(SoldReceiptEntity::getReceiptSn, -5)).as("max_sequence"))
                        .where(SoldReceiptEntity::getCompanyId).eq(companyId)
                        .and(SoldReceiptEntity::getReceiptSn).likeLeft("XS" + date),
                    Long.class
                );
                if (maxSequence == null) {
                    maxSequence = 0L;
                }
                redisTemplate.opsForValue().set(key, String.valueOf(maxSequence));
                redisTemplate.expire(key, java.time.Duration.ofDays(7));
            } catch (Exception e) {
                log.error("同步销售单号最大序号失败", e);
            }
        });
    }

    /**
     * 同步退货单号最大序号
     */
    public static void syncReturnMaxSequence() {
        Long companyId = getCompanyId();
        CommonUtils.asyncExecute(() -> {
            try {
                String date = DateUtil.format(DateUtil.date(), "yyMMdd");
                String key = generateRedisKey("sold_return", date);
                Long maxSequence = soldReturnMapper.selectOneByQueryAs(
                        QueryWrapper.create()
                                .select(QueryMethods.max(QueryMethods.substring(SoldReturnEntity::getReturnCode, -5)).as("max_sequence"))
                                .where(SoldReturnEntity::getCompanyId).eq(companyId)
                                .and(SoldReturnEntity::getReturnCode).likeLeft("XT" + date),
                        Long.class
                );
                if (maxSequence == null) {
                    maxSequence = 0L;
                }
                redisTemplate.opsForValue().set(key, String.valueOf(maxSequence));
                redisTemplate.expire(key, java.time.Duration.ofDays(7));
            } catch (Exception e) {
                log.error("同步旧料编号最大序号失败", e);
            }
        });
    }

    /**
     * 同步旧料销售单号最大序号
     */
    public static void syncOldMaterialOrderMaxSequence() {
        Long companyId = getCompanyId();
        CommonUtils.asyncExecute(() -> {
            try {
                String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
                String key = generateRedisKey("old_material_order", date);
                Long maxSequence = oldMaterialOrderMapper.selectOneByQueryAs(
                    QueryWrapper.create()
                        .select(QueryMethods.max(QueryMethods.substring(OldMaterialOrderEntity::getOrderSn, -5)).as("max_sequence"))
                        .where(OldMaterialOrderEntity::getCompanyId).eq(companyId)
                        .and(OldMaterialOrderEntity::getOrderSn).likeLeft("OXS" + date),
                    Long.class
                );
                if (maxSequence == null) {
                    maxSequence = 0L;
                }
                redisTemplate.opsForValue().set(key, String.valueOf(maxSequence));
                redisTemplate.expire(key, java.time.Duration.ofDays(7));
            } catch (Exception e) {
                log.error("同步旧料销售单号最大序号失败", e);
            }
        });
    }
}
