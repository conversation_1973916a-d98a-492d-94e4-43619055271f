package com.xc.boot.common.util.excel.model;

import com.xc.boot.common.base.IBaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 通知目标类型枚举
 *
 * <AUTHOR>
 * @since 2022/10/14
 */
@Getter
@Schema(enumAsRef = true)
public enum ExportStatusEnum implements IBaseEnum<Integer> {
    WAITING(0, "初始化"),
    EXPORTING(2, "导出中"),
    SUCCESS(1, "导出成功"),
    FAIL(-1, "导出失败");


    private final Integer value;

    private final String label;

    ExportStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
}
