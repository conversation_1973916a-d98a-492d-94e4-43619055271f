package com.xc.boot.common.util;

import cn.hutool.core.util.StrUtil;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询工具类
 */
public class QueryUtils {
    
    /**
     * 解析逗号分隔的ID字符串，转换为ID列表
     * 
     * @param idsStr 逗号分隔的ID字符串，如 "1,2,3"
     * @return ID列表，如果字符串为空则返回空列表
     */
    public static List<Long> parseIds(String idsStr) {
        if (StrUtil.isBlank(idsStr)) {
            return List.of();
        }
        return Arrays.stream(idsStr.split(","))
            .map(String::trim)
            .filter(StrUtil::isNotBlank)
            .map(Long::parseLong)
            .collect(Collectors.toList());
    }
    
    /**
     * 解析逗号分隔的ID字符串，转换为Integer ID列表
     * 
     * @param idsStr 逗号分隔的ID字符串，如 "1,2,3"
     * @return Integer ID列表，如果字符串为空则返回空列表
     */
    public static List<Integer> parseIntegerIds(String idsStr) {
        if (StrUtil.isBlank(idsStr)) {
            return List.of();
        }
        return Arrays.stream(idsStr.split(","))
            .map(String::trim)
            .filter(StrUtil::isNotBlank)
            .map(Integer::parseInt)
            .collect(Collectors.toList());
    }
} 