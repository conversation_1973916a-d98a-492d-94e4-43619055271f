package com.xc.boot.common.util.excel;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.enums.baseColum.JoinColumEnum;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.ContextUtils;
import com.xc.boot.common.util.FileUtil;
import com.xc.boot.common.util.excel.model.Excel;
import com.xc.boot.common.util.excel.model.ExcelExportConfig;
import com.xc.boot.common.util.excel.model.ExcelImgDto;
import com.xc.boot.common.util.excel.model.ExportStatusEnum;
import com.xc.boot.common.util.excel.service.ExcelService;
import com.xc.boot.system.model.entity.FileEntity;
import com.xc.boot.system.model.entity.SysExportEntity;
import com.xc.boot.system.model.enums.ImageSizeCtrlEnum;
import com.xc.boot.system.model.vo.HeadingColumns;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.openxml4j.opc.PackageRelationship;
import org.apache.poi.openxml4j.opc.PackageRelationshipTypes;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFPicture;
import org.openxmlformats.schemas.drawingml.x2006.main.CTHyperlink;
import org.openxmlformats.schemas.drawingml.x2006.main.CTNonVisualDrawingProps;
import org.openxmlformats.schemas.drawingml.x2006.spreadsheetDrawing.CTPicture;
import org.openxmlformats.schemas.drawingml.x2006.spreadsheetDrawing.CTPictureNonVisual;

import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;


/**
 * <AUTHOR>
 * @ClassName ExcelUtil
 * @Date: 2025/6/6 16:29
 * @Description: 描述
 */
@Slf4j
@Accessors(chain = true)
public class ExcelUtil<T> {
    /**
     * 导出service
     */
    private ExcelService excelService;

    /**
     * mapper
     */
    private BaseMapper<?> mapper;

    /**
     * 查询条件
     */
    private QueryWrapper wrapper;

    /**
     * 类型
     */
    private Class<T> clazz;

    /**
     * 动态表头sign
     */
    private String sign;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 最终导出的表头字段
     */
    private List<HeadingColumns> columns;

    /**
     * 最终导出数据
     */
    private List<?> data;

    /**
     * 导出配置类
     */
    private ExcelExportConfig config;

    /**
     * 图片资源Map
     */
    private Map<String, ExcelImgDto> imgMap;

    /**
     * FieldMap
     */
    private Map<String, Field> fieldMap;

    /**
     * 获取导出数据的方法
     */
    private GetExportDataFunction<BaseMapper<?> , QueryWrapper> getExportDataFunction;

    /**
     * 修改导出表头配置的方法
     */
    private Function<List<HeadingColumns>, List<HeadingColumns>> signHeadingConfig;

    /**
     * 修改配置的方法
     */
    private Consumer<ExcelExportConfig> modifyConfig;

    private final Set<String> imgFiled = new HashSet<>();

    /**
     * 导出中参数
     */
    // 工作簿
    private SXSSFWorkbook wb;
    // 样式列表
    private Map<String, CellStyle> styles;
    // 当前sheet
    private SXSSFSheet sheet;
    // 当前索引
    private int curIndex = 0;

    private ExcelUtil () {
    }

    public static <T> ExcelUtil<T> of(BaseMapper<?> mapper, QueryWrapper wrapper, Class<T> clazz, String sign) {
        return ExcelUtil.of(mapper, wrapper, clazz, sign, IdUtil.nanoId(8));
    }

    public static <T> ExcelUtil<T> of(BaseMapper<?> mapper, QueryWrapper wrapper, Class<T> clazz, String sign, String name) {
        ExcelUtil<T> excelUtil = new ExcelUtil<>();
        excelUtil.excelService = ContextUtils.getBean(ExcelService.class);
        excelUtil.mapper = mapper;
        excelUtil.clazz = clazz;
        excelUtil.wrapper = wrapper;
        excelUtil.sign = sign;
        excelUtil.fileName = name;
        excelUtil.config = new ExcelExportConfig();
        return excelUtil;
    }

    /**
     * 设置获取导出数据的方法
     * @param getExportDataFunction
     */
    public ExcelUtil<T> getData(GetExportDataFunction<BaseMapper<?> , QueryWrapper> getExportDataFunction) {
        this.getExportDataFunction = getExportDataFunction;
        return this;
    }

    /**
     * 设置修改配置的方法
     * @param modifyConfig
     */
    public ExcelUtil<T> modifyConfig(Consumer<ExcelExportConfig> modifyConfig) {
        this.modifyConfig = modifyConfig;
        return this;
    }

    /**
     * 设置表头修改方法
     */
    public ExcelUtil<T> signHeadingConfig(Function<List<HeadingColumns>, List<HeadingColumns>> signHeadingConfig) {
        this.signHeadingConfig = signHeadingConfig;
        return this;
    }

    /**
     * 执行单sheet导出
     */
    public void doExport() {
        if (getExportDataFunction == null) {
            throw new BusinessException("请设置获取导出数据的方法");
        }
        // 获取默认导出配置
        excelService.config(config);
        // 修改导出配置
        if (modifyConfig != null) {
            modifyConfig.accept(config);
        }
        // 检查是否可以导出
        Long count = excelService.check(mapper, wrapper, sign, config);
        // 获取导出数据
        this.data = this.getExportDataFunction.apply(mapper, wrapper);
        // 检查类型
        Assert.notNull(this.clazz, "请设置导出数据类型");
        // 获取表头字段
        this.columns = excelService.getUserFiledBySign(sign).stream().filter(HeadingColumns::getShow).toList();
        // 修改表头字段
        if (signHeadingConfig != null) {
            this.columns = signHeadingConfig.apply(this.columns);
        }
        // 保存导出任务
        SysExportEntity exportTask = excelService.insertExport(fileName, sign, count);
        // 异步执行导出
        excelService.doAsyncTask(() -> {
            try {
                // 统一处理注解
                this.fieldMap = getFieldMapByColumns(columns);
                List<String> imgFiled = columns.stream().filter(HeadingColumns::getIsImg).map(HeadingColumns::getProp).toList();
                this.imgFiled.addAll(imgFiled);
                // 获取图片资源
                this.imgMap = excelService.getImgMapAsync(data, imgFiled);
                // 导出到本地文件
                String filePath = this.export();
                // 上传到oss，删除本地文件
                File file = new File(filePath);
                try(InputStream inputStream = new FileInputStream(file)) {
                    FileEntity fileEntity = excelService.upload(inputStream, file.getName(), file.length());
                    // 更新任务中心状态
                    exportTask.setUrl(fileEntity.getUrl())
                            .setSize(file.length())
                            .setStatus(ExportStatusEnum.SUCCESS.getValue());
                    excelService.updateExportTask(exportTask);
                    CommonUtils.updateFileStatus(fileEntity.getId(), 1);
                }finally {
                    file.delete();
                }
            }catch (Exception e) {
                exportTask.setStatus(ExportStatusEnum.FAIL.getValue());
                exportTask.setRemark(e.getMessage().substring(0, Math.min(e.getMessage().length(), 250)));
                excelService.updateExportTask(exportTask);
                log.error(e.getMessage(), e);
            }finally {
                try {
                    if (wb != null) {
                        wb.close();
                    }
                }catch (Exception ignore){}
            }
        });
    }

    private Map<String, Field> getFieldMapByColumns(List<HeadingColumns> columns) {
        HashMap<String, Field> fieldHashMap = new HashMap<>();
        for (HeadingColumns column : columns) {
            Field field = ReflectUtil.getField(this.clazz, column.getProp());
            if (field == null) {
                continue;
            }
            field.setAccessible(true);
            fieldHashMap.put(column.getProp(), field);
        }
        return fieldHashMap;
    }

    /**
     * 执行多sheet导出
     */
    public void doMultiSheetExport() {

    }

    /**
     * 执行导出,返回本地文件路径
     */
    private String export() {
        String sheetName = config.getSheetName();
        createWorkbook();
        this.sheet = createSheet(StringUtils.isNotBlank(sheetName) ? sheetName : fileName);
        this.curIndex = 0;
        createHeading(sheet);
        writeSheet();
        // 文件名 + 日期yyyyMMdd + 6位随机数 + 后缀
        String dateStr = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
        // 6位随机数
        String randomStr = String.format("%06d", new Random().nextInt(900000) + 100000);
        String fileName = this.fileName + "_" + dateStr + randomStr + ".xlsx";
        String filePath = getAbsoluteFile(fileName);
        try (OutputStream out = new FileOutputStream(filePath)){
            wb.write(out);
            out.flush();
            wb.close();
            this.sheet.getWorkbook().close();
        } catch (IOException ex) {
            throw new BusinessException(ex);
        }
        return filePath;
    }

    /**
     * 上传到oss，删除本地缓存文件,返回url
     */
    private String uploadAndDelete(String filePath) {
        return null;
    }


    /**
     * 写入sheet
     */
    private void writeSheet() {
        // 创建表头行
        Row row = sheet.createRow(curIndex++);
        int column = 0;
        for (HeadingColumns columns : this.columns) {
            this.createHeadCell(columns, row, column++);
        }

        // 填充数据
        for (Object t : this.data) {
            row = sheet.createRow(curIndex++);
            int cellIndex = 0;
            for (HeadingColumns columns : this.columns) {
                Cell cell = row.createCell(cellIndex++);
                cellFill(t, cell, columns);
            }
        }
    }

    /**
     * 处理cell数据填充
     */
    private void cellFill(Object obj, Cell cell, HeadingColumns columns) {
        try {
            CellStyle style = styles.get("data");
            style.setWrapText(true);
            cell.setCellStyle(style);
            // 加密后的列表导出
            if (obj instanceof JSONObject jsonObject) {
                Object value = jsonObject.get(JoinColumEnum.getClearColumnName(columns.getProp()));
                if (value != null) {
                    // 图片字段
                    if (imgFiled.contains(columns.getProp())) {
                        // 加密后的图片字段填充
                        if (!value.toString().startsWith("http")) {
                            stringCellFill(value, cell);
                            return;
                        }
                        imgCellFill(value, cell);
                        return;
                    }
                    stringCellFill(value, cell);
                }
                return;
            }
            // 普通vo列表导出
            Field field = this.fieldMap.get(columns.getProp());
            if (field == null) {
                return;
            }
            // 如果有excel注解，根据注解处理
            Excel annotation = field.getAnnotation(Excel.class);
            if (annotation != null) {
                switch (annotation.type()) {
                    case 2:
                        imgCellFill(field.get(obj), cell);
                        break;
                    case 3:
                        dictCellFill(obj, cell, field, annotation);
                        break;
                    case 4:
                        collectionCellFill(obj, cell, field, annotation);
                        break;
                    default:
                        stringCellFill(field.get(obj), cell);
                }
            }else {
                // 没有注解，但是表头设置为图片，也处理图片
                if (columns.getIsImg()) {
                    imgCellFill(field.get(obj), cell);
                    return;
                }
                stringCellFill(field.get(obj), cell);
            }
        }catch (Exception ignore) {
        }
    }

    /**
     * 字符串字段处理
     */
    private void stringCellFill(Object value, Cell cell) throws IllegalAccessException {
        if (Objects.isNull(value)) {
            return;
        }
        if (value instanceof Date date) {
            cell.setCellValue(DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN));
            return;
        }
        if (value instanceof LocalDateTime localDateTime) {
            cell.setCellValue(DateUtil.format(localDateTime, DatePattern.NORM_DATETIME_PATTERN));
            return;
        }
        if (value instanceof BigDecimal bigDecimal) {
            cell.setCellValue(bigDecimal.toPlainString());
            return;
        }
        cell.setCellValue(value.toString());
    }

    /**
     * 图片cell处理
     */
    private void imgCellFill(Object imgField, Cell cell) throws IllegalAccessException {
        if (Objects.isNull(imgField)) {
            return;
        }
        ExcelImgDto excelImgDto = imgMap.get(imgField.toString());
        if (Objects.isNull(excelImgDto)) {
            return;
        }
        // 将图片添加到表格
        int pictureIdx;
        if (excelImgDto.getWbIndex() != null) {
            pictureIdx = excelImgDto.getWbIndex();
        }else {
            pictureIdx = wb.addPicture(excelImgDto.getImg(), getImageType(excelImgDto.getImg()));
            excelImgDto.setWbIndex(pictureIdx);
        }
        XSSFDrawing drawing = (XSSFDrawing) getDrawingPatriarch(sheet);

        // 创建一个图片对象
        int columnIndex = cell.getColumnIndex();
        int cellRowIndex = cell.getRowIndex();
        XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0,
                columnIndex, cellRowIndex, columnIndex + 1, cellRowIndex + 1);
        // 计算列宽和行高
        int columnWidth = (int) (excelImgDto.getWidth() / 7.3); // 单位转换
        int rowHeight = (int) (excelImgDto.getHeight() / 1.33); // 单位转换

        // 如果config配置了图片宽高，则使用配置的宽高
        if(config.getImgWidth() != null && !config.getSizeCtrlEnum().equals(ImageSizeCtrlEnum.HEIGHT)) {
            columnWidth = (int) (config.getImgWidth() / 7.3);
        }
        if (config.getImgHeight() != null && !config.getSizeCtrlEnum().equals(ImageSizeCtrlEnum.WIDTH)) {
            rowHeight = (int) (config.getImgHeight() / 1.33);
        }

        // 设置单元格的列宽
        sheet.setColumnWidth(columnIndex, columnWidth * 256);
        // 设置单元格的行高
        cell.getRow().setHeightInPoints(rowHeight);

        // 调整图片的锚点，留出2像素的边距
        anchor.setDx1(0); // x偏移量
        anchor.setDy1(0);  // y偏移量
        anchor.setDx2(0); // x偏移量 + 图片宽度
        anchor.setDy2(0); // y偏移量 + 图片高度
        anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
        XSSFPicture picture = drawing.createPicture(anchor, pictureIdx);
        // 设置超链接
        String imgUrl = excelImgDto.getImgUrl();
        addHyperlink(cell, imgUrl, picture, drawing);
    }

    /**
     * 字典cell处理
     */
    private void dictCellFill(Object obj, Cell cell, Field field, Excel annotation) throws IllegalAccessException {
        String[] keys = annotation.key();
        String[] values = annotation.value();
        Map<String, String> dict = new HashMap<>(values.length);
        if (values.length != 0 && keys.length == values.length) {
            for (int i = 0; i < keys.length; i++) {
                dict.put(keys[i], values[i]);
            }
            Object key = field.get(obj);
            if (Objects.nonNull(key)) {
                cell.setCellValue(dict.get(key.toString()));
            }
        }else {
            stringCellFill(field.get(obj), cell);
        }
    }

    /**
     * 集合cell处理
     */
    private void collectionCellFill(Object obj, Cell cell, Field field, Excel annotation) throws IllegalAccessException {
        String subFieldName = annotation.subFieldName();
        Object value = field.get(obj);
        if (value instanceof Collection<?> collection) {
            if (StringUtils.isNotBlank(subFieldName)) {
                List<String> list = collection.stream().map(item -> ReflectUtil.getFieldValue(item, subFieldName).toString()).toList();
                String join = String.join(",", list);
                cell.setCellValue(join);
            }else {
                List<String> list = collection.stream().map(Object::toString).toList();
                cell.setCellValue(String.join(",", list));
            }
        }
    }

    /**
     * 创建sheet
     */
    private SXSSFSheet createSheet(String sheetName) {
        return wb.createSheet(sheetName);
    }

    /**
     * 创建工作簿
     */
    private void createWorkbook() {
        wb = new SXSSFWorkbook(500);
        styles = createStyles(wb);
    }

    /**
     * 创建标题
     */
    private void createHeading(Sheet sheet) {
        if (StringUtils.isBlank(config.getTitle())) {
            return;
        }
        int titleLastCol = this.columns.size() - 1;
        Row titleRow = sheet.createRow(curIndex++);
        titleRow.setHeightInPoints(30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellStyle(styles.get("title"));
        titleCell.setCellValue(config.getTitle());
        sheet.addMergedRegion(new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), titleRow.getRowNum(), titleLastCol));
    }

    /**
     * 获取本地文件保存位置
     */
    private String getAbsoluteFile(String filename) {
        String downloadPath = ContextUtils.getEnv() + "/download/" + filename;
        File desc = new File(downloadPath);
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        return downloadPath;
    }

    /**
     * 创建表头单元格
     */
    private void createHeadCell(HeadingColumns columns, Row row, int column) {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue(columns.getLabel());
        // 设置列宽
        sheet.setColumnWidth(column, (int) ((config.getWidth() + 0.72) * 256));
        // 设置列头单元格样式
        cell.setCellStyle(styles.get("data"));
    }

    /**
     * 创建表格样式
     *
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        // 写入各条记录,每条记录对应excel表中的一行
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setBold(true);
        style.setFont(titleFont);
        styles.put("title", style);
        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font totalFont = wb.createFont();
        totalFont.setFontName("Arial");
        totalFont.setFontHeightInPoints((short) 10);
        style.setFont(totalFont);
        styles.put("total", style);

        // 根据@Excel注解设置style样式
        return styles;
    }

    /**
     * 获取画布
     */
    private static Drawing<?> getDrawingPatriarch(Sheet sheet) {
        if (sheet.getDrawingPatriarch() == null) {
            sheet.createDrawingPatriarch();
        }
        return sheet.getDrawingPatriarch();
    }

    /**
     * 获取图片类型,设置图片插入类型
     */
    private int getImageType(byte[] value) {
        String type = FileUtil.getFileExtendName(value);
        if ("JPG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_JPEG;
        } else if ("PNG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_PNG;
        }
        return Workbook.PICTURE_TYPE_JPEG;
    }

    /**
     * 添加超链接
     */
    private void addHyperlink(Cell cell, String imgUrl, XSSFPicture picture, XSSFDrawing drawing) {
        // 去除查询参数
        if (imgUrl.contains("?")) {
            int i = imgUrl.indexOf("?");
            imgUrl = imgUrl.substring(0, i);
        }
        PackageRelationship packagerelationship = drawing.getPackagePart()
                .addExternalRelationship(imgUrl, PackageRelationshipTypes.HYPERLINK_PART);
        String rid = packagerelationship.getId();
        CTPicture ctpicture = picture.getCTPicture();
        CTPictureNonVisual ctpicturenonvisual = ctpicture.getNvPicPr();
        if (ctpicturenonvisual == null) {
            ctpicturenonvisual = ctpicture.addNewNvPicPr();
        }
        CTNonVisualDrawingProps ctnonvisualdrawingprops = ctpicturenonvisual.getCNvPr();
        if (ctnonvisualdrawingprops == null) {
            ctnonvisualdrawingprops = ctpicturenonvisual.addNewCNvPr();
        }
        CTHyperlink cthyperlink = ctnonvisualdrawingprops.getHlinkClick();
        if (cthyperlink == null) {
            cthyperlink = ctnonvisualdrawingprops.addNewHlinkClick();
        }
        cthyperlink.setId(rid);
        // 添加超链接
        if (StringUtils.isNotEmpty(imgUrl)) {
            CreationHelper helper = wb.getCreationHelper();
            Hyperlink hyperlink = helper.createHyperlink(HyperlinkType.URL);
            hyperlink.setAddress(imgUrl);
            cell.setHyperlink(hyperlink);
        }
    }

}
