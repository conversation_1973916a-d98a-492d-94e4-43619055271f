package com.xc.boot.common.util.listFill;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 填充处理节点 V2 - 支持泛型和函数式接口
 * @param <T> 列表元素类型
 */
@Data
@AllArgsConstructor
public class FillHandleNodeV2<T> {
    private FillUtilHandle handle;
    private Function<T, Object> keyGetter;
    private BiConsumer<T, Object> fieldSetter;
} 