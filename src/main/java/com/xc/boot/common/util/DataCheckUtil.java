package com.xc.boot.common.util;

import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.enums.baseColum.JoinColumEnum;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.*;
import com.xc.boot.modules.goods.model.entity.*;
import com.xc.boot.modules.income.mapper.GoodsIncomeDetailMapper;
import com.xc.boot.modules.income.model.entity.GoodsIncomeDetailEntity;
import com.xc.boot.modules.merchant.mapper.GoldPriceMapper;
import com.xc.boot.modules.merchant.model.entity.GoldPriceEntity;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 检查是否可以操作
 */

@Component
public class DataCheckUtil implements ApplicationContextAware {
    @SuppressWarnings("unused")
    private static ApplicationContext applicationContext;
    private static GoodsIncomeDetailMapper incomeDetailMapper;
    private static GoodsMapper goodsMapper;
    private static GoodsTakeCategoryMapper takeCategoryMapper;
    private static GoodsTakeStyleMapper takeStyleMapper;
    private static GoodsTakeCounterMapper takeCounterMapper;
    private static GoldPriceMapper goldPriceMapper;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        DataCheckUtil.applicationContext = applicationContext;
        incomeDetailMapper = applicationContext.getBean(GoodsIncomeDetailMapper.class);
        goodsMapper = applicationContext.getBean(GoodsMapper.class);
        takeCategoryMapper = applicationContext.getBean(GoodsTakeCategoryMapper.class);
        goldPriceMapper = applicationContext.getBean(GoldPriceMapper.class);
        takeStyleMapper = applicationContext.getBean(GoodsTakeStyleMapper.class);
        takeCounterMapper = applicationContext.getBean(GoodsTakeCounterMapper.class);
    }

    /**
     * 检查id类型字段是否可以删除
     * @param id
     * @return
     */
    public static boolean checkJoinFieldDelete(Long id, JoinColumEnum columEnum) {
        // 通用校验
        // 入库单详情
        long count = incomeDetailMapper.selectCountByQuery(QueryWrapper.create()
                .where(GoodsIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .where(QueryMethods.column(columEnum.getSign()).eq(id)));
        if (count > 0) {
            return false;
        }
        // 货品列表
        count = goodsMapper.selectCountByQuery(QueryWrapper.create()
                .where(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .where(QueryMethods.column(columEnum.getSign()).eq(id)));
        if (count > 0) {
            return false;
        }
        // 自定义校验
        count = switch (columEnum) {
            case SUBCLASS_ID -> {
                // 盘点单
                long takeCount = takeCategoryMapper.selectCountByQuery(QueryWrapper.create()
                        .where(GoodsTakeCategoryEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .where(GoodsTakeCategoryEntity::getSubclassId).eq(id));
                // ...
                yield takeCount;
            }
            case STYLE_ID -> {
                // 盘点单
                long takeCount = takeStyleMapper.selectCountByQuery(QueryWrapper.create()
                        .where(GoodsTakeStyleEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .where(GoodsTakeStyleEntity::getStyleId).eq(id));
                // ...
                yield takeCount;
            }
            case COUNTER_ID -> {
                // 盘点单
                long takeCount = takeCounterMapper.selectCountByQuery(QueryWrapper.create()
                        .where(GoodsTakeCounterEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .where(GoodsTakeCounterEntity::getCounterId).eq(id));
                // ...
                yield takeCount;
            }
            case QUALITY_ID -> {
                // 金价
                long goldPriceCount = goldPriceMapper.selectCountByQuery(QueryWrapper.create()
                        .where(GoldPriceEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .where(GoldPriceEntity::getQualityId).eq(id));
                yield goldPriceCount;
            }
            default -> 0;
        };
        return count == 0;
    }

}
