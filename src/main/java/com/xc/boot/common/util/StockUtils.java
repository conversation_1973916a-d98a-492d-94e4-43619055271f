package com.xc.boot.common.util;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.row.Db;
import com.mybatisflex.core.row.Row;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsNumLogsMapper;
import com.xc.boot.modules.goods.model.bo.StockNumChangeBO;
import com.xc.boot.modules.goods.model.entity.GoodsNumLogsEntity;
import com.xc.boot.modules.gift.mapper.GiftNumLogsMapper;
import com.xc.boot.modules.gift.model.bo.GiftStockNumChangeBO;
import com.xc.boot.modules.gift.model.entity.GiftNumLogsEntity;
import com.xc.boot.modules.oldmaterial.mapper.OldMaterialNumLogsMapper;
import com.xc.boot.modules.oldmaterial.model.bo.OldMaterialStockNumChangeBO;
import com.xc.boot.modules.oldmaterial.model.entity.OldMaterialNumLogsEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存工具类
 */
@Slf4j
@Component
public class StockUtils implements ApplicationContextAware {
    private static GoodsNumLogsMapper goodsNumLogsMapper;
    private static GiftNumLogsMapper giftNumLogsMapper;
    private static OldMaterialNumLogsMapper oldMaterialNumLogsMapper;
    @SuppressWarnings("unused")
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        StockUtils.applicationContext = applicationContext;
        goodsNumLogsMapper = applicationContext.getBean(GoodsNumLogsMapper.class);
        giftNumLogsMapper = applicationContext.getBean(GiftNumLogsMapper.class);
        oldMaterialNumLogsMapper = applicationContext.getBean(OldMaterialNumLogsMapper.class);
    }

    /**
     * 批量更新库存数量
     *
     * @param changes 库存变更列表
     */
    public static void updateStocks(List<StockNumChangeBO> changes) {
        if (changes == null || changes.isEmpty()) {
            return;
        }

        Db.tx(() -> {
            // 获取所有商品ID
            List<Long> goodsIds = changes.stream()
                    .map(StockNumChangeBO::getGoodsId)
                    .toList();

            // 使用悲观锁查询当前数据
            String lockSql = "SELECT * FROM goods WHERE id IN (" +
                    String.join(",", goodsIds.stream().map(String::valueOf).toList()) +
                    ") AND company_id = " + SecurityUtils.getCompanyId() +
                    " FOR UPDATE";
            List<Row> currentData = Db.selectListBySql(lockSql);

            // 将当前数据转换为Map，方便查找
            Map<Long, Row> currentDataMap = currentData.stream()
                    .collect(Collectors.toMap(
                            row -> row.getLong("id"),
                            row -> row));

            // 存储计算后的数量
            Map<Long, Map<String, Integer>> calculatedNums = new HashMap<>();

            // 计算并检查数量
            for (StockNumChangeBO change : changes) {
                Row current = currentDataMap.get(change.getGoodsId());
                Assert.isTrue(current.getInt("take_status").equals(0), String.format("货品: %s 正在盘点中，请结束盘点后再试", current.getString("goods_sn")));
                Map<String, Integer> nums = new HashMap<>();

                // 计算各个数量
                int num = current.getInt("num") + change.getNum();
                int stockNum = current.getInt("stock_num") + change.getStockNum();
                int returnNum = current.getInt("return_num") + change.getReturnNum();
                int soldNum = current.getInt("sold_num") + change.getSoldNum();
                int transferNum = current.getInt("transfer_num") + change.getTransferNum();
                int frozenNum = current.getInt("frozen_num") + change.getFrozenNum();

                // 检查数量是否为负数
                CommonUtils.abortIf(num < 0, "原始数量, 更新后为 " + num + ", 请检查");
                CommonUtils.abortIf(stockNum < 0, "库存数量, 更新后为 " + stockNum + ", 请检查");
                CommonUtils.abortIf(returnNum < 0, "采购退数量, 更新后为 " + returnNum + ", 请检查");
                CommonUtils.abortIf(soldNum < 0, "销售数量, 更新后为 " + soldNum + ", 请检查");
                CommonUtils.abortIf(transferNum < 0, "调拨数量, 更新后为 " + transferNum + ", 请检查");
                CommonUtils.abortIf(frozenNum < 0, "冻结数量, 更新后为 " + frozenNum + ", 请检查");

                // 存储计算后的数量
                nums.put("num", num);
                nums.put("stock_num", stockNum);
                nums.put("return_num", returnNum);
                nums.put("sold_num", soldNum);
                nums.put("transfer_num", transferNum);
                nums.put("frozen_num", frozenNum);
                calculatedNums.put(change.getGoodsId(), nums);
            }

            // 构建 UNION ALL 子查询，使用计算后的最终值
            StringBuilder unionSql = new StringBuilder();
            for (int i = 0; i < changes.size(); i++) {
                StockNumChangeBO change = changes.get(i);
                Map<String, Integer> nums = calculatedNums.get(change.getGoodsId());

                if (i > 0) {
                    unionSql.append(" UNION ALL ");
                }
                unionSql.append("SELECT ")
                        .append(change.getGoodsId()).append(" as id, ")
                        .append(nums.get("num")).append(" as num, ")
                        .append(nums.get("stock_num")).append(" as stock_num, ")
                        .append(nums.get("return_num")).append(" as return_num, ")
                        .append(nums.get("sold_num")).append(" as sold_num, ")
                        .append(nums.get("transfer_num")).append(" as transfer_num, ")
                        .append(nums.get("frozen_num")).append(" as frozen_num");
            }

            // 构建完整的更新SQL，使用最终值
            String sql = "UPDATE goods g " +
                    "JOIN (" + unionSql + ") updates ON g.id = updates.id " +
                    "SET g.num = updates.num, " +
                    "g.stock_num = updates.stock_num, " +
                    "g.return_num = updates.return_num, " +
                    "g.sold_num = updates.sold_num, " +
                    "g.transfer_num = updates.transfer_num, " +
                    "g.frozen_num = updates.frozen_num " +
                    "WHERE g.company_id = " + SecurityUtils.getCompanyId();

            int updatedRows = Db.updateBySql(sql);

            if (updatedRows != changes.size()) {
                log.warn("批量更新库存数量不完整，预期更新 {} 条记录，实际更新 {} 条记录", changes.size(), updatedRows);
            }

            // 记录库存变化记录
            Long userId = SecurityUtils.getUserId();
            Long companyId = SecurityUtils.getCompanyId();
            List<GoodsNumLogsEntity> logs = new ArrayList<>();
            for (StockNumChangeBO change : changes) {
                GoodsNumLogsEntity log = new GoodsNumLogsEntity()
                        .setCompanyId(companyId)
                        .setGoodsId(change.getGoodsId())
                        .setComment(change.getComment())
                        .setNum(change.getNum())
                        .setStockNum(change.getStockNum())
                        .setReturnNum(change.getReturnNum())
                        .setSoldNum(change.getSoldNum())
                        .setTransferNum(change.getTransferNum())
                        .setFrozenNum(change.getFrozenNum())
                        .setCreatedBy(userId);
                logs.add(log);
            }
            goodsNumLogsMapper.insertBatchSelective(logs);

            return updatedRows > 0;
        });
    }

    /**
     * 批量更新赠品库存数量
     *
     * @param changes 赠品库存变更列表
     */
    public static void updateGiftStocks(List<GiftStockNumChangeBO> changes) {
        if (changes == null || changes.isEmpty()) {
            return;
        }

        Db.tx(() -> {
            // 获取所有赠品ID
            List<Long> giftIds = changes.stream()
                    .map(GiftStockNumChangeBO::getGiftId)
                    .toList();

            // 使用悲观锁查询当前数据
            String lockSql = "SELECT * FROM gift WHERE id IN (" +
                    String.join(",", giftIds.stream().map(String::valueOf).toList()) +
                    ") AND company_id = " + SecurityUtils.getCompanyId() +
                    " FOR UPDATE";
            List<Row> currentData = Db.selectListBySql(lockSql);

            // 将当前数据转换为Map，方便查找
            Map<Long, Row> currentDataMap = currentData.stream()
                    .collect(Collectors.toMap(
                            row -> row.getLong("id"),
                            row -> row));

            // 存储计算后的数量
            Map<Long, Map<String, Integer>> calculatedNums = new HashMap<>();

            // 计算并检查数量
            for (GiftStockNumChangeBO change : changes) {
                Row current = currentDataMap.get(change.getGiftId());
                Map<String, Integer> nums = new HashMap<>();

                // 计算各个数量
                int num = current.getInt("num") + change.getNum();
                int stockNum = current.getInt("stock_num") + change.getStockNum();
                int soldNum = current.getInt("sold_num") + change.getSoldNum();
                int transferNum = current.getInt("transfer_num") + change.getTransferNum();
                int frozenNum = current.getInt("frozen_num") + change.getFrozenNum();

                // 检查数量是否为负数
                CommonUtils.abortIf(num < 0, "原始数量, 更新后为 " + num + ", 请检查");
                CommonUtils.abortIf(stockNum < 0, "库存数量, 更新后为 " + stockNum + ", 请检查");
                CommonUtils.abortIf(soldNum < 0, "销售数量, 更新后为 " + soldNum + ", 请检查");
                CommonUtils.abortIf(transferNum < 0, "调拨数量, 更新后为 " + transferNum + ", 请检查");
                CommonUtils.abortIf(frozenNum < 0, "冻结数量, 更新后为 " + frozenNum + ", 请检查");

                // 存储计算后的数量
                nums.put("num", num);
                nums.put("stock_num", stockNum);
                nums.put("sold_num", soldNum);
                nums.put("transfer_num", transferNum);
                nums.put("frozen_num", frozenNum);
                calculatedNums.put(change.getGiftId(), nums);
            }

            // 构建 UNION ALL 子查询，使用计算后的最终值
            StringBuilder unionSql = new StringBuilder();
            for (int i = 0; i < changes.size(); i++) {
                GiftStockNumChangeBO change = changes.get(i);
                Map<String, Integer> nums = calculatedNums.get(change.getGiftId());

                if (i > 0) {
                    unionSql.append(" UNION ALL ");
                }
                unionSql.append("SELECT ")
                        .append(change.getGiftId()).append(" as id, ")
                        .append(nums.get("num")).append(" as num, ")
                        .append(nums.get("stock_num")).append(" as stock_num, ")
                        .append(nums.get("sold_num")).append(" as sold_num, ")
                        .append(nums.get("transfer_num")).append(" as transfer_num, ")
                        .append(nums.get("frozen_num")).append(" as frozen_num");
            }

            // 构建完整的更新SQL，使用最终值
            String sql = "UPDATE gift g " +
                    "JOIN (" + unionSql + ") updates ON g.id = updates.id " +
                    "SET g.num = updates.num, " +
                    "g.stock_num = updates.stock_num, " +
                    "g.sold_num = updates.sold_num, " +
                    "g.transfer_num = updates.transfer_num, " +
                    "g.frozen_num = updates.frozen_num " +
                    "WHERE g.company_id = " + SecurityUtils.getCompanyId();

            int updatedRows = Db.updateBySql(sql);

            if (updatedRows != changes.size()) {
                log.warn("批量更新赠品库存数量不完整，预期更新 {} 条记录，实际更新 {} 条记录", changes.size(), updatedRows);
            }

            // 记录库存变化记录
            Long userId = SecurityUtils.getUserId();
            Long companyId = SecurityUtils.getCompanyId();
            List<GiftNumLogsEntity> logs = new ArrayList<>();
            for (GiftStockNumChangeBO change : changes) {
                GiftNumLogsEntity log = new GiftNumLogsEntity()
                        .setCompanyId(companyId)
                        .setGiftId(change.getGiftId())
                        .setComment(change.getComment())
                        .setNum(change.getNum())
                        .setStockNum(change.getStockNum())
                        .setSoldNum(change.getSoldNum())
                        .setTransferNum(change.getTransferNum())
                        .setFrozenNum(change.getFrozenNum())
                        .setCreatedBy(userId);
                logs.add(log);
            }
            giftNumLogsMapper.insertBatchSelective(logs);

            return updatedRows > 0;
        });
    }

    /**
     * 批量更新旧料库存数量
     *
     * @param changes 旧料库存变更列表
     */
    public static void updateOldMaterialStocks(List<OldMaterialStockNumChangeBO> changes) {
        if (changes == null || changes.isEmpty()) {
            return;
        }

        Db.tx(() -> {
            // 获取所有旧料ID
            List<Long> oldMaterialIds = changes.stream()
                    .map(OldMaterialStockNumChangeBO::getOldMaterialId)
                    .toList();

            // 使用悲观锁查询当前数据
            String lockSql = "SELECT * FROM old_material WHERE id IN (" +
                    String.join(",", oldMaterialIds.stream().map(String::valueOf).toList()) +
                    ") AND company_id = " + SecurityUtils.getCompanyId() +
                    " FOR UPDATE";
            List<Row> currentData = Db.selectListBySql(lockSql);

            // 将当前数据转换为Map，方便查找
            Map<Long, Row> currentDataMap = currentData.stream()
                    .collect(Collectors.toMap(
                            row -> row.getLong("id"),
                            row -> row));

            // 存储计算后的数量
            Map<Long, Map<String, Integer>> calculatedNums = new HashMap<>();

            // 计算并检查数量
            for (OldMaterialStockNumChangeBO change : changes) {
                Row current = currentDataMap.get(change.getOldMaterialId());
                Map<String, Integer> nums = new HashMap<>();

                // 计算各个数量
                int num = current.getInt("num") + change.getNum();
                int frozenNum = current.getInt("frozen_num") + change.getFrozenNum();

                // 检查数量是否为负数
                CommonUtils.abortIf(num < 0, "库存数量, 更新后为 " + num + ", 请检查");
                CommonUtils.abortIf(frozenNum < 0, "冻结数量, 更新后为 " + frozenNum + ", 请检查");

                // 存储计算后的数量
                nums.put("num", num);
                nums.put("frozen_num", frozenNum);
                calculatedNums.put(change.getOldMaterialId(), nums);
            }

            // 构建 UNION ALL 子查询，使用计算后的最终值
            StringBuilder unionSql = new StringBuilder();
            for (int i = 0; i < changes.size(); i++) {
                OldMaterialStockNumChangeBO change = changes.get(i);
                Map<String, Integer> nums = calculatedNums.get(change.getOldMaterialId());

                if (i > 0) {
                    unionSql.append(" UNION ALL ");
                }
                unionSql.append("SELECT ")
                        .append(change.getOldMaterialId()).append(" as id, ")
                        .append(nums.get("num")).append(" as num, ")
                        .append(nums.get("frozen_num")).append(" as frozen_num");
            }

            // 构建完整的更新SQL，使用最终值
            String sql = "UPDATE old_material om " +
                    "JOIN (" + unionSql + ") updates ON om.id = updates.id " +
                    "SET om.num = updates.num, " +
                    "om.frozen_num = updates.frozen_num " +
                    "WHERE om.company_id = " + SecurityUtils.getCompanyId();

            int updatedRows = Db.updateBySql(sql);

            if (updatedRows != changes.size()) {
                log.warn("批量更新旧料库存数量不完整，预期更新 {} 条记录，实际更新 {} 条记录", changes.size(), updatedRows);
            }

            // 记录库存变化记录
            Long userId = SecurityUtils.getUserId();
            Long companyId = SecurityUtils.getCompanyId();
            List<OldMaterialNumLogsEntity> logs = new ArrayList<>();
            for (OldMaterialStockNumChangeBO change : changes) {
                OldMaterialNumLogsEntity log = new OldMaterialNumLogsEntity()
                        .setCompanyId(companyId)
                        .setOldMaterialId(change.getOldMaterialId())
                        .setComment(change.getComment())
                        .setNum(change.getNum())
                        .setFrozenNum(change.getFrozenNum())
                        .setCreatedBy(userId);
                logs.add(log);
            }
            oldMaterialNumLogsMapper.insertBatchSelective(logs);

            return updatedRows > 0;
        });
    }
}