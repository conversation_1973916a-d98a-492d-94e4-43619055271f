package com.xc.boot.common.util.listFill;

import cn.hutool.core.collection.CollectionUtil;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @ClassName ListFillUtilV2
 * @Date: 2025/6/2 12:20
 * @Description: list填充工具类 V2 - 支持泛型和函数式接口
 */

public class ListFillUtilV2<T> {
    private final List<T> list;
    private final Map<BiConsumer<T, Object>, Map<String, ?>> valuesMap;
    private final Map<BiConsumer<T, Object>, Set<Object>> keysMap;
    private final List<FillHandleNodeV2<T>> handles;
    private Consumer<T> peek;

    public ListFillUtilV2(List<T> list) {
        this.list = list;
        this.valuesMap = new HashMap<>();
        this.handles = new ArrayList<>();
        this.keysMap = new HashMap<>();
    }

    /**
     * 静态工厂方法，创建 ListFillUtilV2 实例
     * @param list 要填充的列表
     * @param <T> 列表元素类型
     * @return ListFillUtilV2 实例
     */
    public static <T> ListFillUtilV2<T> of(List<T> list) {
        return new ListFillUtilV2<>(list);
    }

    /**
     * 构建填充节点（类型安全版本）
     * @param handle 数据填充处理器
     * @param keyGetter 获取key的函数
     * @param fieldSetter 设置字段值的函数
     * @return 当前实例
     */
    public <V> ListFillUtilV2<T> build(FillUtilHandle handle, Function<T, Object> keyGetter, BiConsumer<T, V> fieldSetter) {
        // 创建一个类型转换的包装器
        BiConsumer<T, Object> wrapper = (obj, value) -> {
            @SuppressWarnings("unchecked")
            V typedValue = (V) value;
            fieldSetter.accept(obj, typedValue);
        };
        this.handles.add(new FillHandleNodeV2<>(handle, keyGetter, wrapper));
        return this;
    }

    /**
     * 添加处理函数
     * @param consumer 处理函数
     * @return 当前实例
     */
    public ListFillUtilV2<T> peek(Consumer<T> consumer) {
        peek = consumer;
        return this;
    }

    /**
     * 执行填充逻辑
     */
    public void handle() {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        // 第一次循环：收集所有需要的keys
        for (T obj : list) {
            for (FillHandleNodeV2<T> node : handles) {
                Set<Object> set = keysMap.getOrDefault(node.getFieldSetter(), new HashSet<>());
                Object key = node.getKeyGetter().apply(obj);
                if (key != null) {
                    set.add(key);
                }
                keysMap.put(node.getFieldSetter(), set);
            }
        }

        // 第二次循环：填充数据
        for (T obj : list) {
            for (FillHandleNodeV2<T> node : handles) {
                Map<String, ?> resultMap;
                if (!valuesMap.containsKey(node.getFieldSetter())) {
                    Set<Object> keySet = keysMap.get(node.getFieldSetter());
                    if (CollectionUtil.isEmpty(keySet)) {
                        valuesMap.put(node.getFieldSetter(), new HashMap<>());
                        resultMap = new HashMap<>();
                    } else {
                        Map<String, ?> fillData = node.getHandle().getFillData(keySet);
                        valuesMap.put(node.getFieldSetter(), fillData);
                        resultMap = fillData;
                    }
                } else {
                    resultMap = valuesMap.getOrDefault(node.getFieldSetter(), new HashMap<>());
                }
                
                String key = Optional.ofNullable(node.getKeyGetter().apply(obj)).orElse("").toString();
                Object value = resultMap.get(key);
                node.getFieldSetter().accept(obj, value);
            }
            if (peek != null) {
                peek.accept(obj);
            }
        }
    }
}
