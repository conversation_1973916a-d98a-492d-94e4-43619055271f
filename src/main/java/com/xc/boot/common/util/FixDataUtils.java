package com.xc.boot.common.util;

import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.merchant.service.GoodsColumnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据修复工具类
 */
@Slf4j
@Component
public class FixDataUtils implements ApplicationContextAware {

    private static GoodsColumnService goodsColumnService;

    /**
     * 数据修复处理器映射
     */
    private static final Map<String, DataFixHandler> FIX_HANDLERS = new ConcurrentHashMap<>();

    /**
     * 数据修复处理器接口
     */
    @FunctionalInterface
    public interface DataFixHandler {
        /**
         * 执行数据修复
         *
         * @param params 命令参数数组
         * @return 修复结果描述
         */
        String execute(String[] params);
    }

    /**
     * 修复标识信息
     */
    public static class FixIdentifierInfo {
        private final String identifier;
        private final String name;
        private final String description;

        public FixIdentifierInfo(String identifier, String name, String description) {
            this.identifier = identifier;
            this.name = name;
            this.description = description;
        }

        public String getIdentifier() { return identifier; }
        public String getName() { return name; }
        public String getDescription() { return description; }
    }

    /**
     * 修复标识信息映射
     */
    private static final Map<String, FixIdentifierInfo> FIX_IDENTIFIER_INFOS = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        goodsColumnService = applicationContext.getBean(GoodsColumnService.class);
        
        // 注册数据修复处理器
        registerFixHandlers();
    }

    /**
     * 注册数据修复处理器
     */
    private static void registerFixHandlers() {
        // reset_goods_column: 重置货品字段
        registerFixHandler(
            "reset_goods_column",
            "重置货品字段",
            "重新组织货品字段配置，保留自定义字段并重新排序",
            (params) -> {
                log.info("开始执行货品字段重置修复...");

                try {
                    // 检查是否有指定商户ID参数
                    if (params.length > 0) {
                        // 处理指定商户
                        String companyIdStr = params[0].trim();
                        try {
                            Long companyId = Long.parseLong(companyIdStr);
                            SecurityUtils.mockUserByCompanyId(companyId);
                            String result = goodsColumnService.resetGoodsColumns();
                            return String.format("🔧 <b>货品字段重置结果</b>\n━━━━━━━━━━━━━━━━━━━━\n\n✅ 商户ID:%d %s", companyId, result);
                        } catch (NumberFormatException e) {
                            return "❌ 商户ID格式错误：" + companyIdStr;
                        } catch (Exception e) {
                            log.error("处理商户ID:{} 时发生异常", companyIdStr, e);
                            return "❌ 商户ID:" + companyIdStr + " 处理失败：" + e.getMessage();
                        }
                    } else {
                        // 处理所有商户
                        List<Long> companyIds = CommonUtils.getAllCompanyIds();
                        if (companyIds.isEmpty()) {
                            return "❌ 未找到任何公司数据";
                        }

                        StringBuilder result = new StringBuilder();
                        result.append("🔧 <b>货品字段重置修复结果</b>\n");
                        result.append("━━━━━━━━━━━━━━━━━━━━\n\n");
                        result.append("📊 总计处理公司数量：").append(companyIds.size()).append("\n\n");

                        int successCount = 0;
                        int failCount = 0;

                        for (Long companyId : companyIds) {
                            try {
                                SecurityUtils.mockUserByCompanyId(companyId);
                                String fixResult = goodsColumnService.resetGoodsColumns();
                                successCount++;
                                result.append("✅ 商户ID:").append(companyId).append(" ").append(fixResult).append("\n");
                            } catch (Exception e) {
                                failCount++;
                                result.append("❌ 商户ID:").append(companyId)
                                      .append(" 处理失败：").append(e.getMessage()).append("\n");
                                log.error("处理商户ID:{} 时发生异常", companyId, e);
                            }
                        }

                        result.append("\n━━━━━━━━━━━━━━━━━━━━\n");
                        result.append("📈 <b>处理统计</b>\n");
                        result.append("✅ 重置成功：").append(successCount).append(" 个公司\n");
                        result.append("❌ 处理失败：").append(failCount).append(" 个公司\n");

                        if (failCount == 0) {
                            result.append("\n🎉 所有公司处理完成！");
                        } else {
                            result.append("\n⚠️ 部分公司处理失败，请检查日志");
                        }

                        return result.toString();
                    }
                } catch (Exception e) {
                    log.error("货品字段重置修复执行失败", e);
                    return "❌ 货品字段重置修复执行失败：" + e.getMessage();
                }
            });
    }

    /**
     * 注册修复处理器
     *
     * @param identifier 修复标识
     * @param name 修复名称
     * @param description 修复描述
     * @param handler 处理器
     */
    private static void registerFixHandler(String identifier, String name, String description, DataFixHandler handler) {
        FIX_HANDLERS.put(identifier, handler);
        FIX_IDENTIFIER_INFOS.put(identifier, new FixIdentifierInfo(identifier, name, description));
    }

    /**
     * 执行数据修复
     *
     * @param identifier 修复标识
     * @return 修复结果
     */
    public static String executeDataFix(String identifier) {
        return executeDataFix(identifier, new String[0]);
    }

    /**
     * 执行数据修复
     *
     * @param identifier 修复标识
     * @param params 命令参数
     * @return 修复结果
     */
    public static String executeDataFix(String identifier, String[] params) {
        // 当标识为空时，返回可用的修复标识列表
        if (identifier == null || identifier.trim().isEmpty()) {
            return getAvailableIdentifiers();
        }

        DataFixHandler handler = FIX_HANDLERS.get(identifier.trim());
        if (handler == null) {
            return getAvailableIdentifiers();
        }

        try {
            return handler.execute(params != null ? params : new String[0]);
        } catch (Exception e) {
            log.error("执行数据修复失败，标识：{}", identifier, e);
            return "❌ 数据修复执行失败：" + e.getMessage();
        }
    }

    /**
     * 获取可用的修复标识列表
     *
     * @return 可用标识的提示信息
     */
    private static String getAvailableIdentifiers() {
        StringBuilder message = new StringBuilder();
        message.append("❌ <b>未找到指定的修复标识</b>\n\n");
        message.append("📋 <b>可用的修复标识：</b>\n");
        message.append("━━━━━━━━━━━━━━━━━━━━\n\n");

        // 动态获取所有可用的修复标识
        if (FIX_IDENTIFIER_INFOS.isEmpty()) {
            message.append("⚠️ 暂无可用的修复标识\n\n");
        } else {
            for (FixIdentifierInfo info : FIX_IDENTIFIER_INFOS.values()) {
                message.append("🔧 <b>").append(info.getIdentifier()).append("</b>\n");
                message.append("   📝 ").append(info.getName()).append("\n");
                message.append("   🎯 ").append(info.getDescription()).append("\n\n");
            }
        }

        message.append("━━━━━━━━━━━━━━━━━━━━\n");
        message.append("💡 使用方法：数据修复 <标识>\n");

        // 动态生成示例
        if (!FIX_IDENTIFIER_INFOS.isEmpty()) {
            String firstIdentifier = FIX_IDENTIFIER_INFOS.keySet().iterator().next();
            message.append("📖 示例：数据修复 ").append(firstIdentifier);
        }

        return message.toString();
    }

    /**
     * 检查修复标识是否存在
     *
     * @param identifier 修复标识
     * @return 是否存在
     */
    public static boolean hasFixHandler(String identifier) {
        return identifier != null && FIX_HANDLERS.containsKey(identifier.trim());
    }

    /**
     * 获取所有可用的修复标识
     *
     * @return 修复标识列表
     */
    public static List<String> getAvailableIdentifierList() {
        return new ArrayList<>(FIX_IDENTIFIER_INFOS.keySet());
    }

    /**
     * 获取修复标识信息
     *
     * @param identifier 修复标识
     * @return 修复标识信息，如果不存在则返回null
     */
    public static FixIdentifierInfo getFixIdentifierInfo(String identifier) {
        return identifier != null ? FIX_IDENTIFIER_INFOS.get(identifier.trim()) : null;
    }
}
