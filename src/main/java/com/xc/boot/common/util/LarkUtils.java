package com.xc.boot.common.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;

import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;

import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.event.EventDispatcher;
import com.lark.oapi.service.im.ImService;
import com.lark.oapi.service.im.v1.model.P2MessageReceiveV1;
import com.lark.oapi.service.im.v1.model.ReplyMessageReq;
import com.lark.oapi.service.im.v1.model.ReplyMessageReqBody;
import com.lark.oapi.service.im.v1.model.ReplyMessageResp;
import com.xc.boot.common.constant.RedisConstants;
import com.xc.boot.common.enums.EnvEnum;
import com.xc.boot.common.util.ContextUtils;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 飞书工具类
 */
@Slf4j
public class LarkUtils {
    public static String APP_ID = "cli_a8cbda95523e500d";
    public static String APP_SECRET = "bHVVmnTycE7sLjSPyePMmhr4Mh8hEzN6";

    /**
     * 命令列表
     */
    public static List<Command> COMMANDS = new ArrayList<>();

    /**
     * Redis模板
     */
    private static RedisTemplate<String, Object> redisTemplate;

    /**
     * 命令对象
     */
    @Data
    @Builder
    public static class Command {
        private String name;
        private String[] commands;
        private String description;
        private String example;
        /**
         * 命令处理函数
         *
         * @param event 事件
         * @param args  命令参数
         * @return 是否处理成功
         */
        private BiFunction<P2MessageReceiveV1, String[], Boolean> handler;
    }

    /**
     * 标记信息对象
     */
    @Data
    @Builder
    public static class MarkInfo {
        private String messageId;
        private long timestamp;
    }

    /**
     * 事件处理器
     */
    private static final EventDispatcher EVENT_HANDLER = EventDispatcher.newBuilder("", "")
            .onP2MessageReceiveV1(new ImService.P2MessageReceiveV1Handler() {
                @Override
                public void handle(P2MessageReceiveV1 event) throws Exception {
                    log.info("[ onP2MessageReceiveV1 access ], data: {}", Jsons.DEFAULT.toJson(event.getEvent()));

                    LarkUtils.onMessageReceive(event);
                }
            })
            .build();

    /**
     * 消息ID缓存，key为消息ID，value为时间戳
     */
    private static final ConcurrentHashMap<String, Long> MESSAGE_CACHE = new ConcurrentHashMap<>();

    /**
     * 消息缓存过期时间（毫秒）
     */
    private static final long MESSAGE_CACHE_EXPIRE_TIME = 3 * 60 * 1000;

    /**
     * 检查消息是否重复
     *
     * @param messageId 消息ID
     * @return 是否重复
     */
    private static boolean isMessageDuplicate(String messageId) {
        Long timestamp = MESSAGE_CACHE.get(messageId);
        if (timestamp == null) {
            return false;
        }

        // 检查是否过期
        if (System.currentTimeMillis() - timestamp > MESSAGE_CACHE_EXPIRE_TIME) {
            MESSAGE_CACHE.remove(messageId);
            return false;
        }

        return true;
    }

    /**
     * 添加消息到缓存
     *
     * @param messageId 消息ID
     */
    private static void addMessageToCache(String messageId) {
        MESSAGE_CACHE.put(messageId, System.currentTimeMillis());
    }

    /**
     * 清理过期的消息缓存
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public static void cleanExpiredMessages() {
        long now = System.currentTimeMillis();
        MESSAGE_CACHE.entrySet().removeIf(entry -> now - entry.getValue() > MESSAGE_CACHE_EXPIRE_TIME);
    }

    /**
     * 设置Redis模板
     */
    public static void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        LarkUtils.redisTemplate = redisTemplate;
    }

    /**
     * 注册命令
     */
    public static void registerCommands(String env) {
        // * 帮助
        COMMANDS.add(Command.builder()
                .name("帮助")
                .commands(new String[] { "help", "h", "?", "帮助" })
                .description("查看命令列表")
                .example("@机器人 help")
                .handler((event, args) -> {
                    StringBuilder message = new StringBuilder();
                    message.append("🤖 <b>命令列表</b>\n");
                    message.append("━━━━━━━━━━━━━━━━━━━━\n\n");

                    for (Command command : COMMANDS) {
                        // 命令名称和描述
                        message.append("📌 <b>").append(command.getName()).append("</b>\n");
                        message.append("   ").append(command.getDescription()).append("\n");

                        // 命令别名
                        message.append("   > 别名：");
                        for (int i = 0; i < command.getCommands().length; i++) {
                            if (i > 0)
                                message.append("、");
                            message.append(command.getCommands()[i]);
                        }
                        message.append("\n");

                        // 使用示例
                        message.append("   > 示例：").append(command.getExample()).append("\n\n");
                    }

                    message.append("━━━━━━━━━━━━━━━━━━━━\n");
                    message.append("💡 提示：直接输入命令即可使用，无需添加任何前缀");

                    LarkUtils.replyTextMessageWithEvent(event, message.toString());
                    return true;
                })
                .build());

        // * 更新API文档
        if (!env.equals(EnvEnum.PROD.getValue())) {
            // 非生产环境才添加更新API文档命令
            COMMANDS.add(Command.builder()
                    .name("更新API文档")
                    .commands(new String[] { "doc", "更新文档" })
                    .description("更新API文档到Apifox")
                    .example("@机器人 doc")
                    .handler((event, args) -> {
                        LarkUtils.replyTextMessageWithEvent(event, "开始更新API文档...");
                        String result = CommonUtils.updateApiDoc();
                        LarkUtils.replyTextMessageWithEvent(event, result);
                        return true;
                    })
                    .build());
        }

        // * 数据修复命令
        COMMANDS.add(Command.builder()
                .name("数据修复")
                .commands(new String[] { "数据修复", "修复数据", "fix" })
                .description("执行数据修复操作")
                .example("@机器人 数据修复 reset_goods_column [商户ID]")
                .handler((event, args) -> {
                    // 检查参数
                    if (args.length == 0) {
                        String result = FixDataUtils.executeDataFix("");
                        LarkUtils.replyTextMessageWithEvent(event, result);
                        return true;
                    }

                    String identifier = args[0];

                    // 提取额外参数（从第二个参数开始）
                    String[] params = new String[0];
                    if (args.length > 1) {
                        params = new String[args.length - 1];
                        System.arraycopy(args, 1, params, 0, args.length - 1);
                    }

                    // 检查标识是否存在
                    if (!FixDataUtils.hasFixHandler(identifier)) {
                        String result = FixDataUtils.executeDataFix(identifier, params);
                        LarkUtils.replyTextMessageWithEvent(event, result);
                        return true;
                    }

                    // 先回复开始处理的消息
                    String paramInfo = params.length > 0 ? " (参数: " + String.join(", ", params) + ")" : "";
                    LarkUtils.replyTextMessageWithEvent(event,
                        "🔧 开始执行数据修复：" + identifier + paramInfo + "\n⏳ 正在处理中，请稍候...");

                    // 异步执行修复操作
                    final String[] finalParams = params;
                    CommonUtils.asyncExecute(() -> {
                        try {
                            String result = FixDataUtils.executeDataFix(identifier, finalParams);
                            LarkUtils.replyTextMessageWithEvent(event, result);
                        } catch (Exception e) {
                            log.error("数据修复异步执行失败", e);
                            LarkUtils.replyTextMessageWithEvent(event,
                                "❌ 数据修复执行失败：" + e.getMessage());
                        }
                    });

                    return true;
                })
                .build());

        // * 获取当前环境标识
        COMMANDS.add(Command.builder()
                .name("环境标识")
                .commands(new String[] { "env", "环境" })
                .description("获取当前环境标识")
                .example("@机器人 env")
                .handler((event, args) -> {
                    String currentEnv = ContextUtils.getActiveProfile();
                    LarkUtils.replyTextMessageWithEvent(event, currentEnv);
                    return true;
                })
                .build());

        // * mark标记功能 - 仅在dev环境生效
        if (env.equals(EnvEnum.DEV.getValue())) {
            COMMANDS.add(Command.builder()
                    .name("标记消息")
                    .commands(new String[] { "mark", "标记" })
                    .description("标记当前消息，应用重启时会提醒该标记已发版")
                    .example("@机器人 mark")
                    .handler((event, args) -> {
                        if (redisTemplate == null) {
                            LarkUtils.replyTextMessageWithEvent(event, "Redis未初始化，无法使用标记功能");
                            return false;
                        }

                        String messageId = event.getEvent().getMessage().getMessageId();
                        try {
                            // 创建包含时间戳的标记信息
                            MarkInfo markInfo = MarkInfo.builder()
                                    .messageId(messageId)
                                    .timestamp(System.currentTimeMillis())
                                    .build();

                            // 将标记信息序列化后添加到Redis列表中
                            redisTemplate.opsForList().rightPush(RedisConstants.LARK_MSG_MARK_KEY, JSONUtil.toJsonStr(markInfo));
                            LarkUtils.replyTextMessageWithEvent(event, "🚩 Get it! ");
                            log.info("[ mark command ] Message {} marked successfully at {}", messageId, markInfo.getTimestamp());
                            return true;
                        } catch (Exception e) {
                            log.error("[ mark command error ] Failed to mark message {}", messageId, e);
                            LarkUtils.replyTextMessageWithEvent(event, "❌ 标记失败：" + e.getMessage());
                            return false;
                        }
                    })
                    .build());
        }
    }

    /**
     * 消息接收事件
     *
     * @param event 事件
     * @return 是否处理
     */
    public static boolean onMessageReceive(P2MessageReceiveV1 event) {
        // 获取消息ID
        String messageId = event.getEvent().getMessage().getMessageId();

        // 检查消息是否重复
        if (isMessageDuplicate(messageId)) {
            log.info("[ onMessageReceive ] Message {} is duplicate, skipping", messageId);
            return false;
        }

        // 记录消息ID到缓存
        addMessageToCache(messageId);

        // 获取消息内容
        String content = event.getEvent().getMessage().getContent();
        if (content == null || content.isEmpty()) {
            replyTextMessageWithEvent(event, "消息为空");
            return false;
        }

        // 解析消息内容
        Map<String, Object> contentMap = JSONUtil.toBean(content, Map.class);
        String text = (String) contentMap.get("text");
        if (text == null || text.isEmpty()) {
            replyTextMessageWithEvent(event, "消息解析失败");
            return false;
        }

        // 按空格拆分命令和参数
        String[] parts = text.trim().split("\\s+");
        if (parts.length == 0) {
            replyTextMessageWithEvent(event, "命令解析失败");
            return false;
        }

        // 如果第一块以@开头，则移除第一块
        int startIndex = 0;
        if (parts[0].startsWith("@")) {
            startIndex = 1;
            if (parts.length <= 1) {
                replyTextMessageWithEvent(event, "命令解析失败");
                return false;
            }
        }

        String command = parts[startIndex];
        String[] args = new String[parts.length - startIndex - 1];
        System.arraycopy(parts, startIndex + 1, args, 0, args.length);

        // 遍历命令列表进行匹配
        for (Command cmd : COMMANDS) {
            for (String cmdName : cmd.getCommands()) {
                if (command.equals(cmdName)) {
                    // 执行命令处理函数
                    try {
                        return cmd.getHandler().apply(event, args);
                    } catch (Exception e) {
                        log.error("[ onMessageReceive error ], command: {}, error: {}", cmdName, e.getMessage(), e);
                        replyTextMessageWithEvent(event, "命令执行失败：" + e.getMessage());
                        return true;
                    }
                }
            }
        }

        replyTextMessageWithEvent(event, "<b>未定义的命令:</b> [ " + command + " ] \n\n" + "你可以使用 <b>help</b> 查看命令列表");
        return false;
    }

    /**
     * 回复文字消息
     *
     * @param event 事件
     * @param text  文本
     * @return 是否处理
     */
    public static boolean replyTextMessageWithEvent(P2MessageReceiveV1 event, String text) {
        Client client = getClient();

        // 创建请求对象
        ReplyMessageReq req = ReplyMessageReq.newBuilder()
                .messageId(event.getEvent().getMessage().getMessageId())
                .replyMessageReqBody(ReplyMessageReqBody.newBuilder()
                        .content(JSONUtil.toJsonStr(Map.of("text", text)))
                        .msgType("text")
                        .build())
                .build();

        // 发起请求
        try {
            ReplyMessageResp resp = client.im().v1().message().reply(req);

            if (!resp.success()) {
                throw new RuntimeException(resp.getMsg());
            }
        } catch (Exception e) {
            log.error("[ replyTextMessageWithEvent error ], data: {}", Jsons.DEFAULT.toJson(req), e);
        }

        return false;
    }

    public static Client getClient() {
        return Client.newBuilder(APP_ID, APP_SECRET).build();
    }

    /**
     * 启动事件处理器
     *
     * @param env 环境
     */
    public static void startEventHandler(String env, AsyncTaskExecutor asyncTaskExecutor) {
        // 本地环境不启动事件处理器
        if (env.equals(EnvEnum.LOCAL.getValue())) {
            return;
        }

        // 异步启动事件处理器
        asyncTaskExecutor.execute(() -> {
            // dev 机器人配置
            APP_ID = "cli_a8cbda95523e500d";
            APP_SECRET = "bHVVmnTycE7sLjSPyePMmhr4Mh8hEzN6";

            if (env.equals(EnvEnum.PROD.getValue())) {
                // 替换为 prod 机器人配置
                APP_ID = "cli_a81aa770e81f100b";
                APP_SECRET = "UZ1k1PI7zpkU6cJzM6MAdhf61yETy3dI";
            }

            // 注册命令
            registerCommands(env);

            // 启动事件处理器
            com.lark.oapi.ws.Client client = new com.lark.oapi.ws.Client.Builder(APP_ID, APP_SECRET)
                    .eventHandler(EVENT_HANDLER)
                    .build();

            client.start();
        });
    }

    /**
     * 检查并处理标记的消息
     */
    public static void checkAndHandleMarkedMessages(String env, AsyncTaskExecutor asyncTaskExecutor) {
        // 仅在dev环境下处理标记消息
        if (!env.equals(EnvEnum.DEV.getValue()) || redisTemplate == null) {
            return;
        }

        asyncTaskExecutor.execute(() -> {
            try {
                // 获取所有标记的数据
                List<Object> markedData = redisTemplate.opsForList().range(RedisConstants.LARK_MSG_MARK_KEY, 0, -1);

                if (markedData != null && !markedData.isEmpty()) {
                    log.info("[ checkAndHandleMarkedMessages ] Found {} marked items", markedData.size());

                    long currentTime = System.currentTimeMillis();
                    long threeMinutesInMillis = 3 * 60 * 1000; // 3分钟

                    List<MarkInfo> pendingMarks = new ArrayList<>(); // 未到时间的标记
                    List<MarkInfo> processedMarks = new ArrayList<>(); // 已处理的标记

                    // 解析并分类标记
                    for (Object dataObj : markedData) {
                        String dataStr = dataObj.toString();
                        try {
                            MarkInfo markInfo = JSONUtil.toBean(dataStr, MarkInfo.class);

                            // 检查是否超过3分钟
                            if (currentTime - markInfo.getTimestamp() >= threeMinutesInMillis) {
                                processedMarks.add(markInfo);
                            } else {
                                pendingMarks.add(markInfo);
                            }
                        } catch (Exception e) {
                            log.error("[ checkAndHandleMarkedMessages ] Failed to parse mark data: {}", dataStr, e);
                        }
                    }

                    // 处理超过3分钟的标记
                    for (MarkInfo markInfo : processedMarks) {
                        try {
                            replyToMarkedMessage(markInfo.getMessageId());
                            log.info("[ checkAndHandleMarkedMessages ] Replied to marked message: {} (marked at: {})",
                                    markInfo.getMessageId(), markInfo.getTimestamp());
                        } catch (Exception e) {
                            log.error("[ checkAndHandleMarkedMessages ] Failed to reply to marked message: {}",
                                    markInfo.getMessageId(), e);
                        }
                    }

                    // 清除所有标记并重新添加未到时间的标记
                    redisTemplate.delete(RedisConstants.LARK_MSG_MARK_KEY);

                    // 重新添加未到时间的标记
                    for (MarkInfo pendingMark : pendingMarks) {
                        redisTemplate.opsForList().rightPush(RedisConstants.LARK_MSG_MARK_KEY, JSONUtil.toJsonStr(pendingMark));
                    }

                    log.info("[ checkAndHandleMarkedMessages ] Processed {} marks, {} pending marks remain",
                            processedMarks.size(), pendingMarks.size());
                }
            } catch (Exception e) {
                log.error("[ checkAndHandleMarkedMessages ] Error processing marked messages", e);
            }
        });
    }

    /**
     * 回复标记的消息
     */
    private static void replyToMarkedMessage(String messageId) {
        Client client = getClient();

        // 创建请求对象
        ReplyMessageReq req = ReplyMessageReq.newBuilder()
                .messageId(messageId)
                .replyMessageReqBody(ReplyMessageReqBody.newBuilder()
                        .content(JSONUtil.toJsonStr(Map.of("text", "🔔 这个标记可能已经发版了哦~")))
                        .msgType("text")
                        .build())
                .build();

        // 发起请求
        try {
            ReplyMessageResp resp = client.im().v1().message().reply(req);
            if (!resp.success()) {
                throw new RuntimeException(resp.getMsg());
            }
        } catch (Exception e) {
            log.error("[ replyToMarkedMessage error ], messageId: {}, error: {}", messageId, e.getMessage(), e);
        }
    }

    /**
     * 应用启动提醒
     */
    public static void appSetupRemind(String env, AsyncTaskExecutor asyncTaskExecutor) {
        if (env.equals(EnvEnum.LOCAL.getValue())) {
            return;
        }

        asyncTaskExecutor.execute(() -> {
            String url = "https://open.feishu.cn/open-apis/bot/v2/hook/20adb3a2-2fc0-4314-b26a-4f4370fc986b";
            String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String content = "✅ " + now + " \\n\\n" + env + " 已启动🎉";
            String body = "{\"msg_type\": \"text\", \"content\": {\"text\": \"" + content + "\"}}";
            HttpUtil.post(url, body);
        });
    }

    /**
     * 上报异常到飞书（仅生产环境）
     *
     * @param exception 异常对象
     * @param context 异常上下文信息（可选）
     */
    public static void reportException(Throwable exception, String context) {
        // 仅在生产环境上报异常
        if (!EnvEnum.PROD.getValue().equals(ContextUtils.getActiveProfile())) {
            return;
        }

        try {
            // 新机器人webhook地址
            String webhookUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/9e9858b5-e150-4f04-a945-d95c99b860c8";

            // 构建富文本消息
            String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            // 异常消息处理
            String exceptionMessage = exception.getMessage();
            if (exceptionMessage != null && exceptionMessage.length() > 500) {
                exceptionMessage = exceptionMessage.substring(0, 500) + "...";
            }

            // 上下文信息处理
            String contextInfo = context;
            if (contextInfo != null && contextInfo.length() > 300) {
                contextInfo = contextInfo.substring(0, 300) + "...";
            }

            // 堆栈信息处理
            StringBuilder stackTraceInfo = new StringBuilder();
            StackTraceElement[] stackTrace = exception.getStackTrace();
            if (stackTrace != null && stackTrace.length > 0) {
                int maxLines = Math.min(15, stackTrace.length); // 最多显示15行堆栈
                for (int i = 0; i < maxLines; i++) {
                    stackTraceInfo.append(stackTrace[i].toString());
                    if (i < maxLines - 1) stackTraceInfo.append("\n");
                }
                if (stackTrace.length > maxLines) {
                    stackTraceInfo.append("\n...");
                }
            }

            // 构建简洁清晰的文本消息
            StringBuilder messageContent = new StringBuilder();
            messageContent.append("🚨 系统异常告警 🚨\n");
            messageContent.append("━━━━━━━━━━━━━━━━━━━━\n\n");

            // 基本信息
            messageContent.append("⏰ 发生时间: ").append(now).append("\n");
            messageContent.append("🔥 异常类型: ").append(exception.getClass().getSimpleName()).append("\n\n");

            // 异常消息
            if (exceptionMessage != null && !exceptionMessage.isEmpty()) {
                messageContent.append("💬 异常消息:\n");
                messageContent.append(exceptionMessage).append("\n\n");
            }

            // 上下文信息
            if (contextInfo != null && !contextInfo.isEmpty()) {
                messageContent.append("📍 上下文信息:\n");
                messageContent.append(contextInfo).append("\n\n");
            }

            // 堆栈信息
            if (stackTraceInfo.length() > 0) {
                messageContent.append("📋 堆栈信息:\n");
                messageContent.append(stackTraceInfo.toString()).append("\n\n");
            }

            // 底部提示
            messageContent.append("━━━━━━━━━━━━━━━━━━━━\n");
            messageContent.append("💡 请及时处理异常，确保系统稳定运行");

            String requestBody = JSONUtil.createObj()
                    .set("msg_type", "text")
                    .set("content", JSONUtil.createObj().set("text", messageContent.toString()))
                    .toString();

            // 检查内容长度，如果超过限制则降级为简单文本消息
            final String finalRequestBody;
            if (requestBody.length() > 18000) {
                String simpleContent = String.format(
                    "🚨 系统异常告警\\n\\n" +
                    "⏰ 时间: %s\\n" +
                    "🔥 类型: %s\\n" +
                    "%s%s",
                    now,
                    exception.getClass().getSimpleName(),
                    exceptionMessage != null ? "💬 消息: " + exceptionMessage + "\\n" : "",
                    contextInfo != null ? "📍 上下文: " + contextInfo : ""
                );

                finalRequestBody = JSONUtil.createObj()
                        .set("msg_type", "text")
                        .set("content", JSONUtil.createObj().set("text", simpleContent))
                        .toString();
            } else {
                finalRequestBody = requestBody;
            }

            // 异步发送，避免影响主业务
            new Thread(() -> {
                try {
                    HttpUtil.post(webhookUrl, finalRequestBody);
                    log.info("[ reportException ] Exception reported to Lark successfully");
                } catch (Exception e) {
                    log.error("[ reportException ] Failed to report exception to Lark", e);
                }
            }).start();

        } catch (Exception e) {
            log.error("[ reportException ] Error while reporting exception", e);
        }
    }

    /**
     * 上报异常到飞书（仅生产环境）- 重载方法
     *
     * @param exception 异常对象
     */
    public static void reportException(Throwable exception) {
        reportException(exception, null);
    }
}
