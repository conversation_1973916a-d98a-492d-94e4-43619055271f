package com.xc.boot.common.util;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.hutool.core.collection.CollectionUtil;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.system.mapper.GoodsLogMapper;
import com.xc.boot.system.mapper.LoginLogMapper;
import com.xc.boot.system.model.entity.GoodsLogEntity;
import com.xc.boot.system.model.entity.LoginLogEntity;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.system.mapper.OpLogMapper;
import com.xc.boot.system.model.entity.OpLogEntity;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 操作日志工具类
 */
@Component
public class OpLogUtils implements ApplicationContextAware {
    private static OpLogMapper opLogMapper;
    private static GoodsLogMapper goodsLogMapper;
    private static LoginLogMapper loginLogMapper;
    private static AsyncTaskExecutor asyncTaskExecutor;
    @SuppressWarnings("unused")
    private static ApplicationContext applicationContext;
    // 常量字符
    public static final String MODIFY_STRING = " 修改为 ";
    public static final String NEW_LINE = "\n";
    public static final String COLON = ": ";
    public static final String SPACE = " ";

    /**
     * 操作日志列表
     */
    public static final ThreadLocal<List<OpLogEntity>> OP_LOG_LIST = new ThreadLocal<>();
    /**
     * 货品日志列表
     */
    public static final ThreadLocal<List<GoodsLogEntity>> GOODS_LOG_LIST = new ThreadLocal<>();
    /**
     * 登录日志列表
     */
    public static final ThreadLocal<List<LoginLogEntity>> LOGIN_LOG_LIST = new ThreadLocal<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        OpLogUtils.applicationContext = applicationContext;

        opLogMapper = applicationContext.getBean(OpLogMapper.class);
        goodsLogMapper = applicationContext.getBean(GoodsLogMapper.class);
        loginLogMapper = applicationContext.getBean(LoginLogMapper.class);
        asyncTaskExecutor = applicationContext.getBean(AsyncTaskExecutor.class);
    }

    /**
     * 初始化操作日志列表
     */
    public static void init(ThreadLocal threadLocal) {
        threadLocal.set(new ArrayList<>());
    }

    /**
     * 移除操作日志列表
     */
    public static void remove() {
        OP_LOG_LIST.remove();
        GOODS_LOG_LIST.remove();
        LOGIN_LOG_LIST.remove();
    }

    /**
     * 异步执行任务
     * 
     * @param runnable 任务
     */
    public static void asyncExecute(Runnable runnable) {
        asyncTaskExecutor.execute(runnable);
    }

    /**
     * 添加操作日志
     * 
     * @param comment 操作日志描述
     * @param content 操作日志内容
     * @param extra   操作日志额外信息
     */
    public static void appendOpLog(String comment, String content, Object extra) {
        // 如果 ThreadLocal 为空，则初始化
        if (OP_LOG_LIST.get() == null) {
            init(OP_LOG_LIST);
        }
        
        OpLogEntity opLog = new OpLogEntity();
        opLog.setComment(comment);
        opLog.setContent(content);
        opLog.setExtra(JSONUtil.toJsonStr(extra));
        OP_LOG_LIST.get().add(opLog);
    }

    /**
     * 添加登录日志
     * @param comment 操作日志描述
     * @param content 操作日志内容
     * @param sideCode 登录端
     * @param username 用户名
     */
    public static void appendLoginLog(String comment, String content, String sideCode, String username) {
        if (LOGIN_LOG_LIST.get() == null) {
            init(LOGIN_LOG_LIST);
        }
        LoginLogEntity loginLog = new LoginLogEntity();
        loginLog.setComment(comment);
        loginLog.setContent(content);
        loginLog.setSideCode(sideCode);
        loginLog.setUsername(username);
        LOGIN_LOG_LIST.get().add(loginLog);
    }

    /**
     * 添加日志
     *
     * @param comment 日志描述
     * @param content 日志内容
     * @param extra   日志额外信息
     */
    public static void appendGoodsLog(String comment, String content, Object extra, GoodsEntity goods) {
        // 如果 ThreadLocal 为空，则初始化
        if (GOODS_LOG_LIST.get() == null) {
            init(GOODS_LOG_LIST);
        }
        GoodsLogEntity goodsLog = new GoodsLogEntity();
        goodsLog.setComment(comment);
        goodsLog.setContent(content);
        goodsLog.setExtra(JSONUtil.toJsonStr(extra));
        goodsLog.setGoodsId(goods.getId());
        goodsLog.setGoodsSn(goods.getGoodsSn());
        goodsLog.setCompanyId(goods.getCompanyId());
        goodsLog.setMerchantId(goods.getMerchantId());
        goodsLog.setCreatedAt(new Date());
        GOODS_LOG_LIST.get().add(goodsLog);
    }

    /**
     * 收集日志
     */
    public static void collect(HttpServletRequest request) {
        // 操作日志
        List<OpLogEntity> opLogs = OP_LOG_LIST.get();
        // 货品日志
        List<GoodsLogEntity> goodsLogs = GOODS_LOG_LIST.get();
        // 登录日志
        List<LoginLogEntity> loginLogs = LOGIN_LOG_LIST.get();

        if (CollectionUtil.isEmpty(opLogs) && CollectionUtil.isEmpty(goodsLogs) && CollectionUtil.isEmpty(loginLogs)) {
            return;
        }
        String requestMethod = request.getMethod();
        String requestUri = request.getRequestURI();
        String requestParams = JSONUtil.toJsonStr(request.getParameterMap());
        String ip = IPUtils.getIpAddr(request);
        CommonUtils.asyncExecute(() -> {
            // * 处理基础数据
            Long userId = SecurityUtils.getUserId();
            Long companyId = SecurityUtils.getCompanyId();
            String province = "";
            String city = "";
            if (StrUtil.isNotBlank(ip)) {
                String region = IPUtils.getRegion(ip);
                // 中国|0|四川省|成都市|电信 解析省和市
                if (StrUtil.isNotBlank(region)) {
                    String[] regionArray = region.split("\\|");
                    if (regionArray.length > 2) {
                        province = regionArray[2];
                        city = regionArray[3];
                    }
                }
            }

            // * 处理操作日志
            if (CollectionUtil.isNotEmpty(opLogs)) {
                List<OpLogEntity> insertOpLogs = new ArrayList<>();
                for (OpLogEntity opLog : opLogs) {
                    opLog.setUserId(userId);
                    opLog.setCompanyId(companyId);
                    opLog.setRequestMethod(requestMethod);
                    opLog.setRequestUri(requestUri);
                    opLog.setRequestParams(requestParams);
                    opLog.setIp(ip);
                    opLog.setProvince(province);
                    opLog.setCity(city);
                    insertOpLogs.add(opLog);
                }
                opLogMapper.insertBatchSelective(insertOpLogs, 2000);
            }
            // * 处理货品日志
            if (CollectionUtil.isNotEmpty(goodsLogs)) {
                List<GoodsLogEntity> insertLogs = new ArrayList<>();
                for (GoodsLogEntity goodsLog : goodsLogs) {
                    goodsLog.setUserId(userId);
                    goodsLog.setCompanyId(companyId);
                    goodsLog.setRequestMethod(requestMethod);
                    goodsLog.setRequestUri(requestUri);
                    goodsLog.setRequestParams(requestParams);
                    goodsLog.setIp(ip);
                    goodsLog.setProvince(province);
                    goodsLog.setCity(city);
                    insertLogs.add(goodsLog);
                }
                goodsLogMapper.insertBatchSelective(insertLogs, 2000);
            }
            // * 处理登录日志
            if (CollectionUtil.isNotEmpty(loginLogs)) {
                List<LoginLogEntity> insertLoginLogs = new ArrayList<>();
                for (LoginLogEntity loginLog : loginLogs) {
                    loginLog.setUsername(loginLog.getUsername());
                    loginLog.setSideCode(loginLog.getSideCode());
                    loginLog.setCompanyId(companyId);
                    loginLog.setRequestMethod(requestMethod);
                    loginLog.setRequestUri(requestUri);
                    loginLog.setRequestParams(requestParams);
                    loginLog.setIp(ip);
                    loginLog.setProvince(province);
                    loginLog.setCity(city);
                    insertLoginLogs.add(loginLog);
                }
                loginLogMapper.insertBatchSelective(insertLoginLogs, 2000);
            }
        });
        remove();
        // 清理所有线程变量
        CommonUtils.clearThreadLocal();
    }
}
