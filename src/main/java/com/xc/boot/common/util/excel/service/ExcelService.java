package com.xc.boot.common.util.excel.service;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.FileUtil;
import com.xc.boot.common.util.excel.model.ExcelExportConfig;
import com.xc.boot.common.util.excel.model.ExcelImgDto;
import com.xc.boot.common.util.excel.model.ExportStatusEnum;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.shared.file.service.FileService;
import com.xc.boot.system.mapper.ExportMapper;
import com.xc.boot.system.model.entity.FileEntity;
import com.xc.boot.system.model.entity.SysExportEntity;
import com.xc.boot.system.model.vo.CompanySettingsVO;
import com.xc.boot.system.model.vo.HeadingColumns;
import com.xc.boot.system.model.vo.TableHeading;
import com.xc.boot.system.service.HeadingSignsService;
import lombok.RequiredArgsConstructor;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

import static com.xc.boot.system.model.entity.table.SysExportTableDef.SYS_EXPORT;

/**
 * <AUTHOR>
 * @ClassName ExcelService
 * @Date: 2025/6/6 16:30
 * @Description: 导出工具服务类
 */
@Service
@RequiredArgsConstructor
public class ExcelService {
    private final static Long  MAX_FILE_SIZE = 1024 * 1024 * 100L;
    private final ExportMapper exportMapper;
    private final TransactionTemplate transactionTemplate;
    private final HeadingSignsService headingSignsService;
    private final AsyncTaskExecutor executor;
    private final FileService fileService;

    /**
     * 默认导出配置
     */
    public void config(ExcelExportConfig config) {
        // 图片尺寸
        CompanySettingsVO settings = CommonUtils.getCompanySettings(SecurityUtils.getCompanyId());
        if (settings != null && settings.getImageExportSize() != null) {
            config.setImgHeight(settings.getImageExportSize().getHeight());
            config.setImgWidth(settings.getImageExportSize().getWidth());
            config.setSizeCtrlEnum(settings.getImageExportSize().getCtrl());
        }
    }

    /**
     * 校验是否有任务正在进行中
     * 校验导出条数是否超出限制
     */
    public Long check(BaseMapper<?> mapper, QueryWrapper wrapper, String sign, ExcelExportConfig config) {
        Long userId = SecurityUtils.getUserId();
        Long companyId = SecurityUtils.getCompanyId();
        long count = mapper.selectCountByQuery(wrapper);
        Assert.isTrue(count <= CommonUtils.getMaxExportSize(), String.format("导出数据条数超出最大限制%d条", CommonUtils.getMaxExportSize()));

        long exportCount = exportMapper.selectCountByQuery(QueryWrapper.create()
                .where(SYS_EXPORT.COMPANY_ID.eq(companyId))
                .where(SYS_EXPORT.USER_ID.eq(userId))
                .where(SYS_EXPORT.SIGN.eq(sign))
                .where(SYS_EXPORT.STATUS.in(List.of(ExportStatusEnum.WAITING.getValue(), ExportStatusEnum.EXPORTING.getValue())))
                .where(SYS_EXPORT.CREATED_AT.ge(LocalDateTime.now().minusDays(1))));
        Assert.isTrue(exportCount == 0, "该列表存在导出任务正在执行中");
        return count;
    }

    /**
     * 保存导出任务
     */
    public SysExportEntity insertExport(String name, String sign, Long count) {
        Long userId = SecurityUtils.getUserId();
        Long companyId = SecurityUtils.getCompanyId();
        SysExportEntity sysExportEntity = new SysExportEntity().setStatus(ExportStatusEnum.EXPORTING.getValue())
                .setName(name)
                .setLineCount(count)
                .setSign(sign)
                .setType(1)
                .setUserId(userId)
                .setCompanyId(companyId);
        transactionTemplate.execute(status -> exportMapper.insertSelective(sysExportEntity));
        return sysExportEntity;
    }

    /**
     * 根据sign获取用户自定义表头字段
     */
    public List<HeadingColumns> getUserFiledBySign(String sign) {
        TableHeading tableHeading = headingSignsService.getTableHeading(sign, null);
        return tableHeading.getColumns();
    }

    /**
     * 设置导出任务状态
     * (直接提交事务)
     */
    public void updateExportTask(SysExportEntity sysExportEntity) {
        transactionTemplate.execute(status -> exportMapper.update(sysExportEntity));
    }

    /**
     * 提交一个异步任务
     * @param runnable
     */
    public void doAsyncTask(Runnable runnable) {
        executor.submit(runnable);
    }

    /**
     * 根据图片字段获取图片数据
     * @param list
     * @param imgFields
     * @return
     */
    public Map<String, ExcelImgDto> getImgMapAsync(List<?> list, List<String> imgFields) {
        Map<String, ExcelImgDto> result = new ConcurrentHashMap<>();
        // 获取待下载图片列表、去重
        Set<String> imgUrlSet = new HashSet<>();
        for (String imgField : imgFields) {
            for (Object obj : list) {
                String url = null;
                if (obj instanceof JSONObject jsonObject) {
                    Object object = jsonObject.get(imgField);
                    if (object != null) {
                        url = object.toString();
                    }
                }else {
                    Object fieldValue = ReflectUtil.getFieldValue(obj, imgField);
                    if (fieldValue != null) {
                        url = fieldValue.toString();
                    }
                }
                if (url != null && url.startsWith("http")) {
                    // 阿里云存储图片处理,原始列表和Set中图片均加上后缀，作为map的key
                    url = url + "?x-oss-process=image/quality,Q_20/resize,m_fixed,h_160,w_160";
                    if (obj instanceof JSONObject jsonObject) {
                        jsonObject.set(imgField, url);
                    }else {
                        ReflectUtil.setFieldValue(obj, imgField, url);
                    }
                    imgUrlSet.add(url);
                }
            }
        }
        if (imgUrlSet.isEmpty()) {
            return result;
        }
        // 已读取文件大小
        AtomicLong readSize = new AtomicLong(0);
        // 临时创建一个线程池,异步读取图片数据,执行完毕后自动关闭
        try(ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<CompletableFuture<?>> futures = new ArrayList<>();
            for (String imgUrl : imgUrlSet) {
                if (readSize.get() > MAX_FILE_SIZE) {
                    executor.shutdownNow();
                    throw new BusinessException("导出失败：图片文件内容过大！");
                }
                futures.add(CompletableFuture.runAsync(() -> downloadImg(imgUrl, result, readSize), executor));
            }
            // 等待线程全部执行完毕
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
        return result;
    }

    private void downloadImg(String imgUrl, Map<String, ExcelImgDto> result, AtomicLong readSize) {
        if (readSize.get() > MAX_FILE_SIZE) {
            return;
        }
        byte[] bytes = FileUtil.readFile(imgUrl);
        ExcelImgDto excelImgDto = new ExcelImgDto().setImg(bytes).setImgUrl(imgUrl);
        // 设置默认宽高
        getImageDimensions(bytes, excelImgDto);
        result.put(imgUrl, excelImgDto);
        if (bytes != null) {
            readSize.addAndGet(bytes.length);
        }
    }

    public FileEntity upload(InputStream inputStream, String name, Long size) {
        return fileService.uploadFileByStream(inputStream, name, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", size, "系统导出文件");
    }

    public static void getImageDimensions(byte[] imageBytes, ExcelImgDto excelImgDto) {
        try (ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes)) {
            BufferedImage bufferedImage = ImageIO.read(bis);
            if (bufferedImage != null) {
                int width = bufferedImage.getWidth();
                int height = bufferedImage.getHeight();
                excelImgDto.setWidth(width);
                excelImgDto.setHeight(height);
            }
        } catch (Exception ignore) {
        }
    }
}
