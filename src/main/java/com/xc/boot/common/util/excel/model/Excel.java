package com.xc.boot.common.util.excel.model;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface Excel {
    /**
     * 类型 1:基础字段(如Long、String、Date等，date会进行format，Bigdecimal会toPlantString，其余字段会直接toString)
     * 2:图片字段 获取图片
     * 3:字典 根据配置的字典类型映射，如果配置错误会直接为空
     * 4:集合 配置了子字段 则取子字段join 否则直接join
     * @return
     */
    int type() default 1;

    /**
     * 字典类型key
     * @return
     */
    String[] key() default "";

    /**
     * 字典类型value
     * @return
     */
    String[] value() default "";

    String subFieldName() default "";
}
