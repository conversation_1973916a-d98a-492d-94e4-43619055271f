package com.xc.boot.common.constant;

/**
 * 缓存常量
 *
 * <AUTHOR>
 * @since 2023/11/24
 */
public interface SecurityConstants {

    /**
     * 验证码缓存前缀
     */
    String CAPTCHA_CODE_PREFIX = "captcha_code:";

    /**
     * 角色和权限缓存前缀
     */
    String ROLE_PERMS_PREFIX = "role_perms:";

    /**
     * 黑名单Token缓存前缀
     */
    String BLACKLIST_TOKEN_PREFIX = "token:blacklist:";

    /**
     * redis登录验证前缀
     */
    String REDIS_TOKEN_PREFIX = "token:redis:";


    /**
     * 登录路径
     */
    String LOGIN_PATH = "/api/auth/login";


    /**
     * JWT Token 前缀
     */
    String JWT_TOKEN_PREFIX = "Bearer ";

    /**
     * 微信登录路径
     */
    String WECHAT_LOGIN_PATH = "/api/auth/wechat-login";

    /**
     * 密码正则表达式(忽略了开头和结尾的空格，需要使用trim())
     * 允许开头和结尾有空字符，核心部分严格限制长度为8～20，必须同时有字母和数字，允许所有特殊字符包括空格、中文
     */
    String PASSWORD_PATTERN = "^\\s*(?=.*[a-zA-Z])(?=.*\\d)[\\S\\s]{8,20}\\s*$";
    /**
     * 手机号正则
     */
    String MOBILE_PATTERN = "^$|^1(3\\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\\d|9[0-35-9])\\d{8}$";

    String PASSWORD_TIPS = "密码必须包含字母和数字，长度为8-20位!";

    /**
     * 用户名新增、修改、更新锁前缀
     */
    String USERNAME_VERIFY_KEY = "username:verify:";

}
