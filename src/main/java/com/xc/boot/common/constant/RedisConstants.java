package com.xc.boot.common.constant;

/**
 * Redis Key常量
 *
 * <AUTHOR>
 * @since 2024-7-29 11:46:08
 */
public interface RedisConstants {

    /**
     * 系统配置Redis-key
     */
    String SYSTEM_CONFIG_KEY = "system:config";

    /**
     * IP限流Redis-key
     */
    String IP_RATE_LIMITER_KEY = "ip:rate:limiter:";

    /**
     * 防重复提交Redis-key
     */
    String RESUBMIT_LOCK_PREFIX = "resubmit:lock:";

    /**
     * 单个IP请求的最大每秒查询数（QPS）阈值Key
     */
    String IP_QPS_THRESHOLD_LIMIT_KEY = "IP_QPS_THRESHOLD_LIMIT";

    /**
     * 文件大小限制
     */
    String FILE_SIZE_LIMIT = "FILE_SIZE_LIMIT";

    /**
     * 手机验证码缓存前缀
     */

    String MOBILE_VERIFICATION_CODE_PREFIX = "VERIFICATION_CODE:MOBILE:";

    /**
     * 手机验证码60s前缀
     */
    String MOBILE_VERIFICATION_CODE_AWAIT_PREFIX = "VERIFICATION_CODE:AWAIT:MOBILE:";

    /**
     * 账号密码错误次数统计
     */
    String MOBILE_VERIFICATION_BLOCK_COUNT_PREFIX = "VERIFICATION:BLOCK:COUNT:MOBILE:";

    /**
     * 手机验证码错误次数统计
     */
    String CODE_VERIFICATION_BLOCK_COUNT_PREFIX = "VERIFICATION:BLOCK:COUNT:CODE:";

    /**
     * PDA账号密码错误次数统计
     */
    String PDA_MOBILE_VERIFICATION_BLOCK_COUNT_PREFIX = "PDA:VERIFICATION:BLOCK:COUNT:MOBILE:";

    /**
     * PDA手机验证码错误次数统计
     */
    String PDA_CODE_VERIFICATION_BLOCK_COUNT_PREFIX = "PDA:VERIFICATION:BLOCK:COUNT:CODE:";

    /**
     * 小程序账号密码错误次数统计
     */
    String MINI_APP_MOBILE_VERIFICATION_BLOCK_COUNT_PREFIX = "MINI:APP:VERIFICATION:BLOCK:COUNT:MOBILE:";

    /**
     * 小程序手机验证码错误次数统计
     */
    String MINI_APP_CODE_VERIFICATION_BLOCK_COUNT_PREFIX = "MINI:APP:VERIFICATION:BLOCK:COUNT:CODE:";

    /**
     * 登录错误次数过多 禁止登录
     */
    String MOBILE_VERIFICATION_BLOCK_PREFIX = "VERIFICATION:BLOCK:MOBILE:";

    /**
     * 修改手机号锁前缀
     */

    String MODIFY_MOBILE_PREFIX = "MODIFY_USERNAME:MOBILE:";


    /**
     * 邮箱验证码缓存前缀
     */
    String EMAIL_VERIFICATION_CODE_PREFIX = "VERIFICATION_CODE:EMAIL:";

    /**
     * 刷新金价前缀
     */
    String GOLD_PRICE_PLUSH_PREFIX = "GOLD_PRICE:PLUSH:MOBILE:";

    /**
     * 飞书消息标记前缀
     */
    String LARK_MSG_MARK_KEY = "lark_msg:mark";

}
