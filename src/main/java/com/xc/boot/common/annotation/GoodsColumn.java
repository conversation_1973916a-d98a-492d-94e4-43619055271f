package com.xc.boot.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface GoodsColumn {
    String value() default "";

    /**
     * 是否将sign添加到结果对象中，
     * 当多个字段共用了同一个sign时，
     * 只有其中一个该设为true，其余设为false，避免互相覆盖
     * @return true/false
     */
    boolean signAddList() default true;
}
