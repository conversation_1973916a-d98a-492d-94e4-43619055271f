package com.xc.boot.common.annotation;

import java.lang.annotation.*;

/**
 * 请求级缓存注解
 * 用于在同一个请求中缓存方法调用结果
 * 请求结束后自动清除缓存
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequestCache {
    /**
     * 缓存key的前缀
     */
    String prefix() default "";

    /**
     * 是否使用所有参数作为缓存key
     * 如果为false，则只使用第一个参数作为key
     */
    boolean useAllParams() default true;
} 