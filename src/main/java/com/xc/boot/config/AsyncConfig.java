package com.xc.boot.config;

import com.xc.boot.core.security.decorator.SecurityContextCopyTaskDecorator;
import org.springframework.boot.web.embedded.tomcat.TomcatProtocolHandlerCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.support.TaskExecutorAdapter;

import java.util.concurrent.Executors;

/**
 * 配置虚拟线程池
 */
@Configuration
public class AsyncConfig {
    /**
     * `@Async` 注解用虚拟线程执行
     * @return AsyncTaskExecutor
     */
    @Bean
    @Primary //虚拟线程池作为主线程池
    public AsyncTaskExecutor asyncTaskExecutor(){
        TaskExecutorAdapter executorAdapter = new TaskExecutorAdapter(Executors.newVirtualThreadPerTaskExecutor());
        executorAdapter.setTaskDecorator(new SecurityContextCopyTaskDecorator());
        return executorAdapter;
    }

    /**
     * tomcat用虚拟线程来接收请求
     * @return TomcatProtocolHandlerCustomizer`<T>`
     */
    @Bean
    public TomcatProtocolHandlerCustomizer<?> protocolHandlerVirtualThreadExecutorCustomizer() {
        return protocolHandler -> protocolHandler.setExecutor(Executors.newVirtualThreadPerTaskExecutor());
    }
}
