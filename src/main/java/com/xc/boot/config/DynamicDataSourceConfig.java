package com.xc.boot.config;

import com.xc.boot.config.interceptor.SqlStatementInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <AUTHOR>
 * @ClassName DynamicDataSourceConfig
 * @Date: 2025/5/29 17:11
 * @Description: 描述
 */
@Configuration
@RequiredArgsConstructor
public class DynamicDataSourceConfig {

    @Bean
    public SqlStatementInterceptor sqlStatementInterceptor() {
        return new SqlStatementInterceptor();
    }
}
