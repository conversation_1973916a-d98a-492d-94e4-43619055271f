package com.xc.boot.config;

import com.xc.boot.config.property.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ConfigBeanConfiguration {

    /**
     * 验证码 属性配置
     * @return /
     */
    @Bean
    @ConfigurationProperties(prefix = "captcha")
    public CaptchaProperties captchaProperties() {
        return new CaptchaProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "msg")
    public SmsProperties smsProperties() {
        return new SmsProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "gold")
    public GoldPriceProperties goldPriceProperties() {
        return new GoldPriceProperties();
    }

    /**
     * 代码生成配置属性
     * @return /
     */
    @Bean
    @ConfigurationProperties(prefix = "codegen")
    public CodegenProperties codegenProperties() {
        return new CodegenProperties();
    }

    /**
     * 邮件配置类，用于接收和存储邮件相关的配置属性。
     * @return /
     */
    @Bean
    @ConfigurationProperties(prefix = "spring.mail")
    public MailProperties mailProperties() {
        return new MailProperties();
    }

    /**
     * 安全配置属性
     */
    @Bean
    @ConfigurationProperties(prefix = "security")
    public SecurityProperties securityProperties() {
        return new SecurityProperties();
    }
}
