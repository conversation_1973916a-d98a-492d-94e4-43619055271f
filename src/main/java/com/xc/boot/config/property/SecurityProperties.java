package com.xc.boot.config.property;

import lombok.Data;
import java.util.List;

/**
 * 安全配置属性
 *
 * <AUTHOR>
 * @since 2024/4/18
 */
@Data
public class SecurityProperties {

    /**
     * JWT 配置
     */
    private JwtProperty jwt;

    /**
     * 白名单 URL 集合
     */
    private List<String> ignoreUrls;

    /**
     * JWT 配置
     */
    @Data
    public static class JwtProperty {

        /**
         * JWT 密钥
         */
        private String key;

        /**
         * 访问令牌有效期(单位：秒)
         */
        private Integer accessTokenTimeToLive;
    }
}
