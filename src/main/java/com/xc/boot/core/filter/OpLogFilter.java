package com.xc.boot.core.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.filter.OncePerRequestFilter;

import com.xc.boot.common.util.OpLogUtils;

import java.io.IOException;

/**
 * 操作日志过滤器
 */
@Slf4j
public class OpLogFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {

        // 继续执行过滤器链（业务处理）
        filterChain.doFilter(request, response);

        // 收集日志
        OpLogUtils.collect(request);
    }
} 