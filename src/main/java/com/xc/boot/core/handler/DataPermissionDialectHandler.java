package com.xc.boot.core.handler;

import com.mybatisflex.core.dialect.impl.CommonsDialectImpl;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.core.security.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;


/**
 * 数据权限控制器
 *
 * <AUTHOR>
 * @since 2021-12-10 13:28
 */
@Slf4j
public class DataPermissionDialectHandler extends CommonsDialectImpl {

    @Override
    public String forSelectByQuery(QueryWrapper queryWrapper) {
        queryWrapper = this.dataScopeFilter(queryWrapper);
        return super.forSelectByQuery(queryWrapper);
    }

    /**
     * 构建过滤条件
     *
     * @param queryWrapper 当前查询条件
     * @return 构建后查询条件
     */
    public QueryWrapper dataScopeFilter(QueryWrapper queryWrapper) {

        // 如果是未登录，或者是定时任务执行的SQL，或者是超级管理员，直接返回
        if (SecurityUtils.getUserId() == null || SecurityUtils.isRoot()) {
            return queryWrapper;
        }

        // 获取当前用户的数据权限
//        Integer dataScope = SecurityUtils.getDataScope();
//        DataScopeEnum dataScopeEnum = IBaseEnum.getEnumByValue(dataScope, DataScopeEnum.class);
        // 如果是全部数据权限，直接返回
//        if (DataScopeEnum.ALL.equals(dataScopeEnum)) {
//            return queryWrapper;
//        }
        // List<QueryTable> tables = CPI.getQueryTables(queryWrapper);

        // for (QueryTable table : tables) {
        // }

        return queryWrapper;
    }


}

