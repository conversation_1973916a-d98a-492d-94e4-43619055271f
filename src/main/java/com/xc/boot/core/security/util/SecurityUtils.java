package com.xc.boot.core.security.util;

import cn.hutool.core.collection.CollectionUtil;

import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.constant.SystemConstants;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.system.mapper.CompanyMapper;
import com.xc.boot.system.model.entity.CompanyEntity;
import com.xc.boot.system.model.entity.SysUserEntity;
import com.xc.boot.system.service.UserRoleService;
import com.xc.boot.system.service.UserMerchantService;
import com.xc.boot.system.service.UserService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Spring Security 工具类
 *
 * <AUTHOR>
 * @since 2021/1/10
 */
@Component
public class SecurityUtils {

    private static UserService userService;
    private static UserRoleService userRoleService;
    private static UserMerchantService userMerchantService;
    private static CompanyMapper companyMapper;

    @Autowired
    public void setUserService(UserService userService) {
        SecurityUtils.userService = userService;
    }

    @Autowired
    public void setUserRoleService(UserRoleService userRoleService) {
        SecurityUtils.userRoleService = userRoleService;
    }

    @Autowired
    public void setUserMerchantService(UserMerchantService userMerchantService) {
        SecurityUtils.userMerchantService = userMerchantService;
    }

    @Autowired
    public void setCompanyMapper(CompanyMapper companyMapper) {
        SecurityUtils.companyMapper = companyMapper;
    }

    /**
     * 获取当前登录人信息
     *
     * @return Optional<SysUserDetails>
     */
    public static Optional<SysUserDetails> getUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            Object principal = authentication.getPrincipal();
            if (principal instanceof SysUserDetails) {
                return Optional.of((SysUserDetails) principal);
            }
        }
        return Optional.empty();
    }


    /**
     * 获取用户ID
     *
     * @return Long
     */
    public static Long getUserId() {
        return getUser().map(SysUserDetails::getUserId).orElse(null);
    }

    /**
     * 获取商户ID
     *
     * @return Long
     */
    public static Long getCompanyId() {
        return getUser().map(SysUserDetails::getCompanyId).orElse(null);
    }

    /**
     * 获取门店ID列表
     *
     * @return Long
     */
    @NotNull
    public static Set<Long> getMerchantIds() {
        Set<Long> longs = getUser().map(SysUserDetails::getMerchantIds).orElse(new HashSet<>());
        longs.add(0L);
        return longs;
    }


    /**
     * 获取用户账号
     *
     * @return String 用户账号
     */
    public static String getUsername() {
        return getUser().map(SysUserDetails::getUsername).orElse(null);
    }


    /**
     * 获取用户角色集合
     *
     * @return 角色集合
     */
    public static Set<String> getRoles() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            if (CollectionUtil.isNotEmpty(authorities)) {
                return authorities.stream().map(GrantedAuthority::getAuthority)
                        .collect(Collectors.toSet());
            }
        }
        return Collections.emptySet();
    }

    /**
     * 是否超级管理员
     * <p>
     * 超级管理员忽视任何权限判断
     */
    public static boolean isRoot() {
        Set<String> roles = getRoles();
        return roles.contains(SystemConstants.ROOT_ROLE_CODE);
    }

    /**
     * 是否主账号
     */
    public static boolean isMain() {
        return getUser().map(SysUserDetails::getIsMain).orElse(false);
    }

    /**
     * 是否查看机密
     */
    public static boolean showSecret() {
        return getUser().map(SysUserDetails::getShowSecret).orElse(false);
    }

    /**
     * 获取当前登录端
     */
    public static String  getSideCode() {
        return getUser().map(SysUserDetails::getSideCode).orElse("");
    }

    /**
     * 获取请求中的 Token
     *
     * @return Token 字符串
     */
    public static String getTokenFromRequest() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return request.getHeader(HttpHeaders.AUTHORIZATION);
    }

    /**
     * 根据用户ID模拟用户登录状态（用于模拟登录）
     *
     * @param userId 用户ID
     */
    public static void mockUserById(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        // 获取用户基本信息
        SysUserEntity userEntity = userService.getById(userId);
        if (userEntity == null) {
            throw new IllegalArgumentException("用户不存在，ID: " + userId);
        }

        // 创建用户详情对象
        SysUserDetails userDetails = new SysUserDetails(userEntity);

        // 获取并设置公司信息
        CompanyEntity companyEntity = companyMapper.selectOneByQuery(
            QueryWrapper.create().where(CompanyEntity::getId).eq(userEntity.getCompanyId())
        );
        if (companyEntity != null) {
            userDetails.setCompany(companyEntity);
        }

        // 获取用户角色
        Set<String> roles = userRoleService.getUserRoles(userId);
        userDetails.setRoles(roles);

        // 获取用户门店
        Set<Long> merchantIds = userMerchantService.getUserMerchants(userId, userDetails.getIsMain(), userDetails.getCompanyId());
        userDetails.setMerchantIds(merchantIds);

        // 创建权限列表
        Collection<GrantedAuthority> authorities = new ArrayList<>();
        if (roles != null && !roles.isEmpty()) {
            for (String role : roles) {
                authorities.add(new SimpleGrantedAuthority(role));
            }
        }

        // 创建认证对象
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(userDetails, null, authorities);

        // 设置到安全上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    /**
     * 根据商户ID模拟主账号用户登录状态（用于模拟登录）
     *
     * @param companyId 商户ID
     */
    public static void mockUserByCompanyId(Long companyId) {
        if (companyId == null) {
            throw new IllegalArgumentException("商户ID不能为空");
        }

        // 查询指定商户下的主账号用户
        SysUserEntity mainUser = userService.getOne(
            QueryWrapper.create()
                .where(SysUserEntity::getCompanyId).eq(companyId)
                .and(SysUserEntity::getMainFlag).eq(1)
                .and(SysUserEntity::getStatus).eq(1)
        );

        if (mainUser == null) {
            throw new IllegalArgumentException("未找到商户ID为 " + companyId + " 的主账号用户");
        }

        // 使用找到的主账号用户ID进行模拟登录
        mockUserById(mainUser.getId());
    }
}
