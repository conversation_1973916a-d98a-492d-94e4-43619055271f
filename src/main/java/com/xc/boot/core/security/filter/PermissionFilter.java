package com.xc.boot.core.security.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.filter.OncePerRequestFilter;

import static com.mybatisflex.core.query.QueryMethods.*;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import com.mybatisflex.core.row.Row;
import com.xc.boot.common.result.ResultCode;
import com.xc.boot.common.util.ResponseUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.system.enums.PermissionSceneEnum;
import com.xc.boot.system.model.entity.SysPermissionEntity;
import com.xc.boot.system.model.entity.SysRoleEntity;
import com.xc.boot.system.model.entity.SysRolePermissionEntity;

import cn.hutool.core.util.StrUtil;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权限校验过滤器
 *
 * <AUTHOR> Hao
 * @since 2024/3/21
 */
@Slf4j
public class PermissionFilter extends OncePerRequestFilter {

    /**
     * 鉴权白名单
     * 
     * 配置示例: (不需要配置 "/api/")
     * 
     * - 指定请求方式:
     * POST:auth/login
     * GET:auth/logout
     * 
     * - 放行所有请求方式:
     * auth/logout
     * auth/login
     */
    private static final Set<String> WHITE_LIST = Set.of(
            // pc
            "POST:auth/login",
            "GET:auth/logout",
            "GET:users/me",
            "POST:users/updateMe", // pc: 修改个人信息
            "POST:users/verify/password", // pc: 修改手机号
            "POST:users/verify/code", // pc: 修改手机号
            "PUT:users/password", // pc: 修改个人密码
            "GET:menus/routes",
            "GET:heading/detail", // 显示设置
            "POST:heading/save",
            "GET:company/settings", // 获取商户设置
            "GET:notices/read-all", // 公告
            "GET:notices/my-page",
            "GET:notices/detail",
            "GET:home", // 首页
            "export", // 导出
            "export/count", // 下载次数增加
            "export/page", // 下载中心列表
            "pda/package/latest",
            "POST:files", // 文件上传
            // 小程序
            "GET:mini/home/<USER>/preview", // 小程序:金价预览
            "POST:mini/home/<USER>/detail", // 小程序:金价详情列表
            "GET:mini/system/menus", // 小程序: 菜单
            "POST:mini/user/update", // 小程序: 修改用户信息
            "PUT:mini/user/password", // 小程序: 修改个人密码
            "POST:miniapp/auth/login", // 小程序: 登录
            "GET:miniapp/auth/logout", // 小程序: 退出登录
            // pda
            "GET:pda/auth/logout"
    // ...
    );

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        // 没有用户信息 -> 放行
        if (SecurityUtils.getUser().isEmpty()) {
            filterChain.doFilter(request, response);
            return;
        }

        // 当前请求的标识
        String requestSign = request.getMethod() + ":" + request.getRequestURI().replaceAll("/api/", "");

        // 当前用户角色
        Set<String> roles = SecurityUtils.getRoles();
        // 是否是超级管理员
        Boolean isRoot = SecurityUtils.isRoot();

        // 白名单 / 超级管理员 / 有权限 -> 放行
        if (inWhiteList(requestSign) || isRoot || hasPermission(requestSign, roles)) {
            // 继续执行过滤器链
            filterChain.doFilter(request, response);
            return;
        }

        // 无权限 -> 拦截
        ResponseUtils.writeErrMsg(response, ResultCode.PERMISSION_DENIED);
    }

    /**
     * 鉴权白名单
     * 
     * @param requestSign
     * @return
     */
    private boolean inWhiteList(String requestSign) {
        return canPass(requestSign, WHITE_LIST);
    }

    private boolean hasPermission(String requestSign, Set<String> roles) {
        Boolean isMain = SecurityUtils.isMain();
        Long companyId = SecurityUtils.getCompanyId();

        List<Integer> permissionSceneList = List.of(PermissionSceneEnum.COMMON.getValue(),
                PermissionSceneEnum.PC_ADMIN.getValue());

        // 商户端包含: pc, 小程序, pda
        if (companyId > 1) {
            permissionSceneList = List.of(
                    PermissionSceneEnum.COMMON.getValue(),
                    PermissionSceneEnum.PC_MERCHANT.getValue(),
                    PermissionSceneEnum.MINI_PROGRAM.getValue(),
                    PermissionSceneEnum.PDA.getValue());
        }

        QueryWrapper queryWrapper = QueryWrapper.create();

        if (isMain) {
            // 主账号直接获取全部权限
            queryWrapper
                    .select(column(SysPermissionEntity::getApiList))
                    .from(SysPermissionEntity.class)
                    .in(SysPermissionEntity::getScene, permissionSceneList);
        } else {
            // 非主账号, 根据角色标识查询
            queryWrapper
                    .select(column(SysPermissionEntity::getApiList))
                    .from(SysPermissionEntity.class)
                    .leftJoin(SysRolePermissionEntity.class)
                    .on(SysRolePermissionEntity::getPermissionSign, SysPermissionEntity::getSign)
                    .leftJoin(SysRoleEntity.class).on(SysRoleEntity::getId, SysRolePermissionEntity::getRoleId)
                    .in(SysPermissionEntity::getScene, permissionSceneList)
                    .in(SysRoleEntity::getCode, roles);
        }

        List<Row> apiList = Db.selectListByQuery(queryWrapper);

        Set<String> apiSet = apiList.stream()
                .map(row -> (String) row.get("api_list"))
                .filter(StrUtil::isNotBlank)
                .flatMap(api -> Arrays.stream(api.split(",")))
                .map(String::trim)
                .collect(Collectors.toSet());

        return canPass(requestSign, apiSet);
    }

    private boolean canPass(String requestSign, Set<String> permissions) {
        // 放行 common api
        if (requestSign.contains(":common/")) {
            return true;
        }

        // 是否全匹配
        if (permissions.contains(requestSign)) {
            return true;
        }

        // 是否存在放行所用请求方式的权限
        String path = requestSign.split(":")[1];
        if (permissions.contains(path)) {
            return true;
        }

        return false;
    }
}