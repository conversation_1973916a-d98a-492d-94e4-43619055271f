package com.xc.boot.core.security.extension;

import com.xc.boot.core.security.model.SysUserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * 模拟认证Token
 */
public class MockAuthenticationToken extends UsernamePasswordAuthenticationToken {

    public MockAuthenticationToken(SysUserDetails principal, Collection<? extends GrantedAuthority> authorities) {
        super(principal, null, authorities);
    }

    public static MockAuthenticationToken authenticated(SysUserDetails principal, Collection<? extends GrantedAuthority> authorities) {
        MockAuthenticationToken token = new MockAuthenticationToken(principal, authorities);
        token.setAuthenticated(true);
        return token;
    }
} 