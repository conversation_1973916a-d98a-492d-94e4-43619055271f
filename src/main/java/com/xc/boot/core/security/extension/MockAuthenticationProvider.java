package com.xc.boot.core.security.extension;

import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

/**
 * 模拟认证Provider
 */
public class MockAuthenticationProvider implements AuthenticationProvider {

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        // 直接返回已认证的token，因为传入的token已经是认证过的
        return authentication;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return MockAuthenticationToken.class.isAssignableFrom(authentication);
    }
} 