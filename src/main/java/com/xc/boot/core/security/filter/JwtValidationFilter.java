package com.xc.boot.core.security.filter;

import cn.hutool.core.util.StrUtil;
import com.xc.boot.common.constant.SecurityConstants;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.result.ResultCode;
import com.xc.boot.common.util.ResponseUtils;
import com.xc.boot.shared.auth.service.impl.RedisTokenService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT token 校验过滤器
 *
 * <AUTHOR>
 * @since 2023/9/13
 */
public class JwtValidationFilter extends OncePerRequestFilter {

    private final RedisTokenService redisTokenService;


    public JwtValidationFilter(RedisTokenService redisTokenService) {
        this.redisTokenService = redisTokenService;
    }


    /**
     * 从请求中获取 JWT Token，校验 JWT Token 是否合法
     * <p>
     * 如果合法则将 Authentication 设置到 Spring Security Context 上下文中
     * 如果不合法则清空 Spring Security Context 上下文，并直接返回响应
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        try {
            if (StrUtil.isNotBlank(token) && token.startsWith(SecurityConstants.JWT_TOKEN_PREFIX)) {
                // 去除 Bearer 前缀
                token = token.substring(SecurityConstants.JWT_TOKEN_PREFIX.length());
                // 校验 JWT Token ，包括验签和是否过期
                boolean isValidate = redisTokenService.validateToken(token);
                if (!isValidate) {
                    ResponseUtils.writeErrMsg(response, ResultCode.TOKEN_INVALID);
                    return;
                }
                // 将 Token 解析为 Authentication 对象，并设置到 Spring Security 上下文中
                Authentication authentication = redisTokenService.parseToken(token);
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
        }catch (BusinessException e) {
            SecurityContextHolder.clearContext();
            ResponseUtils.writeErrMsg(response, e);
            return;
        } catch (Exception e) {
            SecurityContextHolder.clearContext();
            ResponseUtils.writeErrMsg(response, ResultCode.TOKEN_INVALID);
            return;
        }
        // Token有效或无Token时继续执行过滤链
        filterChain.doFilter(request, response);
    }
}
