package com.xc.boot.core.security.decorator;

import org.springframework.core.task.TaskDecorator;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * <AUTHOR>
 * @ClassName SecurityContextCopyThreadDecorator
 * @Date: 2025/5/29 09:10
 * @Description: 创建子线程执行异步任务时，将当前线程的securityContext复制到新线程中
 */
public class SecurityContextCopyTaskDecorator implements TaskDecorator {
    @Override
    public Runnable decorate(Runnable runnable) {
        // 获取当前线程的 SecurityContext
        final SecurityContext context = SecurityContextHolder.getContext();
        return () -> {
            try {
                // 将当前线程的 SecurityContext 设置到新线程中
                SecurityContextHolder.setContext(context);
                // 执行原始任务
                runnable.run();
            } finally {
                // 清理线程上下文，避免内存泄漏
                SecurityContextHolder.clearContext();
            }
        };
    }
}
