package com.xc.boot.core.security.model;

import cn.hutool.core.util.ObjectUtil;
import com.xc.boot.system.model.dto.UserAuthInfo;
import com.xc.boot.system.model.entity.CompanyEntity;
import com.xc.boot.system.model.entity.SysUserEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.*;

/**
 * Spring Security 用户对象
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysUserDetails implements UserDetails {

    @Getter
    private Long userId;

    /**
     * 手机号/账号
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    private String password;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 头像文件ID
     */
    private Long avatarId;

    /**
     * 显示机密字段
     */
    private Boolean showSecret;

    private Boolean enabled;

    /**
     * 是否主账号
     */
    private Boolean isMain;

    /**
     * 商家ID
     */
    private Long companyId;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 商家信息
     */
    private CompanyEntity company;

    /**
     * 角色信息
     */
    private Set<String> roles;
    /**
     * 门店信息
     */
    private Set<Long> merchantIds;

    /**
     * 多端多次登录信息
     */
    private Set<String> jwtIdSet;

    private String sideCode;

    private Integer dataScope;

    private Date createdAt;

    /**
     * 当前会话的jwtId
     */
    private String jwtId;

    public SysUserDetails(UserAuthInfo user) {
        this.userId = user.getUserId();
        this.username = user.getUsername();
        this.password = user.getPassword();
        this.nickname = user.getNickname();
        this.enabled = ObjectUtil.equal(user.getStatus(), 1);
        this.isMain = ObjectUtil.equal(user.getMainFlag(), 1);
        this.showSecret = ObjectUtil.equal(user.getSecret(), 1);
        this.companyId = user.getCompanyId();
        this.gender = user.getGender();
        this.avatar = user.getAvatar();
        this.avatarId = user.getAvatarId();
        this.createdAt = user.getCreatedAt();
    }

    public SysUserDetails(SysUserEntity user) {
        this.userId = user.getId();
        this.username = user.getUsername();
        this.password = user.getPassword();
        this.nickname = user.getNickname();
        this.enabled = ObjectUtil.equal(user.getStatus(), 1);
        this.isMain = ObjectUtil.equal(user.getMainFlag(), 1);
        this.showSecret = ObjectUtil.equal(user.getSecret(), 1);
        this.companyId = user.getCompanyId();
        this.gender = user.getGender();
        this.avatar = user.getAvatar();
        this.avatarId = user.getAvatarId();
        this.createdAt = user.getCreatedAt();
    }


    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if (Objects.isNull(roles)) {
            return new HashSet<>();
        }
        Set<GrantedAuthority> set = new HashSet<>();
        for (String role : roles) {
            if (StringUtils.isNotBlank(role)) {
                set.add(new SimpleGrantedAuthority(role));
            }
        }
        return set;
    }

    @Override
    public String getPassword() {
        return this.password;
    }

    @Override
    public String getUsername() {
        return this.username;
    }

    @Override
    public boolean isEnabled() {
        return this.enabled;
    }
}
