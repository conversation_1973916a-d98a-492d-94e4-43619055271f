package com.xc.boot.core.events;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.xc.boot.common.util.LarkUtils;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * 应用启动触发
 */
@Component
@Slf4j
public class Setup {
    @Value("${spring.profiles.active}")
    private String env;

    @Autowired
    private AsyncTaskExecutor asyncTaskExecutor;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @PostConstruct
    public void init(){
        // 设置Redis模板到LarkUtils
        LarkUtils.setRedisTemplate(redisTemplate);

        // 检查并处理标记的消息
        LarkUtils.checkAndHandleMarkedMessages(env, asyncTaskExecutor);

        // 发送飞书消息
        LarkUtils.appSetupRemind(env, asyncTaskExecutor);

        // 启动事件处理器
        LarkUtils.startEventHandler(env, asyncTaskExecutor);
    }
}
