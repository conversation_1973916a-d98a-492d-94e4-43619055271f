package ${packageName}.${moduleName}.${subpackageName};

import lombok.Getter;
import lombok.Setter;
#if(${hasLocalDateTime})
#end
#if(${hasBigDecimal})
#end
import com.mybatisflex.annotation.Table;

/**
 * $!{businessName}实体对象
 *
 * <AUTHOR>
 * @since ${date}
 */
@Getter
@Setter
@Table(value="${tableName}")
public class ${entityName} extends BaseEntity {

    private static final long serialVersionUID = 1L;

#foreach($fieldConfig in ${fieldConfigs})
    #if(!$fieldConfig.fieldName.equals("id") && !$fieldConfig.fieldName.equals("createTime") && !$fieldConfig.fieldName.equals("updateTime"))
        #if("$!fieldConfig.fieldComment" != "")
    /**
     * ${fieldConfig.fieldComment}
     */
        #end
    private ${fieldConfig.fieldType} ${fieldConfig.fieldName};
    #end
#end
}
