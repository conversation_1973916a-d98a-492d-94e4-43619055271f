
# 代码生成器配置
codegen:
  # 下载代码文件名称
  downloadFileName: youlai-admin-code.zip
  # 后端项目名称
  backendAppName: youlai-boot
  # 前端项目名称
  frontendAppName: vue3-element-admin
  # 默认配置
  defaultConfig:
    author: youlaitech
    moduleName: system
  # 排除数据表
  excludeTables:
    - gen_config
    - gen_field_config
  ## 模板配置
  templateConfigs:
    API:
      templatePath: codegen/api.ts.vm
      subpackageName: api
      extension: .ts
    VIEW:
      templatePath: codegen/index.vue.vm
      subpackageName: views
      extension: .vue
    Controller:
      templatePath: codegen/controller.java.vm
      subpackageName: controller
    Service:
      templatePath: codegen/service.java.vm
      subpackageName: service
    ServiceImpl:
      templatePath: codegen/serviceImpl.java.vm
      subpackageName: service.impl
    Mapper:
      templatePath: codegen/mapper.java.vm
      subpackageName: mapper
    MapperXml:
      templatePath: codegen/mapper.xml.vm
      subpackageName: mapper
      extension: .xml
    Converter:
      templatePath: codegen/converter.java.vm
      subpackageName: converter
    Query:
      templatePath: codegen/query.java.vm
      subpackageName: model.query
    Form:
      templatePath: codegen/form.java.vm
      subpackageName: model.form
    VO:
      templatePath: codegen/vo.java.vm
      subpackageName: model.vo
    Entity:
      templatePath: codegen/entity.java.vm
      subpackageName: model.entity
