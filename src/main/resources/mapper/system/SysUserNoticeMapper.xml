<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.system.mapper.UserNoticeMapper">
    <resultMap id="BaseResultMap" type="com.xc.boot.system.model.entity.SysUserNoticeEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="notice_id" jdbcType="BIGINT" property="noticeId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="is_read" jdbcType="BIGINT" property="isRead"/>
        <result column="read_time" jdbcType="TIMESTAMP" property="readTime"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `notice_id`, `user_id`, `is_read`, `read_time`, `created_at`, `updated_at`, `deleted_at`
    </sql>

</mapper>