<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.system.mapper.RoleMapper">
    <resultMap id="BaseResultMap" type="com.xc.boot.system.model.entity.SysRoleEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="data_scope" jdbcType="TINYINT" property="dataScope"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`,
        `deleted_at`
    </sql>

</mapper>