<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.system.mapper.NoticeMapper">
    <resultMap id="BaseResultMap" type="com.xc.boot.system.model.entity.SysNoticeEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="CLOB" property="content"/>
        <result column="publisher_id" jdbcType="BIGINT" property="publisherId"/>
        <result column="publish_status" jdbcType="TINYINT" property="publishStatus"/>
        <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `title`, `content`, `publisher_id`, `publish_status`,
        `publish_time`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`
    </sql>

</mapper>