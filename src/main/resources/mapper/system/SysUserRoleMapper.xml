<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.system.mapper.UserRoleMapper">
    <resultMap id="BaseResultMap" type="com.xc.boot.system.model.entity.SysUserRoleEntity">
        <id column="user_id" jdbcType="BIGINT" property="userId"/>
        <id column="role_id" jdbcType="BIGINT" property="roleId"/>
    </resultMap>
    <sql id="Base_Column_List">
        `user_id`, `role_id`
    </sql>
</mapper>