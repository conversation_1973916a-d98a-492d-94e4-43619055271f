<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.system.mapper.ConfigMapper">
    <resultMap id="BaseResultMap" type="com.xc.boot.system.model.entity.SysConfigEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="config_name" jdbcType="VARCHAR" property="configName"/>
        <result column="config_key" jdbcType="VARCHAR" property="configKey"/>
        <result column="config_value" jdbcType="VARCHAR" property="configValue"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `config_name`, `config_key`, `config_value`, `remark`, `created_at`, `created_by`, `updated_at`,
        `updated_by`, `deleted_at`
    </sql>

</mapper>