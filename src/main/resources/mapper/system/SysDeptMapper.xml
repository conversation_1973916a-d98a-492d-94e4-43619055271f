<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.system.mapper.DeptMapper">
    <resultMap id="BaseResultMap" type="com.xc.boot.system.model.entity.SysDeptEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="tree_path" jdbcType="VARCHAR" property="treePath"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `name`, `code`, `parent_id`, `tree_path`, `sort`, `status`, `created_by`, `created_at`, `updated_by`,
        `updated_at`, `deleted_at`
    </sql>

</mapper>