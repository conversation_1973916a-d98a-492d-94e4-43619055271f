<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.system.mapper.LogMapper">
    <resultMap id="BaseResultMap" type="com.xc.boot.system.model.entity.SysLogEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="module" jdbcType="VARCHAR" property="module"/>
        <result column="request_method" jdbcType="VARCHAR" property="requestMethod"/>
        <result column="request_params" jdbcType="CLOB" property="requestParams"/>
        <result column="response_content" jdbcType="CLOB" property="responseContent"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="request_uri" jdbcType="VARCHAR" property="requestUri"/>
        <result column="method" jdbcType="VARCHAR" property="method"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="execution_time" jdbcType="BIGINT" property="executionTime"/>
        <result column="browser" jdbcType="VARCHAR" property="browser"/>
        <result column="browser_version" jdbcType="VARCHAR" property="browserVersion"/>
        <result column="os" jdbcType="VARCHAR" property="os"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `module`, `request_method`, `request_params`, `response_content`, `content`, `request_uri`, `method`,
        `ip`, `province`, `city`, `execution_time`, `browser`, `browser_version`, `os`, `created_by`, `created_at`,
        `deleted_at`
    </sql>

    <!-- 获取访问量(PV)统计数据 -->
    <select id="getPvStats" resultType="com.xc.boot.system.model.bo.VisitStatsBO">
        SELECT
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) AS todayCount,
            COUNT(*) AS totalCount,
            ROUND(
                    CASE
                        WHEN COUNT(CASE WHEN DATE(created_at) = CURDATE() - INTERVAL 1 DAY AND TIME(created_at) &lt;= TIME(NOW()) THEN 1 END) = 0 THEN 0
                        ELSE
                            (COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) -
                             COUNT(CASE WHEN DATE(created_at) = CURDATE() - INTERVAL 1 DAY AND TIME(created_at)  &lt;= TIME(NOW()) THEN 1 END)) /
                            COUNT(CASE WHEN DATE(created_at) = CURDATE() - INTERVAL 1 DAY AND TIME(created_at)  &lt;= TIME(NOW()) THEN 1 END)
                        END,
                    2) AS growthRate
        FROM
            sys_log
        WHERE
            deleted_at is null
    </select>

    <!-- 获取IP统计数据 -->
    <select id="getUvStats" resultType="com.xc.boot.system.model.bo.VisitStatsBO">
        SELECT
            COUNT(DISTINCT CASE WHEN DATE(created_at) = CURDATE() THEN ip END) AS todayCount,
            COUNT(DISTINCT ip) AS totalCount,
            ROUND(
                    CASE
                        WHEN COUNT(DISTINCT CASE WHEN DATE(created_at) = CURDATE() - INTERVAL 1 DAY AND TIME(created_at) &lt;= TIME(NOW()) THEN ip END) = 0 THEN 0
                        ELSE
                            (COUNT(DISTINCT CASE WHEN DATE(created_at) = CURDATE() THEN ip END) -
                             COUNT(DISTINCT CASE WHEN DATE(created_at) = CURDATE() - INTERVAL 1 DAY AND TIME(created_at) &lt;= TIME(NOW()) THEN ip END)) /
                            COUNT(DISTINCT CASE WHEN DATE(created_at) = CURDATE() - INTERVAL 1 DAY AND TIME(created_at) &lt;= TIME(NOW()) THEN ip END)
                        END,
                    2) AS growthRate
        FROM
            sys_log
        WHERE
            deleted_at is null
    </select>
</mapper>