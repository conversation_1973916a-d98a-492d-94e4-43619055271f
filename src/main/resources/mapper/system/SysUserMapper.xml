<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.system.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="com.xc.boot.system.model.entity.SysUserEntity">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="gender" jdbcType="TINYINT" property="gender"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="avatar" jdbcType="VARCHAR" property="avatar"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt"/>
        <result column="openid" jdbcType="VARCHAR" property="openid"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `username`, `nickname`, `gender`, `password`, `dept_id`, `avatar`, `mobile`, `status`, `email`,
        `created_at`, `created_by`, `updated_at`, `updated_by`, `deleted_at`, `openid`
    </sql>
</mapper>