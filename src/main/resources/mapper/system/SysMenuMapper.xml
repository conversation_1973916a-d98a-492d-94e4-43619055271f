<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.system.mapper.MenuMapper">
    <resultMap id="BaseResultMap" type="com.xc.boot.system.model.entity.SysMenuEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="tree_path" jdbcType="VARCHAR" property="treePath"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="route_name" jdbcType="VARCHAR" property="routeName"/>
        <result column="route_path" jdbcType="VARCHAR" property="routePath"/>
        <result column="component" jdbcType="VARCHAR" property="component"/>
        <result column="perm" jdbcType="VARCHAR" property="perm"/>
        <result column="always_show" jdbcType="TINYINT" property="alwaysShow"/>
        <result column="keep_alive" jdbcType="TINYINT" property="keepAlive"/>
        <result column="visible" jdbcType="TINYINT" property="visible"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="redirect" jdbcType="VARCHAR" property="redirect"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="params" jdbcType="OTHER" property="params"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`,
        `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`
    </sql>

</mapper>