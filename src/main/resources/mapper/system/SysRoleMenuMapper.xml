<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.system.mapper.RoleMenuMapper">
    <resultMap id="BaseResultMap" type="com.xc.boot.system.model.entity.SysRoleMenuEntity">
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="menu_id" jdbcType="BIGINT" property="menuId"/>
    </resultMap>
    <sql id="Base_Column_List">
        `role_id`, `menu_id`
    </sql>
</mapper>