<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.shared.codegen.mapper.GenConfigMapper">
    <resultMap id="BaseResultMap" type="com.xc.boot.shared.codegen.model.entity.GenConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="table_name" jdbcType="VARCHAR" property="tableName"/>
        <result column="module_name" jdbcType="VARCHAR" property="moduleName"/>
        <result column="package_name" jdbcType="VARCHAR" property="packageName"/>
        <result column="business_name" jdbcType="VARCHAR" property="businessName"/>
        <result column="entity_name" jdbcType="VARCHAR" property="entityName"/>
        <result column="author" jdbcType="VARCHAR" property="author"/>
        <result column="parent_menu_id" jdbcType="BIGINT" property="parentMenuId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `table_name`, `module_name`, `package_name`, `business_name`, `entity_name`, `author`, `parent_menu_id`,
        `created_at`, `updated_at`
    </sql>

</mapper>