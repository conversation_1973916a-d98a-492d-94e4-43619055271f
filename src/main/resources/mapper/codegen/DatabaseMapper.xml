<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.shared.codegen.mapper.DatabaseMapper">

    <!-- 查询数据库表分页 -->
    <select id="getTablePage" resultType="com.xc.boot.shared.codegen.model.vo.TablePageVO">
        SELECT
            t1.TABLE_NAME ,
            t1.TABLE_COMMENT ,
            t1.TABLE_COLLATION,
            t1.ENGINE,
            t1.CREATE_TIME,
            CASE WHEN t2.id IS NOT NULL THEN 1 ELSE 0 END AS isConfigured
        FROM
            information_schema.tables t1
            LEFT JOIN gen_config t2 on t1.TABLE_NAME = t2.table_name
        WHERE
            t1.TABLE_SCHEMA = (SELECT DATABASE())
            AND t1.table_type = 'BASE TABLE'
            <if test="queryParams.keywords != null and queryParams.keywords.trim() neq ''">
                AND t1.TABLE_NAME LIKE CONCAT('%',#{queryParams.keywords},'%')
            </if>
        <!-- 排除的表 -->
        <if test="queryParams.excludeTables != null and queryParams.excludeTables.size() > 0">
            AND t1.TABLE_NAME NOT IN
            <foreach collection="queryParams.excludeTables" item="excludeTable" open="(" close=")" separator=",">
                #{excludeTable}
            </foreach>
        </if>
        ORDER BY
            CREATE_TIME DESC
    </select>

    <select id="getTableMetadata" resultType="com.xc.boot.shared.codegen.model.bo.TableMetaData">
        SELECT
            TABLE_NAME ,
            TABLE_COMMENT ,
            TABLE_COLLATION,
            ENGINE,
            CREATE_TIME
        FROM
            information_schema.tables
        WHERE
            TABLE_SCHEMA = (SELECT DATABASE())
          AND TABLE_NAME = #{tableName}
    </select>

    <select id="getTableColumns" resultType="com.xc.boot.shared.codegen.model.bo.ColumnMetaData">
        SELECT
            COLUMN_NAME,
            DATA_TYPE,
            COLUMN_COMMENT,
            CASE COLUMN_KEY WHEN 'PRI' THEN 1 ELSE 0 END AS isPrimaryKey,
            IS_NULLABLE,
            CHARACTER_MAXIMUM_LENGTH,
            CHARACTER_SET_NAME,
            COLLATION_NAME
        FROM
            information_schema.columns
        WHERE
            TABLE_SCHEMA = (SELECT DATABASE())
            AND TABLE_NAME = #{tableName}
        ORDER BY ORDINAL_POSITION ASC
    </select>


</mapper>
