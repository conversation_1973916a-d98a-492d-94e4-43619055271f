<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xc.boot.shared.codegen.mapper.GenFieldConfigMapper">
    <resultMap id="BaseResultMap" type="com.xc.boot.shared.codegen.model.entity.GenFieldConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="config_id" jdbcType="BIGINT" property="configId"/>
        <result column="column_name" jdbcType="VARCHAR" property="columnName"/>
        <result column="column_type" jdbcType="VARCHAR" property="columnType"/>
        <result column="field_name" jdbcType="VARCHAR" property="fieldName"/>
        <result column="field_type" jdbcType="VARCHAR" property="fieldType"/>
        <result column="field_sort" jdbcType="INTEGER" property="fieldSort"/>
        <result column="field_comment" jdbcType="VARCHAR" property="fieldComment"/>
        <result column="is_required" jdbcType="TINYINT" property="isRequired"/>
        <result column="max_length" jdbcType="INTEGER" property="maxLength"/>
        <result column="is_show_in_list" jdbcType="TINYINT" property="isShowInList"/>
        <result column="is_show_in_form" jdbcType="TINYINT" property="isShowInForm"/>
        <result column="is_show_in_query" jdbcType="TINYINT" property="isShowInQuery"/>
        <result column="query_type" jdbcType="TINYINT" property="queryType"/>
        <result column="form_type" jdbcType="TINYINT" property="formType"/>
        <result column="dict_type" jdbcType="VARCHAR" property="dictType"/>
        <result column="column_length" jdbcType="INTEGER" property="columnLength"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`,
        `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`,
        `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`
    </sql>

</mapper>