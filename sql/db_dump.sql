SET FOREIGN_KEY_CHECKS = 0;

CREATE TABLE `gen_config` (
                              `id` bigint NOT NULL AUTO_INCREMENT,
                              `table_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表名',
                              `module_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模块名(xc-system)',
                              `package_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '包名(com.xc.system)',
                              `business_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务名(系统用户)',
                              `entity_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '实体类名(User)',
                              `author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '作者',
                              `parent_menu_id` bigint DEFAULT NULL COMMENT '上级菜单ID，对应sys_menu的id ',
                              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `uk_tablename` (`table_name`)
) ENGINE=InnoDB AUTO_INCREMENT=735 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='代码生成配置表';

CREATE TABLE `gen_field_config` (
                                    `id` bigint NOT NULL AUTO_INCREMENT,
                                    `config_id` bigint NOT NULL COMMENT '关联的配置ID',
                                    `column_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                                    `column_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                                    `field_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段名称',
                                    `field_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字段类型',
                                    `field_sort` int DEFAULT NULL COMMENT '字段排序',
                                    `field_comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字段描述',
                                    `is_required` tinyint(1) DEFAULT NULL COMMENT '是否必填',
                                    `max_length` int DEFAULT NULL,
                                    `is_show_in_list` tinyint(1) DEFAULT '0' COMMENT '是否在列表显示',
                                    `is_show_in_form` tinyint(1) DEFAULT '0' COMMENT '是否在表单显示',
                                    `is_show_in_query` tinyint(1) DEFAULT '0' COMMENT '是否在查询条件显示',
                                    `query_type` tinyint DEFAULT NULL COMMENT '查询方式',
                                    `form_type` tinyint DEFAULT NULL COMMENT '表单类型',
                                    `dict_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字典类型',
                                    `column_length` int DEFAULT NULL,
                                    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    PRIMARY KEY (`id`),
                                    KEY `config_id` (`config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8242 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci CHECKSUM=1 DELAY_KEY_WRITE=1 ROW_FORMAT=DYNAMIC COMMENT='字段配置表';

CREATE TABLE `sys_config` (
                              `id` bigint NOT NULL AUTO_INCREMENT,
                              `config_name` varchar(50) NOT NULL COMMENT '配置名称',
                              `config_key` varchar(50) NOT NULL COMMENT '配置key',
                              `config_value` varchar(100) NOT NULL COMMENT '配置值',
                              `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述、备注',
                              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建人ID',
                              `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新人ID',
                              `deleted_at` datetime DEFAULT NULL COMMENT '逻辑删除',
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统配置';

CREATE TABLE `sys_dept` (
                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                            `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '部门名称',
                            `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门编号',
                            `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父节点id',
                            `tree_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '父节点id路径',
                            `sort` smallint DEFAULT '0' COMMENT '显示顺序',
                            `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1-正常 0-禁用)',
                            `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建人ID',
                            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新人ID',
                            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `deleted_at` datetime DEFAULT NULL COMMENT '逻辑删除',
                            PRIMARY KEY (`id`) USING BTREE,
                            UNIQUE KEY `uk_code` (`code`) USING BTREE COMMENT '部门编号唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='部门表';

CREATE TABLE `sys_dict` (
                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ',
                            `dict_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典编码',
                            `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '类型编码',
                            `status` tinyint(1) DEFAULT '0' COMMENT '状态(0：正常，1：禁用)',
                            `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
                            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `deleted_at` datetime DEFAULT NULL COMMENT '逻辑删除',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='系统字典表';

CREATE TABLE `sys_dict_data` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 `dict_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联字典编码，与sys_dict表中的dict_code对应',
                                 `value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典项值',
                                 `label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典项标签',
                                 `tag_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签类型，用于前端样式展示（如success、warning等）',
                                 `status` tinyint DEFAULT '0' COMMENT '状态（1-正常，0-禁用）',
                                 `sort` int DEFAULT '0' COMMENT '排序',
                                 `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
                                 `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='字典数据表';

CREATE TABLE `sys_log` (
                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                           `module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志模块',
                           `request_method` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求方式',
                           `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求参数(批量请求参数可能会超过text)',
                           `response_content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '返回参数',
                           `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志内容',
                           `request_uri` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求路径',
                           `method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '方法名',
                           `ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IP地址',
                           `province` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '省份',
                           `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '城市',
                           `execution_time` bigint DEFAULT NULL COMMENT '执行时间(ms)',
                           `browser` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '浏览器',
                           `browser_version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '浏览器版本',
                           `os` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '终端系统',
                           `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建人ID',
                           `created_at` datetime NOT NULL COMMENT '创建时间',
                           `deleted_at` datetime DEFAULT NULL COMMENT '逻辑删除',
                           PRIMARY KEY (`id`) USING BTREE,
                           KEY `idx_create_time` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='系统日志表';

CREATE TABLE `sys_menu` (
                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                            `parent_id` bigint NOT NULL COMMENT '父菜单ID',
                            `tree_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父节点ID路径',
                            `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单名称',
                            `type` tinyint NOT NULL COMMENT '菜单类型（1-菜单 2-目录 3-外链 4-按钮）',
                            `route_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '路由名称（Vue Router 中用于命名路由）',
                            `route_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '路由路径（Vue Router 中定义的 URL 路径）',
                            `component` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件路径（组件页面完整路径，相对于 src/views/，缺省后缀 .vue）',
                            `perm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '【按钮】权限标识',
                            `always_show` tinyint DEFAULT '0' COMMENT '【目录】只有一个子路由是否始终显示（1-是 0-否）',
                            `keep_alive` tinyint DEFAULT '0' COMMENT '【菜单】是否开启页面缓存（1-是 0-否）',
                            `visible` tinyint(1) NOT NULL DEFAULT '1' COMMENT '显示状态（1-显示 0-隐藏）',
                            `sort` int DEFAULT '0' COMMENT '排序',
                            `icon` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '菜单图标',
                            `redirect` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '跳转路径',
                            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `params` json DEFAULT NULL COMMENT '路由参数',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=144 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='菜单管理';

CREATE TABLE `sys_notice` (
                              `id` bigint NOT NULL AUTO_INCREMENT,
                              `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '通知标题',
                              `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '通知内容',
                              `type` tinyint NOT NULL COMMENT '通知类型（字典code：notice_type）',
                              `level` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '通知等级（字典code：notice_level）',
                              `target_type` tinyint NOT NULL COMMENT '目标类型（1: 全体, 2: 指定）',
                              `target_user_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '目标人ID集合（多个使用英文逗号,分割）',
                              `publisher_id` bigint DEFAULT NULL COMMENT '发布人ID',
                              `publish_status` tinyint NOT NULL DEFAULT '0' COMMENT '发布状态（0: 未发布, 1: 已发布, -1: 已撤回）',
                              `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
                              `revoke_time` datetime DEFAULT NULL COMMENT '撤回时间',
                              `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建人ID',
                              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新人ID',
                              `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              `deleted_at` datetime DEFAULT NULL COMMENT '逻辑删除',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知公告表';

CREATE TABLE `sys_role` (
                            `id` bigint NOT NULL AUTO_INCREMENT,
                            `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色名称',
                            `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色编码',
                            `sort` int DEFAULT NULL COMMENT '显示顺序',
                            `status` tinyint(1) DEFAULT '1' COMMENT '角色状态(1-正常 0-停用)',
                            `data_scope` tinyint DEFAULT NULL COMMENT '数据权限(0-所有数据 1-部门及子部门数据 2-本部门数据3-本人数据)',
                            `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建人ID',
                            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新人ID',
                            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `deleted_at` datetime DEFAULT NULL COMMENT '逻辑删除',
                            PRIMARY KEY (`id`) USING BTREE,
                            UNIQUE KEY `uk_name` (`name`) USING BTREE COMMENT '角色名称唯一索引',
                            UNIQUE KEY `uk_code` (`code`) USING BTREE COMMENT '角色编码唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=128 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='角色表';

CREATE TABLE `sys_role_menu` (
                                 `role_id` bigint NOT NULL COMMENT '角色ID',
                                 `menu_id` bigint NOT NULL COMMENT '菜单ID',
                                 UNIQUE KEY `uk_roleid_menuid` (`role_id`,`menu_id`) USING BTREE COMMENT '角色菜单唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='角色和菜单关联表';

CREATE TABLE `sys_user` (
                            `id` int NOT NULL AUTO_INCREMENT,
                            `username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名',
                            `nickname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '昵称',
                            `openid` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信OpenID',
                            `gender` tinyint(1) DEFAULT '1' COMMENT '性别((1-男 2-女 0-保密)',
                            `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码',
                            `dept_id` int DEFAULT NULL COMMENT '部门ID',
                            `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户头像',
                            `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系方式',
                            `status` tinyint(1) DEFAULT '1' COMMENT '状态((1-正常 0-禁用)',
                            `email` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户邮箱',
                            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建人ID',
                            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新人ID',
                            `deleted_at` datetime DEFAULT NULL COMMENT '逻辑删除',
                            PRIMARY KEY (`id`) USING BTREE,
                            UNIQUE KEY `login_name` (`username`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=288 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `sys_user_notice` (
                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                                   `notice_id` bigint NOT NULL COMMENT '公共通知id',
                                   `user_id` bigint NOT NULL COMMENT '用户id',
                                   `is_read` bigint NOT NULL DEFAULT '0' COMMENT '读取状态（0未读；1已读）',
                                   `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
                                   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `deleted_at` datetime DEFAULT NULL COMMENT '逻辑删除',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=126 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户通知公告表';

CREATE TABLE `sys_user_role` (
                                 `user_id` bigint NOT NULL COMMENT '用户ID',
                                 `role_id` bigint NOT NULL COMMENT '角色ID',
                                 PRIMARY KEY (`user_id`,`role_id`) USING BTREE,
                                 UNIQUE KEY `uk_userid_roleid` (`user_id`,`role_id`) USING BTREE COMMENT '用户角色唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户和角色关联表';

SET FOREIGN_KEY_CHECKS = 1;

TRUNCATE TABLE `gen_config` ;

INSERT INTO `gen_config` (`id`, `table_name`, `module_name`, `package_name`, `business_name`, `entity_name`, `author`, `parent_menu_id`, `created_at`, `updated_at`) VALUES ('3','gen_config',null,'com.xc.system','代码生成配置','Config','youlaitech',null,'2024-07-28 23:28:09','2024-09-23 16:55:11');
INSERT INTO `gen_config` (`id`, `table_name`, `module_name`, `package_name`, `business_name`, `entity_name`, `author`, `parent_menu_id`, `created_at`, `updated_at`) VALUES ('4','gen_field_config',null,'com.xc.system','字段配置','FieldConfig','youlaitech',null,'2024-07-28 23:29:59','2024-07-30 02:53:22');
INSERT INTO `gen_config` (`id`, `table_name`, `module_name`, `package_name`, `business_name`, `entity_name`, `author`, `parent_menu_id`, `created_at`, `updated_at`) VALUES ('373','sys_dict_item','system','com.xc.boot','字典项','DictItem','youlaitech','119','2024-09-30 17:03:03','2024-10-01 07:58:07');
INSERT INTO `gen_config` (`id`, `table_name`, `module_name`, `package_name`, `business_name`, `entity_name`, `author`, `parent_menu_id`, `created_at`, `updated_at`) VALUES ('460','sys_message','system','com.xc.boot','系统消息','Message','youlaitech',null,'2024-10-19 20:56:33','2024-10-19 20:56:33');
INSERT INTO `gen_config` (`id`, `table_name`, `module_name`, `package_name`, `business_name`, `entity_name`, `author`, `parent_menu_id`, `created_at`, `updated_at`) VALUES ('728','sys_user','system','com.xc.boot','用户信息','User','youlaitech',null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_config` (`id`, `table_name`, `module_name`, `package_name`, `business_name`, `entity_name`, `author`, `parent_menu_id`, `created_at`, `updated_at`) VALUES ('729','sys_config','system','com.xc.boot','系统配置','Config','youlaitech','97','2024-12-06 18:55:47','2024-12-06 20:03:34');
INSERT INTO `gen_config` (`id`, `table_name`, `module_name`, `package_name`, `business_name`, `entity_name`, `author`, `parent_menu_id`, `created_at`, `updated_at`) VALUES ('730','sys_dict_data','system','com.xc.boot','字典数据','DictData','youlaitech',null,'2024-12-06 20:01:31','2024-12-06 20:01:31');
INSERT INTO `gen_config` (`id`, `table_name`, `module_name`, `package_name`, `business_name`, `entity_name`, `author`, `parent_menu_id`, `created_at`, `updated_at`) VALUES ('732','sys_log','system','com.xc.boot','系统日志','Log','youlaitech',null,'2024-12-08 03:30:36','2024-12-08 20:22:31');
INSERT INTO `gen_config` (`id`, `table_name`, `module_name`, `package_name`, `business_name`, `entity_name`, `author`, `parent_menu_id`, `created_at`, `updated_at`) VALUES ('733','sys_dict','system','com.xc.boot','系统字典','Dict','youlaitech',null,'2024-12-08 08:55:09','2024-12-08 08:55:09');

TRUNCATE TABLE `gen_field_config` ;

INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('45','3','author','varchar','author','String',null,'作者','0',null,'0','0','0','1','1',null,null,'2024-07-28 23:28:09','2024-07-29 17:22:43');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('46','3','business_name','varchar','businessName','String',null,'业务名(系统用户)','0',null,'0','0','0','1','1',null,null,'2024-07-28 23:28:09','2024-07-29 17:22:43');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('47','3','create_time','datetime','createTime','LocalDateTime',null,'创建时间','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:28:09','2024-07-29 17:22:43');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('48','3','entity_name','varchar','entityName','String',null,'实体类名(User)','0',null,'0','0','0','1','1',null,null,'2024-07-28 23:28:09','2024-07-29 17:22:43');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('49','3','id','bigint','id','Long',null,'','0',null,'0','0','0','1','1',null,null,'2024-07-28 23:28:09','2024-07-29 17:22:43');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('50','3','is_deleted','bit','isDeleted',null,null,'是否删除','0',null,'0','0','0','1','1',null,null,'2024-07-28 23:28:09','2024-07-29 17:22:43');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('51','3','module_name','varchar','moduleName','String',null,'模块名(xc-system)','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:28:09','2024-07-29 17:22:43');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('52','3','package_name','varchar','packageName','String',null,'包名(com.xc.system)','0',null,'0','0','0','1','1',null,null,'2024-07-28 23:28:09','2024-07-29 17:22:43');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('53','3','parent_menu_id','bigint','parentMenuId','Long',null,'上级菜单ID，对应sys_menu的id ','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:28:09','2024-07-29 17:22:43');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('54','3','table_name','varchar','tableName','String',null,'表名','0',null,'0','0','0','1','1',null,null,'2024-07-28 23:28:09','2024-07-29 17:22:43');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('55','3','update_time','datetime','updateTime','LocalDateTime',null,'更新时间','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:28:09','2024-07-29 17:22:43');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('56','4','column_length','int','columnLength','Integer',null,'','1',null,'1','1','1','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('57','4','column_name','varchar','columnName','String',null,'','1',null,'1','1','1','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('58','4','column_type','varchar','columnType','String',null,'','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('59','4','config_id','bigint','configId','Long',null,'关联的配置ID','0',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('60','4','create_time','datetime','createTime','LocalDateTime',null,'创建时间','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('61','4','field_comment','varchar','fieldComment','String',null,'字段描述','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('62','4','field_name','varchar','fieldName','String',null,'字段名称','0',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('63','4','field_sort','int','fieldSort','Integer',null,'字段排序','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('64','4','field_type','varchar','fieldType','String',null,'字段类型','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('65','4','form_type','tinyint','formType','Integer',null,'表单类型','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('66','4','id','bigint','id','Long',null,'','0',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('67','4','is_required','tinyint','isRequired','Integer',null,'是否必填','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('68','4','is_show_in_form','tinyint','isShowInForm','Integer',null,'是否在表单显示','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('69','4','is_show_in_list','tinyint','isShowInList','Integer',null,'是否在列表显示','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('70','4','is_show_in_query','tinyint','isShowInQuery','Integer',null,'是否在查询条件显示','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('71','4','query_type','tinyint','queryType','Integer',null,'查询方式','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('72','4','update_time','datetime','updateTime','LocalDateTime',null,'更新时间','1',null,'0','0','0','1','1',null,null,'2024-07-28 23:30:00','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('137','4','dict_code','varchar','dictCode','String',null,'字典类型','1',null,'0','0','0','1','1',null,null,'2024-07-30 00:08:01','2024-07-30 00:08:01');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('138','4','dict_type','varchar','dictType','String',null,'字典类型','1',null,'0','0','0','1','1',null,null,'2024-07-30 02:53:22','2024-07-30 02:53:22');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('143','17','create_by','bigint','createBy','Long',null,'创建人ID','0',null,'0','0','0','1','1',null,null,'2024-07-30 16:53:39','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('144','17','create_time','datetime','createTime','LocalDateTime',null,'创建时间','0',null,'0','0','0','1','1',null,null,'2024-07-30 16:53:39','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('145','17','id','bigint','id','Long',null,'','0',null,'0','0','0','1','1',null,null,'2024-07-30 16:53:39','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('146','17','is_deleted','tinyint','isDeleted','Integer',null,'逻辑删除标识(0-未删除 1-已删除)','0',null,'0','0','0','1','1',null,null,'2024-07-30 16:53:39','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('147','17','remark','varchar','remark','String',null,'描述、备注','1',null,'0','0','0','1','1',null,null,'2024-07-30 16:53:39','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('148','17','sys_key','varchar','sysKey','String',null,'配置key','0',null,'0','0','0','1','1',null,null,'2024-07-30 16:53:39','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('149','17','sys_name','varchar','sysName','String',null,'配置名称','0',null,'0','0','0','1','1',null,null,'2024-07-30 16:53:39','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('150','17','sys_value','varchar','sysValue','String',null,'配置值','0',null,'0','0','0','1','1',null,null,'2024-07-30 16:53:39','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('151','17','update_by','bigint','updateBy','Long',null,'更新人ID','1',null,'0','0','0','1','1',null,null,'2024-07-30 16:53:39','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('152','17','update_time','datetime','updateTime','LocalDateTime',null,'更新时间','1',null,'0','0','0','1','1',null,null,'2024-07-30 16:53:39','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('153','17','config_key','varchar','configKey','String',null,'配置key','0',null,'0','0','0','1','1',null,null,'2024-07-31 01:03:25','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('154','17','config_name','varchar','configName','String',null,'配置名称','0',null,'0','0','0','1','1',null,null,'2024-07-31 01:03:25','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('155','17','config_value','varchar','configValue','String',null,'配置值','0',null,'0','0','0','1','1',null,null,'2024-07-31 01:03:25','2024-07-31 23:33:47');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('3937','373','id','bigint','id','Long','1','主键','0','10','1','1','0','1','2','notice_type',null,'2024-09-30 17:03:03','2024-10-01 07:58:07');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('3938','373','dict_id','bigint','dictId','Long','2','字典ID','1','10','1','1','0','1','2',null,null,'2024-09-30 17:03:03','2024-10-01 07:58:07');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('3939','373','dict_code','varchar','dictCode','String','3','字典编码','1','50','1','1','0','1','2',null,null,'2024-09-30 17:03:03','2024-10-01 07:58:07');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('3940','373','name','varchar','name','String','4','字典项名称','1','50','1','1','0','1','2',null,null,'2024-09-30 17:03:03','2024-10-01 07:58:07');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('3941','373','value','varchar','value','String','5','字典项值','1','50','1','1','0','1','2',null,null,'2024-09-30 17:03:03','2024-10-01 07:58:07');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('3942','373','status','tinyint','status','Integer','6','状态（1-正常，0-禁用）','1','10','1','1','0','1','2',null,null,'2024-09-30 17:03:03','2024-10-01 07:58:07');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('3943','373','sort','int','sort','Integer','7','排序','1','10','1','1','0','1','2',null,null,'2024-09-30 17:03:03','2024-10-01 07:58:07');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('3944','373','remark','varchar','remark','String','8','备注','1','255','1','1','0','1','2',null,null,'2024-09-30 17:03:03','2024-10-01 07:58:07');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('3945','373','create_time','datetime','createTime','LocalDateTime','9','创建时间','1','10','1','1','0','1','1',null,null,'2024-09-30 17:03:03','2024-10-01 07:58:07');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('3946','373','update_time','datetime','updateTime','LocalDateTime','10','更新时间','1','10','1','1','0','1','9',null,null,'2024-09-30 17:03:03','2024-10-01 07:58:07');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('4944','460','id','bigint','id','Long','1','主键','0',null,'1','1','0','1','1',null,null,'2024-10-19 20:56:33','2024-10-19 20:56:33');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('4945','460','create_by','bigint','createBy','Long','2','创建人ID','1',null,'1','1','0','1','1',null,null,'2024-10-19 20:56:33','2024-10-19 20:56:33');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('4946','460','create_time','datetime','createTime','LocalDateTime','3','创建时间','1',null,'1','1','0','1','9',null,null,'2024-10-19 20:56:33','2024-10-19 20:56:33');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('4947','460','update_by','bigint','updateBy','Long','4','修改人ID','1',null,'1','1','0','1','1',null,null,'2024-10-19 20:56:33','2024-10-19 20:56:33');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('4948','460','update_time','datetime','updateTime','LocalDateTime','5','更新时间','1',null,'1','1','0','1','9',null,null,'2024-10-19 20:56:33','2024-10-19 20:56:33');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('4949','460','is_deleted','tinyint','isDeleted','Integer','6','逻辑删除标识(1-已删除 0-未删除)','0',null,'1','1','0','1','1',null,null,'2024-10-19 20:56:33','2024-10-19 20:56:33');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8160','728','id','int','id','Integer','1','','0',null,'1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8161','728','username','varchar','username','String','2','用户名','1','64','1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8162','728','nickname','varchar','nickname','String','3','昵称','1','64','1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8163','728','gender','tinyint','gender','Integer','4','性别((1-男 2-女 0-保密)','1',null,'1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8164','728','password','varchar','password','String','5','密码','1','100','1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8165','728','dept_id','int','deptId','Integer','6','部门ID','1',null,'1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8166','728','avatar','varchar','avatar','String','7','用户头像','1','255','1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8167','728','mobile','varchar','mobile','String','8','联系方式','1','20','1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8168','728','status','tinyint','status','Integer','9','状态((1-正常 0-禁用)','1',null,'1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8169','728','email','varchar','email','String','10','用户邮箱','1','128','1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8170','728','create_time','datetime','createTime','LocalDateTime','11','创建时间','1',null,'1','1','0','1','9',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8171','728','create_by','bigint','createBy','Long','12','创建人ID','1',null,'1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8172','728','update_time','datetime','updateTime','LocalDateTime','13','更新时间','1',null,'1','1','0','1','9',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8173','728','update_by','bigint','updateBy','Long','14','修改人ID','1',null,'1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8174','728','is_deleted','tinyint','isDeleted','Integer','15','逻辑删除标识(0-未删除 1-已删除)','1',null,'1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8175','728','openid','varchar','openid','String','16','微信OpenID','1','50','1','1','0','1','1',null,null,'2024-12-06 17:48:44','2024-12-08 13:28:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8176','729','id','bigint','id','Long','1','','0',null,'1','1','0','1','1',null,null,'2024-12-06 18:55:47','2024-12-06 20:03:34');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8177','729','config_name','varchar','configName','String','2','配置名称','0','50','1','1','0','1','1',null,null,'2024-12-06 18:55:47','2024-12-06 20:03:34');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8178','729','config_key','varchar','configKey','String','3','配置key','0','50','1','1','0','1','1',null,null,'2024-12-06 18:55:47','2024-12-06 20:03:34');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8179','729','config_value','varchar','configValue','String','4','配置值','0','100','1','1','0','1','1',null,null,'2024-12-06 18:55:47','2024-12-06 20:03:34');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8180','729','remark','varchar','remark','String','5','描述、备注','1','255','1','1','0','1','1',null,null,'2024-12-06 18:55:47','2024-12-06 20:03:34');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8181','729','create_time','datetime','createTime','LocalDateTime','6','创建时间','1',null,'1','1','0','1','9',null,null,'2024-12-06 18:55:47','2024-12-06 20:03:34');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8182','729','create_by','bigint','createBy','Long','7','创建人ID','1',null,'1','1','0','1','1',null,null,'2024-12-06 18:55:47','2024-12-06 20:03:34');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8183','729','update_time','datetime','updateTime','LocalDateTime','8','更新时间','1',null,'1','1','0','1','9',null,null,'2024-12-06 18:55:47','2024-12-06 20:03:34');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8184','729','update_by','bigint','updateBy','Long','9','更新人ID','1',null,'1','1','0','1','1',null,null,'2024-12-06 18:55:47','2024-12-06 20:03:34');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8185','729','is_deleted','tinyint','isDeleted','Integer','10','逻辑删除标识(0-未删除 1-已删除)','0',null,'1','1','0','1','1',null,null,'2024-12-06 18:55:47','2024-12-06 20:03:34');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8186','730','id','bigint','id','Long','1','主键','0',null,'1','1','0','1','1',null,null,'2024-12-06 20:01:31','2024-12-06 20:01:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8187','730','dict_code','varchar','dictCode','String','2','关联字典编码，与sys_dict表中的dict_code对应','1','50','1','1','0','1','1',null,null,'2024-12-06 20:01:31','2024-12-06 20:01:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8188','730','value','varchar','value','String','3','字典项值','1','50','1','1','0','1','1',null,null,'2024-12-06 20:01:31','2024-12-06 20:01:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8189','730','label','varchar','label','String','4','字典项标签','1','100','1','1','0','1','1',null,null,'2024-12-06 20:01:31','2024-12-06 20:01:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8190','730','status','tinyint','status','Integer','5','状态（1-正常，0-禁用）','1',null,'1','1','0','1','1',null,null,'2024-12-06 20:01:31','2024-12-06 20:01:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8191','730','sort','int','sort','Integer','6','排序','1',null,'1','1','0','1','1',null,null,'2024-12-06 20:01:31','2024-12-06 20:01:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8192','730','remark','varchar','remark','String','7','备注','1','255','1','1','0','1','1',null,null,'2024-12-06 20:01:31','2024-12-06 20:01:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8193','730','create_time','datetime','createTime','LocalDateTime','8','创建时间','1',null,'1','1','0','1','9',null,null,'2024-12-06 20:01:31','2024-12-06 20:01:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8194','730','tag_type','varchar','tagType','String','9','标签类型，用于前端样式展示（如success、warning等）','1','50','1','1','0','1','1',null,null,'2024-12-06 20:01:31','2024-12-06 20:01:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8195','730','update_time','datetime','updateTime','LocalDateTime','10','更新时间','1',null,'1','1','0','1','9',null,null,'2024-12-06 20:01:31','2024-12-06 20:01:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8198','732','id','bigint','id','Long','1','主键','0',null,'1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8199','732','module','varchar','module','String','2','日志模块','0','50','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8200','732','request_method','varchar','requestMethod','String','3','请求方式','0','64','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:31');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8201','732','request_params','text','requestParams','String','4','请求参数(批量请求参数可能会超过text)','1','65535','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8202','732','response_content','mediumtext','responseContent',null,'5','返回参数','1','16777215','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8203','732','content','varchar','content','String','6','日志内容','0','255','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8204','732','request_uri','varchar','requestUri','String','7','请求路径','1','255','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8205','732','method','varchar','method','String','8','方法名','1','255','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8206','732','ip','varchar','ip','String','9','IP地址','1','45','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8207','732','province','varchar','province','String','10','省份','1','100','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8208','732','city','varchar','city','String','11','城市','1','100','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8209','732','execution_time','bigint','executionTime','Long','12','执行时间(ms)','1',null,'1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8210','732','browser','varchar','browser','String','13','浏览器','1','100','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8211','732','browser_version','varchar','browserVersion','String','14','浏览器版本','1','100','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8212','732','os','varchar','os','String','15','终端系统','1','100','1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8213','732','create_by','bigint','createBy','Long','16','创建人ID','1',null,'1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8214','732','create_time','datetime','createTime','LocalDateTime','17','创建时间','1',null,'1','1','0','1','9',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8215','732','is_deleted','tinyint','isDeleted','Integer','18','逻辑删除标识(1-已删除 0-未删除)','0',null,'1','1','0','1','1',null,null,'2024-12-08 03:30:36','2024-12-08 20:22:32');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8216','733','id','bigint','id','Long','1','主键 ','0',null,'1','1','0','1','1',null,null,'2024-12-08 08:55:09','2024-12-08 08:55:09');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8217','733','dict_code','varchar','dictCode','String','2','字典编码','1','50','1','1','0','1','1',null,null,'2024-12-08 08:55:09','2024-12-08 08:55:09');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8218','733','name','varchar','name','String','3','类型编码','1','50','1','1','0','1','1',null,null,'2024-12-08 08:55:09','2024-12-08 08:55:09');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8219','733','status','tinyint','status','Integer','4','状态(0：正常，1：禁用)','1',null,'1','1','0','1','1',null,null,'2024-12-08 08:55:09','2024-12-08 08:55:09');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8220','733','remark','varchar','remark','String','5','备注','1','255','1','1','0','1','1',null,null,'2024-12-08 08:55:09','2024-12-08 08:55:09');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8221','733','create_time','datetime','createTime','LocalDateTime','6','创建时间','1',null,'1','1','0','1','9',null,null,'2024-12-08 08:55:09','2024-12-08 08:55:09');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8222','733','update_time','datetime','updateTime','LocalDateTime','7','更新时间','1',null,'1','1','0','1','9',null,null,'2024-12-08 08:55:09','2024-12-08 08:55:09');
INSERT INTO `gen_field_config` (`id`, `config_id`, `column_name`, `column_type`, `field_name`, `field_type`, `field_sort`, `field_comment`, `is_required`, `max_length`, `is_show_in_list`, `is_show_in_form`, `is_show_in_query`, `query_type`, `form_type`, `dict_type`, `column_length`, `created_at`, `updated_at`) VALUES ('8223','733','is_deleted','tinyint','isDeleted','Integer','8','是否删除(0：未删除，1：已删除)','1',null,'1','1','0','1','1',null,null,'2024-12-08 08:55:09','2024-12-08 08:55:09');

TRUNCATE TABLE `sys_config` ;

INSERT INTO `sys_config` (`id`, `config_name`, `config_key`, `config_value`, `remark`, `created_at`, `created_by`, `updated_at`, `updated_by`, `deleted_at`) VALUES ('1','系统限流QPS','IP_QPS_THRESHOLD_LIMIT','10','单个IP请求的最大每秒查询数（QPS）阈值Key','2024-10-27 10:14:51','1','2024-12-09 00:35:22','0',null);

TRUNCATE TABLE `sys_dept` ;

INSERT INTO `sys_dept` (`id`, `name`, `code`, `parent_id`, `tree_path`, `sort`, `status`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('1','技术部门','jishu','0','0','1','1','2','2022-04-19 12:46:37','2','2024-12-09 00:26:40',null);
INSERT INTO `sys_dept` (`id`, `name`, `code`, `parent_id`, `tree_path`, `sort`, `status`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('2','研发部门','RD001','1','0,1','1','1','2','2022-04-19 12:46:37','2','2024-12-09 00:26:40',null);
INSERT INTO `sys_dept` (`id`, `name`, `code`, `parent_id`, `tree_path`, `sort`, `status`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('3','测试部门','QA001','1','0,1','2','1','2','2022-04-19 12:46:37','2','2024-12-09 00:26:40',null);
INSERT INTO `sys_dept` (`id`, `name`, `code`, `parent_id`, `tree_path`, `sort`, `status`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('4','无回科技','WUHUI','0','0','2','1','2','2022-04-19 12:46:37','2','2024-12-09 00:26:40',null);
INSERT INTO `sys_dept` (`id`, `name`, `code`, `parent_id`, `tree_path`, `sort`, `status`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('5','市场部','M001','4','0,4','1','1','2','2022-04-19 12:46:37','2','2024-12-09 00:26:40',null);

TRUNCATE TABLE `sys_dict` ;

INSERT INTO `sys_dict` (`id`, `dict_code`, `name`, `status`, `remark`, `created_at`, `updated_at`, `deleted_at`) VALUES ('1','gender','性别','1',null,'2024-10-05 23:36:43','2024-10-05 23:36:43',null);
INSERT INTO `sys_dict` (`id`, `dict_code`, `name`, `status`, `remark`, `created_at`, `updated_at`, `deleted_at`) VALUES ('2','notice_type','通知类型','1',null,'2024-10-05 23:36:44','2024-10-05 23:36:44',null);
INSERT INTO `sys_dict` (`id`, `dict_code`, `name`, `status`, `remark`, `created_at`, `updated_at`, `deleted_at`) VALUES ('3','notice_level','通知级别','1',null,'2024-10-05 23:36:45','2024-10-05 23:36:45',null);

TRUNCATE TABLE `sys_dict_data` ;

INSERT INTO `sys_dict_data` (`id`, `dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `created_at`, `updated_at`) VALUES ('1','gender','1','男','primary','1','1',null,'2024-10-05 23:36:58','2024-10-05 23:36:58');
INSERT INTO `sys_dict_data` (`id`, `dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `created_at`, `updated_at`) VALUES ('2','gender','2','女','danger','1','2',null,'2024-10-05 23:36:58','2024-10-05 23:36:58');
INSERT INTO `sys_dict_data` (`id`, `dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `created_at`, `updated_at`) VALUES ('3','gender','0','保密','info','1','3',null,'2024-10-05 23:36:58','2024-10-05 23:36:58');
INSERT INTO `sys_dict_data` (`id`, `dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `created_at`, `updated_at`) VALUES ('4','notice_type','1','系统升级','success','1','1','','2024-10-05 23:36:59','2024-10-05 23:36:59');
INSERT INTO `sys_dict_data` (`id`, `dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `created_at`, `updated_at`) VALUES ('5','notice_type','2','系统维护','primary','1','2','','2024-10-05 23:36:59','2024-10-05 23:36:59');
INSERT INTO `sys_dict_data` (`id`, `dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `created_at`, `updated_at`) VALUES ('6','notice_type','3','安全警告','danger','1','3','','2024-10-05 23:36:59','2024-10-05 23:36:59');
INSERT INTO `sys_dict_data` (`id`, `dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `created_at`, `updated_at`) VALUES ('7','notice_type','4','假期通知','success','1','4','','2024-10-05 23:36:59','2024-10-05 23:36:59');
INSERT INTO `sys_dict_data` (`id`, `dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `created_at`, `updated_at`) VALUES ('8','notice_type','5','公司新闻','primary','1','5','','2024-10-05 23:36:59','2024-10-05 23:36:59');
INSERT INTO `sys_dict_data` (`id`, `dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `created_at`, `updated_at`) VALUES ('9','notice_type','99','其他','info','1','99','','2024-10-05 23:36:59','2024-10-05 23:36:59');
INSERT INTO `sys_dict_data` (`id`, `dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `created_at`, `updated_at`) VALUES ('10','notice_level','L','低','info','1','1','','2024-10-05 23:36:59','2024-10-05 23:36:59');
INSERT INTO `sys_dict_data` (`id`, `dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `created_at`, `updated_at`) VALUES ('11','notice_level','M','中','warning','1','2','','2024-10-05 23:36:59','2024-10-05 23:36:59');
INSERT INTO `sys_dict_data` (`id`, `dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `created_at`, `updated_at`) VALUES ('12','notice_level','H','高','danger','1','3','','2024-10-05 23:36:59','2024-10-05 23:36:59');

TRUNCATE TABLE `sys_log` ;


TRUNCATE TABLE `sys_menu` ;

INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('1','0','0','系统管理','2','','/system','Layout',null,null,null,'1','1','system','/system/user','2024-10-05 23:35:58','2024-10-05 23:35:58',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('2','1','0,1','用户管理','1','User','user','system/user/index',null,null,'1','1','1','el-icon-User',null,'2024-10-05 23:35:59','2024-10-05 23:35:59',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('3','1','0,1','角色管理','1','Role','role','system/role/index',null,null,'1','1','2','role',null,'2024-10-05 23:36:00','2024-10-05 23:36:00',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('4','1','0,1','菜单管理','1','SysMenu','menu','system/menu/index',null,null,'1','1','3','menu',null,'2024-10-05 23:36:01','2024-10-05 23:36:01',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('5','1','0,1','部门管理','1','Dept','dept','system/dept/index',null,null,'1','1','4','tree',null,'2024-10-05 23:36:02','2024-10-05 23:36:02',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('6','1','0,1','字典管理','1','Dict','dict','system/dict/index',null,null,'1','1','5','dict',null,'2024-10-05 23:36:02','2024-10-05 23:36:02',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('20','0','0','多级菜单','2',null,'/multi-level','Layout',null,'1',null,'1','9','cascader','','2024-10-05 23:36:02','2024-10-05 23:36:02',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('21','20','0,20','菜单一级','1',null,'multi-level1','demo/multi-level/level1',null,'1',null,'1','1','','','2024-10-05 23:36:02','2024-10-05 23:36:02',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('22','21','0,20,21','菜单二级','1',null,'multi-level2','demo/multi-level/children/level2',null,'0',null,'1','1','',null,'2024-10-05 23:36:02','2024-10-05 23:36:02',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('23','22','0,20,21,22','菜单三级-1','1',null,'multi-level3-1','demo/multi-level/children/children/level3-1',null,'0','1','1','1','','','2024-10-05 23:36:02','2024-10-05 23:36:02',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('24','22','0,20,21,22','菜单三级-2','1',null,'multi-level3-2','demo/multi-level/children/children/level3-2',null,'0','1','1','2','','','2024-10-05 23:36:02','2024-10-05 23:36:02',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('26','0','0','平台文档','2','','/doc','Layout',null,null,null,'1','8','document','https://juejin.cn/post/7228990409909108793','2024-10-05 23:36:02','2024-10-05 23:36:02',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('30','26','0,26','平台文档(外链)','3',null,'https://juejin.cn/post/7228990409909108793','',null,null,null,'1','2','el-icon-link','','2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('31','2','0,1,2','用户新增','4',null,'',null,'sys:user:add',null,null,'1','1','','','2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('32','2','0,1,2','用户编辑','4',null,'',null,'sys:user:edit',null,null,'1','2','','','2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('33','2','0,1,2','用户删除','4',null,'',null,'sys:user:delete',null,null,'1','3','','','2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('36','0','0','组件封装','2',null,'/component','Layout',null,null,null,'1','10','menu','','2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('37','36','0,36','富文本编辑器','1',null,'wang-editor','demo/wang-editor',null,null,'1','1','2','','','2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('38','36','0,36','图片上传','1',null,'upload','demo/upload',null,null,'1','1','3','','','2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('39','36','0,36','图标选择器','1',null,'icon-selector','demo/icon-selector',null,null,'1','1','4','','','2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('40','0','0','接口文档','2',null,'/api','Layout',null,'1',null,'1','7','api','','2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('41','40','0,40','Apifox','1',null,'apifox','demo/api/apifox',null,null,'1','1','1','api','','2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('70','3','0,1,3','角色新增','4',null,'',null,'sys:role:add',null,null,'1','2','',null,'2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('71','3','0,1,3','角色编辑','4',null,'',null,'sys:role:edit',null,null,'1','3','',null,'2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('72','3','0,1,3','角色删除','4',null,'',null,'sys:role:delete',null,null,'1','4','',null,'2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('73','4','0,1,4','菜单新增','4',null,'',null,'sys:menu:add',null,null,'1','1','',null,'2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('74','4','0,1,4','菜单编辑','4',null,'',null,'sys:menu:edit',null,null,'1','3','',null,'2024-10-05 23:36:04','2024-10-05 23:36:04',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('75','4','0,1,4','菜单删除','4',null,'',null,'sys:menu:delete',null,null,'1','3','',null,'2024-10-05 23:36:04','2024-10-05 23:36:04',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('76','5','0,1,5','部门新增','4',null,'',null,'sys:dept:add',null,null,'1','1','',null,'2024-10-05 23:36:04','2024-10-05 23:36:04',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('77','5','0,1,5','部门编辑','4',null,'',null,'sys:dept:edit',null,null,'1','2','',null,'2024-10-05 23:36:04','2024-10-05 23:36:04',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('78','5','0,1,5','部门删除','4',null,'',null,'sys:dept:delete',null,null,'1','3','',null,'2024-10-05 23:36:04','2024-10-05 23:36:04',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('79','6','0,1,6','字典新增','4',null,'',null,'sys:dict:add',null,null,'1','1','',null,'2024-10-05 23:36:04','2024-10-05 23:36:04',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('81','6','0,1,6','字典编辑','4',null,'',null,'sys:dict:edit',null,null,'1','2','',null,'2024-10-05 23:36:04','2024-10-05 23:36:04',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('84','6','0,1,6','字典删除','4',null,'',null,'sys:dict:delete',null,null,'1','3','',null,'2024-10-05 23:36:04','2024-10-05 23:36:04',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('88','2','0,1,2','重置密码','4',null,'',null,'sys:user:password:reset',null,null,'1','4','',null,'2024-10-05 23:36:04','2024-10-05 23:36:04',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('89','0','0','功能演示','2',null,'/function','Layout',null,null,null,'1','12','menu','','2024-10-05 23:36:04','2024-10-05 23:36:04',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('90','89','0,89','Websocket','1',null,'/function/websocket','demo/websocket',null,null,'1','1','3','','','2024-10-05 23:36:04','2024-10-05 23:36:04',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('95','36','0,36','字典组件','1',null,'dict-demo','demo/dictionary',null,null,'1','1','4','','','2024-10-05 23:36:05','2024-10-05 23:36:05',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('97','89','0,89','Icons','1',null,'icon-demo','demo/icons',null,null,'1','1','2','el-icon-Notification','','2024-10-05 23:36:05','2024-10-05 23:36:05',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('102','26','0,26','document','3','','internal-doc','demo/internal-doc',null,null,null,'1','1','document','','2024-10-05 23:36:05','2024-10-05 23:36:05',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('105','2','0,1,2','用户查询','4',null,'',null,'sys:user:query','0','0','1','0','',null,'2024-10-05 23:36:05','2024-10-05 23:36:05',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('106','2','0,1,2','用户导入','4',null,'',null,'sys:user:import',null,null,'1','5','',null,'2024-10-05 23:36:05','2024-10-05 23:36:05',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('107','2','0,1,2','用户导出','4',null,'',null,'sys:user:export',null,null,'1','6','',null,'2024-10-05 23:36:05','2024-10-05 23:36:05',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('108','36','0,36','增删改查','1',null,'curd','demo/curd/index',null,null,'1','1','0','','','2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('109','36','0,36','列表选择器','1',null,'table-select','demo/table-select/index',null,null,'1','1','1','','','2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('110','0','0','路由参数','2',null,'/route-param','Layout',null,'1','1','1','11','el-icon-ElementPlus',null,'2024-10-05 23:36:07','2024-10-05 23:36:07',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('111','110','0,110','参数(type=1)','1',null,'route-param-type1','demo/route-param',null,'0','1','1','1','el-icon-Star',null,'2024-10-05 23:36:07','2024-10-05 23:36:07','{"type": "1"}');
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('112','110','0,110','参数(type=2)','1',null,'route-param-type2','demo/route-param',null,'0','1','1','2','el-icon-StarFilled',null,'2024-10-05 23:36:07','2024-10-05 23:36:07','{"type": "2"}');
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('117','1','0,1','系统日志','1','Log','log','system/log/index',null,'0','1','1','6','document',null,'2024-10-05 23:36:07','2024-10-05 23:36:07',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('118','0','0','系统工具','2',null,'/codegen','Layout',null,'0','1','1','2','menu',null,'2024-10-05 23:36:07','2024-10-05 23:36:07',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('119','118','0,118','代码生成','1','Codegen','codegen','codegen/index',null,'0','1','1','1','code',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('120','1','0,1','系统配置','1','Config','config','system/config/index',null,'0','1','1','7','setting',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('121','120','0,1,120','系统配置查询','4',null,'',null,'sys:config:query','0','1','1','1','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('122','120','0,1,120','系统配置新增','4',null,'',null,'sys:config:add','0','1','1','2','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('123','120','0,1,120','系统配置修改','4',null,'',null,'sys:config:update','0','1','1','3','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('124','120','0,1,120','系统配置删除','4',null,'',null,'sys:config:delete','0','1','1','4','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('125','120','0,1,120','系统配置刷新','4',null,'',null,'sys:config:refresh','0','1','1','5','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('126','1','0,1','通知公告','1','Notice','notice','system/notice/index',null,null,null,'1','9','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('127','126','0,1,126','通知查询','4',null,'',null,'sys:notice:query',null,null,'1','1','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('128','126','0,1,126','通知新增','4',null,'',null,'sys:notice:add',null,null,'1','2','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('129','126','0,1,126','通知编辑','4',null,'',null,'sys:notice:edit',null,null,'1','3','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('130','126','0,1,126','通知删除','4',null,'',null,'sys:notice:delete',null,null,'1','4','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('133','126','0,1,126','通知发布','4',null,'',null,'sys:notice:publish','0','1','1','5','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('134','126','0,1,126','通知撤回','4',null,'',null,'sys:notice:revoke','0','1','1','6','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('135','1','0,1','字典数据','1','DictData','dict-data','system/dict/data',null,'0','1','0','6','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('136','135','0,1,135','字典数据新增','4',null,'',null,'sys:dict-data:add',null,null,'1','2','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('137','135','0,1,135','字典数据编辑','4',null,'',null,'sys:dict-data:edit',null,null,'1','3','',null,'2024-10-05 23:36:09','2024-10-05 23:36:09',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('138','135','0,1,135','字典数据删除','4',null,'',null,'sys:dict-data:delete',null,null,'1','4','',null,'2024-10-05 23:36:09','2024-10-05 23:36:09',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('139','3','0,1,3','角色查询','4',null,'',null,'sys:role:query',null,null,'1','1','',null,'2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('140','4','0,1,4','菜单查询','4',null,'',null,'sys:menu:query',null,null,'1','1','',null,'2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('141','5','0,1,5','部门查询','4',null,'',null,'sys:dept:query',null,null,'1','1','',null,'2024-10-05 23:36:03','2024-10-05 23:36:03',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('142','6','0,1,6','字典查询','4',null,'',null,'sys:dict:query',null,null,'1','1','',null,'2024-10-05 23:36:04','2024-10-05 23:36:04',null);
INSERT INTO `sys_menu` (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `created_at`, `updated_at`, `params`) VALUES ('143','135','0,1,135','字典数据查询','4',null,'',null,'sys:dict-data:query',null,null,'1','1','',null,'2024-10-05 23:36:08','2024-10-05 23:36:08',null);

TRUNCATE TABLE `sys_notice` ;

INSERT INTO `sys_notice` (`id`, `title`, `content`, `type`, `level`, `target_type`, `target_user_ids`, `publisher_id`, `publish_status`, `publish_time`, `revoke_time`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('1','v2.12.0 新增系统日志，访问趋势统计功能。','<p>1. 消息通知</p><p>2. 字典重构</p><p>3. 代码生成</p>','1','L','1','2','2','1','2024-09-30 17:21:41','2024-09-30 17:21:04','2','2024-09-28 11:21:06','2','2024-12-09 00:26:39',null);
INSERT INTO `sys_notice` (`id`, `title`, `content`, `type`, `level`, `target_type`, `target_user_ids`, `publisher_id`, `publish_status`, `publish_time`, `revoke_time`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('2','v2.13.0 新增菜单搜索。','<p>1. 消息通知</p><p>2. 字典重构</p><p>3. 代码生成</p>','1','L','1','2','2','1','2024-09-30 17:22:41','2024-09-30 17:21:04','2','2024-09-28 11:21:06','2','2024-12-09 00:26:39',null);
INSERT INTO `sys_notice` (`id`, `title`, `content`, `type`, `level`, `target_type`, `target_user_ids`, `publisher_id`, `publish_status`, `publish_time`, `revoke_time`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('3','
v2.14.0 新增个人中心。','<p>1. 消息通知</p><p>2. 字典重构</p><p>3. 代码生成</p>','1','L','1','2','2','1','2024-09-30 17:23:41','2024-09-30 17:21:04','2','2024-09-28 11:21:06','2','2024-12-09 00:26:39',null);
INSERT INTO `sys_notice` (`id`, `title`, `content`, `type`, `level`, `target_type`, `target_user_ids`, `publisher_id`, `publish_status`, `publish_time`, `revoke_time`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('4','v2.15.0 登录页面改造。','<p>1. 消息通知</p><p>2. 字典重构</p><p>3. 代码生成</p>','1','L','1','2','2','1','2024-09-30 17:24:41','2024-09-30 17:21:04','2','2024-09-28 11:21:06','2','2024-12-09 00:26:39',null);
INSERT INTO `sys_notice` (`id`, `title`, `content`, `type`, `level`, `target_type`, `target_user_ids`, `publisher_id`, `publish_status`, `publish_time`, `revoke_time`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('5','v2.16.0 通知公告、字典翻译组件。','<p>1. 消息通知</p><p>2. 字典重构</p><p>3. 代码生成</p>','1','L','1','2','2','1','2024-09-30 17:25:41','2024-09-30 17:21:04','2','2024-09-28 11:21:06','2','2024-12-09 00:26:39',null);
INSERT INTO `sys_notice` (`id`, `title`, `content`, `type`, `level`, `target_type`, `target_user_ids`, `publisher_id`, `publish_status`, `publish_time`, `revoke_time`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('6','系统将于本周六凌晨 2 点进行维护，预计维护时间为 2 小时。','<p>1. 消息通知</p><p>2. 字典重构</p><p>3. 代码生成</p>','2','H','1','2','2','1','2024-09-30 17:26:41','2024-09-30 17:21:04','2','2024-09-28 11:21:06','2','2024-12-09 00:26:39',null);
INSERT INTO `sys_notice` (`id`, `title`, `content`, `type`, `level`, `target_type`, `target_user_ids`, `publisher_id`, `publish_status`, `publish_time`, `revoke_time`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('7','最近发现一些钓鱼邮件，请大家提高警惕，不要点击陌生链接。','<p>1. 消息通知</p><p>2. 字典重构</p><p>3. 代码生成</p>','3','L','1','2','2','1','2024-09-30 17:27:41','2024-09-30 17:21:04','2','2024-09-28 11:21:06','2','2024-12-09 00:26:39',null);
INSERT INTO `sys_notice` (`id`, `title`, `content`, `type`, `level`, `target_type`, `target_user_ids`, `publisher_id`, `publish_status`, `publish_time`, `revoke_time`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('8','国庆假期从 10 月 1 日至 10 月 7 日放假，共 7 天。','<p>1. 消息通知</p><p>2. 字典重构</p><p>3. 代码生成</p>','4','L','1','2','2','1','2024-09-30 17:28:41','2024-09-30 17:21:04','2','2024-09-28 11:21:06','2','2024-12-09 00:26:39',null);
INSERT INTO `sys_notice` (`id`, `title`, `content`, `type`, `level`, `target_type`, `target_user_ids`, `publisher_id`, `publish_status`, `publish_time`, `revoke_time`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('9','公司将在 10 月 15 日举办新产品发布会，敬请期待。','<p>1. 消息通知</p><p>2. 字典重构</p><p>3. 代码生成</p>','5','H','1','2','2','1','2024-09-30 17:29:41','2024-09-30 17:21:04','2','2024-09-28 11:21:06','2','2024-12-09 00:26:39',null);
INSERT INTO `sys_notice` (`id`, `title`, `content`, `type`, `level`, `target_type`, `target_user_ids`, `publisher_id`, `publish_status`, `publish_time`, `revoke_time`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('10','v2.16.1 版本修复了 WebSocket 重复连接导致的后台线程阻塞问题，优化了通知公告。','<p>1. 消息通知</p><p>2. 字典重构</p><p>3. 代码生成</p>','1','M','1','2','2','1','2024-09-30 17:30:41','2024-09-30 17:21:04','2','2024-09-28 11:21:06','2','2024-12-09 00:26:39',null);

TRUNCATE TABLE `sys_role` ;

INSERT INTO `sys_role` (`id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('1','超级管理员','ROOT','1','1','0','0','2021-05-21 14:56:51','0','2024-12-09 00:35:24',null);
INSERT INTO `sys_role` (`id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('2','系统管理员','ADMIN','2','1','0','0','2021-03-25 12:31:54','0','2024-12-09 00:35:24',null);
INSERT INTO `sys_role` (`id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('3','访问游客','GUEST','3','1','2','0','2021-04-16 15:49:05','0','2024-12-09 00:35:24',null);
INSERT INTO `sys_role` (`id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('4','系统管理员1','ADMIN1','4','1','1','0','2021-04-25 12:39:51','0','2024-12-09 00:35:24',null);
INSERT INTO `sys_role` (`id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('5','系统管理员2','ADMIN2','5','1','1','0','2021-04-25 12:39:52','0','2024-12-09 00:35:24',null);
INSERT INTO `sys_role` (`id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('6','系统管理员3','ADMIN3','6','1','1','0','2021-04-25 12:39:53','0','2024-12-09 00:35:24',null);
INSERT INTO `sys_role` (`id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('7','系统管理员4','ADMIN4','7','1','1','0','2021-04-25 12:39:54','0','2024-12-09 00:35:24',null);
INSERT INTO `sys_role` (`id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('8','系统管理员5','ADMIN5','8','1','1','0','2021-05-25 12:39:52','0','2024-12-09 00:35:24',null);
INSERT INTO `sys_role` (`id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('9','系统管理员6','ADMIN6','9','1','1','0','2021-05-25 12:39:53','0','2024-12-09 00:35:24',null);
INSERT INTO `sys_role` (`id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('10','系统管理员7','ADMIN7','10','1','1','0','2021-05-25 12:39:54','0','2024-12-09 00:35:24',null);
INSERT INTO `sys_role` (`id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('11','系统管理员8','ADMIN8','11','1','1','0','2021-06-25 12:39:54','0','2024-12-09 00:35:24',null);
INSERT INTO `sys_role` (`id`, `name`, `code`, `sort`, `status`, `data_scope`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`) VALUES ('12','系统管理员9','ADMIN9','12','1','1','0','2021-07-25 12:39:54','0','2024-12-09 00:35:24',null);

TRUNCATE TABLE `sys_role_menu` ;

INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','1');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','2');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','3');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','4');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','5');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','6');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','20');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','21');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','22');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','23');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','24');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','26');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','30');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','31');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','32');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','33');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','36');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','37');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','38');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','39');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','40');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','41');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','70');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','71');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','72');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','73');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','74');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','75');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','76');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','77');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','78');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','79');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','81');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','84');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','85');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','86');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','87');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','88');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','89');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','90');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','91');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','95');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','97');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','102');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','105');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','106');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','107');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','108');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','109');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','110');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','111');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','112');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','117');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','118');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','119');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','120');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','121');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','122');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','123');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','124');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','125');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','126');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','127');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','128');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','129');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','130');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','131');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','132');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','133');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','134');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','135');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','136');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','137');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','138');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','139');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','140');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','141');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','142');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('2','143');

TRUNCATE TABLE `sys_user` ;

INSERT INTO `sys_user` (`id`, `username`, `nickname`, `openid`, `gender`, `password`, `dept_id`, `avatar`, `mobile`, `status`, `email`, `created_at`, `created_by`, `updated_at`, `updated_by`, `deleted_at`) VALUES ('1','admin','系统管理员',null,'1','$2a$10$xVWsNOhHrCxh5UbpCE7/HuJ.PAOKcYAqRxD2CO2nVnJS.IAXkr5aq','3','https://foruda.gitee.com/images/1723603502796844527/03cdca2a_716974.gif','17621210366','1','','2019-10-10 13:41:22','0','2024-12-09 00:35:24','0',null);
INSERT INTO `sys_user` (`id`, `username`, `nickname`, `openid`, `gender`, `password`, `dept_id`, `avatar`, `mobile`, `status`, `email`, `created_at`, `created_by`, `updated_at`, `updated_by`, `deleted_at`) VALUES ('3','test','测试小用户',null,'1','$2a$10$xVWsNOhHrCxh5UbpCE7/HuJ.PAOKcYAqRxD2CO2nVnJS.IAXkr5aq','3','https://foruda.gitee.com/images/1723603502796844527/03cdca2a_716974.gif','17621210366','1','<EMAIL>','2021-06-05 01:31:29','0','2025-01-06 20:38:56','2',null);

TRUNCATE TABLE `sys_user_notice` ;

INSERT INTO `sys_user_notice` (`id`, `notice_id`, `user_id`, `is_read`, `read_time`, `created_at`, `updated_at`, `deleted_at`) VALUES ('1','1','2','1',null,'2024-09-28 17:42:46','2024-12-09 00:26:39',null);
INSERT INTO `sys_user_notice` (`id`, `notice_id`, `user_id`, `is_read`, `read_time`, `created_at`, `updated_at`, `deleted_at`) VALUES ('2','2','2','1',null,'2024-09-29 09:39:08','2024-12-09 00:26:39',null);
INSERT INTO `sys_user_notice` (`id`, `notice_id`, `user_id`, `is_read`, `read_time`, `created_at`, `updated_at`, `deleted_at`) VALUES ('3','3','2','1',null,'2024-09-29 09:50:41','2024-12-09 00:26:39',null);
INSERT INTO `sys_user_notice` (`id`, `notice_id`, `user_id`, `is_read`, `read_time`, `created_at`, `updated_at`, `deleted_at`) VALUES ('4','4','2','1',null,'2024-09-29 09:50:41','2024-12-09 00:26:39',null);
INSERT INTO `sys_user_notice` (`id`, `notice_id`, `user_id`, `is_read`, `read_time`, `created_at`, `updated_at`, `deleted_at`) VALUES ('5','5','2','1',null,'2024-09-29 09:56:42','2024-12-09 00:26:39',null);
INSERT INTO `sys_user_notice` (`id`, `notice_id`, `user_id`, `is_read`, `read_time`, `created_at`, `updated_at`, `deleted_at`) VALUES ('6','6','2','1',null,'2024-09-29 09:56:42','2024-12-09 00:26:39',null);
INSERT INTO `sys_user_notice` (`id`, `notice_id`, `user_id`, `is_read`, `read_time`, `created_at`, `updated_at`, `deleted_at`) VALUES ('7','7','2','1',null,'2024-09-29 10:08:01','2024-12-09 00:26:39',null);
INSERT INTO `sys_user_notice` (`id`, `notice_id`, `user_id`, `is_read`, `read_time`, `created_at`, `updated_at`, `deleted_at`) VALUES ('8','8','2','1',null,'2024-09-29 10:08:01','2024-12-09 00:26:39',null);
INSERT INTO `sys_user_notice` (`id`, `notice_id`, `user_id`, `is_read`, `read_time`, `created_at`, `updated_at`, `deleted_at`) VALUES ('9','9','2','1',null,'2024-09-29 10:17:51','2024-12-09 00:26:39',null);
INSERT INTO `sys_user_notice` (`id`, `notice_id`, `user_id`, `is_read`, `read_time`, `created_at`, `updated_at`, `deleted_at`) VALUES ('10','10','2','1',null,'2024-09-29 10:17:51','2024-12-09 00:26:39',null);

TRUNCATE TABLE `sys_user_role` ;

INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES ('1','1');
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES ('2','2');
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES ('3','3');
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES ('3','4');
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES ('3','5');
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES ('3','6');

